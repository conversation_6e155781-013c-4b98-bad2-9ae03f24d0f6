<template>
    <div class='GeneralCreate' v-loading="loading">
        <div class="dmbEdit">
            <table class="table" border="1">
                <tr style="height:100px;line-height: 100px;text-align: center;">
                    <th> {{ dmbName }} </th>
                </tr>
                <tr>
                    <th style="display:flex;align-items: center;justify-content: center;"> 餐段 </th>
                    <th v-for="(item, index) in dmb_spec" class="screen" :key="index">
                        <span :class="item == 0 ? 'arcoss' : 'vision'"> {{ index + 1 }} </span>
                    </th>
                </tr>
                <tr v-for="(item, index) in timeframe" :key="item.name">
                    <th style="width:120px;display: flex;justify-content: center;align-items: center;"> {{ item.name }}
                        <!-- ( {{ item.value }} ) -->
                    </th>
                    <th v-for="(item1, index1) in item.addContentArray" class="screen_content" :key="index1">
                        <div class="newdmb" @click="openSelectImage(item, item1, index, index1)"
                            v-show="item1.imgUrl == ''">
                            <img src="@/assets/img/newDmb.svg" alt="">
                            新增
                        </div>
                        <div class="newdmb1" style="position: relative;" v-show="item1.imgUrl != ''">
                            <img :src="item1.imgUrl" :class="item1.direction == 0 ? 'screen_arcoss' : 'screen_vision'"
                                alt="">
                            <!-- {{item1}} -->
                            <div v-if="item1.active == false" class="activeState"> 未投放... </div>
                            <div v-else-if="item1.active == true" class="activeState"> 投放中... </div>
                            <div style="position:absolute;right:6.2%" class="activeState" v-show="item.imgLength != 0">
                                该屏幕有{{ item1.imgLength }}个内容</div>
                            <div class="preview" @click="toPreview(item1)"> 预览 </div>
                            <div @click.stop="deleteContent(item1)" class="delete">
                                删除
                            </div>
                        </div>

                    </th>
                </tr>
                <el-button @click="pushing" type="primary" class="ab">发布</el-button>

            </table>

        </div>




        <el-dialog title="新增内容" :visible.sync="showAlertBox" width="61%" custom-class="tabsDatat" @close="closeBox"
            :before-close="showAlertBox">
            <!--      <div class='top'>-->
            <!--        <h3>新增内容</h3>-->
            <!--        <p @click.stop='showAlertBox=false'>×</p>-->
            <!--      </div>-->
            <div class='threeParts tag_set'>
                <!--        tab栏切换-->
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="图片" name="first">
                        <picture-resources :tagsList='tagsList' :delTagsList='delTagsList' @checkedList='checkedList'
                            @setImgPreview='setImgPreview' ref="picture"></picture-resources>

                    </el-tab-pane>
                    <el-tab-pane label="视频" name="second">
                        <video-resources @setVideoPreview='setVideoPreview' @checkedList='checkedList' ref="video">
                        </video-resources>
                    </el-tab-pane>
                    <el-tab-pane label="H5" name="third">
                        <h5-resources @setH5Preview='setH5Preview' @checkedList='checkedList2' ref="h5"></h5-resources>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class='btns dialog_btns'>
                <el-button type='primary' @click.stop='cancel' style="margin-right:85px">取消</el-button>
                <el-button type='primary' @click.stop='alertOk' :loading="SaveBtnLoading">确定</el-button>
                <!-- <el-button type='primary' v-debounce.stop='alertOk'>确定</el-button> -->
            </div>
        </el-dialog>
        <!-- 预览mask -->
        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask'>
        </previsualization>
    </div>
</template>

<script>
// import { self_photo_list } from "@/api/files/pictureResources";
// import { get_video_list } from "@/api/files/videoResources";
import { get_btpub_detail } from "@/api/contentdeploy/contentdeploy"
import { quick_get_btplan_detail, quick_exec_btplan_handler } from "@/api/strategy/strategy"
import pictureResources from '@/components/ContentCenter/pictureResources'
import videoResources from '@/components/ContentCenter/videoResources'
import h5Resources from '@/components/ContentCenter/h5Resources'
import previsualization from '@/components/communal/previsualization'
import { get_daypart_data, del_btpub_content, create_group_classify, add_btpub_content, batch_create_draft, edit_group_classify_cs } from "@/api/contentdeploy/contentdeploy"
import { get_classify_detail } from "@/api/device/device"


export default {
    name: 'GeneralCreate',
    components: {
        pictureResources,
        videoResources,
        h5Resources,
        // multiScreenResources,
        // floatsResources,
        previsualization
    },
    data() {
        return {
            //选择框
            picList: [{
                "img_url": "",
                "checked": 1
            }],
            checkIndex: -1,
            checkShow: false,
            //  图片、视频、H5
            activeName: 'first',
            //  大图
            showBg: false,
            showAlertBox: false,
            fileList: [],
            // 搜索框
            searchValue: "",
            activeName: "first",
            imgList: [],
            VideoList: [],
            currentPage: 1, //页码
            totalNum: 0, //总数据数量
            pageSize: 10, //每页显示多少条
            tagsList: [],
            delTagsList: [],
            PreviewType: '',
            PreviewSrc: '',
            disabled: false,
            cs_list: [],
            pictureList: false,
            timeframe: [],
            daypartgroup: "",
            dmbContent: [],
            loading: true,
            dmbData: {},
            dmbName: "",
            screen_index: '',
            isSuccess: false,
            SaveBtnLoading: false
        }
    },

    watch: {

    },
    props: {
        btplan_id: {
            type: String
        },
        selectData: {}
    },
    created() {
        this.get_btpub_detail_info(
            {
                btplan_id: this.btplan_id,
                range: 'simple'
            });
    },
    mounted() {

    },
    methods: {
        // 预览
        setImgPreview(val) {
            this.PreviewType = 'image'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val) {
            this.PreviewType = 'video'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        setH5Preview(val) {
            this.PreviewType = 'h5'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = '';
        },
        checkedList(imgList) {
            imgList.forEach(item => {
                item.thumb_url = item.thumb
            })
            this.imgList = imgList
            //   this.$emit("selectImage", imgUrl);
        },
        checkedList2(imgList) {
            imgList.forEach(item => {
                item.thumb_url = item.thumb_url
            })
            this.imgList = imgList
            //   this.$emit("selectImage", imgUrl);
        },
        goBack() {
            this.$router.go(-1);
        },
        //  弹出框取消按钮
        // 确认选择图片
        async alertOk() {
            this.SaveBtnLoading = true;
            this.timeframe = JSON.parse(JSON.stringify(this.timeframe))
            this.loading = true;
            if (this.cf_id) {

            } else {
                await create_group_classify(this.selectData).then(res => {
                    console.log(res, 'res');
                    if (res.rst == "ok") {
                        this.cf_id = res.data[0].g_cf_id;
                        this.showAlertBox = true;
                        const params1 = {
                            btplan_id: this.btplan_id,
                            ref_id: this.cf_id,
                            ref_source: "ds",
                            daypartname: this.timeframe[this.selectDayPart].name,
                            screen_index: this.screen_index
                        }

                        add_btpub_content(params1).then(res => {
                            if (res.rst == "ok") {
                            }
                        })

                    } else {
                        this.$message.warning("创建专辑失败");
                        return;
                    }
                })
            }

            this.imgList.forEach(item => {
                let paramsa = {
                    shop_group_id: Number(localStorage.getItem("group_id")),
                    attr_list: [
                        {
                            tpl_name: this.activeName == 'first' ?
                                this.v_or_h == 0 ? 'HD_welposterH_0002854' : 'HD_welposter_0002853'
                                : this.v_or_h == 0 ? 'HD_video_tplH_0007' : 'HD_video_tpl_0007',
                            file_id: item.file_id,
                            file_type: this.activeName == 'first' ? 'img' : 'video'
                        }
                    ]
                }
                batch_create_draft(paramsa).then(resa => {
                    if (resa.rst == "ok") {
                        const pas = {
                            g_group_id: Number(localStorage.getItem("group_id")),
                            g_cf_id: this.cf_id,
                            attrs: {
                                add_cs_list: resa.data[0].cs_list
                            }
                        }
                        edit_group_classify_cs(pas).then(ress => {
                            if (ress.rst == "ok") {
                                this.$message.closeAll()
                                this.$message.success("添加内容成功");
                                this.showAlertBox = false;
                                this.isSuccess = true;
                                this.SaveBtnLoading = false;
                                this.get_btpub_detail_info({
                                    btplan_id: this.btplan_id,
                                    range: 'simple'
                                });
                                this.getDmbContent()
                            } else {
                                this.$message.warning(ress.error_msg);
                            }
                        })
                    }

                })
            })

        },
        cancel() {
            this.showAlertBox = false;
            this.get_btpub_detail_info({
                btplan_id: this.btplan_id,
                range: 'simple'
            });
            // this.getDmbContent()
        },
        closeBox() {
            this.showAlertBox = false;
            this.get_btpub_detail_info({
                btplan_id: this.btplan_id,
                range: 'simple'
            });
            this.getDmbContent()
        },
        //tab切换
        handleClick(tab, event) {
        },
        //选择框
        checked(idx) {
            this.checkShow = !this.checkShow;
            this.checkIndex = idx;
        },
        get_btpub_detail_info(btplan_id) {
            const params = btplan_id
            quick_get_btplan_detail(params).then(data => {
                if (data.rst == 'ok') {
                    console.log("dadatadatadatadatadatadatadatadatata", data);
                    this.dmb_spec = data["data"][0]["dmb_spec"];
                    this.daypartgroup = data["data"][0]['play_info']['daypartgroup']
                    this.dmbContent = data["data"][0].contents;
                    this.DeployForm = data["data"][0]['pub_info'] ? data["data"][0]['pub_info']['pub_json'] : {}
                    this.dmbData = data["data"][0]
                    this.dmbName = data["data"][0]["name"]

                    this.getDayPart(data['data'][0])
                    this.loading = false;
                } else {

                }
            })
        },

        // daypart
        getDayPart() {
            const params = {
                classModel: "SysDayPart",
                page: 0,
                size: 10
            }
            this.timeframe = []
            get_daypart_data(params).then(res => {
                res.data[0].content.forEach((item, index) => {
                    if (item.group_name == this.daypartgroup) {
                        this.timeframe = item.attr_list
                    }
                })
                this.addContentArray = []

                this.dmb_spec.split("").forEach((item, index) => {
                    this.addContentArray.push({
                        imgUrl: "",
                        direction: item,
                        screen_index: index + 1,
                    })
                })

                this.timeframe.forEach(item => {
                    this.$set(item, 'addContentArray', this.addContentArray)
                })
                this.timeframe = JSON.parse(JSON.stringify(this.timeframe))
                this.timeframe.forEach(item => {
                    item.addContentArray.forEach(item1 => {
                        item1.daypartname = item.daypartname
                    })
                })
                this.getDmbContent()
                this.loading = false;
            })
        },

        openSelectImage(item, item1, index, index1) {
            this.btplan_id = this.btplan_id;
            this.selectDayPart = index;
            this.selectImage = item1.screen_index;
            setTimeout(() => {
                this.$refs.picture.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.video.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.h5.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.picture.batchState = true;
                this.$refs.video.batchState = true;
                this.$refs.h5.batchState = true;
                this.$refs.video.isDMB = true;
                this.$refs.picture.isDMB = true;
                this.$refs.picture.isContent = true;
                this.$refs.video.isContent = true;
                this.$refs.h5.isContent = true;
                this.$refs.h5.isDMB = true;
            }, 500);

            this.selectData = {
                group_id: localStorage.getItem("group_id"),
                dire: item1.direction == 0 ? 'h' : 'v',
                create_from: 14,
                // cf_label: `${this.btplan_id}_${item.name}_${item1.screen_index + 1}`,
                cf_label: `${item.name}_${item1.screen_index}_${new Date().getTime()}`,
                attributes: {
                    // label: `${this.btplan_id}_${item1.screen_index + 1}`,
                    play_mode: "full_day",
                    daypart_name: item.name
                }
            }
            this.v_or_h = item1.direction;
            this.screen_index = item1.screen_index;
            this.showAlertBox = true;

            this.cf_id = item1.cf_id ? item1.cf_id : '';
        },
        getDmbContent() {
            this.timeframe.forEach(item => {
                item.addContentArray.forEach(item1 => {
                    this.dmbContent.forEach(item2 => {
                        if (item1.daypartname == item2.daypartname && item1.screen_index == item2.screen_index) {
                            item1.ref_id = item2.ref_id;
                            item1.cf_id = item2.ref_id;
                            item1.item_id = item2.item_id;
                            get_classify_detail({ g_cf_id: item1.ref_id,offset:0,limit:300 }).then(res => {
                                item1.active = res['data'][0].active;

                                item1.imgUrl = res['data'][0]['cs_list'][0] ? res['data'][0]['cs_list'][0]["thumb_url"] : ""
                                item1.imgLength = res['data'][0]['cs_list'].length
                            })
                        }
                    })
                })
            })
        },
        // 删除专辑内容
        deleteContent(item1) {
            const params = {
                item_id: item1.item_id
            }
            del_btpub_content(params).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("删除成功")
                    this.get_btpub_detail_info({
                        btplan_id: this.btplan_id,
                        range: 'simple'
                    });
                    this.getDmbContent()
                } else {
                    this.$message.warning(res.error_msg)
                }
            })
        },
        toPreview(item) {
            this.$router.push({
                path: "/deploy/editDMB",
                query: {
                    ref_id: item.ref_id,
                    name: this.dmbName,
                    btplan_id: item.item_id,
                    showInput: ""
                }
            })
        },
        pushing() {
            let status = false;
            this.timeframe.forEach(item => {
                item.addContentArray.forEach(item1 => {
                    if (item1.imgUrl) {
                        status = true;
                    } else {
                        status = false;
                    }
                })
            })
            if (status == true) {
                quick_exec_btplan_handler({
                    btplan_id: this.btplan_id
                }).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success("发布成功");
                        this.$router.push('/deploy/quickHistory')
                    } else {
                        this.$message.error("发布失败")
                    }
                })
            } else {
                this.$message.warning("请选择内容")
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.dialog_btns {
  height: 50px;
  button {
       margin: 0 20px 0 0 !important;
  }
}
</style>
<style lang="scss" scoped>
.GeneralCreate {
    width: 100%;
    /* padding: 0 14px 0 13px; */
    /* background-color: #f8f7f7; */
    margin-top: 20px;
    border: rgba(229, 229, 229, 1) solid 1px;
}

.actionBtn {
    position: relative;
    width: 100%;
    height: 12px;
    padding-right: 71px;
}

.pageHead {
    height: 58px;
    line-height: 58px;
    font-size: 14px;
    color: #505050;
    font-weight: bold;
    border-bottom: 1px solid #ecebeb;
}

.el-icon-back {
    color: var(--btn-background-color);
    font-size: 25px;
    margin-right: 10px;
    vertical-align: middle;
}

button {
    position: absolute;
    right: 110px;
    margin: 15px 15px;
}

.el-button--primary {
    right: 0;
}

ul {
    margin-top: 40px;
    color: rgba(80, 80, 80, 1);
    background-color: rgba(255, 255, 255, 1);
    font-size: 14px;
    /* border: rgba(229, 229, 229, 1) solid 1px; */
    text-align: center;
    list-style: none;
    padding: 10px 10px;
    flex-wrap: wrap;
    display: flex;
    align-content: flex-start;
}

ul>li {
    width: 292px;
    height: 284px;
    color: rgba(80, 80, 80, 1);
    background-color: rgba(255, 255, 255, 1);
    font-size: 15px;
    border: rgba(229, 229, 229, 1) solid 1px;
    margin: 10px 9px;
}

ul>li>img {
    width: 284px;
    height: 157px;
    margin: 5px 0;
}

ul>li>div {
    margin-top: 12px;
    color: rgba(80, 80, 80, 1);
    font-size: 12px;
    text-align: left;
    margin-left: 12px;
    font-weight: bold;
}

/*取消*/
.btns {
    display: flex;
    margin-top: -12px;
    padding-bottom: 10px;
    justify-content: flex-end;
    width: 100%;
}


.dmbEdit {
    display: flex;
    align-items: center;
    justify-content: center;
}

.table {
    width: 1115px;
    // height: 575px;
    // height: 100vh;
    margin-top: 20px;

    tr {
        border: 0 !important;
    }

    .screen {
        height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 300px;

        span {
            display: inline-block;
            line-height: 30px;
            text-align: center;
            background-color: rgba(108, 178, 255, 0.3642857142857143);
        }
    }
}

.arcoss {
    width: 34px;
    height: 30px;
}

.vision {
    width: 32px;
    height: 40px;
    line-height: 40px !important;
}

.newdmb {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;

    img {
        width: 25px;
        height: 25px;
    }
}


.delete {
    width: 45px;
    height: 45px;
    color: white;
    background-color: #ccc;
    position: absolute;
    left: 55%;
    top: 45%;
    line-height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
}


.preview {
    width: 45px;
    height: 45px;
    color: white;
    background-color: #ccc;
    position: absolute;
    left: 30%;
    top: 45%;
    line-height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
}



.newdmb1 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}


.newdmb1:hover {
    .delete {
        display: block;
    }

    .preview {
        display: block;
    }
}

.screen_content {
    height: 250px;
    // width: 400px !important;
}

tr {
    width: 100% !important;
    display: flex !important;

    th {
        flex: 1;
    }
}

.screen_arcoss {
    // width: 340px !important;
    // height: 230px !important;
    width: 87%;
    height: 145px;
    // object-fit: contain;
}

.screen_vision {
    // width: 185px !important;
    // height: 240px !important;
    width: 242px !important;
    height: 239px !important;
    object-fit: contain;
}

.activeState {
    position: absolute;
    right: 66.8%;
    top: 21%;
    padding: 5px 10px;
    background: rgba($color: #000000, $alpha: 0.4);
    color: #fff;
}

.pushing {
    width: 100%;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 10%;
}

.ab {
    clear: both;
    right: 45% !important;
    margin-top: 30px;
    width: 150px;
    height: 45px;
    font-size: 20px;
}
</style>
