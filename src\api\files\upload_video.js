import store from '@/store/upload_video.js'
import OSS from 'ali-oss';
import { Message } from 'element-ui'
import Vue from 'vue'
import SparkMD5 from 'spark-md5'
import { add_video_source } from "@/api/files/files";

// 注意切换环境时，需要改变 environment 环境，沙盒-sandbox  灰度-dsgray  公网-public，此文件中有两处需要更改
// let store = this.$store
// 把需要上传资源全部存起来
let downloadUrl = '';

const setUploadingList = (files, checkFiles, url) => {
    downloadUrl = url
    let list = [...checkFiles];
    for (let file of files) {
        const AlreadyExists = list.find(item => {
            return item.name === file.name && item.size === file.size
        })
        if (!AlreadyExists) {
            const obj = {
                index: list.length + 1,
                client: null,
                file: file,                                  // 文件file
                name: file.name,                             // 文件名
                size: file.size,                             // 文件大小
                format_size: formatBytes(file.size),         // 格式化后文件大小
                file_type: getFileType(file.name),           // 文件后缀
                progress: 0,                                 // 上传进度
                done: 0,                                     // 是否上传完成 0-等待上传 1-上传完成 2-正在上传 3-暂停 4-上传失败 5-接口上传失败，可重新上传
                failure_reason: '',                          // 失败原因
                source_url: '',                              // 上传完阿里oss后文件链接
                file_md5: '',                                // 文件md5值
                file_identify: '',                           // 获取md5后文件名
                addAgainInfo: {},                            // oss上传完毕后，如果接口请求失败，请求参数放入这里用来重新请求
            }
            obj.client = new OSS(store.state.ossInfo)
            list.push(obj)
        }
    }
    store.dispatch('videoInputChange', list);
    prepareForUpload();
}

// 准备上传
const prepareForUpload = () => {
    if (store.state.successNum + store.state.failNum === store.state.videoUploadList.length && store.state.videoUploadList.length != 0) {
        store.state.uploadCompleted = 1;
        console.log(store.state.videoUploadList, 'store.state.videoUploadList');
        store.state.videoUploadList.forEach(item => {
            if (item.duplicate == 1) {
                Message.warning(item.failure_reason);
            } else {
                Message.success('视频上传完成');
            }
        })
        // failure_reason
        return
    }

    let list = [...store.state.videoUploadList];
    let uploadingList = [...store.state.uploadingVideo];
    let canUploadNum = store.state.uploadNum;
    let reg = /[`~!@#$%^&*()+=<>?:"{}|,\/;'\\[\]·~！@#￥%……&*——+={}|《》？：“”【】、；‘’'，。、\s]/;
    let reg2 = /\s/;

    for (let i in list) {
        if (list[i]['done'] === 0 && uploadingList.includes(list[i]['index']) === false && uploadingList.length < canUploadNum) {
            list[i]['done'] = 2;
            if (!store.state.uploadType.includes(list[i]['file_type'])) {
                list[i]['done'] = 4;
                list[i]['failure_reason'] = '上传失败：不支持的文件类型';
                list[i]['progress'] = 100;
                store.state.failNum += 1;
            } else if (list[i]['size'] > store.state.maxSize) {
                list[i]['done'] = 4;
                list[i]['failure_reason'] = '上传失败：资源大小超过限制';
                list[i]['progress'] = 100;
                store.state.failNum += 1;
            } else if (reg.test(list[i].name)) {
                list[i]['done'] = 4;
                list[i]['failure_reason'] = '上传失败：资源名仅支持中,英文以及 _（）特殊字符';
                list[i]['progress'] = 100;
                store.state.failNum += 1;
            } else if (reg2.test(list[i].name)) {
                list[i]['done'] = 4;
                list[i]['failure_reason'] = '上传失败：视频名称不能包含空格';
                list[i]['progress'] = 100;
                store.state.failNum += 1;
            } else {
                uploadingList.push(list[i]['index']);
                store.state.uploadingVideo = uploadingList;
                startUpload(list[i]);
                setTimeout(() => {
                    prepareForUpload();
                }, 500);
                break;
            }
        }
    }

}


// 开始上传
const startUpload = (file) => {
    let subscript = file.index - 1;
    readFileMd5(file.file).then(md5 => {
        store.state.videoUploadList[subscript]['file_md5'] = md5;
        store.state.videoUploadList[subscript]['file_identify'] = `${md5}.${file.file_type}`;

        //key可以自定义为文件名（例如file.txt）或目录（例如abc/test/file.txt）的形式，实现将文件上传至当前Bucket或Bucket下的指定目录。
        const authInfo = JSON.parse(localStorage.getItem("device_info"));
        const environment = 'public';  // 环境：沙盒-sandbox  灰度-dsgray  公网-public
        const key = `${environment}/ds/${authInfo["uid"]}/${file.name}`;

        file.client.multipartUpload(key, file.file, {
            progress: function (p, cpt) {
                let percentNum = parseFloat(Math.floor(p * 10000) / 100);
                if (percentNum >= 99) {
                    percentNum = 99;
                }
                store.state.videoUploadList[subscript]['progress'] = percentNum;
                // console.log("store.state.videoUploadList", store.state.videoUploadList)
            },
            partSize: 1 * 1024 * 1024,   // 分片上传时每一个分片大小,这里设置为1M
        }).then(res => {
            addVideoSourceApi(file, subscript, environment);
        }).catch(err => {
            if (err.name && err.name == 'cancel') {
                store.state.videoUploadList[subscript]['done'] = 3;
            } else {
                store.state.failNum += 1;
                store.state.videoUploadList[subscript]['done'] = 4;
                store.state.videoUploadList[subscript]['failure_reason'] = '上传失败:' + err;
                store.state.videoUploadList[subscript]['progress'] = 100;
            }
            store.state.uploadingVideo = store.state.uploadingVideo.filter(i => i != store.state.videoUploadList[subscript].index);
            prepareForUpload();
        })
    })

}

/**添加视频接口 */
const addVideoSourceApi = (file, subscript, environment) => {
    const authInfo = JSON.parse(localStorage.getItem("device_info"));
    const params = {
        "size": file.size,  // (Int) 文件大小(B)
        "owner_uid": authInfo["uid"],  // (Int) 用户id
        "md5": file.file_md5,  // (String) md5
        "file_identify": file.file_identify,  // (String) 文件名称
        "file_realname": file.name,  // (InStringt) 文件名称
        "v_or_h": 0,  // (Int) 文件横竖
        "duration": 0,  // (Int) 
        "width": 0,  // (Int) 像素(宽)
        "height": 0,  // (Int) 像素(高)
        // "source_url": file.source_url  // (String) 文件链接
        "source_url": ''  // (String) 文件链接
    }
    const url = 'http://' + downloadUrl + '/' + environment + '/ds/' + authInfo.uid + '/' + file.name.replace(/\(/g, "%28").replace(/\)/g, "%29")
    params.source_url = url;
    // console.log(params);
    let str = '';
    let status = 0;
    add_video_source(params).then(response => {
        if (response.rst == 'ok') {
            let data = response.data[0];
            console.log(data, 'video_data');

            if (data.duplicate == 1) {
                // str = '上传完成：视频资源已存在';
                str = `资源已存在，上传的视频与现有资源 [ ${data['exists_filename']} ] 重复`;
                // Message({
                //     type: 'warning',
                //     message: str
                // })
                console.log(store.state.videoUploadList[subscript]);
                store.state.videoUploadList[subscript]['duplicate'] = data.duplicate

                // store.state.uploadCompleted = 1;
                // return
            } else {
                str = '上传完成';
            }
            status = 1;
        } else {
            if(response.duplicate == 1){
                str = `资源名称重复`;
            }
            str = '上传失败：' + response.error_msg.rst;
            status = 0;
        }
        store.state.videoUploadList[subscript]['progress'] = 100
        if (status == 1) {
            store.state.successNum += 1;
            store.state.videoUploadList[subscript]['done'] = 1;
        } else {
            store.state.failNum += 1;
            store.state.videoUploadList[subscript]['done'] = 5;
            store.state.videoUploadList[subscript]['addAgainInfo'] = params;
        }
        // store.state.failNum += 1;
        // store.state.videoUploadList[subscript]['done'] = 5;
        // store.state.videoUploadList[subscript]['addAgainInfo'] = params;

        store.state.videoUploadList[subscript]['failure_reason'] = str;
        store.state.uploadingVideo = store.state.uploadingVideo.filter(i => i != store.state.videoUploadList[subscript].index);
        prepareForUpload();
    }).catch(error => {
        store.state.failNum += 1;
        store.state.videoUploadList[subscript]['done'] = 5;
        store.state.videoUploadList[subscript]['failure_reason'] = '上传出错';
        store.state.videoUploadList[subscript]['progress'] = 100;
        store.state.uploadingVideo = store.state.uploadingVideo.filter(i => i != store.state.videoUploadList[subscript].index);
        console.log(error, 'err');
        prepareForUpload();
    })
}

/**调用接口失败后，重新上传方法 */
const uploadAgain = (item) => {
    const subscript = item.index - 1;
    const environment = 'public';  // 环境：沙盒-sandbox  灰度-dsgray  公网-public
    store.state.videoUploadList[subscript]['done'] = 2;
    store.state.videoUploadList[subscript]['progress'] = 99;
    store.state.uploadingVideo.push(item.index);
    addVideoSourceApi(item, subscript, environment);
}

/**清空上传列表 */
const clearUploadList = () => {
    store.state.videoUploadList.forEach(item => {
        item.done = 3;
        item.client.cancel();
    })
    store.dispatch('resetList');
}

/**获取文件md5值 */
const readFileMd5 = (file) => {
    return new Promise((resolve, reject) => {
        var blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
            chunkSize = 2097152, // read in chunks of 2MB
            chunks = Math.ceil(file.size / chunkSize),
            currentChunk = 0,
            spark = new SparkMD5.ArrayBuffer(),
            frOnload = function (e) {
                spark.append(e.target.result); // append array buffer
                currentChunk++;
                if (currentChunk < chunks) {
                    loadNext();
                }
                else {
                    const fileMd5 = spark.end();
                    console.log("加载结束 :计算后的文件md5:" + fileMd5);

                    resolve(fileMd5);
                }
            },
            frOnerror = function () {
                console.log("error")
            };
        function loadNext() {
            var fileReader = new FileReader();
            // fileReader.readAsDataURL(file);  
            fileReader.onload = frOnload;
            fileReader.onerror = frOnerror;
            var start = currentChunk * chunkSize,
                end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
        };
        loadNext();
    })

}

/** 格式化文件大小 */
const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**获取文件后缀 */
const getFileType = (name) => {
    const arr = name.split('.');
    const suffix = arr[arr.length - 1];
    return suffix;
}


export { setUploadingList, clearUploadList, uploadAgain }