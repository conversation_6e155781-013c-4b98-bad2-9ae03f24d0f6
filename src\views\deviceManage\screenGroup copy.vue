<template>
  <div class="box" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <div class="top">
      <!--          运营市场-->
      <el-select v-model="queryList.opsmarket" placeholder="营运市场" clearable filterable>
        <el-option v-for="item in options1.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>
      <!-- <el-select v-model="queryList.itmarket" placeholder="IT市场" clearable filterable>
        <el-option v-for="item in options2.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select> -->
      <el-input placeholder="门店ID/设备ID" prefix-icon="el-icon-search" v-model="queryList.blurry"
        @keyup.native.enter="get_adm_datas" style="width: auto !important;"></el-input>
      <!-- </div> -->
      <!--          设备类型-->
      <el-select v-model="queryList.pmodel" placeholder="设备类型" clearable>
        <el-option v-for="item in options3.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>
      <el-select v-model="queryList.dsusagetype" placeholder="屏幕类型" clearable>
        <el-option v-for="item in options4.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>

      <el-select v-model="queryList.shop_status" @change="cbcbcbcb" clearable placeholder="门店营业状态">
        <el-option v-for="item in serachShopStatus" :label="item.label" :value="item.value"
          :key="item.value"></el-option>
      </el-select>
      <el-select v-model="queryList.jdetrades" clearable placeholder="商圈类型" multiple
        collapse-tags style="margin-right: 10px;">
            <el-option v-for="item in options7.options" :label="item[1]" :value="item[0]" :key="'jde'+item[0]"></el-option>
     </el-select>
      <!--          设备状态-->
      <!-- <el-date-picker v-model="queryList.stm" type="datetime" format="yyyy-MM-dd HH" value-format="yyyy-MM-dd HH"
        placeholder="选择开始日期时间" style="width: 199px;"></el-date-picker>

      <el-date-picker v-model="queryList.etm" type="datetime" format="yyyy-MM-dd HH" value-format="yyyy-MM-dd HH"
        placeholder="选择结束日期时间" style="width: 199px;"></el-date-picker> -->
        <el-date-picker
        style="width: 260px;"
        v-model="queryList.timeslot"
        format="yyyy-MM-dd"
        value-format="yyyyMMdd"
        type="daterange"
        :range-separator="queryList.timeslot&&queryList.timeslot.length>0?'至':''"
        start-placeholder="选择注册时间"
        end-placeholder=""></el-date-picker>
      <el-select v-model="screenOnline" @change="changeScreenOnline" placeholder="设备状态/全部">
        <el-option v-for="item in screenOnlineList" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>
      <el-select v-model="queryList.screen_tags" placeholder="设备标签" multiple collapse-tags>
        <el-option v-for="item in searchTagsList" :key="item" :label="item" :value="item"></el-option>
      </el-select>

      <el-select v-model="queryList.filterscrtags_type" placeholder="设备标签条件状态">
        <el-option v-for="item in condition" :label="item.label" :value="item.value" :key="item.value"></el-option>
      </el-select>

      <!-- <div v-if="screenSearchonlineType">
        <el-select v-model="screenOnline" @change="changeScreenOnline" placeholder="设备状态/全部">
          <el-option
            v-for="item in screenOnlineList"
            :key="item[1]"
            :label="item[1]"
            :value="item[0]"
          ></el-option>
        </el-select>
      </div>-->
      <!--      搜索-->
      <el-button type="primary" style="background-color: var(--text-color); margin-left: 5px;"
        @click="handleSearch">搜索</el-button>
      <el-button type="primary" style="background-color: var(--text-color); border: 1px solid var(--text-color);"
        @click="exportExcel" v-if="checkPer(['dm.scr.exportdslist'])">导出屏幕数据</el-button>
      <el-button type="primary"
        style="background-color: var(--text-color);border: 1px solid var(--text-color); height: 32px"
        @click="installScreen">添加设备</el-button>

    </div>
    <div class="filter_box">
        <el-dropdown placement="bottom-end" trigger="click" :hide-on-click='false'> 
            <el-tooltip class="el-dropdown-link" effect="dark" content="列表显示隐藏" placement="left">
                <el-button icon="el-icon-menu" circle size="mini"></el-button>
            </el-tooltip>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(option,index) in table_filtering_options" :key="'option_' + index">
                    <el-checkbox :value='option.checked' @change="checkFilteringOption($event,option)">{{option.label}}</el-checkbox>
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
    <!--    表格-->
    <div style="flex:1;flex-grow: 1;min-height: 1px;">
      <el-table v-if="true" ref="multipleTable" height="100%" :data="tableData" :row-key="shop_id" tooltip-effect="dark"
        :header-cell-style="getRowClass" @selection-change="handleSelectionChange">
        <el-table-column type="selection" header-align="center" width="55"></el-table-column>
        <!-- <el-table-column label="设备方向" prop="devDir" align="center" width="80">
            </el-table-column>-->
        <el-table-column prop="devId" label="屏幕编号" align="center" min-width="100"  v-if="filteredColumns('displaynum')">
          <template slot-scope="scope">
            <div align="center">
              <span v-for="(item, index) in scope.row.all_displayinfo"
                :class="item.display.v_or_h == 'horizontal' ? 'setDev' : 'shu'" class :key="index">{{ item.displaynum
                }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="screen_id" label="设备ID" align="center" min-width="80" show-overflow-tooltip v-if="filteredColumns('screen_id')"></el-table-column>
        <el-table-column prop="storecode" label="门店编号" align="center" min-width="100"
          show-overflow-tooltip v-if="filteredColumns('storecode')"></el-table-column>
        <el-table-column prop="storename" label="门店名称" align="center" min-width="100" v-if="filteredColumns('storename')"></el-table-column>
        <el-table-column prop="usage_type_cn" label="屏幕类型" align="center" min-width="100" v-if="filteredColumns('usage_type_cn')"></el-table-column>
        <!-- <el-table-column prop="itmarketname" label="IT市场" align="center" show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="isonline" align="center" label="屏幕状态" min-width="100" show-overflow-tooltip v-if="filteredColumns('isonline')">
          <template slot-scope="scope">
            <div>{{ scope.row.isonline == 0 ? "离线" : "在线" }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="pmodel" label="设备类型" min-width="100" align="center"
          show-overflow-tooltip v-if="filteredColumns('pmodel')"></el-table-column>
        <el-table-column prop="storestatus_display" label="设备门店状态" align="center" min-width="150"
          show-overflow-tooltip v-if="filteredColumns('storestatus_display')"></el-table-column>
        <el-table-column prop="net_type_diplay" label="网络状态" align="center" min-width="180"
          show-overflow-tooltip v-if="filteredColumns('net_type_diplay')">
            <template slot-scope="scope">
                <div>{{ scope.row.lan_ip ? scope.row.lan_ip : '-'  }} /  {{ scope.row.net_type_diplay ? scope.row.net_type_diplay : '-'  }}</div>
            </template>
        </el-table-column>
        <el-table-column prop="tags" align="center" label="设备标签" min-width="150" show-overflow-tooltip v-if="filteredColumns('tags')">
          <template slot-scope="scope">
            <div class="flex" style="flex-wrap: wrap;align-items:center">
              <div class="flex" style="flex-direction:column;width:105px" v-if="scope.row.tags.length > 2">
                <div style="display:flex;align-items:center">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ scope.row.tags[0].name }}
                </div>
                <div style="display:flex;align-items:center">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ scope.row.tags[1].name }}
                </div>
                <el-popover placement="top-start" title="设备标签" popper-class="popperOptions" width="200" trigger="hover">
                  <div v-for="item in scope.row.tags" :key="item" style="display:flex;align-items:center">
                    <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                    <span>
                      {{
                        item.name
                      }}
                    </span>
                  </div>
                  <span class="cursor" slot="reference">...</span>
                </el-popover>
              </div>
              <div class="flex" style="flex-direction:column;width:105px"
                v-else-if="scope.row.tags.length > 0 && scope.row.tags.length <= 2">
                <div style="display:flex;align-items:center" v-for="item in scope.row.tags" :key="item">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ item.name }}
                </div>
              </div>
              <div v-else>
                —
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="jdetradezonename" label="商圈类型" align="center" show-overflow-tooltip
          min-width="150" v-if="filteredColumns('jdetradezonename')">
            <template slot-scope="scope">
                <div v-if="scope.row.jdetradezonename && scope.row.jdetradezonename!= ''">
                    {{ scope.row.jdetradezonename }}
                </div>
                <div v-else>
                    —
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="lastonlinetime" label="最后在线时间" align="center" show-overflow-tooltip
          min-width="150" v-if="filteredColumns('lastonlinetime')"></el-table-column>
        <el-table-column prop="createdtime" align="center" label="注册时间" show-overflow-tooltip
          min-width="150" v-if="filteredColumns('createdtime')"></el-table-column>
        <!-- <el-table-column prop="data" align="center" width="150px" label="操作" show-overflow-tooltip>
                <template slot-scope="scope">
                <el-button @click.native.prevent="shotScreen(scope.row)" type="text" element-loading-text="正在截屏中"
                    element-loading-background="transparent" v-loading.fullscreen.lock="fullscreenLoading" size="small">
                    截屏
                </el-button>
                <el-button @click.native.prevent="restart(scope.row)" type="text" size="small">
                    重启
                </el-button>
                <el-button @click.native.prevent="record(scope.row)" type="text" size="small">
                    记录
                </el-button>
                <el-button @click.native.prevent="detail(scope.row)" type="text" size="small">
                    详情
                </el-button>
                </template>
            </el-table-column>-->
        <el-table-column  align="center" width="150" key="table_edit" label="操作" fixed="right">
          <template slot-scope="scope">
            <div class="event" style="display: flex;flex-wrap: wrap;">
              <el-button @click.native.prevent="shotScreen(scope.row)" type="text" element-loading-text="正在截屏中"
                element-loading-background="transparent" v-loading.fullscreen.lock="fullscreenLoading"
                size="small">截屏</el-button>
              <!-- v-if="checkPer([ 'dm.scr.shotrecord'])" -->
              <el-button @click.native.prevent="restart(scope.row)" style="color: var(--text-color-light) !important;"
                type="text" size="small" v-if="checkPer(['dm.scr.control.reboot'])">重启</el-button>
              <el-button @click.native.prevent="record(scope.row)" type="text" size="small">记录</el-button>
              <el-button @click.native.prevent="detail(scope.row)" type="text" size="small"
                v-if="checkPer(['dm.scr.detailview'])">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--    按钮+分页-->
    <div class="bottom">
      <el-button style="background-color: var(--btn-background-color);color:#fff" @click="openBatchdialog"
        v-if="checkPer(['dm.scr.edit'])">批量标签</el-button>
      <el-button style="background-color: var(--btn-background-color);color:#fff" @click="batchParameters"
        v-if="checkPer(['dm.scr.edit'])">批量参数设置</el-button>
      <div class="block">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage" :page-size="this.pageSize" :page-sizes="[10, 20, 50, 100]" :pager-count="5"
          background layout="total,sizes,prev, pager, next, jumper" :total="totalNum"></el-pagination>
      </div>
    </div>
    <!--      重启该设备-->
    <el-dialog title="重启该设备" :visible.sync="dialogVisible" width="20%" custom-class="resDevice"
      :close-on-click-modal="false">
      <div class="icon">
        <span class="el-icon-warning"></span>
        <!-- <i class="el-icon-warning"></i> -->
        <!-- 重启该设备 -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button style="background-color: #1890ff; color: #fff" @click="restartDev">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 批量标签 -->
    <Dialog :tagsDialog="tagsDialog" @handleCloseDialog="handleCloseDialog" :tagsList="tagsList"
      :delTagsList="delShowList" @saveTags="saveTags"></Dialog>
    <!-- 批量参数设置 -->
    <ParametersDialog ref="ParametersDialog" :parameterShow="parameterShow" @cancelEditing="cancelEditing"
      @confirmEditing="confirmEditing"></ParametersDialog>

    <el-drawer :wrapperClosable='false' :visible.sync="shop_drawer_show" :show-close="false"
      custom-class="add_screen_drawer">
      <el-button type="text" icon="el-icon-close" class="drawer_close" @click="close_or_cancel_drawer"></el-button>
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="设备激活" name="device"></el-tab-pane>
        <el-tab-pane label="服务卡激活" name="service_card"></el-tab-pane>
      </el-tabs>

      <el-form :model="form" :rules="rules" label-position="top" ref="ruleForm" class="drawer_form"
        style="box-sizing:border-box;padding:0 20px">
        <el-form-item label="归属门店" label-width="80px" prop="install_shop_group">
          <el-select v-model="drawer_level.L1" placeholder="请选择组织架构" style="width:160px " filterable
            @change="changeLevel($event, 'L1', 'L2')">
            <el-option v-for="item in level_L1_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
          </el-select>
          <el-select v-model="drawer_level.L2" placeholder="请选择省份" style="width:160px " filterable
            @change="changeLevel($event, 'L2', 'L3')">
            <el-option v-for="item in level_L2_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
          </el-select>
          <el-select v-model="drawer_level.L3" placeholder="请选择市区" style="width:160px " filterable
            @change="changeLevel($event, 'L3', 'shop')">
            <el-option v-for="item in level_L3_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
          </el-select>
          <el-select v-model="form.install_shop_group" placeholder="请选择归属门店" style="width:160px " filterable>
            <el-option v-for="item in level_Shop_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="屏幕验证码" label-width="80px" prop="verify_code">
          <el-input v-model="form.verify_code" style="width:100%" placeholder="请输入屏幕验证码" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="屏幕序列" label-width="80px" prop="install_screen_id">
          <el-input v-model="form.install_screen_id" style="width:100%" placeholder="请输入屏幕序列"
            autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="服务卡PING码" label-width="80px" prop="install_code" v-if="activeTabName == 'service_card'">
          <el-input v-model="form.install_code" style="width:100%" placeholder="请输入服务卡PING码"
            autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="屏幕名称" label-width="80px" prop="screen_name">
          <el-input v-model="form.screen_name" style="width:100%" placeholder="请输入屏幕名称" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="屏幕编号" label-width="80px" prop="devicenum">
          <el-input v-model="form.devicenum" class="inputFund" type="number" style="width:100%" placeholder="请输入屏幕编号"
            autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="设备类型" label-width="80px" prop="usage_type">
          <el-select v-model="form.usage_type" style="width:100% " filterable>
            <el-option v-for="(item, index) in options4.options" :key="'screentype_' + index" :label="item[1]"
              :value="item[0]"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="屏幕方向" label-width="80px" prop="screen_orientation">
          <el-radio-group v-model="form.screen_orientation">
            <el-radio :label="1">
              <img src="../../assets/img/screen_orientation_01.png" alt="">
            </el-radio>
            <el-radio :label="2">
              <img src="../../assets/img/screen_orientation_02.png" alt="">
            </el-radio>
            <el-radio :label="3">
              <img src="../../assets/img/screen_orientation_03.png" alt="">
            </el-radio>
            <el-radio :label="4">
              <img src="../../assets/img/screen_orientation_04.png" alt="">
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="my_drawer__footer">
        <el-button size="medium" @click="close_or_cancel_drawer">取 消</el-button>
        <el-button type="primary" size="medium" @click="confirm_submit" :loading="confirm_loading">
          {{ confirm_loading ? '提交中...' : '保 存' }}
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { get_adm_datas, send_ds_player_cmd, simple_get_shop_struct, install_screen_by_verify_code, screen_edit } from "@/api/device/device";
import { datas_filter_cond } from "@/api/commonInterface";
import {
  screen_capture,
  detection_screenshot,
  screen_add_delete_tags
} from "@/api/screen/screen";
import { get_kuaiyin_cs_tags } from "@/api/files/files";
import { get_screen_tags } from "@/api/system/label";

import Dialog from "./components/Dialog.vue";
import ParametersDialog from "./components/ParametersDialog.vue";
import { Actiontags, removeDuplicateObj } from "@/utils/setArray";
import {
  set_screen_timing,
  set_batch_screen_timing
} from "@/api/timing/timing";
import {
  get_screen_info,
} from "@/api/device/device";
export default {
  name: "ScreenDevice",
  components: {
    Dialog,
    ParametersDialog
  },
  data() {
    return {
      drawer_title: '添加设备',
      shop_drawer_show: false,
      loading: false,
      pageSize: 10, //每页显示多少条数据
      currentPage: 1, //页码
      totalNum: 0, //数据总数
      //横屏
      hengShow: false,
      shuShow: false,
      inputValue: "",
      queryList: {
        blurry: "", //门店
        isonline: "0", // -1 :all 1: online 0 : offline
        opsmarket: "", //营运市场
        pmodel: "", //设备类型
        jdetrades:"", //商圈类型
        itmarket: "",
        dsusagetype: "",
        screen_tags: [],
        filterscrtags_type: "should",
        stm: '',
        etm: '',
        timeslot:''
      },
      //运营市场
      options1: [],
      //IT市场
      options2: [],
      //设备类型
      options3: [],
      //屏幕类型
      options4: [],
      //设备状态
      options5: [],
      // 商圈类型
      options7: [],
      //表格
      tableData: [],
      multipleSelection: [],
      //截屏loading
      fullscreenLoading: false,
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      table_filtering_options:[
        {label:'屏幕编号',value:'displaynum',checked:true},
        {label:'设备ID',value:'screen_id',checked:true},
        {label:'门店编号',value:'storecode',checked:true},
        {label:'门店名称',value:'storename',checked:true},
        {label:'屏幕类型',value:'usage_type_cn',checked:true},
        {label:'屏幕状态',value:'isonline',checked:true},
        {label:'设备类型',value:'pmodel',checked:true},
        {label:'设备门店状态',value:'storestatus_display',checked:true},
        {label:'网络状态',value:'net_type_diplay',checked:true},
        {label:'设备标签',value:'tags',checked:false},
        {label:'商圈类型',value:'jdetradezonename',checked:false},
        {label:'最后在线时间',value:'lastonlinetime',checked:true},
        {label:'注册时间',value:'createdtime',checked:true},
      ],
      show_tab_list_checked:[],
      dialogVisible: false,
      screen_id: "",
      // 标签列表
      tagsList: [],
      delTagsList: [],
      delShowList: [],
      // 标签dialog状态
      tagsDialog: false,
      //批量参数dialog状态
      parameterShow: false,
      TagePageSize: 500,
      timeSelection: [],
      Homequery: null,
      screenOnlineList: [
        ["0", "设备状态"],
        ["1", "在线"],
        ["2", "离线(5分钟)"],
        // ["3", "离线5分钟"],
        ["3", "离线(24小时)"],
        ["4", "离线(72小时)"]
      ],
      condition: [
        {
          label: "且(包含全部已选标签,标签选择不能超过十个)",
          value: 'must'
        },
        {
          label: "或(包含任何一个已选标签)",
          value: 'should'
        },
        {
          label: "非(不包含任何已选标签)",
          value: 'must_not'
        }
      ],
      screenOnline: "0",
      screenSearchonlineType: true,
      serachShopStatus: [
        {
          label: "营业中",
          value: 9
        },

        {
          label: "暂停营业",
          value: 8
        },
        {
          label: "待开业",
          value: 7
        },
        {
          label: "筹建中",
          value: 5
        },
        {
          label: "已闭店",
          value: 4
        },
        {
          label: "修整",
          value: 3
        },
        {
          label: "翻新",
          value: 2
        }
      ],
      activeTabName: 'device',
      form: {
        install_shop_group: '',
        verify_code: '',
        install_screen_id: '',
        screen_name: '',
        tags: [],
        devicenum: '', // 屏幕编号，默认与displaynum保持一致
        displaynum: '',  // 屏幕编号，默认与devicenum保持一致
        usage_type: '', // 设备使用场景。设备类型编码，后台维护。类似广告屏(dsad)、菜单屏(dsorder)、音乐屏（dsaudio）,
        screen_orientation: 1
      },
      drawer_level: {
        L1: '',
        L2: '',
        L3: ''
      },
      headquarters: {},
      level_L1_list: [],
      level_L2_list: [],
      level_L3_list: [],
      level_Shop_list: [],
      confirm_loading: false,
      rules: {
        install_shop_group: [
          { required: true, message: '请选择归属门店', trigger: 'change' }
        ],
        verify_code: [
          { required: true, message: '请输入屏幕验证码', trigger: 'blur' }
        ],
        install_screen_id: [
          { required: true, message: '请输入屏幕序列', trigger: 'blur' }
        ],
        screen_name: [
          { required: true, message: '请输入屏幕名称', trigger: 'blur' }
        ],
        install_code: [
          { required: true, message: '服务卡PING码', trigger: 'blur' }
        ],
        devicenum: [
          { required: true, message: '请输入屏幕编号', trigger: 'blur' }
        ],
        usage_type: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        screen_orientation: [
          { required: true, message: '请选择屏幕方向', trigger: 'change' }
        ]
      },
      searchTagsList: []
    };
  },
  computed:{
    filteredColumns(){
        return (label)=>{
            if(this.show_tab_list_checked.indexOf(label) == -1){
                return false
            }else{
                return true
            }
        }
    }
  },
  methods: {
    cbcbcbcb(e) {
      this.queryList.shop_status = e;
    },
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background: var(--text-color-light);color:#fff";
      }
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    changeScreenOnline(e) {
      switch (this.screenOnline) {
        case "0":
          this.queryList.isonline = 0;
          this.queryList.loffline = "";
          break;

        case "1":
          this.queryList.isonline = 1;
          this.queryList.loffline = "";
          break;

        case "2":
          this.queryList.isonline = 2;
          this.queryList.loffline = 0;
          break;

        case "3":
          this.queryList.isonline = 2;
          this.queryList.loffline = 24;
          break;
        case "4":
          this.queryList.isonline = 2;
          this.queryList.loffline = 72;
          break;
      }

    },
    handleSelectionChange(val) {
      this.multipleSelection = [];
      this.timeSelection = [];
      this.delTagsList = [];
      this.delShowList = [];
      // val.forEach(item => {
      //   item.screen_tags = []
      //   this.multipleSelection.push(item.screen_id)
      //   item.tags.forEach(item1 => {
      //     item.screen_tags.push(item1.name)
      //   })
      //   this.delTagsList.push(...[item.screen_tags])
      // })
      val.forEach(item => {
        item.screen_tags = [];
        this.multipleSelection.push(item.screen_id);
        if (item.pmodel != "X86") {
          this.timeSelection.push(item.screen_id);
        }
        if (item.tags.length != 0) {
          item.tags.forEach(item1 => {
            item.screen_tags.push(item1.name);
            this.delTagsList.push(item1.name);
          });
        }
      });
      // this.delTagsList.forEach((item, index) => {
      //   if (item.length == 0) {
      //     this.delTagsList.splice(index, 1)
      //   }
      // })
      // this.delTagsList = Actiontags(this.delTagsList)
      // this.delTagsList = removeDuplicateObj(this.delTagsList)

      this.delTagsList.forEach(item => {
        this.delShowList.push({
          active: false,
          name: item
        });
      });
      this.delShowList = removeDuplicateObj(this.delShowList);
    },
    // 导出
    exportExcel() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let date_str = `${year}/${month}/${day}`;
      // https://ssddvc.yumchina.com/excel/exportimpl/check_data/2023/7/19/allscreenwithsrore.xls
      let localhost = window.location.origin;
      let url = `${localhost}/excel/exportimpl/check_data/${date_str}/allscreenwithsrore.xls`;
      let a = document.createElement("a");
      a.href = url;
      a.style.display = "none";
      a.click();
      a.remove();
    },
    //截屏的loading
    shotScreen(row) {
      if (row.isonline == 0) {
        this.$message.closeAll();
        this.$message.warning("设备离线,不可以截屏");
      } else {
        this.fullscreenLoading = true;
        const params = {
          screen_id: row.screen_id
        };
        screen_capture(params).then(res => {
          if (res.rst == "ok") {
            let timerNumber = 0;
            let timer = setInterval(() => {
              timerNumber++;
              detection_screenshot({
                screen_id: row.screen_id
              }).then(res => {
                if (res.data[0].ack != 0) {
                  this.$message.success("截屏成功");
                  this.fullscreenLoading = false;
                  clearInterval(timer);
                } else {
                  if (timerNumber == 15) {
                    this.$message.warning(
                      "截图失败,请检查设备网络和截图功能是否安装！"
                    );
                    this.fullscreenLoading = false;
                    clearInterval(timer);
                  }
                }
              });
            }, 1000);
          } else if (res.error_msg.indexOf("send cmd interval time is") != -1) {
            this.$message.warning("操作太过频繁,请5秒后再试");
            this.fullscreenLoading = false;
          } else {
            this.$message.warning(res.rst);
            this.fullscreenLoading = false;
          }
        });
      }
    },
    //  重启
    restart(row) {
      if (row.isonline != 1) {
        this.$message.warning("设备离线无法操作");
      } else {
        this.screen_id = row.screen_id;
        this.dialogVisible = true;
      }
    },
    // 设备重启的确认按钮
    restartDev() {
      this.loading = true;
      const params = {
        screen_id: this.screen_id, // (Int) 设备id
        cmd: "reboot_device", // (String) 固定参数
        range_type: "with_admin" // (String) 固定参数
      };
      send_ds_player_cmd(params).then(res => {
        this.dialogVisible = false;

        if (res.rst == "ok") {
          this.$message.success("重启成功");
          this.loading = false;
        } else {
          this.$message.fail("重启失败");
        }
      });
    },
    //  记录
    record(data) {
      let tempStr = {};
      console.log(data.all_displayinfo, 'data');
      data.all_displayinfo.forEach((item, index) => {
        tempStr[item.displaynum] = item.display.v_or_h;
      });
      this.$router.push({
        path: "/deviceManage/shotRecord",
        query: {
          screen_id: data.screen_id,
          displaynuminfo: data.displaynuminfo ? data.displaynuminfo : null,
          all_displayinfo: JSON.stringify(data.all_displayinfo)
        }
      });
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage
      };
      sessionStorage.setItem(
        "ScreenSearchCondition",
        JSON.stringify(sessionQuery)
      );
    },
    //  详情
    detail(data) {
      let tempStr = {};
      data.all_displayinfo.forEach((item, index) => {
        tempStr[item.displaynum] = item.display.v_or_h;
      });


      const params = {
        screen_key: data.screen_id, //屏幕id/key
        range: "full" //"full":全量数据; "simple":简易数据
      };

      get_screen_info(params).then(res => {
        console.log(res, 'res');
        if (res['rst'] == 'ok') {
          this.$router.push({
            path: "/deviceManage/screenDetail",
            query: {
              sid: data.screen_id,
              storecode: data.storecode,
              shop_id: data.shop_id,
              pmodel: data.pmodel,
              all_displayinfo: JSON.stringify(tempStr)
            }
          });
          let sessionQuery = {
            ...this.queryList,
            pagesize: this.pageSize,
            curpage: this.currentPage
          };
          setTimeout(() => {
            sessionStorage.setItem(
              "ScreenSearchCondition",
              JSON.stringify(sessionQuery)
            );
          }, 1000);
        } else {
          this.$message.closeAll()
          this.$message.warning(res['error_msg'])
        }
      })
      // debugger;


      // this.$store.commit("setScreenTags",data.tags)
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 200 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    // 获取数据
    get_adm_datas() {
      this.loading = true;
      this.queryList.blurry = this.queryList.blurry.toUpperCase();
      const params = {
        classModel: "ScreenMgmt",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize, //每页数据量,
        isonline: this.queryList.isonline,
        //  storecode:'XMN191'
        blurry: this.queryList.blurry,
        opsmarket: this.queryList.opsmarket,
        pmodel: this.queryList.pmodel,
        jdetrades: this.queryList.jdetrades,
        dsusagetype: this.queryList.dsusagetype,
        itmarket: this.queryList.itmarket,
        loffline: this.queryList.loffline,
        alert_db_act: this.queryList.alert_db_act,
        alert_db_val: this.queryList.alert_db_val,
        shop_status: this.queryList.shop_status,
        chk_exp: this.queryList.chk_exp,
        screen_tags: this.queryList.screen_tags,
        filterscrtags_type: this.queryList.filterscrtags_type,
        // stm: this.queryList.stm ? this.queryList.stm.replace(/-/g, '').replace(/ /g, '') : '',
        // etm: this.queryList.etm ? this.queryList.etm.replace(/-/g, '').replace(/ /g, '') : '',
        //  shop_id:89566
        stm: this.queryList.timeslot&&this.queryList.timeslot.length > 0 ? this.queryList.timeslot[0] +'00': '',
        etm: this.queryList.timeslot&&this.queryList.timeslot.length > 0 ? this.queryList.timeslot[1] +'00': ''
      };

    //   console.log(this.queryList.stm, params, "params");
    //   console.log(this.queryList.etm, params, "params");
    //   console.log(this.queryList.timeslot, params, "params");
      get_adm_datas(params)
        .then(res => {
          if (res.rst == "ok") {
            this.tableData = res.data[0].content;
            this.totalNum = res.data[0].totalElements;
            this.loading = false;
            // sessionStorage.removeItem("ScreenSearchCondition");
          } else {
            this.$message.error(res.error_msg);
            this.loading = false;
          }
        })
        .catch(rej => { });
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "ScreenMgmt" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        this.options1 = res.data[0][0]; //营运市场下拉数据
        this.options2 = res.data[0][1]; //IT市场下拉数据
        this.options3 = res.data[0][3]; //设备类型下拉数据
        this.options4 = res.data[0][4]; // 屏幕类型下拉数据
        this.options5 = res.data[0][5]; // 设备状态下拉数据
        this.options7 = res.data[0][7]; // 设备状态下拉数据

        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
      });
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.get_adm_datas();
    },
    // pageSize改变
    handleSizeChange(e) {
      this.pageSize = e;
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage
      };
      sessionStorage.setItem(
        "ScreenSearchCondition",
        JSON.stringify(sessionQuery)
      );
      this.get_adm_datas();
    },
    // 页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage
      };
      console.log(sessionQuery, 'sessionQuery');
      sessionStorage.setItem(
        "ScreenSearchCondition",
        JSON.stringify(sessionQuery)
      );

      console.log(sessionStorage.getItem('ScreenSearchCondition'));

      this.get_adm_datas();
    },
    // 获取标签
    getTagsList() {
      this.tagsList = [];
      const params = {
        page: 0,
        size: this.TagePageSize
      };
      get_screen_tags(params).then(res => {
        if (res.rst == "ok") {
          console.log(res, 'resx');
          this.searchTagsList = res['data'][0]['tags_list']
          this.TagePageSize = res.data[0].totalElements;
          res.data[0].tags_list.forEach(item => {
            this.tagsList.push({
              name: item,
              active: false
            });
          });
          // this.tagsList = JSON.parse(JSON.stringify(res.data[0].tags_list));
          this.tagsList.forEach((item, index) => {
            this.delTagsList.forEach(item1 => {
              if (item.name == item1.name) {
                this.tagsList.splice(index, 1);
              }
            });
          });
        }
      });
    },
    // 打开标签
    openBatchdialog() {
      if (this.multipleSelection.length != 0) {
        this.tagsDialog = true;
        this.getTagsList();
      } else {
        this.$message.closeAll();
        this.$message.warning("请选择需要增加标签的屏幕");
      }
    },
    // 关闭标签弹框
    handleCloseDialog() {
      this.tagsDialog = false;
    },
    saveTags(tags, message) {
      let params = {
        screen_ids: this.multipleSelection,
        tags: tags,
        action: "add"
      };
      if (tags.length != 0) {
        if (message == "deltag") {
          params.action = "del";
          screen_add_delete_tags(params).then(res => {
            if (res.rst == "ok" || res.data.failed == 0) {
              this.$message.success("屏幕标签删除成功");
              this.tagsDialog = false;
              this.get_adm_datas();
            } else {
              this.$message.error(res.error_msg);
            }
          });
        } else {
          params.action = "add";
          screen_add_delete_tags(params).then(res => {
            if (res.rst == "ok" || res.data.failed == 0) {
              this.$message.success("屏幕标签添加成功");
              this.tagsDialog = false;
              this.get_adm_datas();
            } else {
              this.$message.error(res.error_msg);
            }
          });
        }
      } else {
        this.tagsDialog = false;
      }
    },
    // 批量参数设置
    batchParameters() {
      if (this.timeSelection.length != 0) {
        this.parameterShow = true;
      } else {
        this.$message.closeAll();
        this.$message.warning("暂无可设置开关机设备");
      }
    },
    confirmEditing(val) {
      const timeing = [];
      this.timeSelection.forEach(item => {
        timeing.push(item.toString());
      });
      let param = {
        screen_ids: timeing,
        batch_job: "edit_screen",
        batch_info: {
          deploy_info: {
            on_off_policy: {
              every_day: val
            }
          }
        }
      };
      set_batch_screen_timing(param).then(res => {
        if (res.rst == "ok") {
          this.$message.success("批量设置定时开关机成功");
          this.parameterShow = false;
        } else {
          this.$message.warning(res.error_msg);
          this.parameterShow = false;
        }
      });
    },
    cancelEditing() {
      this.$refs.ParametersDialog.timing = [];
      this.parameterShow = false;
    },
    installScreen() {
      this.shop_drawer_show = true;
      const level_group_id = this.headquarters.id.split('g')[1];
      this.getOrganizationalStructure(level_group_id, 'L1');
    },
    close_or_cancel_drawer() {
      this.drawer_level.L1 = '';
      this.drawer_level.L2 = '';
      this.drawer_level.L3 = '';
      this.$refs.ruleForm.resetFields();
      this.shop_drawer_show = false;
      this.confirm_loading = false;
    },
    confirm_submit(validate = false) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.confirm_loading = true
          let params = {
            ...this.form,
            tags: [],
            displaynum: this.form.devicenum,
            hdmi_port: '0',
            foce: validate ? 1 : 0
          }
          params.install_shop_group = this.form.install_shop_group.split('g')[1];
          if (this.activeTabName == 'device') {
            // 设备激活
            params.install_type = 'bind-ds'
            params.control_server = 'must_pre_year'
          } else {
            // 服务卡激活
            params.install_code = this.form.install_code
            params.install_type = 'bind-ds'
          }

          install_screen_by_verify_code(params).then(res => {
            if (res.rst == 'ok') {
              this.screen_edit_fun();
            } else {
              this.confirm_loading = false;
              if (res.error_code == 6033) {
                this.$message.error('屏幕已经被绑定')
              } else if (res.error_code == 6008) {
                this.$message.error('激活码过期')
              } else if (res.error_code == 6048) {
                this.$message.error('您的账号激活值为0，请联系您的销售申请激活值或使用服务卡进行绑屏')
              } else if (res.error_code == 6050) {
                this.$message.error('您的账号激活值不足，如需使用请联系数拓官方客服')
              } else if (res.error_code == 6049) {
                this.$message.error('该型号设备不支持激活绑定')
              } else if (res.error_code == 6046) {
                this.$message.error('屏幕ID或屏幕验证码输入错误')
              } else if (res.error_code == 6055) {
                this.$confirm('设备编号在其他门店已经使用，是否覆盖', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  this.confirm_submit(true);
                }).catch(() => {
                  console.log('取消');
                });
              } else {
                this.$message.error(`error_code：${res.error_code}，${res.error_msg}`)
              }
            }
          }).catch(err => {
            this.confirm_loading = false;
          })
        }
      });
    },
    screen_edit_fun() {
      let obj = {
        status: '',
        reverse_rotate: ''
      }

      // screen_orientation:屏幕方向，
      // 1：正向横屏 2：反向横屏 3：正向竖屏 4：反向竖屏
      switch (this.form.screen_orientation) {
        case 1:
          obj.status = 0;
          obj.reverse_rotate = 0;
          break;
        case 2:
          obj.status = 0;
          obj.reverse_rotate = 1;
          break;
        case 3:
          obj.status = 1;
          obj.reverse_rotate = 0;
          break;
        case 4:
          obj.status = 1;
          obj.reverse_rotate = 1;
          break;
      }

      let params = {
        display_info: {
          reverse_rotate: obj.reverse_rotate,
          status: obj.status,
          resolution: obj.status == 0 ? '1920*1080' : '1080*1920',
          screenratio: obj.status == 0 ? '16:9' : '9:16'
        },
        screen_key: this.form.install_screen_id,
        screen_name: this.form.screen_name
      }

      screen_edit(params).then(res => {
        if (res.rst == 'ok') {
          this.$message.success('添加成功');
          this.close_or_cancel_drawer();
          this.handleSearch();
        } else {
          this.$message.error(res.error_msg)
        }
        this.confirm_loading = false;
      }).catch(err => {
        this.confirm_loading = false;
      })

    },
    changeLevel(value, level_now, level_next) {
      const level_group_id = value.split('g')[1];

      if (level_now == 'L1') {
        this.level_L2_list = [];
        this.level_L3_list = [];
        this.level_Shop_list = [];
        this.drawer_level.L2 = '';
        this.drawer_level.L3 = '';
        this.form.install_shop_group = '';
      } else if (level_now == 'L2') {
        this.level_L3_list = [];
        this.level_Shop_list = [];
        this.drawer_level.L3 = '';
        this.form.install_shop_group = '';
      } else if (level_now == 'L3') {
        this.level_Shop_list = [];
        this.form.install_shop_group = '';
      }

      if (level_next) {
        this.getOrganizationalStructure(level_group_id, level_next)
      }
    },
    getOrganizationalStructure(group_id, level) {
      const params = {
        group_id,
        sel_unit: 'shop',
        purpose: 'loading_tree'
      }
      simple_get_shop_struct(params).then(res => {
        // console.log(res.data[0].structure,'treeeeeeeeeeeeee');
        if (res.rst == 'ok') {
          if (group_id == '#') {
            this.headquarters = res.data[0].structure[0];
          } else {
            if (level == 'L1') {
              this.level_L1_list = [...res.data[0].structure]
            } else if (level == 'L2') {
              this.level_L2_list = [...res.data[0].structure]
            } else if (level == 'L3') {
              this.level_L3_list = [...res.data[0].structure]
            } else if (level == 'shop') {
              this.level_Shop_list = [...res.data[0].structure]
            }
          }
        } else {
          this.$message.error(res.error_msg)
        }
      })
    },
    checkFilteringOption(status,value){
        value.checked = status;
        this.getTableCheckedList()
    },
    getTableCheckedList(){
        let arr = [];
        this.table_filtering_options.filter(item=>{
            if(item.checked){
                arr.push(item.value)
            }
        })
        this.show_tab_list_checked = [...arr];
        localStorage.setItem('screen_table_filtering',JSON.stringify(arr))
        this.$nextTick(()=>{
            // 对 Table 进行重新布局
            // this.$refs.multipleTable?.doLayout();
            this.$refs.multipleTable.doLayout();
        })
    },
  },
  created() {
    // window.addEventListener("resize", this.getHeight);
    this.getHeight();
    let shop_table_filtering = localStorage.getItem('screen_table_filtering');
    if(shop_table_filtering){
        this.show_tab_list_checked = JSON.parse(shop_table_filtering);
        this.table_filtering_options.forEach(item=>{
            if(this.show_tab_list_checked.indexOf(item.value) == -1){
                item.checked = false;
            }else{
                item.checked = true;
            }
        })
    }else{
        this.getTableCheckedList();
    }
    this.getSelectDataList();
    this.getOrganizationalStructure('#')


    // this.currentPage = Number(sessionStorage.getItem('screen_page')) || 1
    // this.pagesize = Number(sessionStorage.getItem('screen_pagesize')) || 10

    if (JSON.stringify(this.$route.query) != '{}') {
      // if (this.$route.query.inonline == 'false' || this.$route.query.isonline == false) {
      //   this.screenOnline = '4'
      //   this.$set(this.queryList, 'shop_status', 9)
      //   this.queryList.loffline = 72

      // } else {
      //   this.screenOnline = '0'
      // }
      // this.screenSearchonlineType = JSON.stringify(this.$route.query.inonline)
      //   ? this.screenOnline = '4' 
      //   : true;
      // if(JSON.stringify(this.$route.query.inonline)){
      //   // this.screenOnline = '4';
      //   this.queryList.shop_status = 9
      // }


      this.queryList.blurry = this.$route.query.screen_id
        ? this.$route.query.screen_id
        : "";
      this.getSelectDataList();
      //   console.log(this.$route.fcond);
      //   console.log(JSON.parse(this.$route.query.fcond));
      let query_obj = JSON.parse(this.$route.query.fcond)
      for (let i in query_obj) {
        this.queryList[i] = query_obj[i]
      }
      if (this.queryList.loffline == 72) {
        this.screenOnline = '4'
      }
      console.log(this.queryList, 'xxx');

      // if (this.$route.query.quertType == 0) {
      //   this.queryList.alert_db_act = "screenalert";
      //   this.queryList.loffline = this.$route.query.queryString;
      //   this.queryList.isonline = "2";
      // } else if (this.$route.query.quertType == 1) {
      //   this.queryList.alert_db_act = "hdmialert";
      //   // this.queryList.loffline = this.$route.query.queryString;
      // } else if (this.$route.query.quertType == 2) {
      //   this.queryList.alert_db_act = "dsspacealert";
      //   // this.queryList.alert_db_val = this.$route.query.queryString;
      //   this.queryList.alert_db_val = "200";
      //   this.queryList.loffline = this.$route.query.queryString;
      // } else if (this.$route.query.quertType == 3) {
      //   // this.queryList.alert_db_act = "dsspacealert";
      //   this.queryList.alert_db_act = "upanalert";
      //   this.queryList.alert_db_val = this.$route.query.queryString;
      // }
    }

    if (sessionStorage.getItem("ScreenSearchCondition")) {
      let ScreenSearchQuery = JSON.parse(
        sessionStorage.getItem("ScreenSearchCondition")
      );
      this.pageSize = ScreenSearchQuery.pagesize;
      this.currentPage = ScreenSearchQuery.curpage;
      this.queryList = ScreenSearchQuery;
      // this.handleSearch();
      this.get_adm_datas();
    } else {
      this.get_adm_datas();
    }
    this.getTagsList();
  },
  destroyed() {
    // window.removeEventListener("resize", this.getHeight);
    sessionStorage.removeItem("ScreenSearchCondition")

  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .inputFund input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  .inputFund input[type="number"] {
    -moz-appearance: textfield;
  }
}

.box {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  width: 100%;
  height: calc(100vh - 50px);
  min-height: calc(100vh - 50px);
}

.box>.top {
  display: flex;
  padding: 20px 20px 0 0;
//   padding-right: 20px;
  align-items: center;
  flex-wrap: wrap;
  min-height: 70px;
}

.box>.top>div {
  margin: 5px;

}


table {
  color: rgba(91, 91, 91, 1);
  font-size: 14px;
  font-weight: bold;
}

::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-checkbox__inner::before {
  top: 7px !important;
}

::v-deep .el-checkbox__inner::after {
  top: 3px;
  left: 6px;
}

::v-deep .el-checkbox__inner {
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .cell {
  display: flex;
  justify-content: center;
}

::v-deep .cell>img {
  width: 23px;
  height: 23px;
}

.setDev {
  display: inline-block;
  width: 30px;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
  color: #fff;
  background-color: var(--screen-color);
  font-size: 14px;
  border: var(--screen-color) solid 1px;
  text-align: center;
}

.shu {
  display: inline-block;
  height: 26px;
  width: 22px;
  line-height: 26px;
  vertical-align: middle;
  color: #fff;
  background-color: var(--screen-color);
  font-size: 14px;
  border: var(--screen-color) solid 1px;
  text-align: center;
}

::v-deep .el-tooltip {
  display: flex;
  flex-wrap: wrap;
}

/*.cell>p{*/
/*  width: 50px;*/
/*  color: rgba(108, 178, 255, 1);*/
/*}*/
::v-deep .el-table__row>td:last-child>.cell.el-tooltip {
  display: flex;
}

::v-deep .el-table .cell.el-tooltip>button {
  width: 50px;
}

.reset {
  color: var(--background-color) !important;
}

.bottom {
  display: flex;
  align-items: center;
  position: relative;
  height: 75px;
  width: 100%;
}

.bottom>button {
  margin-left: 20px;
  width: 106px;
  height: 32px;
}



.block {
  /* width: 4.85rem; */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 0.32rem;
  position: absolute;
  right: 10px;
}

::v-deep .el-pagination__jump>.el-input {
  width: 45px;
}

/*重启颜色*/
.el-button--text:nth-child(2) {
  color: var(--background-color);
}


.icon {
  font-size: 18px;

  span {
    color: rgba(255, 170, 0, 1);
    font-weight: bold;
    position: absolute;
    font-size: 23px;
    top: 22px;
    left: 10px;
  }
}
</style>
<style scoped>
::v-deep .resDevice .el-dialog__title {
  margin-left: 27px !important;
}

.resDevice {
  padding: 0 19px;
}

.resDevice .el-dialog__body {
  margin-top: -26px;
  font-size: 25px;
  color: #ffab04;
  margin-left: -27px;
  border-radius: 4px;
}

.event {
  justify-content: space-between;
}

.event button {
  width: 50%;
  margin-left: 0 !important;
}

::v-deep .add_screen_drawer {
  box-sizing: border-box;
  padding: 0 10px;
}

::v-deep .add_screen_drawer .el-drawer__header {
  margin-bottom: 0px !important;
  padding: 0px !important;
}

::v-deep .add_screen_drawer .el-tabs {
  margin-bottom: 0px !important;
}

.drawer_close {
  position: absolute;
  top: 0;
  right: 12px;
  font-size: 18px;
  z-index: 10;
}

.drawer_form {
  height: calc(100% - 140px);
  overflow-y: auto;
  /* border: 1px solid blue; */
  box-sizing: border-box;
  border-bottom: 1px solid #dfe4ed;
}

::v-deep .drawer_form .el-radio-group .el-radio {
  border: 1px solid #ececec;
  border-radius: 5px;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__input {
  display: none !important;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__label {
  padding-left: 0 !important;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__label img {
  width: 65px;
  height: 65px;
}

::v-deep .drawer_form .el-radio-group .is-checked {
  border-color: var(--background-color);
  box-sizing: border-box;
}

.my_drawer__footer {
  height: 80px;
  line-height: 80px;
  text-align: right;
  padding: 0 20px;
  box-sizing: border-box;
}
.filter_box{
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
}
</style>
