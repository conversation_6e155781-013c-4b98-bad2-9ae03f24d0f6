<template>
    <div class="open_new_shop">
        <!-- 顶部tabs -->
        <div class="open_new_shop_header">
            <el-tabs v-model="activeTabName" @tab-click="handleChangeTab" ref="tabs_item">
                <el-tab-pane label="全部餐厅" name="first" v-if="checkPer(['dgm.shop_group.newshopmgmt.alllist'])">
                    <all-restaurants></all-restaurants>
                </el-tab-pane>
                <el-tab-pane label="待办清单" name="second"
                    v-if="checkPer(['dgm.shop_group.newshopmgmt.waitlist'])" ref="tabs_item">
                    <to-do-list></to-do-list>
                </el-tab-pane>
                <el-tab-pane label="待办历史" name="third"
                    v-if="checkPer(['dgm.shop_group.newshopmgmt.hislist'])" ref="tabs_item">
                    <to-dohistory></to-dohistory>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import AllRestaurants from '../../components/shopManage/allRestaurants.vue';
import ToDohistory from '../../components/shopManage/toDohistory.vue';
import ToDoList from '../../components/shopManage/toDoList.vue';

export default {
    components: {
        AllRestaurants,
        ToDoList,
        ToDohistory,
    },

    data() {
        return {
            activeTabName: 'first',
            roleList:[]
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.roleList = this.$store.state.user.roles;
        this.roleList.some(item=>{
            if(item == 'dgm.shop_group.newshopmgmt.alllist'){
                this.activeTabName = 'first'
                return
            }else if(item == 'dgm.shop_group.newshopmgmt.waitlist'){
                this.activeTabName = 'second'
                return
            }else if(item == 'dgm.shop_group.newshopmgmt.hislist'){
                this.activeTabName = 'third'
                return
            }
        })
    },
    mounted() {
    },
    methods: {
        handleChangeTab(tab, event) {
            console.log(tab, event);
            // if(tab.name=="third"){
            //     console.log(this.$refs.history.getInfo,'00');
            //     this.$refs.history.getInfo()
            // }
        }
    },
};
</script>

<style scoped>
.open_new_shop {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    /* padding: 0 20px; */
}


</style>
<style>
/* tabs选中的样式 */
.open_new_shop .is-active {
    color: var(--text-color) !important;
    background-color: var(--active-color) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.open_new_shop #tab-first {
    width: 100px;
    text-align: center;
}

/* 给最后一个设置padding */
.open_new_shop #tab-second {
   width: 100px;
   text-align: center;
}
.open_new_shop #tab-third {
   width: 100px;
   text-align: center;
}

/* 选中tabs下边横线样式 */
.open_new_shop .el-tabs__active-bar {
    /* display: none; */
    
    width: 100px !important;
    background-color: var(--text-color) !important;
}
.open_new_shop .el-tabs__item{
    padding: 0 !important;
}

/* tabs鼠标移入样式 */
.open_new_shop .el-tabs__item:hover {
    color: var(--text-color) !important;
}

/* tabs取消下边距 */
.open_new_shop .el-tabs__header {
    margin-bottom: 0 !important;
}
</style>
