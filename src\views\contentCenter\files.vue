<template>
  <div class="files_management" v-loading='isVideoUploading' element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="" element-loading-spinner="el-icon-loading">
    <div class="files_management_header">
      <el-tabs v-model="activeTabName" @tab-click="handleChangeTab">
        <el-tab-pane label="图片" name="images">
          <span slot="label">
            <i class="el-icon-picture-outline"></i> 图片
          </span>
          <picture-resources @setImgPreview="setImgPreview" ref="picture"></picture-resources>
        </el-tab-pane>
        <el-tab-pane label="视频" name="videos">
          <span slot="label">
            <i class="el-icon-video-camera"></i> 视频
          </span>
          <video-resources @setVideoPreview="setVideoPreview" ref="video"></video-resources>
        </el-tab-pane>
        <!-- <el-tab-pane label="H5模板" name="h5">
          <span slot="label">
            <i class="el-icon-mobile"></i> H5内容
          </span>
          <h5-resources @setH5Preview="setH5Preview" @setImgPreview="setImgPreview"></h5-resources>
        </el-tab-pane> -->
        <el-tab-pane label="H5内容" name="h5">
          <span slot="label">
            <i class="el-icon-mobile"></i> H5内容
          </span>
          <templateFileResources @setH5Preview="setH5Preview" @setImgPreview="setImgPreview"></templateFileResources>
        </el-tab-pane>

        <!-- <el-tab-pane label="联屏资源" name="multiScreen">
                    <span slot="label"><i class="el-icon-connection"></i> 联屏资源</span>
                    <multi-screen-resources></multi-screen-resources>
        </el-tab-pane>-->
        <!-- <el-tab-pane label="浮层内容" name="floats">
                    <span slot="label"><i class="el-icon-news"></i> 浮层内容</span>
                    <floats-resources></floats-resources>
        </el-tab-pane>-->
      </el-tabs>
      <div class="operation_wrap">
        <div class="upload_image flex" v-show="activeTabName == 'images'">
          <div class="upload_tips" v-if="checkPer(['cm.res.upload'])">支持格式 JPG/PNG/GIF</div>
          <input type="file" class="upload_ipt" accept=".jpg, .png, .gif" ref="upload_img" multiple
            @change="imageChange" />
          <div v-if="isPictureUploading && checkPer(['cm.res.upload'])" class="upload_btn">
            <i class="el-icon-loading"></i> 上传中
          </div>
          <div v-else-if="!isPictureUploading && checkPer(['cm.res.upload'])" class="upload_btn" @click="upgradeImage">
            <span>上传图片</span>
          </div>
        </div>
        <div class="upload_image flex" v-show="activeTabName == 'videos'" v-if="checkPer(['cm.res.upload'])">
          <!-- <div class="show_progress" v-show="isVideoUploading" @click="showProgess = true">上传进度</div> -->
          <div class="upload_tips">支持格式 MOV/MP4/AVI</div>
          <input type="file" v-show="!isVideoUploading" class="upload_ipt upload_video_ipt" accept=".mov,.mp4,.avi"
            ref="upload_video" @change="videoChange">

          <div v-if="isVideoUploading" class="upload_btn"><i class="el-icon-loading"></i> 上传中</div>
          <div v-else class="upload_btn" @click="upgradeVideo">上传视频</div>
          <!-- <div class="upload_btn" @click="upgradeVideoLink" v-if="checkPer(['cm.res.upload'])">上传视频</div> -->
        </div>
      </div>
    </div>
    <!-- 视频上传进度条 -->
    <div style="z-index:9999" class="progress_bar" v-show="activeTabName == 'videos' && showProgessVideo">
      <p class="progress_title">
        <span v-show="isuploadSuccess" style="color:#e11313">上传失败</span>
        <span v-show="!isuploadSuccess">{{ isVideoUploading ? '上传中 ( 请不要刷新或关闭页面 )' : '上传完成' }}</span>
        <i class="el-icon-close" @click="closeProgress"></i>
      </p>
      <el-progress :percentage="videoUploadProgress" :stroke-width="16" :color="customColorMethod"
        style="display:flex;align-items: center"></el-progress>
      <!-- <p style="margin-top:15px">上传速率：{{ uploadRate }}</p>-->
    </div>
    <!-- 预览mask -->
    <previsualization :isPreviewMaskShow="isPreviewMaskShow" :PreviewSrc="PreviewSrc" :PreviewType="PreviewType"
      @closePreviewMask="closePreviewMask" ref="Previsualization" :v_h="v_h"></previsualization>
  </div>
</template>

<script>
import readFileMd5 from "../../../static/js/readFileMd5";
import pictureResources from "@/components/ContentCenter/pictureResources";
import videoResources from "@/components/ContentCenter/videoResources";
import h5Resources from "@/components/ContentCenter/h5Resources";
import multiScreenResources from "@/components/ContentCenter/multiScreenResources";
import floatsResources from "@/components/ContentCenter/floatsResources";
import templateFileResources from "../../components/ContentCenter/templateFileResources.vue";
import {
  upload_photo,
  get_kuaiyin_cs_tags,
  add_video_source,
  get_tags_list,
  refresh_ali_sts
} from "@/api/files/files";
import { setUploadingList } from "@/api/files/upload_video"
import uploadStore from '@/store/upload_video.js'
import previsualization from "@/components/communal/previsualization";
import ObsClient from "esdk-obs-browserjs/src/obs";

var obsClient; // obs实例
export default {
  components: {
    pictureResources,
    videoResources,
    h5Resources,
    multiScreenResources,
    floatsResources,
    previsualization,
    templateFileResources
  },
  data() {
    return {
      userId: localStorage.getItem("user_id"),
      activeTabName: "images",
      tagsList: [],
      delTagsList: [],
      PreviewType: "",
      PreviewSrc: "",
      video_info: {},
      isPreviewMaskShow: false,
      isPictureUploading: false, //图片是否正在上传
      isVideoUploading: false, //视频是否正在上传
      showProgessVideo: false, //是否显示上传进度
      isuploadSuccess: false,
      uploadShow: false,
      videoUploadProgress: "00.00", //视频上传进度
      uploadRate: "0.00 kb/s",
      v_h: "",
      customColors: [
        { color: "#f56c6c", percentage: 0 },
        { color: "#6f7ad3", percentage: 100 }
      ],
      html_str: "",
      successUploadImages: [],
      errorUploadImages: []
    };
  },
  computed: {
    isChange() {
      console.log("uploadStore", uploadStore)
      if (uploadStore.state.videoUploadList.length > 0) {
        return uploadStore.state.videoUploadList[0]['progress']
      } else {
        return 0
      }
    }
  },
  watch: {
    isChange(newVal, oldVal) {
      this.videoUploadProgress = newVal
      if (this.videoUploadProgress == 100) {
        this.showProgessVideo = false
        this.isVideoUploading = false
        this.$refs.video.resetDataList();
      }
    }
  },
  created() {
    this.getVideoTags();
  },
  mounted() {
    let that = this;
    window.setPrice = function () {
      return that.html_str;
    };
    window.getType = function () {
      return "预览";
    };
  },
  destroyed() {
    sessionStorage.getItem("html_url")
      ? sessionStorage.removeItem("html_url")
      : "";
  },
  methods: {
    handleChangeTab(tab, event) {
      console.log(tab, event);
      console.log(tab.name);
    },
    // 获取视频标签列表
    getVideoTags() {
      let params = {
        user_id: localStorage.getItem("user_id"),
        tag_type: "photo"
      };
      get_tags_list(params).then(res => {
        console.log(res, "视频标签");
      });
    },
    // 点击上传按钮触发input的点击事件
    upgradeImage() {
      this.$refs.upload_img.dispatchEvent(new MouseEvent("click"));
    },
    upgradeVideo() {
      this.$refs.upload_video.dispatchEvent(new MouseEvent("click"));
    },
    // 确定后上传的文件
    // 图片
    async imageChange(e) {
      const files = e.target.files;
      if (files.length > 10) {
        this.$message.error("多选上传图片最多10个");
        return;
      }
      this.$refs.picture.loading = true;
      this.isPictureUploading = true;
      console.log(files, "files");

      this.errorUploadImages = [];

      let num = 0;
      console.log(files.length, "files");
      for (let i = 0; i < files.length; i++) {
        console.log("?iiii");
        await upload_photo(files[i])
          .then(res => {
            if (res.rst == "ok") {
              // this.$refs.picture.loading = false;
              // this.$message.success("图片上传成功");
              this.successUploadImages.push("success");
              console.log(this.successUploadImages, "successUploadImages");
              // this.$message.closeAll()
              // this.$message.success('图片上传成功')
              // if (files.length == this.successUploadImages.length + this.errorUploadImages.length) {
              // } else {
              //   this.$message.warning(`图片上传${this.successUploadImages.length}个成功 ${this.errorUploadImages.length}个失败 `)
              // }

              num++;
            } else {
              console.log("????");
              this.$refs.picture.loading = false;
              this.errorUploadImages.push("error");
              // this.$message.closeAll()
              console.log(this.successUploadImages, "successUploadImages");
              num++;
            }
          })
          .catch(() => {
            this.$refs.upload_img.value = "";
            this.errorUploadImages.push("error");
            this.$message.closeAll();
            // if (files.length == this.successUploadImages.length + this.errorUploadImages.length) {
            //   this.$message.success('图片上传成功')
            // } else {
            //   this.$message.warning(`图片上传${this.successUploadImages.length}个成功 ${this.errorUploadImages.length}个失败`)
            // }
            num++;
          });
      }
      console.log(num, "num");
      console.log("%c num", "color:red");

      if (num == files.length) {
        if (files.length == this.successUploadImages.length) {
          this.$message.success("图片上传成功");
          this.$refs.upload_img.value = "";
        } else {
          this.$message.warning(
            `图片上传${this.successUploadImages.length}个成功 ${this.errorUploadImages.length}个失败 `
          );
        }
      }
      this.isPictureUploading = false;
      this.$refs.picture.loading = false;

      setTimeout(() => {
        this.$refs.picture.getDataLsit();
      }, 100);
      console.log(this.errorUploadImages, "errorUploadImages");
      console.log(this.successUploadImages, "successUploadImages");
      this.errorUploadImages = [];
      this.successUploadImages = [];
      // if (files.length == this.successUploadImages.length) {
      //   this.$message.success('图片上传成功')
      // } else {
      //   this.$message.warning(`图片上传${this.successUploadImages.length}个成功 ${this.errorUploadImages.length}个失败 `)
      // }
    },
    videoChange(e) {

      const params = {
        'expire_tm': '1800',
        'bt': 'wcmp'
      }
      refresh_ali_sts(params).then(res => {
        console.log("res", res)
        const serverInfo = res.data[0]['ServerInfo'];
        const { BucketName, ServerHost } = serverInfo;

        const url = `${BucketName}.${ServerHost}`;
        if (res.rst == 'ok') {
          const bucket = BucketName;
          const region = ServerHost.replace('.aliyuncs.com', '');
          const Credentials = res.data[0]['Credentials'];
          const { AccessKeyId, AccessKeySecret, SecurityToken } = Credentials;

          const ossInfo = {
            region,
            accessKeyId: AccessKeyId,
            accessKeySecret: AccessKeySecret,
            stsToken: SecurityToken,
            bucket
          }
          // let client = new OSS(ossInfo);
          uploadStore.dispatch('setOssInfo', ossInfo);
          if (uploadStore.state.uploadCompleted === 1) {
            uploadStore.dispatch('resetList')
          }
          const files = e.target.files;
          this.isVideoUploading = true
          setUploadingList(files, uploadStore.state.videoUploadList, url);
          this.uploadShow = true;
          this.showProgessVideo = true
          this.$refs.upload_video.value = '';
          // _hmt.push(['_trackEvent', '素材管理', '视频','视频上传']);

        } else {
          this.$message.error('上传出错', res.error_msg)
        }
      }).catch(err => {
        this.$message.error('上传出错', err.error_msg)
      })

    },

    // 预览
    setImgPreview(val) {
      this.PreviewType = "image";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setVideoPreview(val) {
      this.PreviewType = "video";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setH5Preview(val, html_str, v_h) {
      console.log(val);
      this.v_h = v_h;
      this.PreviewType = "h5";
      // this.PreviewSrc = 'http://localhost:8000/Desktop/work/iframe_ml/firstIframe/iframe.html';
      this.PreviewSrc = val;
      this.html_str = html_str;
      console.log(this.html_str);
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    },
    customColorMethod(percentage) {
      // return percentage < 100 ? "#f56c6c" : "#67c23a";
      return "#67c23a";
    },
    closeProgress() {
      if (!this.isVideoUploading) {
        this.videoUploadProgress = "00.00";
        this.uploadRate = "0.00 kb/s";
      }
      this.showProgessVideo = false;
    }
  }
};
</script>

<style lang='scss' scoped>
* {
  box-sizing: border-box;
}

.files_management {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  /* padding: 0 20px; */
}

.files_management_header {
  box-sizing: border-box;
  width: 100%;
  position: relative;
}

.operation_wrap {
  position: absolute;
  right: 0;
  top: 0;
  height: 40px;
  // width: 260px;
  width: 315px;
  /* border: 1px solid blue; */
}

.operation_wrap::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: #e4e7ed;
  z-index: 1;
}

.upload_image {
  position: relative;
  height: 100%;
  align-items: center;
  justify-content: flex-end;
  padding-right: 16px;
}

.upload_tips {
  font-size: 12px;
  color: rgba(166, 166, 166, 1);
}

.upload_btn {
  height: 31px;
  top: 99px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  line-height: 31px;
  text-align: center;
  cursor: pointer;
  margin-top: -1px;
  padding: 0 10px;
  margin-left: 17px;
  white-space: nowrap;
}

.upload_video_ipt {
  visibility: visible !important;
  width: 76px !important;
  top: 4px !important;
  right: 16px !important;
  font-size: 0;
  cursor: pointer;
  opacity: 0;
}

.upload_video_ipt:hover~.upload_btn {
  background-color: rgba(211, 57, 57, 0.8);
}

.upload_ipt {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  width: 118px;
  height: 32px;
  border: 1px solid blue;
  overflow: hidden;
  border-radius: 6px;
  visibility: hidden;
}

.upload_btn:hover {
  background-color: rgba(211, 57, 57, 0.8);
}

/* 预览mask */
.preview_mask {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  z-index: 9999;
  overflow-y: auto;
}

.preview_content {
  position: absolute !important;
  left: 50%;
  top: 50%;
  /* background: #fff; */
  width: 1920px;
  height: 1080px;
  // transform: translate(-50%, -50%) scale(0.5);
}

.close_preview_mask {
  position: absolute;
  right: 40px;
  top: 40px;
  height: 40px;
  width: 40px;
  font-size: 18px;
  text-align: center;
  border-radius: 50%;
  line-height: 40px;
  cursor: pointer;
  color: #fff;
  background: #606266;
}

.close_preview_mask:hover {
  /* color: rgba(0, 0, 0, 0.6); */
  background: #6d6d6e;
}

.progress_bar {
  --wrap-width: 450px;
  --wrap-height: 125px;
  /* border: 1px solid red; */
  border: 1px solid #ebeef5;
  position: absolute;
  width: var(--wrap-width);
  height: var(--wrap-height);
  top: calc(50% - (var(--wrap-height) / 2) - 50px);
  left: calc(50% - (var(--wrap-width) / 2));
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  padding: 15px;
  z-index: 2;
}

.progress_title {
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
  padding-bottom: 0px;

  i {
    cursor: pointer;
  }
}

.show_progress {
  // width: 95px;
  color: #409eff;
  margin-right: 8px;
  cursor: pointer;
}

.search_btn {
  height: 30px;
  top: 99px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  margin-top: -1px;
  padding: 0 20px;
  margin-left: 17px;
  white-space: nowrap;
}

::v-deep .el-progress__text {
  font-size: 16px !important;
}
</style>
<style>
.files_management .btns {
  padding: 9px 15px;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
  cursor: pointer;
  color: #fff;
  white-space: nowrap;
  user-select: none;
}

.files_management .btns_blue {
  background: rgba(108, 178, 255, 1);
}

.files_management .btns_blue:hover {
  background: rgba(108, 178, 255, 0.8);
}

.files_management .btns_red {
  background: var(--text-color);
}

.files_management .btns_red:hover {
  background: rgba(211, 57, 57, 0.8);
}

.files_management .search_btn {
  height: 30px;
  top: 99px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  margin-top: -1px;
  padding: 0 20px;
  margin-left: 17px;
  white-space: nowrap;
}


/* tabs选中的样式 */
.files_management .is-active {
  color: var(--btn-color) !important;
  background-color: #25b27d !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.files_management #tab-images {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.files_management #tab-h5 {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.files_management .el-tabs__active-bar {
  /* display: none; */
  background-color: #25b27d !important;
}

/* tabs鼠标移入样式 */
.files_management .el-tabs__item:hover {
  color: var(--text-color) !important;
}

/* tabs取消下边距 */
.files_management .el-tabs__header {
  margin-bottom: 0 !important;
  width: calc(100% - 260px);
}
</style>
