<template>
    <div class="editDialog">
        <el-dialog title="修改数据范围名称" :visible.sync="editDialogState" width="30%" :before-close="handleClose">
            <el-input v-model="editDataNameInput"></el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog">取 消</el-button>
                <el-button type="primary" @click="requestEditData">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<script>
export default {
    data() {
        return{
        }
    },
    props: {
        editDialogState: {
            type: <PERSON>olean
        },
        editDataNameInput:{
            type:String
        }
    },
    methods: {
        handleClose() {
            this.$emit("handleClose")
        },
        cancelDialog() {
            this.$emit("handleClose")
        },
        requestEditData(){
            this.$emit("requestEditData",this.editDataNameInput)
        }
    }
}
</script>
<style>
</style>