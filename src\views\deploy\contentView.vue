<template>
    <div class="content_view" >
        <!-- 搜索区 -->
        <div class="flex content_view_header">
            <div class="go_back" >
                <i class="el-icon-back" @click="goBack" ></i>
            </div>
            <div class="title flex-1" style="font-weight:bold;font-size:15px;color:rgba(91, 91, 91, 1);text-align:center">
                内容下发详情
            </div>
        </div>  
        <!-- 列表区 -->
        <div class="table_wrap" :style="{height:autoHeight.height}">
            <el-table 
            :data="tableData" 
            v-loading='loading' 
            :height="autoHeight.height" 
            @selection-change="handleSelectionChange"  
            :header-cell-style="{ background: '#24b17d', color: '#fff','font-size': '13px'}" >
                
                <el-table-column prop="name"  label="缩略图" width="" align="center">
                    <template slot-scope="scope">
                        <div class="thumbnail">
                            <img :src="scope.row.thumb_url" style="height:73px;object-fit:cover" alt="">
                            <el-button type="text" size="small" @click="showImagePreview(scope.row.thumb_url)">查看大图</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="daypart_info"  label="daypart" width="" align="center"></el-table-column>
                <el-table-column prop="duration"  label="时长" width="" align="center">
                    <template slot-scope="scope">
                        <div >
                            {{scope.row.duration}}s
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="date_info"  label="播放时段" width="" align="center"></el-table-column>
                <el-table-column prop="status"  label="内容状态" width="" align="center"></el-table-column>
                
                <!-- <el-table-column label="操作"  width="150" fixed="right" align="center">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="toDeviceDetails(scope.row)"  type="text" size="small">重新下发</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="content_view_footer">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :pager-count='5'
                :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper"
                :total="totalNum">
            </el-pagination>
        </div>
        <!-- 预览mask -->
        <previsualization 
        :isPreviewMaskShow='isPreviewMaskShow' 
        :PreviewSrc='PreviewSrc' 
        :PreviewType='PreviewType'
        @closePreviewMask = 'closePreviewMask'
        >
        </previsualization>
    </div>
</template>

<script>
import previsualization from '@/components/communal/previsualization'
import { get_screen_pub_content_list } from "@/api/historical/historical"
export default {
    components: {
        previsualization
    },
    data() {
        return {
            loading:false,
            currentPage:1, //页码
            totalNum:0, //总数据数量
            pageSize:10,
            isPreviewMaskShow:false,
            PreviewSrc:'',
            PreviewType:'image',
            queryList:{
                search:'',
                shebeitype:'',
                fangxiang:'',
                pingmuleixing:''
            },
            tableData: [  //列表数据
                
            ],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getTableData()
    },
    mounted() {

    },
    methods: {
        getTableData(){
            let params = {
                "batch_id": this.$route.query.batch_id, //(String) , 批次号
                "shop_id": this.$route.query.shop_id,  // (String), 门店id
                "screen_id": this.$route.query.sid,     //(Int), 屏幕id
                "page_size": this.pageSize,
                "page_num": this.currentPage -1
            }
            // console.log(this.queryList);
            get_screen_pub_content_list(params).then(res=>{
                if(res.rst == 'ok'){
                    console.log(res.data[0]);
                    this.tableData = res.data[0].content;
                    this.totalNum = res.data[0].totalElements;
                }else{
                    this.$message.error(res.error_code)
                }
            })
        },
        // 设备详情
        toDeviceDetails(){
            this.$message.success('设备详情')
        },
        // 内容查看
        toContentView(val){
            console.log(val);
            this.$router.push({path:'/deploy/contentView'})
            this.$message.success('内容查看')
        },
        // 关闭Popover
        closePopover(index){
            this.$refs[`popover${index}`].doClose()
        },
        //页码改变
        handleCurrentChange(val) {
            console.log(`现在是第${val}页`);
            this.currentPage = val;
            this.getTableData();
        },
        handleSizeChange(val){
            console.log(`每页${val}条`);
            this.pageSize = val;
            this.getTableData();
        },
        // 查看大图
        showImagePreview(val){
            this.isPreviewMaskShow = true;
            this.PreviewSrc = val
        },
        closePreviewMask(){
            this.isPreviewMaskShow = false;
            this.PreviewSrc = '';
        },
        goBack(){
            console.log('返回');
            this.$router.go(-1)
        },
        getOnlineClass(val){
            switch(val){
                case 0:
                    return 'off_line';
                case 1:
                    return 'on_line'
                case 2:
                    return 'error'
            }
        },
        getOnlineStatus(val){
            switch(val){
                case 0:
                    return '离线';
                case 1:
                    return '在线'
                case 2:
                    return '异常'
            }
        },
        // 列表区高度自适应
        getHeight(){
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 173 + 'px';
            this.autoHeight.heightNum = windowHeight - 168;
        },
    },
    destroyed () {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style lang='scss' scoped>
    *{
        box-sizing: border-box;
    }
    .content_view{
        width: 100%;
        padding:0 11px;
    }
    
    /* 底部页码区 */
    .content_view_footer{
        box-sizing: border-box;
        width: 100%;
        height: 66px;
        font-size: 14px;
        padding-top: 17px;
        text-align: right;
        padding-right: 20px;
    }
    .content_view_header{
        align-items: center;
        justify-content: space-between;
        padding: 6px 0;
        .go_back{
            display: flex;
            height: 45px;
            align-items: center;
            i{
                color:rgba(108, 178, 255, 1);
                font-weight:bold;
                font-size:24px;
                cursor: pointer;
            }
        }
    }
    .btn{
        height:32px;
        width:88px;
        border-radius: 6px;
        background:var(--text-color);
        border:1px solid var(--text-color);
    }
    .off_line{
        color: var(--text-color);
    }
    .on_line{
        color: rgba(23, 159, 78, 1);
    }
    .error{
        color: rgba(56, 56, 56, 1);
    }
    .thumbnail{
        display: flex;
        flex-direction: column;
        align-items: center;
    }
</style>
<style>
    .el-popover{
        overflow-y: visible !important;
    }
</style>
