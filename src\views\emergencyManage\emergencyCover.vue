<template>
  <div class="second_step" v-loading="shop_loading">
    <div class="center">
      <div class="center_title"></div>
      <div class="times" v-if="coverData.active">
        <div class="event_time">操作时间 {{ coverData.create_tm }}</div>
        <div style="display: flex;">
          <div class="time">持续时间 {{ continueTime }}</div>
          <div class="withdraw" @click="revocation">撤销应急状态</div>
        </div>
      </div>
    </div>
    <div class="search_shop" :style="{ marginTop: coverData.active ? '10px' : '40px' }">
      <div class="center_title">选择覆盖设备</div>
      <div class="center_form" style="padding-bottom: 0 !important;">
        <el-form :inline="true" :model="searchShopForm" label-width="80px" class="demo-form-inline">
          <el-form-item label="营运市场:">
            <el-select v-model="searchShopForm.opsmarkets" placeholder="营运市场" multiple collapse-tags>
              <el-option v-for="item in serviceOptions" :label="item[1]" :value="item[0]" :key="item[0]"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="按门店:" style="margin-left:20px">
                        <el-input v-model="searchShopForm.shopId" placeholder="门店ID/名称" />
          </el-form-item>-->
          <el-form-item label="门店类型:">
            <el-select v-model="searchShopForm.storetypes" placeholder="门店类型" multiple collapse-tags>
              <el-option v-for="item in shopTypeOptions" :label="item[1]" :value="item[0]" :key="item[0]"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <!-- <el-form-item label="门店类型:">
                        <el-select v-model="searchShopForm.storetypes" placeholder="营运市场" multiple collapse-tags>
                            <el-option v-for="item in shopTypeOptions" :label="item[1]" :value="item[0]"
                                :key="item[0]"></el-option>
                        </el-select>
                    </el-form-item>
          <br>-->
          <el-form-item label="选中标签:">
            <el-select v-model="searchShopForm.shop_tags" placeholder="门店标签" multiple collapse-tags>
              <el-option v-for="item in tagsList" :label="item" :value="item" :key="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="门店编号:">
            <el-input v-model="searchShopForm.blurry" placeholder="请输入门店编号" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="center_form" style="margin-top: 0; padding-top:0px !important">
        <el-form :inline="true" :model="searchShopForm" label-width="80px" class="demo-form-inline">
          <el-form-item label="门店编号:">
            <el-input v-model="storeCode" @blur="searchShopStroe" placeholder="请输入门店编号" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>-->
      <div class="search_btn">
        <el-button @click.stop="searchShop()">筛选</el-button>
      </div>
      <div class="search_shop" v-show="shopsList.length != 0" v-loading="shop_loading">
        <div class="shop_header">
          <el-checkbox v-model="checkedAllState" @change="checkedAll">全选</el-checkbox>
          <span style="color:#ccc">
            已选择 {{
              totalShop - noSelectShops.length
            }} 个门店
          </span>
        </div>
        <div class="shop_content flex">
          <div v-for="(item, index) in shopsList" :key="index" class="every_shop flex">
            <el-checkbox v-model="item.checked" style="margin-right:5px" @change="selectCheckout(item)"></el-checkbox>
            <img src="@/assets/img/home_img/shop.png" style="width:30px;height:30px;margin-top:-2px" alt />
            <!-- <div class="flex-1 shop_info" style="width:65%;"> -->
            <div class="flex-1 shop_info" style="width:65%;overflow:hidden;" :title="item.storecode">
              <p>{{ item.storecode }}</p>
              <p class="text_overflow" :title="item.storename">{{ item.storename }}</p>
            </div>
          </div>
        </div>
        <div class="paginat" v-show="shopsList.length != 0">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper"
            :total="totalShop"></el-pagination>
        </div>
      </div>
      <div class="fotter">
        <el-button type="success" @click="EmergencyCoverage('update')" v-if="coverData.active">区域更新</el-button>

        <el-button type="success" @click="EmergencyCoverage('first')"
          v-else-if="noSelectShops.length != totalShop && totalShop != 0">紧急覆盖</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { datas_filter_cond } from "@/api/commonInterface";
import { get_shop_tags } from "@/api/system/label";
import { get_adm_datas } from "@/api/shopManage/shop";
import {
  get_curr_urgent_task_detail,
  create_or_upt_urgent_task,
  exec_urgent_task_handler
} from "@/api/emergency/cover";
export default {
  data() {
    return {
      searchShopForm: {
        classModel: "GroupShop",
        opsmarkets: "", // 营运市场
        // itmarket: '', // IT市场
        dsusage_type: "", // 屏幕类型
        shopId: "", // 门店id
        storetypes: "", // 门店类型
        shop_tags: [],
        page: 0,
        size: 10,
        blurry: ""
      },

      serviceOptions: [],
      shopTypeOptions: [],
      tagsList: [],
      checkedAllState: false,
      shopsList: [],
      shop_loading: false,
      noSelectShops: [],
      totalShop: 0,
      coverData: {},
      continueTime: "",
      newTime: "",
      old: "",
      taskId: "",
      totalShop: 0,
      storeCode: "",
      inSelectShops: []
    };
  },
  methods: {
    getCoverData() {
      get_curr_urgent_task_detail({}).then(res => {
        console.log(res, "resx");
        if (res["data"][0].length == 0) {
          this.coverData = {};
        } else {
          this.coverData = res["data"][0][0];
          this.searchShopForm.opsmarkets = this.coverData.pub_cond.opsmarkets;
          this.taskId = this.coverData.task_id;
          if (this.coverData.pub_cond.store_code) {
            if (this.coverData.pub_cond.include_shops) {
              this.inSelectShops = this.coverData.pub_cond.include_shops;
            } else {
              this.inSelectShops = [];
            }
          } else {
            if (this.coverData.pub_cond.exclude_shops) {
              this.noSelectShops = this.coverData.pub_cond.exclude_shops;
            } else {
              this.noSelectShops = [];
            }
          }
          console.log(this.noSelectShops, " this.noSelectShops");
          this.searchShopForm.shop_tags = this.coverData.pub_cond.shop_tags;
          this.searchShopForm.storetypes = this.coverData.pub_cond.storetypes;
          this.searchShopForm.blurry = this.coverData.pub_cond.store_code;
          this.searchShop();
          if (this.coverData.begin_tm) {
            this.continueTime =
              new Date().getTime() -
              new Date(this.coverData.begin_tm).getTime();
            this.old = new Date(this.coverData.begin_tm).getTime();
            this.newTime = new Date().getTime();
            this.TimeFn(this.newTime, this.old);
          }
        }
      });
    },
    // 撤销
    revocation() {
      exec_urgent_task_handler({
        task_id: this.taskId,
        action: "cancel"
      }).then(res => {
        console.log(res, "res");
        if (res.rst == "ok") {
          this.$message.success("撤销应急状态成功");
          setTimeout(() => {
            window.location.reload();
          }, 200);
        }
      });
    },

    // 区域更新 / 紧急覆盖
    EmergencyCoverage(title) {
      let params = {
        task_id: this.taskId,
        sel_info: {
          opsmarkets: this.searchShopForm.opsmarkets,
          storetypes: this.searchShopForm.storetypes,
          shop_tags: this.searchShopForm.shop_tags,
          exclude_shops: this.noSelectShops,
          store_code: this.searchShopForm.blurry
        }
      };
      if (this.inSelectShops.length != 0) {
        delete params.sel_info.exclude_shops;
        params.sel_info.include_shops = this.inSelectShops;
      }

      create_or_upt_urgent_task(params).then(res => {
        if (res.rst == "ok") {
          this.taskId = res["data"][0]["task_id"];
          this.EmergenPubTask(title);
        } else {
          this.$message.closeAll();
          this.$message.warning(res.error_msg);
          this.getCoverData();
        }
      });
    },
    // 紧急任务 下发/更新
    EmergenPubTask(title) {
      let params = {
        task_id: this.taskId,
        action: "pub"
      };
      exec_urgent_task_handler(params).then(res => {
        console.log(res, "res");
        if (res.rst == "ok") {
          if (title == "first") {
            this.$message.success("下发成功");
          } else {
            this.$message.success("区域更新成功");
          }
          setTimeout(() => {
            window.location.reload();
          }, 300);
        } else {
          this.$message.closeAll();
          this.$message.error(res.error_msg);
        }
      });
    },

    // 时间戳转换时分秒
    TimeFn(nVal, oVal) {
      let date = nVal - oVal;

      var leave1 = date % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
      var hours =
        Math.floor(date / (3600 * 1000)) < 10
          ? "0" + Math.floor(leave1 / (3600 * 1000))
          : Math.floor(leave1 / (3600 * 1000));
      //计算相差分钟数
      var leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
      var minutes =
        Math.floor(leave2 / (60 * 1000)) < 10
          ? "0" + Math.floor(leave2 / (60 * 1000))
          : Math.floor(leave2 / (60 * 1000));

      //计算相差秒数

      var leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
      var seconds =
        Math.round(leave3 / 1000) < 10
          ? "0" + Math.round(leave3 / 1000)
          : Math.round(leave3 / 1000);
      // console.log(days + "天 " + hours + "小时 ")

      this.newTime += 1000;
      this.continueTime = `${hours == 0 ? "" : hours + "时"}${minutes == 0 ? "" : minutes + "分"
        }${seconds}秒`;
      setTimeout(() => {
        this.TimeFn(this.newTime, this.old);
      }, 1000);
    },

    getSelectDataList() {
      const params = {
        classModel: "ScreenMgmt" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        console.log(res, "resxxxxxc");
        // this.$store.commit("changeDataFilters", res["data"][0])
        this.serviceOptions = res["data"][0][0]["options"];
      });
      datas_filter_cond({ classModel: "GroupShop" }).then(res => {
        this.shopTypeOptions = res["data"][0][2]["options"];
      });

      get_shop_tags({
        page: 0,
        size: 50
      }).then(res => {
        console.log(res, "res");
        this.tagsList = res["data"][0]["tags_list"];
        console.log(this.tagsList, "this.tagsList");
      });
    },
    searchShop() {
      this.shop_loading = true;

      get_adm_datas(this.searchShopForm).then(res => {
        console.log(res, "店铺");
        this.shopsList = res["data"][0]["content"];
        this.totalShop = res["data"][0]["totalElements"];
        this.shop_loading = false;
        this.shopsList.forEach(item => {
          if (this.searchShopForm.blurry != "") {
            this.noSelectShops = [];
            this.inSelectShops.push(item.shop_id);
            this.checkedAllState = true;
            this.$set(item, "checked", true);
            return;
          }
          this.inSelectShops = [];
          if (this.noSelectShops.length != 0) {
            let index = this.noSelectShops.indexOf(item.shop_id);
            console.log(index, "index");
            if (index != -1) {
              this.$set(item, "checked", false);
            } else {
              this.$set(item, "checked", true);
            }
          } else {
            // item.checked = true;
            let index = this.noSelectShops.indexOf(item.shop_id);
            this.$set(item, "checked", true);
            this.noSelectShops.splice(index, 1);
          }
          console.log(this.noSelectShops, "noSelectShops");

          if (item.checked == false) {
            this.checkedAllState = false;
            // if (this.noSelectShops.length != this.shopsList.length) {
            //     this.checkedAllState = false
            // } else {
            //     this.checkedAllState = true
            // }
          } else {
            this.checkedAllState = true;
          }
        });

        this.shopsList = JSON.parse(JSON.stringify(this.shopsList));
        console.log(this.noSelectShops, "noSelectShops");
      });
    },
    selectCheckout(shop) {
      console.log(shop.checked, "shop");
      if (this.searchShopForm.blurry != "") {
        if (this.inSelectShops.indexOf(shop.shop_id) != -1) {
          let index = this.inSelectShops.indexOf(shop.shop_id);
          this.inSelectShops.splice(index, 1);
        } else {
          this.inSelectShops.push(shop.shop_id);
        }
        if (this.shopsList.length == this.inSelectShops.length) {
          this.checkedAllState = true;
        } else {
          this.checkedAllState = false;
        }

        return;
      }
      if (shop.checked) {
        let index = this.noSelectShops.indexOf(shop.shop_id);
        this.noSelectShops.splice(index, 1);
      } else {
        this.noSelectShops.push(shop.shop_id);
      }

      this.noSelectShops = Array.from(new Set(this.noSelectShops));

      console.log(this.noSelectShops, "noSelectShops");
      if (this.noSelectShops.length == 0) {
        this.checkedAllState = true;
      } else {
        this.checkedAllState = false;
      }
    },
    handleSizeChange(val) {
      console.log("val", val);
      this.searchShopForm.size = val;
      this.searchShop();
    },
    handleCurrentChange(val) {
      console.log("val", val);
      this.searchShopForm.page = val - 1;
      this.searchShop();
    },
    checkedAll() {
      if (this.searchShopForm.blurry != "") {
        if (this.checkedAllState) {
          this.shopsList.forEach(item => {
            this.inSelectShops.push(item.shop_id);
            this.$set(item, "checked", true);
          });
        } else {
          this.shopsList.forEach(item => {
            let index = this.inSelectShops.indexOf(item.shop_id);
            console.log(index, "index");
            if (index != -1) {
              this.noSelectShops.splice(item.shop_id);
              this.$set(item, "checked", false);
            }
          });
        }
      }
      if (this.checkedAllState) {
        console.log(this.shopsList, "this.shopsList");
        this.shopsList.forEach(item => {
          let index = this.noSelectShops.indexOf(item.shop_id);
          console.log(index, "index");
          if (index != -1) {
            this.noSelectShops.splice(index, 1);
            this.$set(item, "checked", true);
          }
        });
      } else {
        console.log(this.noSelectShops, "this.noSelectShops");

        this.shopsList.forEach(item => {
          let index = this.noSelectShops.indexOf(item.shop_id);
          console.log(index, "index");
          if (index == -1) {
            this.noSelectShops.push(item.shop_id);
            this.$set(item, "checked", false);
          }
        });
      }
      console.log(this.noSelectShops, "noSelectShops");
      this.noSelectShops = Array.from(new Set(this.noSelectShops));
    },
    searchShopStroe() {
      this.shop_loading = true;
      console.log(this.storeCode, "storeCode");
      let params = {
        classModel: "ScreenMgmt",
        page: 0,
        size: 10,
        blurry: this.storeCode
      };
      this.inSelectShops = [];
      get_adm_datas(params).then(res => {
        console.log(res, "店铺");
        this.shopsList = res["data"][0]["content"];
        this.totalShop = res["data"][0]["totalElements"];
        this.shop_loading = false;
        console.log(this.shopsList, "shopsList");
        this.shopsList.forEach(item => {
          //   if (this.noSelectShops.length != 0) {
          //     let index = this.noSelectShops.indexOf(item.shop_id);
          //     console.log(index, "index");
          //     if (index != -1) {
          //       this.$set(item, "checked", false);
          //     } else {
          //       this.$set(item, "checked", true);
          //     }
          //   } else {
          //     // item.checked = true;
          //     let index = this.noSelectShops.indexOf(item.shop_id);
          //     this.$set(item, "checked", true);
          //     this.noSelectShops.splice(index, 1);
          //   }
          this.inSelectShops.push(item.shop_id);
          this.$set(item, "checked", true);
          console.log(this.noSelectShops, "noSelectShops");
          this.noSelectShops = [];
          if (item.checked == false) {
            this.checkedAllState = false;
            // if (this.noSelectShops.length != this.shopsList.length) {
            //     this.checkedAllState = false
            // } else {
            //     this.checkedAllState = true
            // }
          } else {
            this.checkedAllState = true;
          }
        });

        this.shopsList = JSON.parse(JSON.stringify(this.shopsList));
      });
    }
  },
  created() {
    this.getSelectDataList();
    this.getCoverData();
  }
};
</script>

<style lang="scss" scoped>
.second_step {
  width: 100%;
  height: 100%;
  background-color: #f8f7f7;
  box-sizing: border-box;
  padding: 0 30px;

  .center {
    width: 100% !important;
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .center_title {
      color: rgba(80, 80, 80, 1);
      font-size: 16px;
      font-weight: bold;
    }

    .times {
      display: flex;
      flex-direction: column;
      align-items: center;

      .time {
        width: 272px;
        height: 57px;
        text-align: center;
        line-height: 57px;
        background-color: #df6d6d;
        color: #fff;
        border-radius: 4px 0 0 4px;
        font-size: 14px;
        font-weight: bold;
        margin-top: 20px;
        cursor: pointer;
      }

      .withdraw {
        width: 150px;
        height: 57px;
        text-align: center;
        line-height: 57px;
        background-color: var(--background-color);
        color: #fff;
        border-radius: 0 4px 4px 0;
        font-size: 14px;
        font-weight: bold;
        margin-top: 20px;
        cursor: pointer;
      }

      .event_time {
        padding-top: 20px;
        color: rgba(80, 80, 80, 1);
        font-size: 14px;
      }
    }
  }

  .search_shop {
    // margin-top: 40px;

    .center_title {
      color: rgba(80, 80, 80, 1);
      font-size: 16px;
      font-weight: bold;
    }

    .center_form {
      background-color: #fff;
      margin-top: 20px;
      box-sizing: border-box;
      padding: 30px;

      ::v-deep .el-form-item__content {
        width: 220px;
      }

      ::v-deep .el-select {
        width: 100%;
      }
    }

    .search_btn {
      button {
        width: 100px;
        background-color: var(--btn-background-color);
        color: #fff;
        font-weight: bold;
        border-radius: 5px;
        margin-top: 10px;
      }
    }

    .search_shop {
      background-color: #fff;
      margin-top: 20px;
      padding: 30px;

      .shop_header {
        ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
          background: var(--base-color) !important;
          border-color: var(--base-color) !important;
        }

        ::v-deep .el-checkbox__input.is-checked .el-checkbox__label {
          color: #000;
        }

        ::v-deep .el-checkbox__label {
          padding-left: 5px !important;
        }

        ::v-deep .el-checkbox__inner {
          /* border-color:red !important; */
          width: 18px;
          height: 18px;
          border-radius: 50%;
        }

        ::v-deep .el-checkbox__inner::after {
          left: 6px !important;
          top: 3px !important;
        }
      }

      .shop_content {
        height: calc(100% - 40px);
        flex-wrap: wrap;
        overflow-y: auto;
        margin-top: 20px;

        .every_shop {
          box-sizing: border-box;
          width: 163px;
          height: 55px;
          align-items: center;
          padding: 0px 7px;
          margin-right: 15px;
          margin-bottom: 20px;
          border: 1px solid rgba(108, 178, 255, 1);

          .shop_info {
            padding: 5px 0;
            margin-left: 5px;

            p {
              font-size: 12px;
              height: 50%;
              line-height: 17px;
              // border: 1px solid red;
            }

            ::v-deep .text_overflow {
              text-overflow: -o-ellipsis-lastline;
              overflow: hidden; //溢出内容隐藏
              text-overflow: ellipsis; //文本溢出部分用省略号表示
              display: -webkit-box; //特别显示模式
              -webkit-line-clamp: 2; //行数
              line-clamp: 2;
              -webkit-box-orient: vertical; //盒子中内容竖直排列
            }
          }
        }
      }
    }
  }
}

.paginat {
  margin-top: 20px;
  margin-bottom: 40px;
}

.el-pagination {
  float: right;
}

.fotter {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 20px;
  justify-content: center;

  button {
    width: 272px;
    height: 57px;
    font-size: 22px;
    letter-spacing: 4px;
    font-weight: bold;
    border: 0;
    background-color: var(--background-color);
    color: #fff;
  }
}
</style>

<style lang="scss" scoped>
::v-deep .shop_content .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .shop_content .el-checkbox__inner:hover {
  border-color: var(--base-color) !important;
}

::v-deep .shop_content .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: var(--base-color) !important;
}

::v-deep .shop_content .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px !important;
  height: 18px !important;
  border-radius: 50%;
}

::v-deep .shop_content .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

::v-deep .shop_content .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}
</style>