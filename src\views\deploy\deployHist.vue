<template>
  <div class="deploy">
    <div class="top">
      <el-input placeholder="发布名称(批次号)" style="width: 150px" prefix-icon="el-icon-search" v-model="queryList.batch_id" clearable>
      </el-input>
      <!--      市场运营-->
      <!-- <el-select v-model="optionValue1" placeholder="运营市场">
        <el-option v-for="item in option1" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select> -->
      <!--  投放状态-->
      <!-- <el-select v-model="optionValue3" placeholder="投放状态">
        <el-option v-for="item in option3" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select> -->
      <!--  推送状态-->
      <!-- <el-select v-model="optionValue4" placeholder="推送状态">
        <el-option v-for="item in option4" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select> -->
      <!-- <div class="queryTime">
        <span>时段筛查</span>
        <div class="block">
          <el-date-picker v-model="value1" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </div>
      </div> -->
      <!--         搜索按钮-->
      <el-button type="danger" style="background: var(--background-color)" @click="search">搜索</el-button>
    </div>
    <!--      表格-->
    <el-table :height="autoHeight.height" :data="tableData" :header-cell-style="{ background: 'var( --text-color-light);' }"
      style="width: 100%" v-loading="loading">
      <el-table-column prop="batch_id" label="发布名称(批次号)" align="center" width="140">
      </el-table-column>
      <!-- <el-table-column prop="all_shop_cnt" align="center" width="100" label="门店数">
      </el-table-column> -->
      <el-table-column prop="all_sche_cnt" align="center" label="屏幕数">
      </el-table-column>
      <el-table-column prop="receive_sche_cnt" align="center" label="内容中台推送数" width="150">
      </el-table-column>
      <el-table-column prop="receive_status" align="center" label="推送状态">
        <template slot-scope="scope">
          <el-popover trigger="click" width="250" placement="top" :ref="`popover${scope.$index}`">
            <!-- scope.row.xxxx 获取该行数据 -->
            <div class="flex">
              <div style="margin-right:10px">
                <i v-if="scope.row.receive_status_code == 2" class="el-icon-circle-close"
                  style="color:var(--text-color);font-size:22px"></i>
                <i v-else-if="scope.row.receive_status_code == 3" class="el-icon-circle-check"
                  style="color:rgba(23, 159, 78, 1);font-size:22px"></i>
                <i v-else-if="scope.row.receive_status_code == 1" class="el-icon-warning-outline"
                  style="color:#e6a23c;font-size:22px"></i>
              </div>
              <div>
                <p style="font-weight:bold;font-size:15px;margin-bottom:5px;">
                  {{ getOnlineStatus(scope.row.receive_status_code) }}</p>
                <p style="font-size:12px">{{ getOnlineStatus(scope.row.receive_status_code) }}</p>
              </div>
            </div>

            <!-- 触发popover的节点 -->
            <div slot="reference" class="name-wrapper" style="cursor:pointer;">
              <div :class="getOnlineClass(scope.row.receive_status_code)">
                {{ getOnlineStatus(scope.row.receive_status_code) }}</div>
              <!-- {{ scope.row.progressStatus?'推送成功':'推送失败' }} -->
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="receive_complete_time" label="推送完成时间" align="center" width="150">
      </el-table-column>
      <el-table-column prop="pub_ok_sche_cnt" align="center" label="内容下发成功数" width="150">
      </el-table-column>
      <el-table-column prop="pub_status" align="center" label="下发状态">
        <template slot-scope="scope">
          <el-popover trigger="click" width="250" placement="top" :ref="`popover${scope.$index}`">
            <!-- scope.row.xxxx 获取该行数据 -->
            <div class="flex">
              <div style="margin-right:10px">
                <i v-if="scope.row.pub_status_code == 2" class="el-icon-circle-close"
                  style="color:var(--text-color);font-size:22px"></i>
                <i v-else-if="scope.row.pub_status_code == 3" class="el-icon-circle-check"
                  style="color:rgba(23, 159, 78, 1);font-size:22px"></i>
                <i v-else-if="scope.row.pub_status_code == 1" class="el-icon-warning-outline"
                  style="color:#e6a23c;font-size:22px"></i>
              </div>
              <div>
                <p style="font-weight:bold;font-size:15px;margin-bottom:5px;">
                  {{ getPubRecordStatus(scope.row.pub_status_code) }}</p>
                <p style="font-size:12px">{{ getPubRecordStatus(scope.row.pub_status_code) }}</p>
              </div>
            </div>
            <!-- 触发popover的节点 -->
            <div slot="reference" class="name-wrapper" style="cursor:pointer;">
              <div :class="getOnlineClass(scope.row.pub_status_code)">{{ getPubRecordStatus(scope.row.pub_status_code) }}
              </div>
              <!-- {{ scope.row.progressStatus?'推送成功':'推送失败' }} -->
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="pub_complete_time" label="下发完成时间" align="center" width="150">
      </el-table-column>
      <!-- <el-table-column align="center" label="投放进度" v-show="0">
        <template slot-scope="scope">
          <div>
            <el-popover placement="top" width="300" trigger="hover" popper-class="my-popover">
              <p @click="toDetail" class="look">查看详情</p> 
              <div class="progress">
                <p class="tou">投放进度
                  <span v-show="scope.row.progressStatus == '发布成功'" style="color:rgba(41, 160, 90, 1)">{{
                      scope.row.progressStatus
                  }}</span>
                  <span v-show="scope.row.progressStatus == '发布失败'" style="color:var(--background-color)">{{
                      scope.row.progressStatus
                  }}</span>
                </p>
                v-show="scope.row.progressStatus !== '发布成功'" 
                <el-progress width="100" v-show="scope.row.progressStatus !== '发布成功'" :percentage="scope.row.progressValue">
                </el-progress>
                <p class="look" @click="toDetail"  v-show="scope.row.progressStatus !== '发布成功'">去处理</p>
              </div>
              <span slot="reference" class="cursor">
                  {{scope.row.progressValue}} %
                </span>
            </el-popover>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <!-- <el-button @click="handleClick(scope.row)" type="text" size="small">编辑</el-button> -->
          <el-button @click="handleClick(scope.row)" type="text" size="small">查看详情</el-button>
          <!-- 暂时隐藏功能 -->
          <!-- <el-button type="text" size="small">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!--      分页-->
    <div class="block">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage3" :page-size="pageSize" layout="total,sizes,prev,pager, next, jumper"
        :total="totalNum" :page-sizes="[10, 20, 50, 100]" background>
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { get_historical_data } from "@/api/historical/historical"
export default {
  name: "TimeFrame",
  data() {
    return {
      inputValue: "",
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      value1: "",
      value2: "",
      //  表格
      tableData: [
      ],
      //  市场运营
      option1: [
        {
          value: "选项1",
          label: "市场运营",
        },
        {
          value: "选项2",
          label: "上海市场",
        },
        {
          value: "选项3",
          label: "杭州市场",
        },
        {
          value: "选项4",
          label: "南京市场",
        },
        {
          value: "选项5",
          label: "广州市场",
        },
        {
          value: "选项5",
          label: "北京市场",
        },
      ],
      optionValue1: "",
      //  播放模式
      option2: [
        {
          value: "选项1",
          label: "播放模式",
        },
        {
          value: "选项2",
          label: "全播",
        },
        {
          value: "选项3",
          label: "轮播",
        },
        {
          value: "选项4",
          label: "插播",
        },
        {
          value: "选项5",
          label: "叠加",
        },
      ],
      optionValue2: "",
      //  未投放
      option3: [
        {
          value: "选项1",
          label: "未投放",
        },
        {
          value: "选项2",
          label: "投放中",
        },
        {
          value: "选项3",
          label: "使用中",
        },
        {
          value: "选项4",
          label: "已过期",
        },
      ],
      optionValue3: "",
      //  播放类型
      option4: [
        {
          value: "选项1",
          label: "单屏内容",
        },
        {
          value: "选项2",
          label: "联屏内容",
        },
        {
          value: "选项3",
          label: "浮层内容",
        },
      ],
      optionValue4: "",
      //  表格区域高度
      autoHeight: {
        height: "",
        heightNum: "",
      },
      // 获取表单条件
      pageSize: 10,
      pageNum: 0,
      // 进度条
      progressValue: 0,
      total: "",
      queryList: {
        batch_id: "",
        page_num: 0,
        page_size: 10
      },
      loading:true
    };
  },
  methods: {
    handleClick(row) {
      console.log(row);
      //  跳转页面
      // this.$router.push({
      //   path: "/deploy/HisEdit",
      // });

      this.$router.push({
        path: "/deploy/pubdetail",
        query: { batch_id: row.batch_id },
      });
    },
    // 获取历史投放
    getHistoricalData() {
      const params = this.queryList
      get_historical_data(params).then(res => {
        console.log(res);
        this.totalNum = res.data[0].totalElements;
        this.tableData = res.data[0].content;
        this.total = res.data[0].totalElements
        this.loading = false;

        this.tableData.forEach(item => {
          item.newDate = new Date().getTime();
          item.endTime = new Date(item.complete_time).getTime();
          item.progressValue = 0;
          if (item.complete_time == "") {
            item.progressStatus = '正在分发'
            item.progressValue = Number(item.pub_sche_cnt) / Number(item.all_sche_cnt) * 100
            console.log(Number(item.pub_sche_cnt) / Number(item.all_sche_cnt));
          } else {
            if (item.pub_sche_cnt / item.all_sche_cnt == 1) {
              item.progressStatus = "发布成功"
              item.progressValue = 100;
            } else {
              item.progressStatus = "发布失败"
              item.progressValue = item.pub_sche_cnt / item.all_sche_cnt * 100
            }
          }
          if (item.complete_time == undefined) {
            if (item.pub_sche_cnt == 0 && item.all_sche_cnt == 0) {
              item.progressStatus = "发布失败"
              item.progressValue = 0
            }
          }
        })
        console.log(this.tableData);
      })
    },
    getOnlineClass(val) {
      switch (val) {
        case 1:
          return 'off_line';
        case 2:
          return 'error'
        case 3:
          return 'on_line'
      }
    },
    getOnlineStatus(val) {
      switch (val) {
        case 1:
          return '接收中';
        case 2:
          return '接收异常'
        case 3:
          return '接收完成'
      }
    },
    getPubRecordStatus(val) {
      switch (val) {
        case 0:
          return '处理数据'
        case 1:
          return '数据异常'
        case 2:
          return '分发中'
        case 3:
          return '分发成功'
      }
    },
    //  列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 200 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    handleSizeChange(val) {
      this.queryList.page_size = val;
      this.getHistoricalData()
    },
    handleCurrentChange(val) {
      this.currentPage3 = val;
      this.queryList.page_num = val - 1;
      this.getHistoricalData()
    },
    // 跳转详情
    toDetail() {
      this.$router.push({
        path: "/deploy/pubFail",
      });
    },
    search(){
      this.loading = true
      this.queryList.page_num = 0;
      if(this.queryList.batch_id.length <= 10){
        this.queryList.batch_id = this.queryList.batch_id.toUpperCase()
      }
      this.getHistoricalData()
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getHistoricalData()
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style scoped lang='scss'>
.deploy {
  padding: 0 20px 0 13px;
  width: 100%;
}

.top {
  display: flex;
  height: 40px;
  align-items: center;
  padding: 27px 0 30px 0;
  margin-bottom: 15px;
}

.top>div {
  margin-right: 8px;
}

.top>.queryTime {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: left;
}

.top>.queryTime {
  display: flex;
  align-items: center;
}

.top>.queryTime>span {
  width: 62px;
}

::v-deep .el-select {
  width: 150px;
}

/*日历*/
.el-input__inner {
  width: 243px;
  height: 32px;
  margin-right: 16px;
}

::v-deep .el-range-separator {
  line-height: 23px;
}

::v-deep .el-range__icon {
  line-height: 17px !important;
}

::v-deep .el-date-editor .el-range-separator {
  width: 6%;
  padding: 0;
}

.top>button {
  width: 88px;
  height: 32px;
  line-height: 9px;
}

/*表格*/
::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-table__body {
  table-layout: auto;
}

/*分页*/
.el-pagination {
  height: 0.32rem;
  margin-top: 35px;
  text-align: right;
}

.look {
  cursor: pointer;
  color: rgba(42, 130, 228, 1);
  width: 60px;
  background-color: rgba(227, 241, 255, 1);
  height: 26px;
  border-radius: 3px;
  line-height: 26px;
  text-align: center;
  position: absolute;
  right: 4%;
  top: 50%;
}

.tou {
  margin-bottom: 10px;
}

.my-popover {
  padding: 20px;
}

.progress {
  position: relative;
  padding: 10px;
}

::v-deep .el-progress-bar {
  width: 75% !important;
}

.off_line {
  color: var(--text-color);
}

.on_line {
  color: rgba(23, 159, 78, 1);
}

.error {
  color: rgba(56, 56, 56, 1);
}
</style>