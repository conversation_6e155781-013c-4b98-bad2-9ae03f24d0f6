<template>
  <div class="sources">
    <!-- 本地上传 -->
    <!-- <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" :on-preview="handlePreview"
      :on-remove="handleRemove" :before-remove="beforeRemove" multiple :limit="3" :on-exceed="handleExceed"
      :file-list="fileList">
    </el-upload> -->
    <div class="upload-demo">
      <el-button size="small" type="primary" @click="$router.push('/contentCenter/srcfiles')">点击上传</el-button>
    </div>
    <!-- 搜索 -->
    <el-input class="search" placeholder="请输入内容" prefix-icon="el-icon-search" v-model="searchValue">
    </el-input>
    <!-- tab图片、视频 -->
    <div class="twoTabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="图片" name="photo" class="tabs">
          <ul class="imgsDiv" v-loading="loadingImage">
            <li v-for="(item, index) in imgList" :key="index" @click="selectImage(item,'picture')"
              style="margin-top:30px !important">
              <img :src="item.original_url" />
              <span style="width: 100%;display: inline-block; padding-top: 10px;overflow:hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow:ellipsis;" :title="item.photo_name">{{ item.photo_name }}</span>
            </li>
          </ul>
        </el-tab-pane>
        <div style="display:flex;justify-content: center;">
          <el-pagination @size-change="handleImageSizeChange" @current-change="handleImageCurrentChange"
            :current-page="currentPage4" :page-sizes="[10, 20, 30, 40]" layout="prev, pager, next" :total="imageTotal"
            small>
          </el-pagination>
        </div>
        <el-tab-pane label="视频" name="video" class="tabs">
          <ul class="imgsDiv">
            <li v-for="(item, index) in VideoList" :key="index" @click="selectImage(item,'video')">
              <img :src="item.thumb" />
            </li>
          </ul>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { self_photo_list } from "@/api/files/pictureResources";
import { get_video_list } from "@/api/files/videoResources";

export default {
  name: "contentSources",
  data() {
    return {
      fileList: [],
      // 搜索框
      searchValue: "",
      activeName: "photo",
      imgList: [],
      VideoList: [],
      currentPage: 1, //页码
      totalNum: 0, //总数据数量
      pageSize: 10, //每页显示多少条,
      loadingImage: true,
      imageTotal: ''
    };
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    /**
     * 获取图片数据
     */
    getPhoneList() {
      const params = {
        unit: "all", // (String) options: “all”， "in_year", "in_month", "in_day"  // optional
        limit: this.pageSize,
        offset: (this.currentPage - 1) * this.pageSize,
        query_status: [1, 2, 9, 10], // (List)
        order_by: "-last_mtime",
        root_flag: 0, // (Int) 是否继承总部图片
        pub_flow_status: 4
      };
      self_photo_list(params).then((res) => {
        // console.log(res, 'photo');
        this.imgList = res.data[0].photos;
        this.imageTotal = res['data'][0]['unit_info']['total']
        this.loadingImage = false;
      });
    },

    // 获取视频
    getVideoList() {
      this.loading = true;
      const params = {
        limit: this.pageSize,
        offset: (this.currentPage - 1) * this.pageSize,
        tags: "", // 用于标签查询
        pub_flow_status: 4
      };
      get_video_list(params).then((res) => {
        // console.log("res", res);
        if (res.rst == "ok") {
          // console.log(res.data[0].video_list.video_list);
          this.VideoList = res.data[0].video_list.video_list
          // console.log(res);
          this.loading = false;
        } else {
          this.$message.error("获取视频内容失败");
        }
      });
      // console.log(params);
      // this.loading = false;
    },
    selectImage(imgObj) {
      // console.log(imgObj, "imgObj");
      this.$emit("selectImage", imgObj, this.activeName);
    },
    handleImageSizeChange(e) {
      this.loadingImage = true;
      this.pageSize = e;
      his.getPhoneList()
    },
    handleImageCurrentChange(e) {
      this.loadingImage = true;
      // console.log(e);
      this.currentPage = e;
      this.getPhoneList()
    }
  },
  created() {
    this.getPhoneList();
    this.getVideoList();
  },
};
</script>

<style scoped>
.sources {
  padding: 10px 5px;
}

.sources ::v-deep .el-upload .el-button {
  width: 297px;
  height: 48px;
  background-color: rgba(227, 91, 91, 1);
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(227, 91, 91, 1);
  margin-bottom: 10px;
}

.sources ::v-deep .el-input--small .el-input__inner {
  background: rgba(56, 56, 56, 1) !important;
  width: 295px;
}

.twoTabs ::v-deep .el-tabs {
  flex-direction: column;
}

.twoTabs ::v-deep #tab-photo,
.twoTabs ::v-deep #tab-video {
  color: #fff;
  font-size: 18px;
}

.twoTabs ::v-deep .el-tabs__nav-wrap::after {
  width: 0;
  height: 0;
}

.tabs .imgsDiv {
  display: flex;
  flex-wrap: wrap;
  height: 5rem;
  margin-top: -30px;
  overflow: auto;
  list-style: none;
}

.tabs .imgsDiv>li {
  width: 130px;
  height: 130px;
  margin: 10px;
  border: rgba(166, 166, 166, 1) solid 1px;
  cursor: pointer;
}

.tabs .imgsDiv>li>img {
  width: 100%;
  height: 100%;
}

::v-deep .el-pagination button {
  background-color: #383838;
  color: white;
}

::v-deep .el-pagination li {
  background-color: #383838;
  color: white;
}

::v-deep .el-pagination .active {
  color: #409eff;
}
.upload-demo button{
  background-color: var(--text-color);
  color: #fff;
  border: 0;
  height: 45px;
  border-radius: 8px;
  font-size: 18px;
  margin-bottom: 10px;
  width: 100%;
}
</style>
