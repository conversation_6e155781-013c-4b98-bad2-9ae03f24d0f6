<template>
    <div class="jump">
        <div class="loader"></div>
        <div class="loadtext">LOADING...</div>
        <div class="text">正在跳转页面，请稍后</div>
    </div>
</template>

<script>
import {ssoLogin} from '@/api/login.js'
export default {
    components: {

    },
    data() {
        return {

        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
        //http://*************:8013/ml/jump?&code=1636cee4-0684-4a54-8917-e25f6e73d95b
        let code = this.$route.query.code;
        
        if(!code){
            this.$router.replace({path:'/login'})
            return
        }
        ssoLogin({code}).then(res=>{
            if(res.rst == 'ok'){
                this.$store.dispatch('Jump',res).then(val=>{
                    this.$router.replace({path:'/'})
                }).catch(rej=>{
                    console.log(rej,'rej');
                })
            }else{
                this.$message.error(res.error_msg);
                this.$router.replace({path:'/login'})
            }
            
        })
    },
    methods: {

    },
};
</script>

<style scoped lang="scss">
.jump{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 50px;
}
.loader{
    width: 120px;
    height: 120px;
    /* 相对定位 */
    position: relative;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #4bc0c8;
    /* 执行动画：动画名 时长 线性的 无限次播放 */
    animation: spin 2s linear infinite;
}
/* 中圈 */
.loader::before{
    content: "";
    /* 绝对定位 */
    position: absolute;
    left: 5px;
    top: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #c779d0;
    /* 执行动画：动画名 时长 线性的 无限次播放 */
    animation: spin 3s linear infinite;
}
/* 内圈 */
.loader::after{
    content: "";
    position: absolute;
    left: 15px;
    top: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: #feac5e;
    /* 执行动画：动画名 时长 线性的 无限次播放 */
    animation: spin 1.5s linear infinite;
}

/* 定义动画 */
@keyframes spin {
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}

.loadtext{
    font-weight: bold;
    margin-top: 30px;
    font-size: 20px;
    margin-bottom: 20px;
}
</style>
