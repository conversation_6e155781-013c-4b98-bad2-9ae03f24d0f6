<template>
    <div class="cardManage">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane v-for="item in tabsList" :label="item.title" :name="item.name" :key="item.name">
                <component :is="item.component" v-if="activeName == item.name"></component>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import AlbumList from './components/AlbumList.vue';
import WorkList from './components/WorkList.vue';
export default {
    data() {
        return {
            tabsList: [{
                title: '专辑列表',
                name: 'albumList',
                component: AlbumList
            },
            {
                title: '任务列表',
                name: 'workList',
                component: WorkList
            }],
            activeName: 'albumList'
        }
    },
    components: {
        AlbumList,
        WorkList
    }
}
</script>


<style lang="scss" scoped></style>