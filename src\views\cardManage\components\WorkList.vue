<template>
    <div class="AlbumList" v-loading="Loading">
        <div class="album_header">
            <div>
                <el-input placeholder="请输入任务名称" v-model="queryList.task_name" clearable
                    style="width: 230px;margin-bottom: 10px">
                </el-input>
                <!-- <el-button type="primary" @click="getWorkContent()">搜索</el-button> -->
                <el-button type="primary" @click="searchWork()">搜索</el-button>
            </div>
            <div>
                <el-button type="primary" v-if="is_quoted" @click="openNewWork()">新建任务</el-button>
            </div>
        </div>
        <div class="album_body">
            <el-table :data="WorkList" style="width: 100%" :height="autoHeight.height"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px' }"
                :cell-style="{ 'text-align': 'center' }">
                <el-table-column type="selection" align="center">
                </el-table-column>
                <el-table-column label="任务名称" width="200" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.task_name }}
                    </template>
                </el-table-column>

                <el-table-column label="近期内容" align="center">
                    <template slot-scope="scope">
                        <div class="card"
                            v-if="scope.row.src_tpl_cf_info.cs_list ? scope.row.src_tpl_cf_info.cs_list.length > 0 : []">
                            <div v-for="item in scope.row.src_tpl_cf_info.cs_list.slice(0, 5)" :key="item.id"
                                class="card_item">
                                <img :src="item.thumb_url" alt="">
                            </div>
                        </div>
                        <div v-else>
                            暂无内容
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="旧专辑信息"   width="250">
                    <template slot-scope="scope">
                        <div class="card_info">
                            <div class="left">
                                <span>专辑名称:{{ scope.row.src_tpl_cf_info.classify_label }}</span>
                                <span>内容数量:{{ scope.row.src_tpl_cf_info.cs_list.length }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="新专辑信息"  width="250">
                    <template slot-scope="scope">
                        <div class="card_info">
                            <div class="left">
                                <span>专辑名称:{{ scope.row.sub_tpl_cf_info.classify_label }}</span>
                                <span>内容数量:{{ scope.row.sub_tpl_cf_info.cs_list.length }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="内容方向" width="80" align="center">
                    <template slot-scope="scope">
                        <div>
                            {{ scope.row.src_tpl_cf_info.v_or_h == 0 ? '横屏' : '竖屏' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="任务状态" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.status_display }}
                        <p v-if="scope.row.status != 1" @click="lookTaskDetail(scope.row)"
                            style="cursor: pointer;color: #24b17d;">查看详情</p>
                    </template>
                </el-table-column>
                <el-table-column label="任务生效时间" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.job_schedule_tm }}
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="180">
                    <template slot-scope="scope">
                        <div class="card_event">
                            <!-- <div class="edit" @click="add_or_edit_content(scope.row)" v-if="is_quoted">编辑</div> -->
                            <!-- <div class="del_text" v-if="is_quoted">删除</div> -->
                            <!-- <el-button @click="add_or_edit_content(scope.row)" type="primary"
                                :disabled="scope.row.is_quoted">编辑</el-button> -->
                            <el-button type="danger" v-if="scope.row.status == 1 || scope.row.status == 9"
                                @click="deleteAlbum(scope.row)">删除</el-button>
                            <!-- v-if="scope.row.is_deleted" -->
                            <!-- <el-button type="primary" @click="lookTaskDetail(scope.row)">查看详情</el-button> -->
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="album_footer flex flex-1">
                <div class="left_button_wrap flex-1">
                    <!-- <el-button type="primary" class="EventButton" @click="openBatchTagDialog"
                        v-if="is_quoted">批量标签</el-button> -->
                </div>
                <div class="right_page_wrap">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 50, 100]" layout="total,sizes,prev,pager, next, jumper"
                        :total="totalNum"></el-pagination>
                </div>
            </div>
        </div>

        <el-dialog title="新增任务" :visible.sync="dialogFormVisible" width="30%">
            <el-form :model="WorkForm" :rules="rules" label-width="100px" ref="WorkForm">
                <el-form-item label="任务名称:" :label-width="formLabelWidth" prop="task_name">
                    <el-input v-model="WorkForm.task_name" autocomplete="off" style="width: 280px;"></el-input>
                </el-form-item>
                <el-form-item label="旧专辑:" :label-width="formLabelWidth" prop="src_tpl_cf">
                 <el-select
                    v-model="WorkForm.src_tpl_cf"
                    style="width: 280px;"
                    filterable 
                    clearable  
                    placeholder="请选择或者输入搜索" 
                    v-loadmore="()=>loadMore()"
                    remote 
                    :remote-method="(queryOld)=>remoteMethod(queryOld,'remote')"
                    @blur="queryOld=''"
                    @clear="handleClear"
                         >
                        <el-option v-for="item in replaceAlmbumList" :key="item.value" :label="item.classify_label"
                            :value="item.sche_id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="新专辑:" :label-width="formLabelWidth" prop="sub_tpl_cf">
                    <el-select 
                    v-model="WorkForm.sub_tpl_cf" 
                     style="width: 280px;" 
                     filterable
                     clearable 
                     placeholder="请选择或者输入搜索" 
                     v-loadmore="()=>loadMore2()" 
                     remote 
                     :remote-method="(queryNew)=>remoteMethod2(queryNew,'remote')"
                     @blur="queryNew=''"
                      @clear="handleClear2"
                   >
                        <el-option v-for="item in beReplaceAlmbumList" :key="item.value" :label="item.classify_label"
                            :value="item.sche_id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="执行时间:" :label-width="formLabelWidth">
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="WorkForm.schedule_tm" type="datetime" style="width: 280px;"  
                        placeholder="选择日期时间">
                    </el-date-picker>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog">取 消</el-button>
                <el-button type="primary" @click="requestAddWork">确 定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="任务详情" v-if="dialogTableVisible" :close-on-click-modal="false"
            :visible.sync="dialogTableVisible" class="table_dialog">
            <p style="margin-bottom: 10px;">本次任务共涉及到{{ workDetail.task_summary.all_cnt }}个投放任务,已成功{{
                workDetail.task_summary.succ_cnt }}
                个,已失败{{ workDetail.task_summary.fail_cnt }}个,未开始{{ workDetail.task_summary.wait_cnt }}个</p>

            <el-select style="margin-bottom: 10px;" clearable v-model="task_Search_form.status" placeholder="请选择">
                <el-option v-for="item in workDetail.status_options" :key="item[0]" :label="item[1]" :value="item[0]">
                </el-option>
            </el-select>
            <el-button @click="get_tpl_detail()" type="primary">搜索</el-button>

            <el-table :data="workDetailList"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <el-table-column label="发布类型">
                    <template slot-scope="scope">
                        <p v-if="scope.row.platform == 2">餐牌组投放</p>
                    </template>
                </el-table-column>
                <el-table-column property="btplan_name" label="发布名称"></el-table-column>
                <el-table-column label="替换状态">
                    <template slot-scope="scope">
                        <p :style="workStatusColor(workStatus(scope.row))"> {{ workStatus(scope.row) }}</p>
                    </template>
                </el-table-column>
            </el-table>
            <div class="album_footer flex flex-1">
                <div class="left_button_wrap flex-1">

                </div>
                <div class="right_page_wrap">
                    <el-pagination background @size-change="handleTaskSizeChange"
                        @current-change="handleTaskCurrentChange" :page-sizes="[10, 20, 50, 100]"
                        layout="total,sizes,prev,pager, next, jumper" :total="workDetail.totalElements"></el-pagination>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import { get_classify_detail } from "@/api/device/device"
import { create_group_classify, get_tpl_classify_list } from "@/api/contentdeploy/contentdeploy"
import { get_tpl_classify_replace_task_list, create_tpl_classify_replace_task, delete_tpl_classify_replace_task, get_tpl_classify_replace_task_items } from "@/api/task/task"

export default {
    data() {
        return {
            Loading: true,
            autoHeight: {
                //列表区高度
                height: "",
                heightNum: ""
            },
            queryList: {
                page_size: 10, //每页显示条数
                page_num: 0,
                task_name: ''
            },
            options: [],
            select_value: '',
            tableData: [],
            totalNum: 0,
            WorkForm: {
                task_name: '',
                src_tpl_cf: [],
                sub_tpl_cf: [],
                schedule_tm: ''
            },
            dialogFormVisible: false,
            rules: {
                task_name: [
                    { required: true, message: '请输入任务名称', trigger: 'blur' }
                ],
                src_tpl_cf: [
                    { required: true, message: '请选择旧专辑', trigger: 'change' }
                ],
                sub_tpl_cf: [
                    { required: true, message: '请选择新专辑', trigger: 'change' }
                ],
                schedule_tm: [
                    { type: 'date', required: true, message: '选择日期时间', trigger: 'change' }
                ],
            },
            cardList: [],
            WorkList: [],



            replaceAlmbumList: [], // 下拉选项
            replaceAlmbumListQuery: {      
               page_size: 20,
               page_num: 0,
               classify_label:''
	    	},
		    pageSearch: 0,                          // 远程搜索页数   旧
	    	serachValue: "",                        // 远程搜索输入内容 旧
            totalReplace : 0,     //旧 总数
            queryOld:'',


            beReplaceAlmbumList: [],
            beReplaceAlmbumListQuery:{
                page_size: 20,
                page_num: 0,
               classify_label:''
            },

            beReplacePageSearch: 0,                          // 远程搜索页数   新
	    	beReplaceSerachValue: "",                        // 远程搜索输入内容 新
            totalBeReplace : 0,     //新 总数
            queryNew:'',


            task_Search_form: {
                page_size: 10,
                page_num: 0,
                status: ''
            },
            dialogTableVisible: false,
            task_id: '',
            workDetailList: [],
            workDetail: {
                task_summary: {
                    all_cnt: 0,
                    success_cnt: 0,
                    fail_cnt: 0,
                }
            },
           
        }
    },
    computed: {
        workStatus() {
            return function (val) {
                let options = this.workDetail.status_options;
                console.log(options);
                let status = null
                options.forEach(item => {
                    if (item[0] == val.replace_status) {
                        status = item[1]
                    }
                })
                return status
            }
        },
        workStatusColor() {
            return function (val) {
                console.log(val, 'val');
                switch (val) {
                    case '执行成功':
                        return 'color: var(--btn-success-color)'
                    case '待执行':
                        return 'color: var(--text-gray)'
                    case '执行中':
                        return 'color: var(--text-gray)'
                    case '执行失败':
                        return 'color: var(--btn-danger-color)'

                }
            }
        }
    },
    props: {
        is_quoted: {
            type: Boolean,
            default: true
        }
    },
    methods: {
      
        getReplaceAlmbumList(){

            this.remoteMethod()
            this.remoteMethod2()
        },
      handleClear(){
            this.totalReplace = 0 // 重置总数
            this.pageSearch = 0;
            this.replaceAlmbumList = []
            this.remoteMethod()
       },
       handleClear2(){
        this.totalBeReplace = 0 // 重置总数
        this.beReplacePageSearch = 0;
        this.beReplaceAlmbumList = []
        this.remoteMethod2()
       },

        loadMore () {
            if (this.pageSearch >= Math.ceil(this.totalReplace / 20)) {
            return
         }
           this.pageSearch+=1;
           this.remoteMethod(this.queryOld, 'scroll')

       },
           // 远程搜索列表 旧
        async  remoteMethod(queryOld,type) {
             const searchText = queryOld || ''
            if(type =='remote'){
                 this.queryOld = searchText
                 this.totalReplace = 0 // 重置总数
                 this.pageSearch = 0;
                 this.replaceAlmbumList = []
                this.replaceAlmbumListQuery.page_num=0
                this.replaceAlmbumListQuery.classify_label=searchText
            }else if(type=='scroll'){
                this.replaceAlmbumListQuery.page_num = this.pageSearch
                this.replaceAlmbumListQuery.classify_label = searchText // 保持搜索条件
            }else{
               this.replaceAlmbumListQuery= {      
                    page_size: 20,
                    page_num: 0,
                    classify_label:''
                }
            }
            const  res=  await  get_tpl_classify_list(this.replaceAlmbumListQuery)
                if (res.rst == 'ok') {
                      // 初次搜索和远端搜索，需要重置总数
                    if (!type || type == 'remote') {
                        this.totalReplace = res['data'][0]['totalElements']
                    }
                    if (type === 'scroll') {
                        this.replaceAlmbumList.push(...res.data[0].content)
                    } else {
                        this.replaceAlmbumList = res.data[0].content
                    }

                } else {
                    this.$message.error(res.error_msg)
                }
         
        },

        loadMore2 () {
        if (this.beReplacePageSearch >= Math.ceil(this.totalBeReplace / 20)) {
                return
            }
          this.beReplacePageSearch+=1
          this.remoteMethod2(this.queryNew, 'scroll')
           
       },
          // 远程搜索列表 新
        async  remoteMethod2 (queryNew,type) {
            const searchText = queryNew || ''
            if(type =='remote'){
                 this.queryNew = searchText
                 this.totalBeReplace = 0 // 重置总数
                 this.beReplacePageSearch = 0;
                 this.beReplaceAlmbumList = []
                this.beReplaceAlmbumListQuery.page_num=0
                this.beReplaceAlmbumListQuery.classify_label=searchText
            }else if(type=='scroll'){
                this.beReplaceAlmbumListQuery.page_num = this.beReplacePageSearch
                this.beReplaceAlmbumListQuery.classify_label = searchText // 保持搜索条件
            }else{
               this.beReplaceAlmbumListQuery= {      
                    page_size: 20,
                    page_num: 0,
                    classify_label:''
                }
            }
            const  res=  await  get_tpl_classify_list(this.beReplaceAlmbumListQuery)
                if (res.rst == 'ok') {
                      // 初次搜索和远端搜索，需要重置总数
                    if (!type || type == 'remote') {
                        this.totalBeReplace = res['data'][0]['totalElements']
                    }
                    if (type === 'scroll') {
                        this.beReplaceAlmbumList.push(...res.data[0].content)
                    } else {
                        this.beReplaceAlmbumList = res.data[0].content
                    }
                } else {
                    this.$message.error(res.error_msg)
                }
        }, 

        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 330 + "px";
            this.autoHeight.heightNum = windowHeight - 330;

            if (!this.is_quoted) {
                this.autoHeight.height = windowHeight - 530 + "px";
            }

        },
        //页码改变
        handleCurrentChange(val) {
            // console.log(`现在是第${val}页`);
            this.queryList.page_num = val - 1;
            this.getWorkContent()
        },
        handleSizeChange(val) {
            // console.log(`每页${val}条`);
            this.queryList.page_size = val;
            this.getWorkContent()
        },
        handleTaskSizeChange(val) {
            this.task_Search_form.page_size = val;
            this.get_tpl_detail()
        },
        handleTaskCurrentChange(val) {
            this.task_Search_form.page_num = val - 1;
            this.get_tpl_detail()
        },

        searchWork(){
            this.queryList.page_num = 0;
            this.getWorkContent()
        },
        getWorkContent() {
            this.Loading = true;
            get_tpl_classify_replace_task_list(this.queryList).then(res => {
                console.log(res, 'resxx');
                if (res.rst == 'ok') {
                    this.WorkList = res['data'][0]['content']
                    this.totalNum = res['data'][0]['totalElements']
                    this.Loading = false;
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },

        getCardContent() {
            get_tpl_classify_list({
                page_size: 1000, //每页显示条数
                page_num: 0,
                task_name: ''
            }).then(res => {
                console.log(res, 'resxx');
                if (res.rst == 'ok') {
                    // this.cardList = res['data'][0]['content']
                    this.replaceAlmbumList = res['data'][0]['content']
                    this.beReplaceAlmbumList = res['data'][0]['content']
                    console.log(this.replaceAlmbumList, 'replaceAlmbumList');
                    console.log(this.beReplaceAlmbumList, 'beReplaceAlmbumList');
                    // console.log(this.cardList, 'cardList');

                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },


        clearTags() {
            this.getCardContent()
        },
        clearTags1() {
            this.getCardContent()
        },

        openNewWork() {

            this.dialogFormVisible = true
            this.getReplaceAlmbumList()
        },

        cancelDialog() {
            this.pageSearch=0
            this.beReplacePageSearch=0
            this.serachValue=''
            this.beReplaceSerachValue=''
            this.replaceAlmbumList=[]
            this.beReplaceAlmbumList=[]
            this.dialogFormVisible = false;
            this.WorkForm.task_name = '';
            this.WorkForm.direction = '横屏'
        },
        requestAddWork() {
            this.$refs['WorkForm'].validate((valid) => {
                if (valid) {
                    create_tpl_classify_replace_task(this.WorkForm).then(res => {
                        console.log(res, 'res');
                        if (res.rst == 'ok') {
                            this.$message.success('创建任务成功');
                            this.dialogFormVisible = false;
                            this.getWorkContent()
                        } else {
                            this.$message.error(res.error_msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        add_or_edit_content(val) {
            console.log(val, 'val');

            this.$router.push({
                path: "/deploy/AddContentCard",
                query: {
                    ref_id: val.sche_id
                }
            })
        },

        relevan(row) {
            this.$emit("relevan", row)
        },
        deleteAlbum(row) {
            this.$confirm('此操作将删除该任务, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                console.log(row, 'row');
                delete_tpl_classify_replace_task({
                    task_id: row.task_id
                }).then(res => {
                    if (res['rst'] == 'ok') {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getWorkContent()
                    }
                })

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        // filterMethod(val, option) {
        //     // beReplaceAlmbumList
        //     console.log(val, 'val');
        //     // this.replaceAlmbumList = []
        //     if (val) { //val存在
        //         this.replaceAlmbumList = this.cardList.filter(item =>
        //             item.classify_label.includes(val)
        //         );
        //         console.log(this.replaceAlmbumList, 'replaceAlmbumList');
        //         this.$forceUpdate();
        //     } else { //val为空时，还原数组
        //         this.replaceAlmbumList = this.cardList;
        //     }
        // },
        // filterMethod1(val, option) {
        //     if (val) { //val存在
        //         this.beReplaceAlmbumList = this.cardList.filter(item =>
        //             item.classify_label.includes(val)
        //         );
        //         this.$forceUpdate();
        //     } else { //val为空时，还原数组
        //         this.beReplaceAlmbumList = this.cardList;
        //     }
        // },

        selectChange() {

        },
        lookTaskDetail(row) {
            this.task_id = row.task_id;
            this.get_tpl_detail()
            this.dialogTableVisible = true;
        },
        get_tpl_detail() {
            get_tpl_classify_replace_task_items({
                task_id: this.task_id,
                page_num: this.task_Search_form.page_num,
                page_size: this.task_Search_form.page_size,
                status: this.task_Search_form.status,

            }).then(res => {
                console.log(res, 'res');
                this.workDetail = res['data'][0]
                this.workDetailList = res['data'][0]['content']
            })
        }
    },
    created() {
     
        window.addEventListener("resize", this.getHeight);
        this.getHeight();
        this.getWorkContent()
        // this.getReplaceAlmbumList()
        // this.getCardContent()

    }
}
</script>

<style lang="scss" scoped>
.AlbumList {
    background-color: #eeeeee;
    padding: 20px 40px;

    .album_header {
        display: flex;
        justify-content: space-between;
    }

    .album_body {
        background-color: #fff;
        margin-top: 30px;
        padding: 20px;

        .card {
            display: flex;
            // justify-content: space-between;

            .card_item {
                width: 60px;
                height: 60px;
                margin-right: 10px;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                }
            }

        }

        .card_info {
            width: 300px;
            .left {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                // padding-left: 65px;
            }

            .right {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                // text-align: left;
            }
        }

        .card_status {
            .look_detail {
                cursor: pointer;
                font-weight: bold;
                color: #30b07f;
            }
        }

        .card_event {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;

            button {
                margin-left: 0 !important;
                margin-bottom: 5px;
            }

            .edit {
                color: #30b07f;
                cursor: pointer;
            }

            .delete {
                color: #fc172c;
                cursor: pointer;
            }
        }
    }

    .album_footer {
        height: 50px;
        display: flex;
        align-items: center;
    }
}

.prview {
    cursor: pointer;
}

::v-deep .table_dialog {
    .el-dialog__body {
        padding-top: 10px !important;
    }
}
</style>