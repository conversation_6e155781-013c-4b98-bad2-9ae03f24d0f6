<template>
    <div class="goods_data_manage">
        <div class="data_table">
            <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            header-row-class-name="table_thead"
            cell-class-name="table_cell"
            @selection-change="handleSelectionChange">

                <el-table-column type="selection" width="100" align="center"></el-table-column>
                <el-table-column label="商品图" prop="address" width=""  align="center"></el-table-column>
                <el-table-column label="商品编码" prop="address" width="" align="center"></el-table-column>
                <el-table-column label="商品名称" prop="address" width="" align="center"></el-table-column>
                <el-table-column label="商品规格" prop="address" width="" align="center"></el-table-column>
                <el-table-column label="商品分类" prop="address" width="" align="center"></el-table-column>
                <el-table-column label="售罄门店" prop="address" width="" align="center"></el-table-column>
                <el-table-column fixed="right" label="操作" width="200" align="center">
                    <template slot-scope="scope">
                        <el-button size="small" @click="SellOut(scope.row)">售罄</el-button>
                        <el-button size="small" @click="SellOutDetail(scope.row)">售罄详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
        
        </div>

        <el-drawer
        title=""
        :size="700"
        :wrapperClosable="false"
        :visible.sync="drawerShow"
        :direction="direction"
        :before-close="handleClose">
            <div class="drawer_content">
                <div>
                    <span class="content_label">商品名称：</span>
                    <span class="content_value">123</span>
                </div>
                <div class="content_list_wrapper">
                    <div class="content_list">
                        <div>
                            <span class="content_label">分区：</span>
                            <span class="content_value">上海区</span>
                        </div>
                        <div>
                            <span class="content_label">门店编码：</span>
                            <span class="content_value">SF1234</span>
                        </div>
                        <div>
                            <span class="content_label">门店名称：</span>
                            <span class="content_value">XXXXXXX</span>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
export default {
    components: {

    },
    data() {
        return {
            drawerShow:false,
            tableData:[
                {address:123}
            ]
        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {
        SellOut(scope){
            console.log(scope)
            this.$router.push('/goodsManage/goods_sell_out')
        },
        SellOutDetail(scope){
            console.log(scope)
            this.drawerShow = true;
        }
    },
};
</script>

<style scoped lang="scss">
    .goods_data_manage{
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 40px 20px 20px;
        background-color: var(--light-gray);
        .el-button{
            border-color: var(--light-green);
        }
        .el-button:hover{
            color: #fff;
            background-color: var(--light-green);
        }
        ::v-deep .el-table .table_thead{
            height: 60px;
            color: #fff;
            th{
                background-color: var(--light-green);
            }
        }
        ::v-deep .el-table .el-checkbox__inner{
            border-radius: 50%;
            width: 20px;
            height: 20px;
            &::after{
                height: 10px;
                left: 6px;
                top: 2px;
                width: 4px;
            }
        }
        ::v-deep .table_cell{
            height: 100px;
        }

        .drawer_content{
            width: 100%;
            height: 100%;
            background-color: #fff;
            box-sizing: border-box;
            padding: 0 20px;
            font-size: 16px;
            .content_label{
                color: var(--text-gray);
            }
            .contet_value{
                color: #000;
            }
            .content_list_wrapper{
                margin-top: 25px;
                height: calc(100% - 100px);
                overflow-y: auto;
                .content_list{
                    list-style: none;
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 10px;
                    div{
                        margin-right: 40px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }
</style>
