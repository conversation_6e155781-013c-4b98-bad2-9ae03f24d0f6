<template>
  <div>
    <table class='vertical' border='1' cellpadding='0' cellspacing='0' align='center' style='font-weight: bold'>
      <tr style='height: 44px;'>
        <td style='width: 100px'>工作日早餐</td>
        <td v-for='(item,index) in 3' :key='index' >
          <p class='connect'>{{item}}</p>
        </td>
      </tr>
      <tr style='height: 240px'>
        <td>早餐</td>
        <td v-for="(item,index) in 3" :key="index">
          <!-- <img src='../../assets/img/home_img/setTypeImg.png'> -->
        </td>
      </tr>
      <tr style='height: 240px'>
        <td>午餐</td>
        <td>
          <!-- <img src='../../assets/img/home_img/setTypeImg.png'> -->
        </td>
        <td>
          <div @click.stop='newAdd'>
            <img src='../../assets/img/home_img/jia.svg'>
            新增
          </div>
        </td>
        <td>
          <div @click.stop='newAdd'>
            <img src='../../assets/img/home_img/jia.svg'>
            新增
          </div>
        </td>
      </tr>
    </table>
    <!--    点击后出现-->
    <!--    点击后出现-->
    <el-dialog
      title="新增内容"
      :visible.sync="showAlertBox"
      width="61%"
      custom-class="tabsDatat"
      :before-close="showAlertBox">
      <!--      <div class='top'>-->
      <!--        <h3>新增内容</h3>-->
      <!--        <p @click.stop='showAlertBox=false'>×</p>-->
      <!--      </div>-->
      <div class='threeParts tag_set'>
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="图片" name="first">
            <div class='content'>
              <div class='chunk' v-for="(item,index) in 6" :key="index">
                <!-- <img src='../../assets/img/home_img/breakfast.png' /> -->
                <div :class="checkShow?'el-icon-circle-check':''" @click.stop="checked(index)"></div>
                <p>1920*1080</p>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="视频" name="second">
            <div class='content'>视频管理</div>
          </el-tab-pane>
          <el-tab-pane label="H5" name="third">
            <div class='content'>角色管理</div>
          </el-tab-pane>
        </el-tabs>
        <div class='selectAll'>
          <el-select v-model="selectValue1" placeholder="请选择">
            <el-option
              v-for="item in selectType1"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="selectValue2" placeholder="请选择">
            <el-option
              v-for="item in selectType1"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class='view' style="cursor:pointer;">查看</div>
      </div>
      <div class='btns'>
        <div class='close' @click.stop='showAlertBox=false'>取消</div>
        <div @click.stop='alertOk'>确定</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SetTypePlayer',
  data(){
    return{
      showAlertBox:false,
      //  图片、视频、H5
      activeName: 'first',
      //  新增内容弹出框的两个select
      selectType1: [{
        value: '选项1',
        label: '横屏1920*1080'
      }, {
        value: '选项2',
        label: '竖屏1920*1080'
      }, {
        value: '选项3',
        label: '其他'
      }],
      selectValue1: '横屏1920*1080',
      selectType2: [{
        value: '选项1',
        label: '全国市场'
      }, {
        value: '选项2',
        label: '上海市场'
      }, {
        value: '选项3',
        label: '北京市场'
      }, {
        value: '选项3',
        label: '成都市场'
      }
      ],
      selectValue2: '全国市场',
    }
  },
  methods:{
    //  新增
    newAdd(){
      this.showAlertBox = true;
    },
  }
}
</script>

<style scoped>
.vertical{
  width: 772px;
  margin: 10px auto;
  /*height: 600px;*/
  /*overflow: scroll;*/
  /*margin: auto;*/
  /*position: absolute;*/
  /*left: 0;*/
  /*top: 30px;*/
  /*right: 0;*/
  /*bottom: 62px;*/
  background-color: rgba(255, 255, 255, 1);
  border: rgba(198, 198, 198, 1) solid 1px;
}
.vertical>tr{
  text-align: center;
}
tr>td{
  position: relative;
  width: 100px;
}
.connect{
  width: 18px;
  height: 28px;
  line-height: 28px;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 0;
  bottom: 0;
}
tr>td>img{
  width: 90%;
  height: 90%;
  vertical-align: middle;
}
tr>td>div{
  position: absolute;
  margin: auto;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  width: 51px;
  height: 51px;
  border-radius: 50%;
  font-weight: 500;
  border: 1px solid #ebebeb;
  color: rgba(229, 229, 229, 1);
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
tr>td>div>img{
  width: 20px;
  height: 20px;
  display: block;
  margin: 6px 15px 2px;
}
/*点击后弹出*/
.alertBox{
  width: 10rem;
  height: 6.75rem;
  position: absolute;
  margin: auto;
  left: 0px;
  right: 0;
  top: -1rem;
  bottom: 0;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.16rem;
  font-size: 0.14rem;
  line-height: 150%;
  text-align: center;
  padding: 0 0.15rem 0.21rem 0.3rem;
  border: 1px solid #ddd;
  z-index: 20;
}
.alertBox>.top{
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.5rem;
  border-bottom: 0.01rem solid #e5e5e5;
}
.alertBox>.top>h3{
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
}
.alertBox>.top>p{
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.3rem;
  color: #999999;
}
/*浮层选择*/
.floatAlert .selectAll{
  width: 290px;
}
.threeParts{
  display: flex;
  justify-content: space-between;
  position: relative;
}
.threeParts .el-tabs{
  width: 100%;
  margin: 0 !important;

}
::v-deep .el-tabs__header{
  margin: 0 !important;
}
.selectAll{
  width: 380px;
  position: absolute;
  right: 116px;
  display: flex;
  align-items: center;
  height: 40px;
}
/*!*下拉框内容*!*/
.select1{
  margin:-10px 8px;
  width: 160px;
  height: 32px;
  border: 1px solid #ebebeb;
  color: rgba(80, 80, 80, 1);
}
::v-deep .el-input__inner{
  height: 32px;
}
.view{
  width: 0.88rem;
  height: 0.32rem;
  color: rgba(80, 80, 80, 1);
  background-color: var(--background-color);
  border-radius: 0.06rem;
  font-size: 0.14rem;
  text-align: center;
  right: 0.24rem;
  top: 3px;
  color: #fff;
  line-height: 0.32rem;
  position: absolute;
}
.content{
  height: 4.3rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  padding: 0.23rem 0 0 11px;
}
.content>.chunk{
  width: 1.85rem;
  height: 1.85rem;
  position: relative;
  margin-right: 0.1rem;
  margin-bottom: 10px;
  box-shadow: 0rem 0.03rem 0.03rem 0rem rgba(0, 0, 0, 0.12857142857142861);
}
.content>.chunk:hover >p{
  display: block;
  cursor: pointer;
}
.content>.chunk>img{
  width: 100%;
  height: 100%;
}
.content>.chunk>div{
  width: 0.23rem;
  height: 0.23rem;
  background-color: #75726a;
  opacity: .9;
  position: absolute;
  border-radius: 50%;
  top: 0.13rem;
  left: 0.1rem;
  cursor: pointer;
}
.content>.chunk>p{
  height: 0.26rem;
  width: 100%;
  line-height: 0.26rem;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(0, 0, 0, 0.35);
  text-align: center;
  position: absolute;
  opacity: 0.7;
  bottom: 0;
  background-color: #a29d86;
  font-size: 0.11rem;
  display: none;
}
.upload-demo{
  position: absolute;
  left: 30px;
  margin-top: 25px;
  background-color: var(--background-color);
}
.el-button{
  background-color: var(--background-color);
}
/*取消*/
.btns{
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}
.btns>div{
  width: 0.81rem;
  height: 0.36rem;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  border: rgba(166, 166, 166, 1) solid 1px;
  text-align: center;
  line-height: 0.36rem;
  color: #fff;
  cursor: pointer;
  font-weight: bold;
  background-color: rgba(108, 178, 255, 1);
}
.btns>.close{
  margin-right: 0.2rem;
  color: rgba(128, 128, 128, 1);
  background-color: #fff;
}
</style>
<style>
.newAddDialog ::v-deep .el-dialog__body,.el-dialog__header{
  padding: 0;
}
.tabsDatat .el-dialog__body{
  padding: 0 20px 10px!important;
}
/* tabs选中的样式 */
.tag_set .is-active{
  color: var(--text-color) !important;
  background-color: var(--active-color) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}
/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-first{
  padding: 0 20px !important;
}
/* 给最后一个设置padding */
.tag_set #tab-third{
  padding: 0 20px !important;
}
/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar{
  /* display: none; */
  background-color: var(--text-color) !important;
}
/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover{
  color: var(--text-color) !important;
}
</style>
