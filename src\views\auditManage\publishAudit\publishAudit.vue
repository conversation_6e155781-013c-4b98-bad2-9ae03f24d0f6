<template>
    <div class="deploy" v-loading="loading">
        <div class="top">
            <!-- <el-input placeholder="发布名称" style="width: 150px" prefix-icon="el-icon-search" v-model="queryList.name"
                clearable></el-input> -->
            <el-select v-model="queryList.flow_job_type" placeholder="审核类型" @change="search">
                <el-option v-for="item in auditTypeList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <el-button type="danger" style="background: var(--background-color)" @click="search">搜索</el-button>
        </div>
        <el-table :height="autoHeight.height" :data="tableData" :header-cell-style="{ background: 'var( --text-color-light);' }"
            style="width: 100%">
            <el-table-column prop="platform" label="发布类型" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.platform == 1">指定店铺投放</div>
                    <div v-else-if="scope.row.platform == 2">餐牌组投放</div>
                    <div v-else-if="scope.row.platform == 3">普通投放</div>
                    <div v-else-if="scope.row.platform == 4">联屏投放</div>
                </template>
            </el-table-column>
            <el-table-column prop="name" align="center" label="发布名称"></el-table-column>
            <el-table-column prop="pre_pubshopcnt" align="center" label="门店数">
                <template slot-scope="scope">
                    {{ scope.row.pub_result.pre_pubshopcnt }}
                </template>
            </el-table-column>
            <el-table-column prop="pre_pubscrcnt" align="center" label="屏幕数">
                <template slot-scope="scope">
                    {{ scope.row.pub_result.pre_pubscrcnt }}
                </template>
            </el-table-column>

            <el-table-column prop="sche_num" align="center" label="内容数量">
                <template slot-scope="scope">
                    {{ scope.row.obj_info.pre_pubscrcnt }}
                </template>

            </el-table-column>
            <el-table-column label="播放时段" align="center" width="180">
                <template slot-scope="scope">
                    {{ scope.row.play_info.start_time }}
                    <br />~
                    <br />
                    {{ scope.row.play_info.end_time }}
                </template>
            </el-table-column>

            <!-- <el-table-column prop="pub_ok_sche_cnt" label="内容下发成功数" align="center"></el-table-column> -->
            <!-- <el-table-column prop="status" label="下发状态" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.status == '下发完成'" class="on_line">{{ scope.row.status }}</div>
                    <div v-else class="off_line">{{ scope.row.status }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="complete_tm" label="下发完成时间" align="center"></el-table-column> -->

            <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                    <div style="display:flex;justify-content: center;" class="event">
                        <el-button @click="consent(scope.row)" type="text" size="small"
                            v-if="queryList.flow_job_type != 4">同意</el-button>
                        <el-button @click="refusal(scope.row)" type="text" size="small" style="color: var(--background-color);"
                            v-if="queryList.flow_job_type != 4">驳回</el-button>
                        <el-button @click="handleEdit(scope.row)" type="text" size="small">查看</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!--      分页-->
        <div class="block">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage3" :page-size="queryList.page_size"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum" :page-sizes="[10, 20, 50, 100]"
                background></el-pagination>
        </div>
        <viewLogInfo :show="show" :detailsData="detailsData" :type="operationType" @close="close"></viewLogInfo>

    </div>
</template>
  
<script>
import { get_aduit_data } from "@/api/audit/audit"

import { filterPublishRole } from "@/utils/filterPublishRole.js"
import { aduit_consent_refusal } from "@/api/audit/audit.js";
import viewLogInfo from '@/views/systemCenter/component/viewLogInfo.vue';


export default {
    name: "TimeFrame",
    data() {
        return {
            //  表格
            tableData: [],
            //  市场运营
            option1: [
                {
                    value: 1,
                    label: "指定门店投放"
                },
                {
                    value: 2,
                    label: "dmb投放"
                },
                {
                    value: 3,
                    label: "普通投放"
                },
                {
                    value: 4,
                    label: "联屏投放"
                }
            ],
            //  表格区域高度
            autoHeight: {
                height: "",
                heightNum: ""
            },
            // 获取表单条件
            total: "",
            loading: true,
            queryList: {
                classModel: "AuditPubflow",
                platform: "",
                page: 0,
                size: 10,
                ref_ct: 35,
                flow_job_type: 1
            },
            show: false,
            detailsData: null,
            auditTypeList: [
                {
                    value: 1,
                    label: "待审核"
                },
                {
                    value: 4,
                    label: "历史审核"
                }
            ]
        };
    },
    components: {
        viewLogInfo
    },
    methods: {

        handleEdit(row) {
            this.show = true;
            console.log(row, 'row');
            this.detailsData = row.obj_info.info;
        },
        close() {
            this.show = false;
        },
        // 获取历史投放
        getHistoricalData() {
            console.log(this.queryList);
            const params = this.queryList;
            get_aduit_data(params).then(res => {
                console.log(res, 'res');
                this.totalNum = res.data[0].totalElements;
                this.tableData = res.data[0].content;
                this.total = res.data[0].totalElements;
                this.loading = false;
                sessionStorage.removeItem("historySearchFrom");
                console.log(this.tableData);
            });
        },

        //  列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 200 + "px";
            this.autoHeight.heightNum = windowHeight - 160;
        },
        handleSizeChange(val) {
            this.loading = true;
            this.queryList.page_size = val;
            this.getHistoricalData();
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.queryList.page_num = val - 1;
            this.getHistoricalData();
        },
        // 删除
        handleDelete(item) {
            check_for_delete_btpub({ btpub_id: item.btpub_id }).then(res => {
                console.log(res, "res");
                if (res.rst == "ok") {
                    this.$confirm("此操作将删除此专辑, 是否继续?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                        .then(() => {
                            delete_btpub({ btpub_id: item.btpub_id }).then(res => {
                                if (res.rst == "ok") {
                                    this.loading = true;
                                    this.$message({
                                        type: "success",
                                        message: "删除成功!"
                                    });
                                    this.getHistoricalData();
                                } else {
                                    this.$message.error("删除失败");
                                }
                            });
                        })
                        .catch(() => {
                            this.$message({
                                type: "info",
                                message: "已取消删除"
                            });
                        });
                } else {
                    this.$message.error("该专辑不可删除");
                }
            });
        },
        search() {
            this.queryList.page_num = 0;
            this.loading = true;
            this.getHistoricalData();
        },
        // 同意
        consent(photo) {
            console.log(photo);
            let params = {
                job_act: "approve",
                job_ids: [photo.id]
            };
            aduit_consent_refusal(params).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("审核同意成功");
                    this.getHistoricalData();
                } else {
                    this.$message.error("审核同意失败");
                    this.getHistoricalData();
                }
            });
        },
        // 驳回
        refusal(photo) {
            let params = {
                job_act: "deny",
                job_ids: [photo.id]
            };
            aduit_consent_refusal(params).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("审核驳回成功");
                    this.getDataLsit();
                } else {
                    this.$message.error("审核驳回失败");
                }
            });
        },
    },
    created() {
        window.addEventListener("resize", this.getHeight);
        this.getHeight();
        if (sessionStorage.getItem("historySearchFrom")) {
            let from = JSON.parse(sessionStorage.getItem("historySearchFrom"));
            this.queryList = from;
            console.log(this.queryList);
            this.currentPage3 = this.queryList.page_num + 1;
            this.search();
        } else {
            this.getHistoricalData();
        }
        console.log(filterPublishRole(['admin']), 'roles');

    },
    destroyed() {
        window.removeEventListener("resize", this.getHeight);
    }
};
</script>
  
<style scoped lang='scss'>
.deploy {
    padding: 0 20px 0 13px;
    width: 100%;
}

.top {
    display: flex;
    height: 40px;
    align-items: center;
    padding: 27px 0 30px 0;
    margin-bottom: 15px;
}

.top>div {
    margin-right: 8px;
}

.top>.queryTime {
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    text-align: left;
}

.top>.queryTime {
    display: flex;
    align-items: center;
}

.top>.queryTime>span {
    width: 62px;
}

::v-deep .el-select {
    width: 150px;
}

/*日历*/
.el-input__inner {
    width: 243px;
    height: 32px;
    margin-right: 16px;
}

::v-deep .el-range-separator {
    line-height: 23px;
}

::v-deep .el-range__icon {
    line-height: 17px !important;
}

::v-deep .el-date-editor .el-range-separator {
    width: 6%;
    padding: 0;
}

.top>button {
    width: 88px;
    height: 32px;
    line-height: 9px;
}

/*表格*/
::v-deep .has-gutter {
    height: 42px;
    color: rgba(80, 80, 80, 1);
    background-color: var(--text-color-light);
    font-size: 14px;
    text-align: center;
}

::v-deep .el-table__body {
    table-layout: auto;
}

/*分页*/
.el-pagination {
    height: 0.32rem;
    margin-top: 35px;
    text-align: right;
}

.look {
    cursor: pointer;
    color: rgba(42, 130, 228, 1);
    width: 60px;
    background-color: rgba(227, 241, 255, 1);
    height: 26px;
    border-radius: 3px;
    line-height: 26px;
    text-align: center;
    position: absolute;
    right: 4%;
    top: 50%;
}

.tou {
    margin-bottom: 10px;
}

.my-popover {
    padding: 20px;
}

.progress {
    position: relative;
    padding: 10px;
}

::v-deep .el-progress-bar {
    width: 75% !important;
}

.off_line {
    color: var(--text-color);
}

.on_line {
    color: rgba(23, 159, 78, 1);
}

.error {
    color: rgba(56, 56, 56, 1);
}

.event {
    flex-wrap: wrap;
}

.event button {
    width: 50%;
    margin-left: 0 !important;
}
</style>