<template>
    <div class="setType">
        <div class="steps">
            <el-steps :active="active" simple finish-status="success">
                <el-step title="设置类型"></el-step>
                <el-step title="发布设置"></el-step>
            </el-steps>
        </div>

        <div v-if="active == 0">
            <SetIsuueType @changeActive="changeActive" :nextActice="active" @next="next"></SetIsuueType>
        </div>
        <div v-else-if="active == 1">
            <pushlishingSetting v-if="active == 1" ref="pushlishingSetting" @SearchState="SearchState">
            </pushlishingSetting>
        </div>
        <div>

        </div>
        <div class="bottom">
            <!-- <div v-show="isEditDMB == false">
                <el-button @click="prev" v-show="active == 1"
                    style="background-color:rgba(201, 227, 255, 0.39);color:rgba(121, 171, 227, 1)">返回上一步</el-button>
            </div> -->
            <el-button v-show="active == 1" @click="publish" :disabled="PushliState">保存</el-button>
        </div>
    </div>
</template>
  
<script>
import ScreenContent from "../deploy/screenContent";
import SetTypePlayer from "../deployCom/setTypePlayer.vue";
import FloatEle from "../../components/DeployCom/FloatEle.vue";
import pushlishingSetting from "./pushing.vue";
import SetIsuueType from "../deploy/components/SetIsuueType.vue";

import { get_shop_data } from "@/api/issue/issue"
import { create_btpub } from "@/api/contentdeploy/contentdeploy"
import { datas_filter_cond } from "@/api/commonInterface";
import { publish_area, publish_launch } from "@/api/contentdeploy/contentdeploy"

export default {
    name: "setType",
    components: {
        SetTypePlayer,
        ScreenContent,
        FloatEle,
        pushlishingSetting,
        SetIsuueType
    },
    data() {
        return {
            active: 0,
            leftActive: 0,
            componentActive: 1,
            isNow: false,
            screenDict: {},
            playerDict: { "轮播": 1, "独占": 2 },
            playMode: { "按全天": "full_day", "按星期": "week_range", "按指定时段": "single_use_range" },
            batchinfo: {},
            dataFilters: [],
            publishData: {
                btplan_id: "",
                sel_info: {
                    opsmarkets: [], // 营运市场
                    storetypes: [], // 店铺类型
                    shop_tags: [], // 店铺标签
                    shop_tags_rela: [], // 店铺标签关系
                }
            },
            requestPublish: {
                btpub_id: "",
                action: "pub"
            },
            screen_type: "",
            v_or_h: "",
            dmb_spec: "",
            daypartgroup: "",
            submitState: false,
            // 发布状态
            PushliState: true,
            componentActiveNo: "",
            DmbRequestState: false,
            saveParams: null,
            isEditDMB: false,
            dmbRef_id: ""
        };
    },
    watch: {
        active(newValue, oldValue) {
            this.active = newValue;
            if (newValue != 2) {
                this.isNow = true;
            }
        },
    },
    created() {
        this.getSelectDataList()
        this.active = localStorage.getItem("active") ? localStorage.getItem("active") : 0;
        this.componentActiveNo = sessionStorage.getItem("componentActiveNo") ? sessionStorage.getItem("componentActiveNo") : 1;
    },
    methods: {
        next(params) {
            if (!sessionStorage.getItem("btpub_id")) {
                console.log(params, 'ppparams');
                this.create_btpub(params)
                // this.saveParams = params
            } else {
                console.log(this.active, "activeactiveactiveactive");
                console.log(this.componentActiveNo);
                this.active++;
                localStorage.setItem("active", this.active)
            }
        },

        getSelectDataList() {
            const params = {
                classModel: "ContentPub", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
            };
            datas_filter_cond(params).then((res) => {
                this.$store.commit("changeDataFilters", res["data"][0])
            });
        },
        changeDate(oldDate) {
            var dateee = new Date(oldDate).toJSON();//这个ie不兼容，会返回1970年的时间
            var date = new Date(+new Date(dateee) + 8 * 3600 * 1000)
                .toISOString()
                .replace(/T/g, " ")
                .replace(/\.[\d]{3}Z/, "");
            return date;
        },

        create_btpub(selectinfo) {
            this.submitState = false;
            console.log(selectinfo, "selectinfo");
            const playerState = selectinfo["playerState"] == "轮播" ? 1 : 2;
            let params = {
                "name": selectinfo["launchName"],                //（string）optional 发布计划名称
                "platform": 3,                 //（int）required 发布平台 2: dmb_plat; 3: general_plat; 4: vs_plat
                "func_type": 2,                //（int）required 计划类型 1: 投放; 2: 策略
                "start_time": this.changeDate(selectinfo["timging"][0]) == '1970-01-01 08:00:00' ? '' : this.changeDate(selectinfo["timging"][0]),      //（string）required, 开始时间
                "end_time": this.changeDate(selectinfo["timging"][1]) == '1970-01-01 08:00:00' ? '' : this.changeDate(selectinfo["timging"][1]),       //（string）required, 结束时间
                "play_style": playerState,          //（int）required 播放类型 1: 轮播  2: 独占
                "play_info": {                  // (dict) required, 具体看play_info说明

                }
            }
            console.log(this.componentActive,'componentActive');
            switch (this.componentActive) {
                case 1:
                    params.play_info.daypartgroup = selectinfo['daypartgroup'][0];
                    this.daypartgroup = params.play_info.daypartgroup;
                    console.log("selectinfo['daypartgroup'][0]", selectinfo['daypartgroup'][0]);
                    localStorage.setItem("daypartgroup", selectinfo['daypartgroup'][0] != undefined ? selectinfo['daypartgroup'][0] : '')
                    this.dmb_spec = selectinfo["dmb_spec"];
                    params.dmb_spec = selectinfo["dmb_spec"]; // (string) required dmb规格的时候
                    break;
                case 2:
                    const screen_type = selectinfo['screen_type']
                    this.screen_type = screen_type;
                    this.v_or_h = selectinfo['v_or_h'];
                    console.log(this.v_or_h, " this.v_or_h");
                    params.play_info.v_or_h = selectinfo['v_or_h']
                    params.play_info.play_mode = this.playMode[selectinfo["detaliRadio"]]
                    switch (selectinfo["detaliRadio"]) {
                        case "按全天":
                            break;
                        case "按星期":
                            params.play_info.week_day = []
                            selectinfo["weekCheckout"].forEach(item => {
                                params.play_info.week_day.push(Number(item))
                            })
                            params.play_info.time_ranges = [`${selectinfo["time_ranges"][0]}~${selectinfo["time_ranges"][1]}`]
                            break;
                        case "按指定时段":
                            console.log(selectinfo["genearlTimeing"]);
                            params.play_info.time_ranges = [`${dayjs(selectinfo["genearlTimeing"][0]).format('YYYY-MM-DD HH:mm:ss')}~${dayjs(selectinfo["genearlTimeing"][1]).format('YYYY-MM-DD HH:mm:ss')}`]
                            // selectinfo["genearlTimeing"].forEach(item => {
                            //   params.play_info.time_ranges.push(new Date().toLocaleDateString().replaceAll("/", "-") + " " + item);
                            // })
                            console.log(params.play_info.time_ranges);
                        default:
                            break;
                    }
                    params.usage_type = selectinfo['screen_type'];   //（string）required 屏幕类型
                    break;
                case 3:
                    params.vs_spec = selectinfo['vs_spec'];
                    this.vs_spec = selectinfo["vs_spec"];
                    console.log("selectinfo", selectinfo);
                    sessionStorage.removeItem("vs_spec")
                    sessionStorage.setItem("vs_spec", this.vs_spec)
                    params.play_info.play_mode = this.playMode[selectinfo["detaliRadio"]]
                    params.play_info.waitting_time = selectinfo['waitting_time']
                    console.log(selectinfo, "selectinfo");
                    switch (selectinfo["detaliRadio"]) {
                        case "按全天":
                            break;
                        case "按星期":
                            params.play_info.week_range = []
                            selectinfo["weekCheckout"].forEach(item => {
                                switch (item) {
                                    case '日':
                                        item = 1;
                                        break;
                                    case '一':
                                        item = 2;
                                        break;
                                    case '二':
                                        item = 3;
                                        break;
                                    case '三':
                                        item = 4;
                                        break;
                                    case '四':
                                        item = 5;
                                        break;
                                    case '五':
                                        item = 6;
                                        break;
                                    case '六':
                                        item = 7;
                                        break;

                                    default:
                                        break;
                                }
                                params.play_info.week_range.push(`${item}|${selectinfo["time_ranges"]}`.replace(",", "~"))
                            })
                            params.play_info.week_range.forEach(item => {
                                if (item[2] == " ") {
                                    this.submitState = true
                                }
                            })
                            break;
                        case "按指定时段":
                            console.log(selectinfo["connectTimeing"]);
                            params.play_info.single_use_range = []
                            params.play_info.single_use_range = dayjs(selectinfo["connectTimeing"][0]).format('YYYY-MM-DD HH:mm:ss') + "~" + dayjs(selectinfo["connectTimeing"][1]).format('YYYY-MM-DD HH:mm:ss')
                            // selectinfo["connectTimeing"].forEach(item => {
                            //   params.play_info.single_use_range.push(new Date().toLocaleDateString().replaceAll("/", "-") + " " + item);
                            // })
                            console.log(params.play_info.single_use_range);
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }

            switch (this.componentActive) {
                case 1:
                    params['platform'] = 2; //  dmb
                    Reflect.deleteProperty(params.play_info, 'v_or_h')
                    Reflect.deleteProperty(params.play_info, 'play_mode')
                    Reflect.deleteProperty(params.play_info, 'waitting_time')
                    Reflect.deleteProperty(params, 'usage_type')
                    break;
                case 2:
                    params['platform'] = 3; // 普通
                    console.log(params);
                    Reflect.deleteProperty(params.play_info, 'daypartgroup')
                    Reflect.deleteProperty(params.play_info, 'waitting_time')
                    Reflect.deleteProperty(params, 'dmb_spec')
                    break;
                case 3:
                    params['platform'] = 4; // 联屏
                    break;
                default:
                    break;
            }

            console.log(params);

            this.submitStat = false;

            for (const key in params) {
                if (Object.hasOwnProperty.call(params, key)) {
                    const element = params[key];
                    console.log(element, 'element');
                    if (element === "") {
                        this.submitState = true;
                    }
                    if (params.play_info) {
                        for (const key1 in params.play_info) {
                            const element1 = params.play_info[key1];
                            console.log(element1, 'element1');
                            if (element1 === undefined || element1 === '') {
                                this.submitState = true
                            }
                        }
                    }
                }
            }
            console.log("submitState", this.submitState);
            if (this.submitState != true) {
                create_btpub(params).then(res => {
                    console.log("res", res);
                    if (res.rst == 'ok') {
                        this.$store.commit("changeDeployInfo", res["data"][0])
                        this.$message.success('创建成功')
                        this.batchinfo = this.$store.state.deployCfInfo;
                        sessionStorage.removeItem("btpub_id")
                        sessionStorage.removeItem("ref_id")
                        sessionStorage.removeItem("btplan_id")
                        sessionStorage.setItem("btpub_id", res["data"][0].btpub_id)
                        sessionStorage.setItem("btplan_id", res["data"][0].btplan_id)
                        sessionStorage.setItem("ref_id", res["data"][0].ref_id)
                        this.active++;
                        localStorage.setItem("active", this.active)
                        this.isNow = true;
                    } else {
                        this.$message.error(res['error_msg']);
                    }
                }).then(() => {
                    params["componentActive"] = this.componentActive
                    //  this.$store.commit("changeDeploySetDetail", params)
                })
            } else {
                this.$message.warning("请选择内容")
            }
            console.log("params", params);

        },
        // 左侧点击
        changeActive(value) {
            this.componentActive = value;
            sessionStorage.setItem("componentActiveNo", this.componentActive)
            this.componentActiveNo = sessionStorage.getItem("componentActiveNo")
            sessionStorage.removeItem("btpub_id")
        },
        prev() {
            if (this.active == 0) {
                return
            }
            this.active--;
            localStorage.setItem("active", this.active)
        },
        publish() {
            this.publishData.btplan_id = this.$store.state.deployCfInfo.btplan_id;
            this.requestPublish.btpub_id = this.$store.state.deployCfInfo.btpub_id;
            this.publishData.sel_info.opsmarkets = this.$refs.pushlishingSetting.queryList.marketname;
            this.publishData.sel_info.storetypes = this.$refs.pushlishingSetting.queryList.shopname;
            this.publishData.sel_info.exclude_shops = this.$refs.pushlishingSetting.noSelectShops;
            publish_area(this.publishData).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("发布策略成功")
                    this.$router.push("/deploy/quickDeploy")
                } else {
                    this.$message.warning(res.error_msg)
                }
            })
        },
        // 获取经营时段
        getDayPart() {

        },
        SearchState(shopsList) {
            console.log(shopsList);
            if (shopsList != 0) {
                this.PushliState = false;
            } else {
                this.PushliState = true
            }
        },
        toPreview(val) {
            console.log(val);
            this.dmbRef_id = val[0]["ref_id"]
            this.isEditDMB = true;

        }
    },
    destroyed() {
        console.log("destroyed");
        localStorage.setItem("active", 0)
        sessionStorage.setItem("componentActiveNo", 0)
        sessionStorage.removeItem("btpub_id")
        sessionStorage.removeItem("btplan_id")
        sessionStorage.removeItem("ref_id")
    }

};
</script>
  
<style lang="scss" scoped>
.myset {
    height: 100%;
}
::v-deep .el-step__title.is-process{
  color:#fff !important;
}


.setType {
    padding: 10px 15px 0 23px;
    /*background-color: #f8f7f7;*/
    width: 100%;
    position: relative;
    height: 100%;

    .steps {
        width: 100%;
        display: flex;
        justify-content: center;
        padding-top: 20px;

      
    .el-steps {
      width: 100%;
      padding-left: 10%;
      padding-right: 10%;
      background-color: var(--text-color-light) !important;
      color: #fff;
    }

    ::v-deep .el-step__title.is-wait {
      color: #fff !important;
    }
    ::v-deep .el-steps i{
      color: #fff !important;
    }
    }

    .bottom {
        width: 100%;
        text-align: center;
        height: 50px;
        margin-top: 20px;
        display: flex;
        justify-content: center;

        button {
            width: 181px;
            height: 48px;
            color: white;
            font-weight: bold;
            background-color: rgba(108, 178, 255, 1);
            border-radius: 10px;
        }
    }
}
</style>