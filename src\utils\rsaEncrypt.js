import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGS/SiuRiGndbZ1bayPhSdZqtXtlVw1Cm+DzFU0E8JZqbr88YLyTVD0Etg+H4jSvjJh5yROUUOizAdjFFsH0MpScUSV3/N396OvyWKezdTs+rT8XyCK0mS0PTl2jwYG9X4/DZ64V09j6FKqHumAoPT0AIbcOL4XXmHkjGxl0QiwwIDAQAB'
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对需要加密的数据进行加密
}

