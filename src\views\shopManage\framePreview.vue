<template>
  <div class="frame_preview">
    <!-- 搜索框 -->
    <!-- <div class="preview_search flex">
            <el-input placeholder="门店编号" size="small" prefix-icon="el-icon-search" v-model="shopId" style="width:186px;margin-right:21px" @keyup.native.enter="handleIdSearch"></el-input>
            <div class="search_button" @click="handleIdSearch">搜索</div>
    </div>-->

    <!-- 内容区 -->
    <div class="frame_preview_content">
      <!-- 时段计划筛选 -->
      <div class="detailed_search flex">
        <div class="shop_select flex" style="align-items: center;">
          <!-- <img src="../../assets/img/shop_icon.png" alt="" style="width:24px;height:24px;margin-right:13px"> -->
          <div>
            <span>门店编号:</span>
            <!-- <el-select v-model="queryList.shopName" placeholder="请选择门店类型" size="small" style="width:190px;margin-right:21px" @change="shopSelected">
                            <el-option  v-for="item in shopSelectList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>-->
            <el-autocomplete
              v-model="autoSelect"
              :fetch-suggestions="querySearchAsync"
              :trigger-on-focus="false"
              placeholder="门店编号"
              style="width:430px;margin-right:21px"
              @select="handleSelect"
            ></el-autocomplete>
          </div>
          <div>
            <span>屏幕类型：</span>
            <el-select
              v-model="queryList.screenType"
              placeholder="请选择屏幕类型"
              size="small"
              style="width:190px;margin-right:21px"
              @change="shopSelected"
            >
              <el-option
                v-for="item in screenTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
          <!-- <div class="search_button" @click="handleSearch">预览</div> -->
        </div>
        <!-- <div class="day_or_month flex">
                    暂时隐藏功能
                    <div class="tabs" @click="changeTabs(0,$event)" :class="tabsIndex==0?'tabs_select':''">今天</div>
                    <div class="tabs" @click="changeTabs(1,$event)" :class="tabsIndex==1?'tabs_select':''">本月</div>
                    <div class="tabs_line"></div>
        </div>-->
        <div class="time_search flex">
          <div class="day_or_month flex" @click="searchToday">
            <!-- <div class="tabs" @click="changeTabs(0,$event)" :class="tabsIndex==0?'tabs_select':''" >今天</div> -->
            <div class="tabs" :class="tabsIndex == 0 ? 'tabs_select' : ''">今天</div>
            <div class="tabs_line"></div>
          </div>
          <el-date-picker
            v-show="tabsIndex == 0"
            v-model="queryList.nowDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            size="small"
            style="width:219px;margin-right:20px"
          ></el-date-picker>
          <!-- 暂时隐藏功能 -->
          <!-- <el-date-picker v-show="tabsIndex==1" v-model="queryList.monthDate" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width:243px;margin-right:8px"></el-date-picker> -->
          <div class="search_button" style="width:80px" @click="handleSearch">搜索</div>
        </div>
      </div>
      <!-- 主店 -->
      <div
        class="table_wrap"
        :style="{ height: autoHeightShop.height }"
        v-show="tabsIndex == 0"
        v-loading="loadingDay"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-text="拼命加载中,请稍等"
        element-loading-spinner="el-icon-loading"
      >
        <div class="shop_table" style="overflow:auto">
          <div class="empty" v-show="!hasData">
            <img src="../../assets/img/empty.png" alt style="width:110px" />
            <span>暂无内容</span>
          </div>
          <table v-show="hasData">
            <thead>
              <tr>
                <td>时段</td>
                <!-- <td v-for="item in contentScreenInfon.screenInfo" :key="item.screen_id" :colspan="item.all_displayinfo.length">
                                    <span :class="dis.display.v_or_h == 'horizontal'?'screen_h':'screen_v'" class="multiple_num" v-for="dis in item.all_displayinfo" :key="dis.screen_id">{{dis.displaynum}}</span>
                                    <span class="mg-left-10">ID：{{item.screen_id}}</span>
                </td>-->
                <td v-for="item in contentScreenInfon.screenInfo" :key="item.screen_id">
                  <span
                    :class="item.display_info.v_or_h == 'horizontal' ? 'screen_h' : 'screen_v'"
                    class="multiple_num"
                  >{{ item.displaynum }}</span>
                  <span class="mg-left-10">ID：{{ item.screen_id }}</span>
                </td>
              </tr>
            </thead>
            <!-- <tr>
                            <td>时段</td>
                            <td v-for="item in contentScreenInfon.screenInfo" :key="item.screen_id" :colspan="item.all_displayinfo.length">
                                <span class="multiple_num scrren_h" v-for="dis in item.all_displayinfo" :key="dis.screen_id">{{dis.displaynum}}</span>
                                <span class="mg-left-20">ID：{{item.screen_id}}</span>
                            </td>
            </tr>-->
            <tbody>
              <tr v-for="item in lists" :key="item.name">
                <td>
                  <p style="font-size:13px;margin-bottom:5px">{{ item.name }}</p>
                  <p style="font-size:13px">{{ item.time }}</p>
                </td>
                <td
                  v-for="(items, index) in item.screen"
                  :key="index"
                  :style="getWidth(item.screen.length)"
                >
                  <div v-if="items.length > 0" class="flex img_td" style="position: relative;">
                    <!-- {{ items }} -->
                    <img
                      :src="items[0] ? items[0]['thumb_list'][0].thumb_url : ''"
                      class="content_image"
                      :style="setImgStyle(items[0]['v_or_h'])"
                      :class="items[0]['v_or_h'] == 'horizontal' ? 'imgs_h' : 'imgs_v'"
                    />
                    <span
                      class="release_status release_suc"
                      v-if="items[0] && items[0]['process'] >= 0.6"
                      :style="tagPosition(items[0]['v_or_h'])"
                    >
                      下发成功
                      <i class="el-icon-check"></i>
                    </span>
                    <span
                      class="release_status release_err"
                      v-else-if="items[0] && items[0]['process'] < 0.6"
                      :style="tagPosition(items[0]['v_or_h'])"
                    >下发失败 !</span>
                    <span
                      class="release_status release_suc keep_time"
                      v-if="items[0] && items[0]['keep_time'] == 1"
                      :style="tagPosition(items[0]['v_or_h'])"
                    >时段独占</span>
                    <p class="con_nums">该屏幕有{{ items[0]['thumb_list'].length }}个内容</p>
                    <div class="td_mask">
                      <div class="img_preview" @click="handlePreview(items)">
                        <p>
                          <i class="el-icon-view"></i>
                        </p>
                        <p>预览</p>
                      </div>
                    </div>
                  </div>
                  <div
                    v-else
                    class="flex"
                    style="height:80px;position: relative;align-items:center;justify-content: center;"
                    v-show="items.length == 0"
                  >暂无</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="export_btn_wrap">
          <!-- 暂时隐藏功能 -->
          <!-- <div class="export_btn" @click="handleExport">批量导出内容</div> -->
        </div>
      </div>
      <!-- 月历 -->
      <!-- <div class="calendar" ref="calendar" :style="{height:autoHeightShop.height,overflow:loadingMonth?'hidden':'auto'}" v-show="tabsIndex == 1"  v-loading='loadingMonth' element-loading-background="rgba(0, 0, 0, 0.8)"  element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
                <el-calendar v-model="dates">
                   <template
                        slot="dateCell"
                        slot-scope="{date, data}">
                        <div style="height:100%;display:flex;flex-direction:column;align-items: center;justify-content: center;" :style="formartBg(data)" @click="showDateInfo(data)">
                            <div>{{formartDate(data)}}</div>
                            <div style="font-size:14px">没有内容计划</div>
                        </div>
                    </template>
</el-calendar>
      </div>-->
    </div>

    <!-- 无内容 -->
    <!-- <div class="empty" v-show="isEmptyShow" v-loading='loadingDay'>
            <img src="../../assets/img/empty.png" alt="" style="width:110px">
            <span>暂无内容</span>
    </div>-->

    <!-- 预览mask -->
    <previsualization
      :isPreviewMaskShow="isPreviewMaskShow"
      :PreviewSrc="PreviewSrc"
      :PreviewType="PreviewType"
      :carouselUrl="carouselUrl"
      @closePreviewMask="closePreviewMask"
    ></previsualization>
  </div>
</template>
<script>
import { load_shop_content, get_adm_datas } from "@/api/shopManage/shop";
import { get_screen_type_tags } from "@/api/system/label";
import previsualization from "@/components/communal/previsualization";

export default {
  components: {
    previsualization
  },
  data() {
    return {
      loading: true,
      loadingDay: false,
      loadingMonth: false,
      shopType: 1,
      isPreviewMaskShow: false,
      PreviewSrc: "",
      PreviewType: "carousel",
      carouselUrl: "thumb_url",
      contentScreenInfon: {
        screenInfo: []
      }, //主店内容
      dessertScreenInfo: {}, //甜品店内容
      lists: [],
      dateColor: [
        { date: "2022-04-13", color: "rgba(67, 207, 124, 0.35)" },
        { date: "2022-04-14", color: "rgba(67, 207, 124, 0.35)" },
        { date: "2022-04-18", color: "rgba(42, 130, 228, 0.257)" }
      ],
      isEmptyShow: true, //空状态
      hasData: false,
      tabsIndex: 0, //tabs 是今天还是本月,0今天 1本月
      shopId: "", //门店名称、编号
      queryList: {
        shopName: "",
        screenType: "",
        nowDate: "", //tabs为今天时的日期选择内容
        monthDate: "" //tabs为本月时的日期选择内容
      },
      shopSelectList: [], //门店类型
      screenTypeList: [], //屏幕类型
      autoHeightShop: {
        height: "",
        heightNum: ""
      },
      dailyDate: "",
      dates: new Date(),
      tagsPosL: 0,
      tagsPosT: 0,
      tagsVPosL: 0,
      tagsVPosV: 0,
      autoSelect: ""
    };
  },
  computed: {},
  watch: {
    tabsIndex(val) {
      let tagsLine = document.querySelector(".tabs_line");
      tagsLine.style.left = val * 88 + 30 + "px";
    }
  },
  created() {
    window.addEventListener("resize", this.getHeightShop);
    this.dailyDate = dayjs(Date.now()).format("YYYY-MM-DD");
    this.queryList.nowDate = dayjs(Date.now()).format("YYYY-MM-DD");
    this.getHeightShop();
    window.addEventListener("resize", this.resizePos);
    this.getScreenType();
    this.$nextTick(() => {
      let tagsLine = document.querySelector(".tabs_line");
      tagsLine.style.left = this.tabsIndex * 88 + 30 + "px";
    });
    // this.getShopContent()
  },
  mounted() {},
  methods: {
    setImgStyle(val) {
      let style;
      if (val == "horizontal") {
        style = "width:100%;height:auto;margin:0;object-fit:contain";
      } else {
        style = "width:auto;height:300px;margin:0 auto;object-fit:contain";
      }

      return style;
    },
    // 根据门店编号/名称搜索
    handleIdSearch() {
      if (this.loadingDay) {
        this.$message.warning("正在获取数据中,请稍后在进行操作");
        return;
      }
      if (!this.shopId) {
        this.$message.warning("请先输入门店编号");
        return;
      } else {
        this.loadingDay = true;
        Promise.all([this.getShopTypeList(), this.getScreenType()])
          .then(res => {
            this.getShopContent();
          })
          .catch(rej => {
            this.$message.error(rej);
            this.loadingDay = false;
          });
      }
    },
    // 获取门店类型
    getShopTypeList(queryString) {
      return new Promise((resolve, reject) => {
        this.shopSelectList = [];
        const params = {
          classModel: "GroupShop",
          sort: "", //非必要，排序规则，storecode,createdAtS
          page: 0, //起始页码,
          size: 1000, //每页数据量,
          blurry: queryString //店铺名
        };
        get_adm_datas(params).then(res => {
          if (res.rst == "ok") {
            console.log(res.data[0]);
            if (res.data[0].content.length == 0) {
              this.isEmptyShow = true;
              // this.hasData = false;
              this.loadingDay = false;
              this.$message.warning("暂无门店数据");
              this.queryList.shopName = "";
              resolve(res.data[0].content);
              return;
            }
            this.queryList.shopName = res.data[0].content[0].storecode;
            res.data[0].content.forEach(item => {
              this.shopSelectList.push({
                id: item.storecode,
                name: `${item.storecode}(${item.storename})`
              });
            });
            resolve(res.data[0].content);
          } else {
            this.$message.error(res.error_msg);
            reject(res.error_msg);
          }
        });
      });
    },
      // 获取屏幕类型
      getScreenType() {
      return new Promise((resolve, reject) => {
        this.screenTypeList = [];
        const params = {
          classModel: "ScreenUsageType",
          page: 0, //（int）页码
          size: 1000, //（int）每页显示的数量
        };
        get_screen_type_tags(params).then((res) => {
          if (res.rst == "ok") {
            res.data[0].content.forEach((item) => {
              if (item.value == "菜单屏") {
                this.queryList.screenType = item.value;
              }
              this.screenTypeList.push({
                id: item.value,
                name: item.value,
              });
            });
            console.log(this.screenTypeList, "this.screenTypeList");

            resolve();
          } else {
            // reject(res.error_msg)
            resolve();
          }
        });
      });
    },
    handleSearch() {
      console.log(this.queryList.shopName,'this.queryList.shopName');
      
      if (this.queryList.shopName.toString().trim() == "") {
        this.$message.warning("请输入正确的门店编号");
        return;
      }
      if (this.loadingDay) {
        this.$message.warning("正在获取数据中,请稍后在进行操作");
        return;
      }
      this.loadingDay = true;
      this.getShopContent();
    },
    searchToday() {
      if (this.loadingDay) {
        this.$message.warning("正在获取数据中,请稍后在进行操作");
        return;
      }
      let day = dayjs(Date.now()).format("YYYY-MM-DD");

      this.loadingDay = true;
      this.queryList.nowDate = day;
      this.getShopContent();
    },
    tagPosition(val) {
      if (val == "horizontal") {
        return `left:${this.tagsPosL}px;top:${this.tagsPosT}px`;
      } else {
        return `left:${this.tagsVPosL}px;top:${this.tagsVPosT}px`;
      }
    },
    querySearchAsync(queryString, cb) {
      console.log(queryString,'queryString');
      
      if (queryString.length < 3) {
        cb([]);
      } else {
        this.getShopTypeList(queryString).then(res => {
          // this.getShopTypeList(queryString.toLocaleUpperCase()).then(res => {
          if (res.length == 0) {
            cb([]);
          } else {
            res.forEach(item => {
              item.value = `${item.storecode}(${item.storename})`;
              item.id = item.storecode;
            });
            cb(res);
          }
        });
      }
    },
    handleSelect(val) {
      console.log(val.id, "val.id");
      // this.queryList.shopName = val.id.toLocaleUpperCase()
      this.queryList.shopName = val.id;
      console.log(this.queryList.shopName, "this.queryList.shopName ");
    },
    // 获取数据
    getShopContent() {
      let params = {
        // "shop_id": "89569",
        // "shop_id": this.shopId,
        store_code: this.queryList.shopName,
        date_str: this.queryList.nowDate,
        screen_type: this.queryList.screenType
      };
      load_shop_content(params)
        .then(res => {
          if (res.rst == "ok") {
            if (this.isEmptyShow) {
              this.isEmptyShow = false;
            }
            if (
              !res.data[0]["daypart_info"] ||
              res.data[0]["daypart_info"].length == 0
            ) {
              // this.isEmptyShow = true;
              this.hasData = false;
              this.$message.warning("暂无数据");
              this.loadingDay = false;
              return;
            }
            console.log(res["data"]);
            this.hasData = true;
            console.log(this.hasData, "this.hasData");
            this.contentScreenInfon.screenInfo = res.data[0].screen_info;
            // console.log(this.contentScreenInfon.screenInfo);
            this.formatContentData(res).then(val => {
              this.lists = val;
              this.loadingDay = false;
              // let oImg = document.getElementsByClassName('imgs_h')
              // console.log(oImg[0].offsetLeft,oImg[0].offsetTop,9999);
              // this.tagsPosL = oImg[0].offsetLeft;
              // this.tagsPosT = oImg[0].offsetTop;
              this.resizePos();
            });
          } else {
            this.$message.warning(res.error_msg);
            this.loadingDay = false;
          }
        })
        .catch(e => {
          this.loadingDay = false;
        });
    },
    resizePos() {
      this.$nextTick(() => {
        let oImg = document.getElementsByClassName("imgs_h");
        let oImgV = document.getElementsByClassName("imgs_v");
        if (oImg.length > 0) {
          if (oImg[0].offsetWidth == 0) {
            setTimeout(() => {
              this.resizePos();
            }, 800);
            return;
          }
          this.tagsPosL = oImg[0].offsetLeft;
          this.tagsPosT = oImg[0].offsetTop;
        }

        if (oImgV.length > 0) {
          if (oImgV[0].offsetWidth == 0) {
            setTimeout(() => {
              this.resizePos();
            }, 800);
            return;
          }
          this.tagsVPosL = oImgV[0].offsetLeft;
          this.tagsVPosT = oImgV[0].offsetTop;
        }
      });
    },
    // 格式化接口数据
    formatContentData(data) {
      return new Promise((resolve, reject) => {
        let screen_list = [];
        let screen_direct = {};
        let a = [];
        let b = {};
        data["data"][0]["screen_info"].sort((a, b) => {
          return a.displaynum - b.displaynum;
        });
        data["data"][0]["screen_info"].forEach(item => {
          if (item["displaynuminfo"].length > 1) {
            this.shopType = 0; //主店
          } else {
            // this.shopType = 1; //甜品店
          }
          // item["all_displayinfo"].forEach(item=>{

          //     screen_list.push({v_or_h:item.display.v_or_h,screen_id:item.screen_id})
          //     screen_direct[item.screen_id] = item.display.v_or_h
          //     // screen_list[item["displaynum"]-1] = item["screen_id"]

          // })
          screen_list.push({
            v_or_h: item.display_info.v_or_h,
            screen_id: item.screen_id
          });
          screen_direct[item.screen_id] = item.display_info.v_or_h;
        });
        let screen_display_list = [];
        let daypart_info = data["data"][0]["daypart_info"];
        let daypart_cf_info = data["data"][0]["daypart_cf_info"];

        daypart_info.forEach(item => {
          screen = [];
          item.stime =
            item.stime.split(":")[0] + ":" + item.stime.split(":")[1];
          item.etime =
            item.etime.split(":")[0] + ":" + item.etime.split(":")[1];
          // item.stime = dayjs(item.tiems).format('HH:mm')
          // item.etime = dayjs(item.eiems).format('HH:mm')
          screen_list.forEach(items => {
            let data_info = daypart_cf_info[String(items.screen_id)];
            let cf_info = [];
            if (data_info) {
              data_info.forEach(cf_item => {
                if (
                  cf_item["daypartname"] == item["daypartname"] &&
                  cf_item["cs_list"]
                ) {
                  let cs_list = cf_item["cs_list"];
                  let process = cf_item["cf_bar"]["process"];
                  let keep_time = cf_item["keep_time"];
                  cf_info.push({
                    keep_time: keep_time,
                    thumb_list: cs_list,
                    process: process,
                    v_or_h: screen_direct[items.screen_id]
                  });
                }
              });
              screen.push(cf_info);
            }
          });
          screen_display_list.push({
            name: item["daypartname"],
            time: `${item["stime"]}-${item["etime"]}`,
            screen
          });
        });

        resolve(screen_display_list);
        // console.log(this.shopType, screen_list, screen_display_list)
      });
    },
    // 定位下发状态标签位置
    positioningLabel() {
      let oImg = document.getElementsByClassName("content_image")[0];
    },
    // 时段计划搜索
    handleTimeSearch() {
      if (this.tabsIndex == 0) {
        // this.$message.success(`这里是今天的时段搜索,${this.queryList.nowDate}`)
        this.loadingDay = true;
        setTimeout(() => {
          this.loadingDay = false;
        }, 1000);
        this.getShopContent();
      } else {
        this.$message.success(
          `这里是本月的时段搜索,${this.queryList.monthDate}`
        );
        if (this.$refs.calendar.scrollTop > 0) {
          this.$refs.calendar.scrollTop = 0;
        }
        this.loadingMonth = true;
        setTimeout(() => {
          this.loadingMonth = false;
        }, 1000);
      }
    },
    // tabs切换,今日还是本月
    changeTabs(val, e) {
      // 暂时隐藏功能
      return;
      this.$message.success(`选中了的是${val}`);
      this.tabsIndex = val;

      //第一次进入页面，月历没有数据，加载loading状态
      // if(val==1 && this.a.length==0){
      //     this.loadingMonth = true
      // }
    },
    // 点击编辑
    handleEdit(items) {
      // this.$message.success(`编辑的是${items}`)
      this.$router.push("/shopManage/frameEdit");
      sessionStorage.setItem("activepath", "/shopManage/frameEdit");
    },
    // 点击某天
    showDateInfo(val) {
      console.log(val);
      this.queryList.nowDate = val.day;
      this.tabsIndex = 0;
    },
    // 格式化日历背景
    formartBg(val) {
      let color;
      this.dateColor.forEach(item => {
        if (item.date == val.day) {
          color = item.color;
        }
      });
      return "background-color:" + color;
    },
    // 格式化日历日期
    formartDate(date) {
      const day = parseInt(
        date.day
          .split("-")
          .slice(2)
          .join("")
      );
      return day;
    },
    // 批量导出
    handleExport() {
      this.$message.success("这里是批量导出");
    },
    getWidth(val) {
      // return `width:(calc(100% - 120px))/${val};max-width:(calc(100% - 120px))/${val};`
      let oTable = document.querySelector(".shop_table");
      let w;
      if (val < 4) {
        w = (oTable.offsetWidth - 120) / 4;
      } else {
        w = (oTable.offsetWidth - 120) / val;
      }
      let styleStr = `width:${w}px;max-width:${w}px`;
      return styleStr;
    },
    // 店铺高度自适应
    getHeightShop() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeightShop.height = windowHeight - 143 + "px";
      this.autoHeightShop.heightNum = windowHeight - 172;
    },
    // 预览
    handlePreview(cs_list) {
      let thumb_list = [];
      cs_list.forEach(item => {
        thumb_list = thumb_list.concat(item.thumb_list);
      });
      this.PreviewSrc = thumb_list;
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeightShop);
    window.removeEventListener("resize", this.resizePos);
  }
};
</script>

<style lang='scss' scoped>
* {
  box-sizing: border-box;
}

.frame_preview {
  width: 100%;
}

.preview_search {
  width: 100%;
  height: 58px;
  justify-content: flex-end;
  align-items: center;
  padding-right: 19px;
  border-bottom: 1px solid rgba(236, 235, 235, 1);
}

/* 搜索按钮 */
.search_button {
  width: 64px;
  height: 31px;
  top: 99px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  line-height: 31px;
  text-align: center;
  cursor: pointer;
  margin-top: -1px;
}

  // .search_button:hover {
  //   background-color: rgba(211, 57, 57, 0.8);
  // }

/* 时段计划筛选 */
.frame_preview_content {
  width: 100%;
  height: 64px;
  padding: 0 17px 0 25px;
}

.detailed_search {
  width: 100%;
  // height: 100%;
  height: 87px;
  align-items: center;
  font-size: 14px;
  /* border: 1px solid blue; */
}

.day_or_month {
  position: relative;
}

.day_or_month .tabs {
  width: 88px;
  text-align: center;
  cursor: pointer;
}

.day_or_month .tabs:hover {
  color: var(--text-color);
}

.day_or_month .tabs_line {
  position: absolute;
  width: 28px;
  height: 3px;
  background: var(--text-color);
  top: -24px;
  left: 30px;
  /* left: 120px; */
  transition: left 0.3s;
}

.tabs_select {
  color: var(--text-color);
}

.time_search {
  justify-content: flex-end;
  align-items: center;
}

/* 内容区 */
.table_wrap {
  width: 100%;
}

.multiple_num {
  display: inline-block;
  /* width: 32px;
        height: 21px; */
  box-sizing: border-box;
  text-align: center;
  font-size: 12px;
  border: var(--text-color-light);
  margin-right: -1px;
  color: #fff;
  background-color: var(--text-color-light)
}

.screen_h {
  display: inline-block;
  width: 32px;
  height: 21px;
  line-height: 21px;
}

.screen_v {
  display: inline-block;
  width: 18px;
  height: 25px;
  line-height: 25px;
}

.release_status {
  position: absolute;
  top: 0;
  left: 0;
  width: 90px;
  height: 23px;
  line-height: 23px;
  color: #fff;
  padding-right: 10px;
  font-size: 10px;
  border-radius: 0 20px 20px 0;
}

.release_suc {
  background: rgba(37, 195, 101, 1);
}

.release_err {
  background: rgba(166, 166, 166, 1);
}

.keep_time {
  left: 0px;
  display: block;
  float: left;
  top: 27px !important;
}

.shop_table {
  width: 100%;
  border-collapse: collapse;
  //批量导出功能加上后 改成-100px
  // height: calc(100% - 100px);
  height: calc(100% - 10px);
  /* overflow-y: auto; */
  min-height: 158px;
  // max-width: 1708px;
  flex-direction: column;
}

.table_header {
  width: 100%;
  margin-bottom: -1px;
}

.header_tr {
  height: 59px;
  line-height: 59px;
}

.tr_colspan {
  width: calc((100% - 135px) / 2);
  line-height: 59px;
  border: 1px solid rgba(236, 235, 235, 1);
  border-left: none;
}

.row_name {
  width: 135px;
  border: 1px solid rgba(236, 235, 235, 1);
}

.main_store_td {
  border: 1px solid rgba(236, 235, 235, 1);
}

.table_body {
  height: calc(100% - 59px);
  overflow-y: auto;
  border-bottom: 1px solid rgba(236, 235, 235, 1);
}

.table_cell_name {
  display: flex;
  flex-direction: column;
  width: 135px;
  /* height:156px; */
  border: 1px solid rgba(236, 235, 235, 1);
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: bold;
}

.table_cell {
  width: calc((100% - 135px) / 4);
  /* height:156px; */
  border: 1px solid rgba(236, 235, 235, 1);
  border-left: none;
  text-align: center;
  position: relative;
  padding: 5px;
  overflow: hidden;
}

.table_cell:hover .table_cell_hover {
  display: block;
}

.table_cell_hover {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(56, 56, 56, 0.52);
}

.edit {
  display: flex;
  flex-direction: column;
  position: absolute;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  left: 50%;
  top: 50%;
  background: rgba(0, 0, 0, 0.46);
  transform: translateX(-30px) translateY(-30px);
  border-radius: 50%;
  cursor: pointer;
  color: #fff;
  font-size: 14px;
}

img[src=""] {
  opacity: 0;
}

/* 月历 */
// .calendar{
// }
.export_btn_wrap {
  display: none;
  justify-content: flex-end;
  align-items: center;
  height: 95px;
  padding-right: 24px;
}

.export_btn {
  width: 132px;
  height: 30px;
  color: #fff;
  text-align: center;
  line-height: 30px;
  background-color: rgba(108, 178, 255, 1);
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.export_btn:hover {
  background-color: rgba(108, 178, 255, 0.8);
}

/*修改滚动条样式*/
.table_body::-webkit-scrollbar {
  width: 0px;
  /* display: none; */
  height: 3px;
}

.table_body::-webkit-scrollbar-track {
  background: rgba(239, 239, 239, 0.5);
  border-radius: 2px;
}

.table_body::-webkit-scrollbar-thumb {
  background: rgba(191, 191, 191, 0.7);
  border-radius: 2px;
}

.table_body::-webkit-scrollbar-thumb:hover {
  background: rgba(191, 191, 191, 1);
}

/* 无内容 */
.empty {
  /* position: relative; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100vh - 107px);
  /* min-height: 140px; */
}

.empty span {
  display: block;
  width: 110px;
  text-align: center;
  color: rgba(134, 134, 134, 1);
  font-size: 14px;
}

.shop_table {
  width: 100%;

  table {
    border-collapse: collapse;
    max-height: 100%;

    // width: 100%;
    // border: 1px solid red;
    thead {
      td {
        height: 56px;
        padding: 0;
      }

      td:nth-of-type(1) {
        font-weight: bold;
      }
    }

    tr {
      td:nth-of-type(1) {
        width: 120px !important;
        max-width: 120px !important;
        min-width: 120px !important;
        font-weight: bold;
        padding: 0;
      }

      td {
        overflow: hidden;
      }
    }

    td {
      text-align: center;
      border: 1px solid rgba(198, 198, 198, 1);
      padding: 3px;
      position: relative;
    }
  }

  .img_td {
    position: relative;
  }

  .img_td:hover .td_mask {
    display: flex;
  }

  .con_nums {
    background: rgba(22, 22, 22, 0.8);
    position: absolute;
    bottom: 0;
    left: 0;
    // top: 0;
    // right: 0;
    padding: 5px;
    font-size: 13px;
    color: #fff;
  }

  .td_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.335);
    color: #fff;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .img_preview {
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.564);
    border-radius: 50%;
    font-size: 11px;
  }
}

/* 预览mask */
.preview_mask {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  z-index: 9999;
  overflow-y: auto;
}

.preview_content {
  position: absolute;
  left: 50%;
  top: 50%;
  /* background: #fff; */
  width: 1920px;
  height: 1080px;
  transform: translate(-50%, -50%) scale(0.5);
}

.close_preview_mask {
  position: absolute;
  right: 40px;
  top: 40px;
  height: 40px;
  width: 40px;
  font-size: 18px;
  text-align: center;
  border-radius: 50%;
  line-height: 40px;
  cursor: pointer;
  color: #fff;
  background: #606266;
}

.close_preview_mask:hover {
  /* color: rgba(0, 0, 0, 0.6); */
  background: #6d6d6e;
}

.con_nums {
  background: rgba(22, 22, 22, 0.8);
  position: absolute;
  bottom: 0;
  left: 0;
  // top: 0;
  // right: 0;
  padding: 5px;
  font-size: 13px;
  color: #fff;
}
</style>
<style>
.frame_preview .el-calendar-day {
  padding: 0 !important;
}

.frame_preview .el-calendar__body thead {
  background: rgba(116, 169, 236, 0.44) !important;
}
</style>