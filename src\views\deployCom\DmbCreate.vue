<template>
    <div class='GeneralCreate' v-loading="loading">
        <div class="set_play_rule">
            <h3 class="content_title">播放时长规则设置</h3>
            <div class="select_wrap">
                <!-- {{ select_screen_optios }} -->
                <!-- {{ saveDelayInfo }} -->
                <div class="select_info" v-for="item in saveDelayInfo" style="margin-right: 10px;">
                    <div>
                        <span>屏幕{{ item.screen_index }}延迟播放时间</span>
                        <el-input v-model="item.first_tile_cuttime" style="width:50px;"
                            placeholder="请输入"></el-input>
                        <span>秒</span>
                    </div>
                </div>
                <div class="select_info">
                    <el-button type="primary" size="small" @click="savePlayRule">保存</el-button>
                </div>
            </div>
        </div>
        <div class="dmbEdit">
            <table class="table">
                <tr class="table_header" style="height:90px;line-height: 90px;text-align: center;">
                    <th>
                        {{ dmbName }}
                    </th>
                </tr>
                <tbody class="table_body">
                    <div class="body_table">
                        <tr class="body_header">
                            <!-- -->
                            <th class="sitting" style="display:flex;align-items: center;justify-content: center;">
                                餐段(点击经营时段复制) </th>
                            <th v-for="(item, index) in dmb_spec" class="screen" :key="index">
                                <span :class="item == 0 ? 'arcoss' : 'vision'"
                                    style="display: flex; align-items: center;"> <img src="@/assets/icons/screen.png"
                                        alt="">
                                    <span style="padding-left: 5px;font-size: 22px;color: #24b07d;">{{ index + 1
                                        }}</span> </span>
                            </th>
                        </tr>
                        <tr v-for="(item, index) in timeframe" :key="item.name">
                            <th class="time_class"
                                style="width:120px;display: flex;justify-content: center;align-items: center;"
                                @click="openCopyDialog(item.name)"> {{ item.name }}
                                <!-- @click="openCopyDialog(item.name)"  点击打开copy餐段  -->
                            </th>
                            <th v-for="(item1, index1) in item.addContentArray" class="screen_content" :key="index1">
                                <div class="newdmb" v-show="item1.imgUrl == ''">
                                    <div class="add" @click="openSelectImage(item, item1, index, index1)">
                                        <img src="@/assets/img/add.png" alt="">
                                        <span style="margin-top: 5px;">
                                            新增
                                        </span>
                                    </div>
                                    <div class="relevance" @click="relevanceClassIfy(item, item1, index, index1)">
                                        <img src="@/assets/img/relevance.png" alt="">
                                        <span style="margin-top: 5px;">
                                            关联
                                        </span>
                                    </div>
                                </div>

                                <!-- <div class="newdmb1" style="position: relative;" v-show="item1.imgUrl != ''">
                                    <img :src="item1.imgUrl"
                                        :class="item1.direction == 0 ? 'screen_arcoss' : 'screen_vision'" alt="">
                                    <div v-if="item1.active == false" class="activeState"> 未投放... </div>
                                    <div v-else-if="item1.active == true" class="activeState"> 投放中... </div>
                                    <div style="position:absolute;" class="activeState1" v-show="item.imgLength != 0">
                                        该屏幕有{{ item1.imgLength }}个内容</div>
                                    <div class="preview" @click="toPreview(item1)"> 预览 </div>
                                    <div @click.stop="deleteContent(item1)" class="delete">
                                        删除
                                    </div>
                                </div> -->
                                <div class="dmb_container" v-show="item1.imgUrl != ''">
                                    <div class="inherit_name"
                                        v-if="item1.inherit_from_name && item1.inherit_from_name != null">
                                        关联模板: {{ item1.inherit_from_name }}
                                    </div>

                                    <img v-if="item1.inherit_from_id && item1.inherit_from_id != null"
                                        src="@/assets/icons/triangle.png" alt="" class="triangle">
                                    <img :src="item1.imgUrl" alt="" class="dmb_img"
                                        :class="item1.direction == 0 ? 'screen_arcoss' : 'screen_vision'">
                                    <div class="event_dmb">
                                        <div @click.stop="deleteContent(item1)" class="delete1">
                                            <i class="el-icon-delete"></i> 删除
                                        </div>
                                        <div>
                                            <img src="../../assets/icons/fei.png" alt=""> 状态:{{ item1.active ? '投放' :
                                                '未投放' }}
                                        </div>
                                        <div>有{{ item1.imgLength }}个内容</div>
                                    </div>
                                    <div class="dmb_mask">
                                        <div class="dmb_mask_preview" @click.stop="toPreview(item1)">预览</div>
                                        <!-- <div class="dmb_mask_setting" @click.stop="settings(item1)">设置</div> -->
                                    </div>
                                </div>
                            </th>
                        </tr>
                    </div>
                </tbody>
            </table>
        </div>


        <el-dialog title="新增内容" :visible.sync="showAlertBox" width="61%" custom-class="tabsDatat" @close="closeBox"
            :before-close="showAlertBox">
            <div class='threeParts tag_set'>
                <!--        tab栏切换-->
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="图片" name="first">
                        <picture-resources style="padding-top: 10px;" :tagsList='tagsList' :delTagsList='delTagsList'
                            @checkedList='checkedList' @setImgPreview='setImgPreview' ref="picture"></picture-resources>
                    </el-tab-pane>
                    <el-tab-pane label="视频" name="second">
                        <video-resources @setVideoPreview='setVideoPreview' @checkedList='checkedList' ref="video">
                        </video-resources>
                    </el-tab-pane>
                    <el-tab-pane label="H5" name="third">
                        <h5-resources @setH5Preview='setH5Preview' @checkedList='checkedList1' ref="h5"></h5-resources>
                    </el-tab-pane>
                    <el-tab-pane label="模板" name="fourth">
                        <templateResources @checkedTemplate='checkedTemplate' :v_or_h="v_or_h" ref="template">
                        </templateResources>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class='btns dialog_btns'>
                <el-button type='primary' @click.stop='cancel' style="margin-right: 100px;">取消</el-button>
                <!-- <el-button type='primary' @click.stop='alertOk'>确定</el-button> -->
                <el-button type='primary' v-debounce.stop='alertOk'>确定</el-button>
            </div>
        </el-dialog>


        <!-- <el-dialog title="关联专辑" :visible.sync="relevanceDialog" width="80%" @close="closeRelevanceBox"
            :before-close="showAlertBox" style="padding-bottom: 20px !important;"> -->
        <el-dialog title="关联专辑" @close="closeRelevanceBox" :before-close="showAlertBox" :visible.sync="relevanceDialog"
            width="80%" class="relevanceDialog">
            <div class='threeParts tag_set'>
                <AlbumList :is_quoted="false" @relevan="relevan"></AlbumList>
            </div>
        </el-dialog>



        <el-dialog title="复制餐段内容" :visible.sync="dialogCopyStatus" width="30%">
            目标经营时段: <el-select v-model="copyDaypartName" placeholder="请选择">
                <el-option v-for="item in timeframe" :key="item.daypartname" :label="item.daypartname"
                    :value="item.daypartname" :disabled="timeDaypartName == item.daypartname">
                </el-option>
            </el-select>
            <div class='btns dialog_btns'>
                <el-button type='primary' @click.stop='cancelCopyDialog' style="margin-right: 100px;">取消</el-button>
                <!-- <el-button type='primary' @click.stop='alertOk'>确定</el-button> -->
                <el-button type='primary' @click='requestCopy'>确定</el-button>
            </div>
        </el-dialog>


        <!-- 预览mask -->
        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask' :v_h="v_h">
        </previsualization>
    </div>
</template>

<script>
// import { self_photo_list } from "@/api/files/pictureResources";
// import { get_video_list } from "@/api/files/videoResources";
import { get_btpub_detail } from "@/api/contentdeploy/contentdeploy"
import pictureResources from '@/components/ContentCenter/pictureResources'
import videoResources from '@/components/ContentCenter/videoResources'
import h5Resources from '@/components/ContentCenter/h5Resources'
import previsualization from '@/components/communal/previsualization'
import { get_daypart_data, del_btpub_content, create_group_classify, add_btpub_content, batch_create_draft, edit_group_classify_cs } from "@/api/contentdeploy/contentdeploy"
import { get_classify_detail } from "@/api/device/device"
import { copy_dmb_btpub_content } from "@/api/dmbcopy/dmbcopy"
import templateResources from '@/components/ContentCenter/templateResources'
import AlbumList from "@/views/cardManage/components/AlbumList.vue";
import {
    edit_btpubplan
} from "@/api/contentdeploy/contentdeploy";

export default {
    name: 'GeneralCreate',
    components: {
        pictureResources,
        videoResources,
        h5Resources,
        // multiScreenResources,
        // floatsResources,
        previsualization,
        templateResources,
        AlbumList
    },
    data() {
        return {
            //选择框
            picList: [{
                "img_url": "",
                "checked": 1
            }],
            checkIndex: -1,
            checkShow: false,
            showAlertBox: false,
            //  图片、视频、H5
            activeName: 'first',
            //  大图
            showBg: false,
            fileList: [],
            // 搜索框
            searchValue: "",
            imgList: [],
            VideoList: [],
            currentPage: 1, //页码
            totalNum: 0, //总数据数量
            pageSize: 10, //每页显示多少条
            tagsList: [],
            delTagsList: [],
            PreviewType: '',
            PreviewSrc: '',
            disabled: false,
            cs_list: [],
            pictureList: false,
            timeframe: [],
            daypartgroup: "",
            dmbContent: [],
            loading: true,
            btpub_id: null,
            paramsa: {},
            dmbName: "",
            dialogCopyStatus: false,
            copyDaypartName: "",
            timeDaypartName: '',
            v_h: '',
            btn_click: false,
            paly_rules: {
                complete_ani_screen: '',
                delay_screen: 1,
                delay_time: 0
            },
            select_screen_optios: [],
            relevanceDialog: false,
            addClassIfyInfo: {

            },
            ClassifyInfo: {

            },
            relevanInfo: {

            },
            relevanContent: {},
            saveDelayInfo: {}
        }
    },

    watch: {

    },
    props: {

    },
    created() {
        this.btpub_id = sessionStorage.getItem("btpub_id")
        this.get_btpub_detail_info({
            btpub_id: this.btpub_id,
            range: 'simple'
        });
    },
    mounted() {
        // this.getDayPart()
        // this.getDmbContent()
    },
    methods: {
        // 预览
        setImgPreview(val) {
            this.PreviewType = 'image'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val) {
            this.PreviewType = 'video'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        setH5Preview(val, html, v_h) {
            this.PreviewType = 'h5'
            this.PreviewSrc = val
            console.log(v_h, 'v_h');
            this.v_h = v_h;
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = '';
        },
        checkedList(imgList) {
            imgList.forEach(item => {
                item.thumb_url = item.thumb
            })
            console.log(imgList, "imgLists");
            this.imgList = imgList
            //   this.$emit("selectImage", imgUrl);
        },
        checkedList1(imgList) {
            imgList.forEach(item => {
                item.thumb_url = item.thumb_url
            })
            console.log(imgList, "imgLists");
            this.imgList = imgList
            //   this.$emit("selectImage", imgUrl);
        },
        checkedTemplate(templateListe) {
            console.log(templateListe, 'templateListe');
            this.imgList = templateListe
        },
        goBack() {
            this.$router.go(-1);
        },
        //新增
        add() {
            this.showAlertBox = true;
            setTimeout(() => {
                // this.$refs.picture.checkedList = []
                this.$refs.picture.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.picture.batchState = true;
                this.$refs.video.batchState = true;
            }, 100);
        },
        //  弹出框取消按钮
        addClassify() {
            create_group_classify(this.addClassIfyInfo).then(res => {
                if (res.rst == "ok") {
                    this.cf_id = res.data[0].g_cf_id;
                    this.showAlertBox = true;
                    const params1 = {
                        btpub_id: this.btpub_id,
                        ref_id: this.cf_id,
                        ref_source: "ds",
                        daypartname: this.timeframe[this.selectDayPart].name,
                        screen_index: this.ClassifyInfo.screen_index
                    }

                    add_btpub_content(params1).then(res => {
                        if (res.rst == "ok") {
                            this.loading = true;
                            this.timeframe = JSON.parse(JSON.stringify(this.timeframe))
                            console.log(this.imgList, 'this.imgList');
                            let h5List = [];
                            console.log(this.activeName, 'activeName');
                            this.imgList.forEach(item => {
                                if (this.activeName == 'third') {
                                    h5List.push(item.cs_id)
                                    h5List = Array.from(new Set(h5List))
                                }
                            })
                            if (this.activeName != 'third') {
                                if (this.activeName == 'fourth') {
                                    let content_list = []
                                    this.imgList.forEach(item => {
                                        content_list.push({
                                            tpl_id: item.tpl_id,
                                        })
                                    })
                                    let params = {
                                        g_cf_id: this.cf_id,
                                        g_group_id: Number(localStorage.getItem("group_id")),
                                        attrs: {
                                            content_list: content_list
                                        }
                                    }
                                    edit_group_classify_cs(params).then(ress => {
                                        if (ress.rst == "ok") {
                                            this.$message.closeAll()
                                            this.$message.success("添加内容成功");
                                            this.get_btpub_detail_info({
                                                btpub_id: this.btpub_id,
                                                range: 'simple'
                                            });
                                            this.getDmbContent()
                                            this.showAlertBox = false;
                                        } else {
                                            this.$message.warning(ress.error_msg);
                                            this.showAlertBox = false;
                                        }
                                    })
                                } else {
                                    this.imgList.forEach(item => {
                                        let paramsa = {}
                                        paramsa = {
                                            shop_group_id: Number(localStorage.getItem("group_id")),
                                            attr_list: [
                                                {
                                                    tpl_name: this.activeName == 'first' ?
                                                        this.v_or_h == 0 ? 'HD_welposterH_0002854' : 'HD_welposter_0002853'
                                                        : this.v_or_h == 0 ? 'HD_video_tplH_0007' : 'HD_video_tpl_0007',
                                                    file_id: item.file_id ? item.file_id : item.cs_id,
                                                    file_type: this.activeName == 'first' ? 'img' : 'video'
                                                }
                                            ]
                                        }
                                        batch_create_draft(paramsa).then(resa => {
                                            if (resa.rst == "ok") {
                                                console.log("resa", resa);
                                                const pas = {
                                                    g_group_id: Number(localStorage.getItem("group_id")),
                                                    g_cf_id: this.cf_id,
                                                    attrs: {
                                                        add_cs_list: resa.data[0].cs_list
                                                    }
                                                }
                                                edit_group_classify_cs(pas).then(ress => {
                                                    if (ress.rst == "ok") {
                                                        this.$message.closeAll()
                                                        this.$message.success("添加内容成功");
                                                        this.get_btpub_detail_info({
                                                            btpub_id: this.btpub_id,
                                                            range: 'simple'
                                                        });
                                                        this.getDmbContent()
                                                        this.showAlertBox = false;
                                                    } else {
                                                        this.$message.warning(ress.error_msg);
                                                        this.showAlertBox = false;
                                                    }
                                                })
                                            }

                                        })
                                    })
                                }
                            } else {
                                const pas = {
                                    g_group_id: Number(localStorage.getItem("group_id")),
                                    g_cf_id: this.cf_id,
                                    attrs: {
                                        add_cs_list: h5List
                                    }
                                }
                                edit_group_classify_cs(pas).then(ress => {
                                    if (ress.rst == "ok") {
                                        this.$message.closeAll()
                                        this.$message.success("添加内容成功");
                                        this.get_btpub_detail_info({
                                            btpub_id: this.btpub_id,
                                            range: 'simple'
                                        });
                                        this.getDmbContent()
                                        this.showAlertBox = false;
                                    } else {
                                        this.$message.warning(ress.error_msg);
                                        this.showAlertBox = false;
                                    }
                                })
                            }
                        }
                    })

                } else {
                    this.$message.warning("创建专辑失败")
                }
            })
        },
        // 确认选择图片
        alertOk() {
            this.addClassify()

        },
        cancel() {
            this.get_btpub_detail_info({ btpub_id: this.btpub_id, range: 'simple' });
            this.getDmbContent()
            this.showAlertBox = false;
        },
        selectScreen(item1) {
            console.log(item1, 'item1');
            this.select_screen_optios.forEach(item => {
                if (item.s_number == item1.screen_index) {
                    this.$set(item, 'disabled', true)
                }
            })
            console.log(this.select_screen_optios, 'select_screen_optios')
            this.clearSelectScreen()
        },
        clearSelectScreen() {
            console.log(this.saveDelayInfo, 'saveDelayInfo')
            let select_num = this.saveDelayInfo.map(item => {
                return item.screen_index
            }).filter(item => {
                return item
            })

            this.select_screen_optios.forEach(item => {
                if (select_num.indexOf(item.s_number) == -1) {
                    this.$set(item, 'disabled', false)
                }
            })

        },
        clearScreen(item1) {
            this.select_screen_optios.forEach(item => {
                if (item.s_number == item1.screen_index) {
                    this.$set(item, 'disabled', false)
                }
            })
        },
        closeBox() {
            this.get_btpub_detail_info({ btpub_id: this.btpub_id, range: 'simple' });
            this.getDmbContent()
            this.showAlertBox = false;
        },
        //tab切换
        handleClick(tab, event) {
            // this.activeName
            console.log(this.activeName);
        },
        //选择框
        checked(idx) {
            this.checkShow = !this.checkShow;
            this.checkIndex = idx;
        },
        get_btpub_detail_info(btpub_id) {
            const params = btpub_id
            // if (btpub_id) {
            //     params["btpub_id"] = btpub_id
            // } else {
            //     params["btpub_id"] = this.$store.state.deployCfInfo["btpub_id"]
            // }

            get_btpub_detail(params).then(data => {
                if (data.rst == 'ok') {
                    console.log("dadatadatadatadatadatadatadatadatata", data);
                    this.dmb_spec = data["data"][0]["dmb_spec"];
                    this.daypartgroup = data["data"][0]['play_info']['daypartgroup']
                    this.btplan_id = data['data'][0]['btplan_id']
                    console.log(this.dmb_spec, 'dmb_spec');

                    let list = new Array(this.dmb_spec.length).fill(0).map((col, idx) => {
                        return {
                            s_number: idx + 1
                        }
                    });


                    this.select_screen_optios = list;
                    this.saveDelayInfo = JSON.parse(JSON.stringify(list))
                    this.saveDelayInfo.forEach(item => {
                        this.$set(item, 'screen_index', item.s_number)
                        this.$set(item, 'first_tile_cuttime', 0)
                    })
                    data['data'][0]['play_info']['tile_cut_infos'].forEach((item, index) => {
                        this.saveDelayInfo.forEach((item2, index1) => {
                            if (item2.screen_index == item.screen_index) {
                                this.$set(this.saveDelayInfo, index1, item)
                            }
                        })
                    })

                    console.log(this.saveDelayInfo, 'saveDelayInfo')

                    this.paly_rules.delay_time = data["data"][0]['play_info']['tile_cut_info'] ? data["data"][0]['play_info']['tile_cut_info']['first_tile_cuttime'] : 0
                    this.paly_rules.delay_screen = data["data"][0]['play_info']['tile_cut_info'] ? data["data"][0]['play_info']['tile_cut_info']['screen_index'] : 1
                    this.dmbContent = data["data"][0].contents;
                    this.dmbName = data["data"][0]["name"]
                    this.getDayPart()
                    // this.loading = false;

                } else {

                }
            })
        },

        // daypart
        getDayPart() {
            const params = {
                classModel: "SysDayPart",
                page: 0,
                size: 10
            }
            get_daypart_data(params).then(res => {
                res.data[0].content.forEach((item, index) => {
                    if (item.group_name == this.daypartgroup) {
                        this.timeframe = item.attr_list
                    }
                })
                this.addContentArray = []
                this.dmb_spec.split("").forEach((item, index) => {
                    this.addContentArray.push({
                        imgUrl: "",
                        direction: item,
                        screen_index: index + 1,
                    })
                })
                this.timeframe.forEach(item => {
                    this.$set(item, 'addContentArray', this.addContentArray)
                })
                this.timeframe = JSON.parse(JSON.stringify(this.timeframe))
                this.timeframe.forEach(item => {
                    item.addContentArray.forEach(item1 => {
                        item1.daypartname = item.daypartname
                    })
                })
                this.getDmbContent()
            })
        },

        openSelectImage(item, item1, index, index1) {
            if (this.btn_click) {
                return
            }
            this.btn_click = true;
            setTimeout(() => {
                this.btn_click = false
            }, 1000);
            this.cf_id = ''
            this.btpub_id = this.btpub_id;
            this.selectDayPart = index;
            this.selectImage = item1.screen_index;
            console.log("item1", item1);
            setTimeout(() => {
                this.$refs.picture.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.video.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.h5.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.h5.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.template.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.picture.batchState = true;
                this.$refs.video.batchState = true;
                this.$refs.h5.batchState = true;
                this.$refs.video.isDMB = true;
                this.$refs.picture.isDMB = true;
                this.$refs.picture.isContent = true;
                this.$refs.video.isContent = true;
                this.$refs.h5.isContent = true;
                this.$refs.h5.isDMB = true;
            }, 500);
            const params = {
                group_id: localStorage.getItem("group_id"),
                dire: item1.direction == 0 ? 'h' : 'v',
                create_from: 14,
                // cf_label: `${this.btpub_id}_${item.name}_${item1.screen_index + 1}`,
                cf_label: `${this.btplan_id}_${item.name}_${item1.screen_index}`,
                attributes: {
                    // label: `${this.btpub_id}_${item1.screen_index + 1}`,
                    play_mode: "full_day",
                    daypart_name: item.name
                }
            }
            this.addClassIfyInfo = JSON.parse(JSON.stringify(params));
            this.ClassifyInfo = JSON.parse(JSON.stringify(item1));
            this.v_or_h = item1.direction;
            this.showAlertBox = true;
            if (item1.cf_id) {
                this.cf_id = item1.cf_id;
            }
        },

        relevanceClassIfy(item, item1, index, index1) {
            this.relevanceDialog = true;
            this.selectDayPart = index;
            this.relevanContent = JSON.parse(JSON.stringify(item1));
            const params = {
                btpub_id: this.btpub_id,
                ref_id: '',
                ref_source: "ds",
                daypartname: this.timeframe[this.selectDayPart].name,
                screen_index: this.relevanContent.screen_index,
                add_mode: 1
            }
            console.log(params, 'params');

            this.relevanInfo = params

            // relevanInfo()
        },


        getDmbContent() {
            this.timeframe.forEach(item => {
                item.addContentArray.forEach(item1 => {
                    this.dmbContent.forEach(item2 => {
                        if (item1.daypartname == item2.daypartname && item1.screen_index == item2.screen_index) {
                            item1.ref_id = item2.ref_id;
                            item1.cf_id = item2.ref_id;
                            item1.item_id = item2.item_id
                            get_classify_detail({ g_cf_id: item1.ref_id, offset: 0, limit: 300 }).then(res => {
                                item1.imgUrl = res['data'][0]['cs_list'][0] ? res['data'][0]['cs_list'][0]["thumb_url"] : ""
                                item1.imgLength = res['data'][0]['cs_list'].length
                                item1.inherit_from_id = res['data'][0]['inherit_from_id']
                                item1.inherit_from_name = res['data'][0]['inherit_from_name']
                            })

                        }
                    })
                })
            })
            this.loading = false;

            console.log(this.timeframe);
        },
        // 删除专辑内容
        deleteContent(item1) {
            const params = {
                item_id: item1.item_id
            }
            del_btpub_content(params).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("删除成功")
                    this.get_btpub_detail_info({ btpub_id: this.btpub_id, range: 'simple' });
                    this.getDmbContent()
                } else {
                    this.$message.warning(res.error_msg)
                }
            })
        },
        toPreview(item) {
            console.log(item.ref_id, "item");
            console.log(this.dmbContent, 'sq');
            this.$emit("toPreview", Number(item.ref_id))
            // this.$router.push({
            //     path: "/deploy/editDMB",
            //     query: {
            //         ref_id: item.ref_id,
            //         name: this.dmbName,
            //         btpub_id: item.item_id,
            //         showInput: ""
            //     }
            // })
        },
        settings(item) {
            console.log(item, 'item');

            this.$router.push({
                path: '/deploy/associatedContent',
                query: {

                }
            })
        },
        openCopyDialog(timename) {
            this.timeDaypartName = timename;
            this.copyDaypartName = timename;
            if (this.timeframe.length > 1) {
                this.dialogCopyStatus = true;
            } else {
                this.$message.error("只有一个经营时段不支持复制")
            }
            // this.dialogCopyStatus = true;
        },
        cancelCopyDialog() {
            this.dialogCopyStatus = false;
        },
        requestCopy() {
            this.$confirm('此操作会覆盖目标经营时段内容, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                copy_dmb_btpub_content({
                    btpub_id: this.btpub_id,
                    src_dptname: this.timeDaypartName,
                    target_dptname: this.copyDaypartName,
                    is_force_cover: 1
                }).then(res => {
                    if (res.rst == 'ok') {
                        this.get_btpub_detail_info({
                            btpub_id: this.btpub_id,
                            range: 'simple'
                        });
                        this.getDmbContent()
                    }
                    this.dialogCopyStatus = false;
                })
            }).catch(() => {
                this.dialogCopyStatus = false;
                this.$message.info("取消复制")
            });
        },
        relevan(row) {
            console.log(row, 'row');
            console.log(this.relevanInfo, 'relevanInfo');

            this.relevanInfo.ref_id = row.sche_id;
            add_btpub_content(this.relevanInfo).then(res => {
                console.log(res, 'res');
                if (res.rst == 'ok') {
                    this.$message.success('关联成功')
                    this.relevanceDialog = false;
                    this.get_btpub_detail_info(
                        {
                            btpub_id: this.btpub_id,
                            range: 'simple'
                        });
                } else {
                    this.$message.error(res.error_msg)
                }
            })

        },
        savePlayRule() {
            console.log(this.saveDelayInfo, 'saveDelayInfo')
            let tile_cut_infos = this.saveDelayInfo.map(item => {
                console.log(item, 'item');
                if (!item.first_tile_cuttime) {
                    return
                }

                return {
                    screen_index: item.screen_index,
                    first_tile_cuttime: item.first_tile_cuttime
                }
            })
            tile_cut_infos = tile_cut_infos.filter(item => item)
            let params = {
                btplan_id: this.btplan_id,
                tile_cut_infos: tile_cut_infos
            }

            edit_btpubplan(params).then(res => {
                console.log(res, 'res')
                if (res.rst == 'ok') {
                    this.$message.success('保存成功');
                }
            })

            // let params = {
            //     btplan_id: this.btplan_id,
            //     tile_cut_info: {
            //         screen_index: this.paly_rules.delay_screen,
            //         first_tile_cuttime: this.paly_rules.delay_time
            //     }
            // }
            // edit_btpubplan(params).then(res => {
            //     if (res.rst == 'ok') {
            //         this.$message.success('保存成功');
            //     }
            // })
        },
    },
}
</script>

<style lang="scss" scoped>
.dialog_btns {
    height: 50px;

    button {
        margin: 0 20px 0 0 !important;
    }
}

.GeneralCreate {
    width: 100%;
    /* padding: 0 14px 0 13px; */
    /* background-color: #f8f7f7; */
    margin-top: 20px;
    border: rgba(229, 229, 229, 1) solid 1px;
}

.actionBtn {
    position: relative;
    width: 100%;
    height: 12px;
    padding-right: 71px;
}

.pageHead {
    height: 58px;
    line-height: 58px;
    font-size: 14px;
    color: #505050;
    font-weight: bold;
    border-bottom: 1px solid #ecebeb;
}

.el-icon-back {
    color: var(--btn-background-color);
    font-size: 25px;
    margin-right: 10px;
    vertical-align: middle;
}

button {
    position: absolute;
    right: 110px;
    margin: 15px 15px;
}

.el-button--primary {
    right: 0;
}


/*取消*/
.btns {
    display: flex;
    margin-top: -12px;
    padding-bottom: 10px;
    justify-content: flex-end;
    width: 100%;
}


.dmbEdit {
    display: flex;
    align-items: center;
    justify-content: center;
}

.table {
    width: 80%;
    margin-top: 20px;
    border: 0 !important;

    .table_header {
        background-color: #30b07f;
        color: #fff;
        font-size: 25px;
        border-radius: 20px 20px 0 0;
        border: none !important;

    }

    .sitting {
        color: #fff;
        background-color: #24b07d !important;
        border-radius: 10px;
        flex: 0.6 !important;
    }

    .time_class {
        border: 2px solid #30b684;
        border-radius: 10px;
        color: #2ab27e;
        font-size: 25px;
        flex: 0.6 !important;
    }

    .table_body {
        background-color: #f7f7f7 !important;
        box-sizing: border-box;
        padding: 20px;
        display: flex;
        flex-direction: column;

        .body_table {
            background-color: #fff;
            border-radius: 20px;
            box-sizing: border-box;
            padding: 20px 60px;

            tr th {
                background-color: #f7f7f7;
                box-sizing: border-box;
                padding: 10px;
                margin-top: 10px;
                margin-left: 10px;

                &:nth-child(1) {
                    margin-left: 0;
                }

            }
        }


        .body_header {
            height: 70px !important;
            margin-top: 0 !important;
        }

        tr {
            // background-color: #fff;
            justify-content: space-between;
        }
    }


    .screen {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 300px;
        border-radius: 10px;

        span {
            display: inline-block;
            line-height: 30px;
            text-align: center;
        }
    }
}

.arcoss {
    width: 34px;
    height: 30px;
}

.vision {
    width: 32px;
    height: 40px;
    line-height: 40px !important;
}


.newdmb {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
    cursor: pointer;

    .add {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;
    }

    .relevance {
        display: flex;
        flex-direction: column;
        align-items: center;

    }

    img {
        width: 25px;
        height: 25px;
    }
}

.preview {
    width: 45px;
    height: 45px;
    color: white;
    background-color: #ccc;
    position: absolute;
    left: 30%;
    top: 45%;
    line-height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
}

.delete {
    width: 45px;
    height: 45px;
    color: white;
    background-color: #ccc;
    position: absolute;
    left: 55%;
    top: 45%;
    line-height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
}

// .newdmb:hover {
//     .delete {
//         display: block;
//     }

// }

.newdmb1 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}

.dmb_container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .triangle {
        position: absolute;
        z-index: 99;
        right: -1px;
        top: 0;
        transform: scale(0.6);
        transform-origin: right top;
    }

    &:hover {
        .dmb_mask {
            display: flex;
        }
    }

    .dmb_img {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
    }

    .event_dmb {
        width: 100%;
        z-index: 99;

    }

    .event_dmb {
        display: flex;
        width: 100%;
        padding: 5px 10px;
        position: absolute;
        bottom: 0;
        background: rgba($color: #000000, $alpha: 0.3);
        color: #fff;
        justify-content: space-between;
    }

    .delete1 {
        cursor: pointer;
    }

    .dmb_mask {
        width: 100%;
        height: 100%;
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        align-items: center;
        justify-content: center;

        .dmb_mask_preview {
            background-color: #f7f7f7;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            margin-right: 10px;
        }

        .dmb_mask_setting {
            background-color: #f7f7f7;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            margin-left: 10px;
        }
    }
}

.screen_content {
    height: 200px;
    // width: 400px !important;
}

tr {
    width: 100% !important;
    display: flex !important;

    th {
        flex: 1;
    }
}

.newdmb1:hover {
    .delete {
        display: block;
    }

    .preview {
        display: block;
    }
}

.screen_arcoss {
    // width: 340px !important;
    // height: 230px !important;
    width: 100%;
    height: 100%;
    // object-fit: contain;
}

.screen_vision {
    // width: 185px !important;
    // height: 240px !important;
    width: 242px !important;
    height: 100% !important;
    object-fit: contain;
}

// .activeState {
//     position: absolute;
//     right: 60%;
//     top: 21%;
//     padding: 5px 20px;
//     background: rgba($color: #000000, $alpha: 0.4);
//     color: #fff;
// }
.activeState {
    position: absolute;
    // right: 66.8%;
    top: 5%;
    left: 0;
    padding: 5px 10px;
    background: rgba($color: #000000, $alpha: 0.4);
    color: #fff;
}

.activeState1 {
    position: absolute;
    // right: 66.8%;
    top: 5%;
    right: 0;
    padding: 5px 10px;
    background: rgba($color: #000000, $alpha: 0.4);
    color: #fff;
}

::v-deep .el-tabs .is-active {
    padding-right: 20px !important;
}

.set_play_rule {
    width: 100%;
    padding: 20px 50px 0 50px;

    .content_title {
        color: var(--text-color-light);
        padding-left: 10px;
        margin: 10px 0px;
    }

    .select_wrap {
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        width: 100%;
        padding-left: 10px;

        .select_info {
            margin-right: 30px;
            margin-bottom: 40px;
            display: flex;
            align-items: center;

            ::v-deep .el-button--primary {
                right: auto;
            }

            .s_t_wrap {
                position: relative;

                .select_tips {
                    position: absolute;
                    top: 40px;
                    left: 0;
                    color: var(--text-color-light);
                }
            }

            ::v-deep .el-input__inner {
                color: var(--text-color-light);
            }
        }
    }
}

::v-deep .relevanceDialog .el-dialog {
    margin-top: 8vh !important;
}

.inherit_name {
    position: absolute;
    color: #fff;
    z-index: 999;
    left: 5px;
    top: 10px;
    background-color: rgba($color: #000000, $alpha: .4);
    padding: 5px 10px;
}
</style>
