import request from '@/utils/request'
import { post, uploadFile } from '@/utils/request'

// 获取所有的Role
export function getAll() {
  const params = {
    'classModel': 'GroupTreeRole',
    'sort': '', // 非必要，排序规则，id,desc
    'createdAtS': '', // 非必要，查询创建时间开始日期
    'createdAtE': '', // 非必要，查询创建时间结束日期
    'page': 0, // 起始页码,
    'size': 100 // 每页数据量
    // "blurry": 1
}
return post('dmb/api/json', params, 'get_adm_datas')
}

export function add(params) {
  // return request({
  //   url: 'api/roles',
  //   method: 'post',
  //   data
  // })
  params['act'] = 'add'
  return post('dmb/api/json', params, 'rolemgmt')
}

export function get(id) {
  const params = {
      'classModel': 'GroupTreeRole',
      'sort': '', // 非必要，排序规则，id,desc
      'createdAtS': '', // 非必要，查询创建时间开始日期
      'createdAtE': '', // 非必要，查询创建时间结束日期
      'page': 0, // 起始页码,
      'size': 1 // 每页数据量
      // "blurry": 1
  }
  return post('dmb/api/json', params, 'get_adm_datas')
}

export function getLevel() {
  return { 'level': 1 }
  // return request({
  //   url: 'api/roles/level',
  //   method: 'get'
  // })
}

export function del(ids) {
  const params = {
    'id': ids[0],
    'act': 'del'
  }
  return post('dmb/api/json', params, 'rolemgmt')
}

export function edit(params) {
  params['act'] = 'edit'
  params['menus'] = []
  return post('dmb/api/json', params, 'rolemgmt')
}

export function editMenu(params) {
  return post('dmb/api/json', params, 'rolemenumgr')
}

export default { add, edit, del, get, editMenu, getLevel }
