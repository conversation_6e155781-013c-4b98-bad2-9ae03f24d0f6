import that from '../../src/main.js'
import url from 'postcss-url';
import $ from 'jquery'
import { Message } from 'element-ui';
const OSS = require('../../static/js/aliyun-oss-sdk-6.0.2.min.js')
const SparkMD5 = require('../../static/js/spark-md5.js')

var autoInfor = ''
let localTemp = localStorage.getItem("device_info")
if (localTemp) {
    autoInfor = JSON.parse(localTemp);
}
var baseUrl = ''
var basekey = ''
var locationOrigin = window.location.origin
// var locationOrigin = 'http://smp.instwall.com'
// var locationOrigin = 'https://grayds.instwall.com'
function getBaseUrlAndBaseKey() {
    let localTemp = localStorage.getItem("device_info")
    if (localTemp) {
        autoInfor = JSON.parse(localTemp);
    }
    console.log('autoInfor 111111900000',autoInfor)
    // autoInfor.uid = autoInfor.auth_id.split('-')[2]
    console.log('autoInfor 99999900000',autoInfor)
    if (locationOrigin == 'http://smp.instwall.com') {
        basekey = 'sandbox'
        console.log('autoInfor.auth_id.split()[2]',autoInfor.uid)
        baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/sandbox/ds/' +  autoInfor.uid + '/'
    } else if (locationOrigin == "http://grayds.instwall.com") {
        basekey = 'dsgray'
        baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/dsgray/ds/' +  autoInfor.uid+ '/'
    } else {
        basekey = 'public'
        baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/public/ds/' + autoInfor.uid+ '/'
    }
}
getBaseUrlAndBaseKey()
// var locationOrigin = window.location.origin
// if (locationOrigin == 'http://smp.instwall.com') {
//     basekey = 'sandbox'
//     baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/sandbox/ds/' + autoInfor["uid"] + '/'
// } else if (locationOrigin == "http://grayds.instwall.com") {
//     basekey = 'dsgray'
//     baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/dsgray/ds/' + autoInfor["uid"] + '/'
// } else {
//     basekey = 'public'
//     baseUrl = 'http://temp-instwall-com.oss-cn-hangzhou.aliyuncs.com/public/ds/' + autoInfor["uid"] + '/'
// }

// sandbox dsgray public
// var appServer = 'http://www.tele.bh/med/home/<USER>'; buyong
//获取ststoken的接口，这边这个地址是我本地的。你们的接口地址自己应该清楚
var bucket = 'temp-instwall-com';
var region = 'oss-cn-hangzhou';//前面新建bucket时选择过的。
var uid = 'x';//用户标识。这个根据自己情况自己定
var Buffer = OSS.Buffer;
var tempCheckpoint = ""
var fileMd5 = ""

window.videoFun = undefined
videoFun = function () {
    let localTemp = localStorage.getItem("device_info")
    var autoInfor = ''
    if (localTemp) {
        autoInfor = JSON.parse(localTemp);
    }
    
    var user_id = ''
    var video_info = {}
    // var domainUrl = "http://smp.instwall.com"
    // var domainUrl = locationOrigin
    var domainUrl = "http://192.168.20.201:8080"


    function muBanFunFun() {
        console.log('123456', '去到视频')
        // muBanFunTwo()
    }
    function setVideoThumbUrlToNoneFun() {
        setVideoThumbUrlToNone()
    }
    function getVideoThumbUrlFun() {
        // getVideoThumbUrl()
    }
    function muBanFunOneFun() {
        console.log('123456', '去到图片')
        muBanFunOne()
    }
    function showUploadImgBolImgFun() {
        // showUploadImgBolImg()
    }
    function hideUploadImgBolImgFun() {
        // hideUploadImgBolImg()
    }
    function hiddleReUploadBtnFun() {
        // hiddleReUploadBtn()
    }
    function showReUploadBtnFun() {
        // showReUploadBtn()
    }
    function getUserImageFilesFun() {
        setTimeout(() => {
            getUserImageFilesTwo()
            $("#contentBoxId").scrollTop(0);
        }, 1500)
    }

    function getVideoListFun() {
        setTimeout(() => {
            getVideoListTwo()
            $("#contentBoxId").scrollTop(0);
        }, 1500)
    }
    function uploadVideo_close_fun() {
        uploadVideo_close()
    }
  
    function qingChuLabelValFun(){
        // qingChuLabelVal()
    }
    function uploadImg(val) {
        var _this = this;
        var time = new Date();
        var id = time.getTime();
        var formdata = new FormData();
        let labelText = '默认标签'
        console.log('labelTextId', $('#labelTextId').val())
        if ($('#labelTextId').val()) {
            labelText = $('#labelTextId').val()
        }

        let params = {
            userfile: val,
            auth_token: autoInfor["device_token"],
            device_type: autoInfor["device_type"],
            source_type: 2,
            identify_str: "ds-admin-edit" + id,
            caption: "ds-admin-edit-content",
            tag_list: JSON.stringify([labelText]),
            auth_id: autoInfor["device_did"],
            client_info: JSON.stringify({
                language: "zh-CN",
                useragent:
                    "Mozilla/5.0(Windows NT 10.0; Win64; x64)AppleWebKit/537.36(KHTML,like Gecko)Chrome/77.0.3865.120 Safari/537.36",
                boundleid: "com.liankexinxi.PicBox"
            })
        };
        for (let i in params) {

            formdata.append(i, params[i]);
        }
        console.log('formdata22', formdata)
        console.log('formdata val', val)

        showUploadImgBolImgFun()
        showAddLabelFunFun()

        $.ajax({
            method: "post",
            url: domainUrl + "/file/upload_photo",
            // headers: { "Content-Type": "application/x-www-form-urlencoded" },
            data: formdata,
            processData: false,
            contentType: false,
            // mimeType:'multipart/form-data'

        })
            .then(res => {
                console.log(res);
                // 将上一次打的标签清除
                qingChuLabelValFun()
                hideUploadImgBolImgFun()
                if (res.rst == "ok") {

                    // that.$message.success('上传成功')
                    Message({
                        message: '上传成功',
                        type: 'success'
                    })

                    $("#file").val('')
                    getUserImageFilesFun()
                    muBanFunOneFun()
                    // _this.getUserImageFiles(this.pageSize,0);
                } else if (res.rst == "error") {
                    if (res.error_code == 460) {
                        that.$message.error("图片宽高必须大于300*300");
                        $("#file").val('')

                    }
                    else if (res.error_code == 461) {
                        that.$message.error('图片宽高必须小于12288*12288')
                        $("#file").val('')
                    }
                    else if (res.error_code == 400) {
                        if (res.error_msg.indexOf('File size is not allowed') !== -1) {
                            that.$message.error('图片大小必须小于30M')
                            $("#file").val('')
                        }
                    }
                    else if (res.error_code == 415) {
                        // 登出
                        that.$message.error({
                            message: '请重新登录',
                            center: true,
                            duration: 5000
                        })
                        setTimeout(() => {
                           
                            that.$router.push('/')
                        }, 5000)

                    } else {
                        that.$message.error("上传出错");
                        $("#file").val('')
                    }

                } else {
                    that.$message.error("网络错误,请稍后重试!");
                    $("#file").val('')
                }
            });
    }
    function readFileMd5(file) {
        var time = new Date().getTime()
        var blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
            chunkSize = 2097152, // read in chunks of 2MB
            chunks = Math.ceil(file.size / chunkSize),
            currentChunk = 0,
            spark = new SparkMD5.ArrayBuffer(),
            frOnload = function (e) {
                //  log.innerHTML+="\nread chunk number "+parseInt(currentChunk+1)+" of "+chunks;
                spark.append(e.target.result); // append array buffer
                currentChunk++;
                if (currentChunk < chunks) {
                    loadNext();
                }
                else {
                    fileMd5 = spark.end()
                    var time1 = new Date().getTime();
                    video_info["md5"] = fileMd5
                    var fileExtension = file.name.split('.').pop().toLowerCase();
                    video_info["file_identify"] = fileMd5 + "." + fileExtension
                    // console.log(file)
                    // video_info["file_realname"] = fileMd5 + "." + fileExtension
                    video_info["file_realname"] = file.name
                    console.log("加载结束 :计算后的文件md5:" + spark.end() + "耗时：" + (time1 - time) / 1000)
                }
            },
            frOnerror = function () {
                console.log("error")
            };
        function loadNext() {
            var fileReader = new FileReader();
            fileReader.onload = frOnload;
            fileReader.onerror = frOnerror;
            var start = currentChunk * chunkSize,
                end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
        };

        loadNext();
    }
    function get_video_info(file) {
        console.log('user_id789000', autoInfor)
        // autoInfor.uid = autoInfor.auth_id.split('-')[2]
        console.log('user_id789111', autoInfor)
        user_id = autoInfor["uid"]
        console.log('user_id789333', user_id)
        console.log('size file', file.size)
        var duration;
        const url = URL.createObjectURL(file)
        console.log('url', url)
        const video = document.createElement('video')
        var time = new Date().getTime()
        video_info['size'] = file.size
        video_info['owner_uid'] = user_id
        video.onloadedmetadata = evt => {

            // Revoke when you don't need the url any more to release any reference
            URL.revokeObjectURL(url)
            duration = video.duration;
            console.log(duration);//单位：秒
            console.log(video.videoWidth, video.videoHeight)
            let width = video.videoWidth,
                height = video.videoHeight
            var size = file.size;//单位：字节(byte)
            var info = {
                'v_or_h': width > height ? 0 : 1,
                // 'source_url': _this.sourceUrl,
                // 'file_identify': parseInt(time)+file.name,
                // 'file_realname': parseInt(time)+file.name,
                'owner_uid': user_id,
                'duration': parseInt(duration),
                'width': width,
                'height': height,
                'size': size,
                // 'md5': fileMd5
            }
            // 改变3
            for (var i in info) {
                video_info[i] = info[i]
            }
            console.log('%c video_info', video_info, 'color:tomato;')
            console.log('%C video_info', video_info, 'color:tomato;')
        }
        video.src = url
        video.load() // fetches metadata
    }
    function add_video_source(upfile) {
        var _this = this
        let request_url = domainUrl + '/store/api/json';
        let method = "add_video_source";
        var time = new Date().getTime()
        console.log('add_video_source', video_info)
        $.ajax({
            type: "post",
            url: request_url,
            data: {
                "data": JSON.stringify({
                    "id": time,
                    "method": method,
                    "params": [
                        video_info
                    ],
                    "client_info": {
                        "language": "zh-CN",
                        "useragent": "Mozilla/5.0(Macintosh;IntelMacOSX10_11_5)AppleWebKit/537.36(KHTML,   likeGecko)Chrome/65.0.3325.181Safari/537.36",
                        "boundleid": "com.liankexinxi.PicBox"
                        // "boundleid":'com.shutuo.littplayer_mgmt'
                    },
                    // "auth": {
                    //     "auth_token": "UsqQzdp2JMlA",
                    //     "auth_id": 'mwa_eapcVh5YbtWH2lO8_08.565',
                    //     "device_type": 71
                    // }
                    "auth": {
                        "auth_token": autoInfor["device_token"] ||autoInfor["auth_token"] ,
                        "auth_id": autoInfor["device_did"]|| autoInfor["auth_id"],
                        "device_type": autoInfor["device_type"]
                    }
                })
            },
            dataType: "json",
            success: function (data) {

                if (data.rst == 'ok') {
                    console.log(11111111111, data.data[0],1921)
                    console.log(11111111111111, data.data[0].file_identify)
                    localStorage.setItem('videoFileId', data.data[0].file_identify)
                    $('#progress-bar_' + upfile.num).html("<div style='font-weight: 600;font-size: 13px;top: 25px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>上传完成</div><div style='top: 68px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>100%</div>");
                    // that.$message.success('上传成功')
                    Message({
                        message: '上传成功',
                        type: 'success'
                    })

                    $(".noticeTextCss").css({ 'display': 'none' })
                    setTimeout(() => {
                        console.log('123348888', $('#progress-bar_'))
                        $('#progress-bar_0').css('width', '100%');
                    }, 500)
                    setTimeout(() => {
                        getVideoThumbUrlFun()
                    }, 500)
                    setTimeout(() => {
                        getVideoThumbUrlFun()
                    }, 1000)

                    $("#file").val('')
                    hiddleReUploadBtnFun()
                    muBanFunFun()
                    canUploadBolFunTrue()
                    getVideoListFun()
                }
                if (data.rst == 'error') {
                    canUploadBolFunTrue()
                    // that.$message.error('上传出错，请重试')
                    Message({
                        message: '上传出错，请重试',
                        type: 'error'
                    })
                    $("#file").val('')

                    var htm = "<div id='up_wrap' class='up_wrapCss' style='width: 100%;font-size:12px' ><dl><dd><div class=\"progress\"><div id=\"progress-bar_\" class=\"progress-bar\" role=\"progressbar\" aria-valuenow=\"0\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"min-width: 1em;\"><div style='top: 68px;position: absolute;color: #fff;width: 100%;left: 0%;text-align: center;'>上传出错,请重新上传</div></div></div></dd></dl></div>";
                    setTimeout(() => {
                        console.log('12334', $('#progress-bar_'))
                        $('#progress-bar_').css('width', '99%');

                    }, 500)

                    $("#imgBoxLeftId").html(htm);
                }
                console.log("res", data)
            },
            fail: function () {
                canUploadBolFunTrue()
                that.$message.error("网络连接失败,请稍后再试")
                $("#file").val('')
            }

        })
    }


    //获取授权STSToken，并初始化client
    var applyTokenDo = function (func) {
        // var url = appServer;
        var client;
        let method = "refresh_ali_sts";
        let params = {
            'expire_tm': '900',
            'bt': 'wcmp' // "ugc" for client upload file, "wcmp" for wechat miniprogram
        };
        var file;
        var time = new Date().getTime()
        let tempUrl = domainUrl + "/file/api/v2/json"
        // let tempUrl = 'http://grayds.instwall.com/' + "/file/api/v2/json"
        // let tempUrl =  window.location.origin + "/file/api/v2/json"
        
       
        return $.ajax({
            type: "post",
            url: tempUrl,
            data: {
                "data": JSON.stringify({
                    "id": time,
                    "method": method,
                    "params": [
                        params
                    ],
                    "client_info": {
                        "language": "zh-CN",
                        "useragent": "Mozilla/5.0(Macintosh;IntelMacOSX10_11_5)AppleWebKit/537.36(KHTML,   likeGecko)Chrome/65.0.3325.181Safari/537.36",
                        "boundleid": "com.liankexinxi.PicBox"
                    },
                    // "auth": {
                    //     "auth_token": "UsqQzdp2JMlA",
                    //     "auth_id": 'mwa_eapcVh5YbtWH2lO8_08.565',
                    //     "device_type": 71
                    // }
                    "auth": {
                        "auth_token": autoInfor["device_token"] ||autoInfor["auth_token"] ,
                        "auth_id": autoInfor["device_did"]|| autoInfor["auth_id"],
                        "device_type": autoInfor["device_type"]
                    }
                })
            },
            dataType: "json",
            success: function (data) {
                var bucket = 'temp-instwall-com';
                var region = 'oss-cn-hangzhou';   //申请oss服务时的区域
                if (data.rst == 'error') {
                    if (data.error_code == 415) {
                        that.$message.error({
                            message: '请重新登录',
                            center: true,
                            duration: 5000
                        })
                        setTimeout(() => {
                           
                            that.$router.push('/')
                        }, 5000)

                    }

                }
                if (data.rst == 'ok') {
                    var res = data["data"][0]["Credentials"]
                    client = new OSS({
                        region: region,
                        accessKeyId: res.AccessKeyId,
                        accessKeySecret: res.AccessKeySecret,
                        stsToken: res.SecurityToken,
                        bucket: bucket
                    });
                    return func(client)
                }
            },
            fail: function (err) {
                canUploadBolFunTrue()
                that.$message.error("网络连接失败,请稍后再试")
                $('#file').val('')
            }
        });
    };
    function checkTv(fileName) {
        var index = fileName.indexOf("."); //（考虑严谨用lastIndexOf(".")得到）得到"."在第几位
        // 改变2
        // tv_id = fileName.substring(index); //截断"."之前的，得到后缀
        var tv_id = fileName.substring(index); //截断"."之前的，得到后缀
        console.log('上传的视频格式', tv_id)
        if (tv_id == '.avi') {
            var info = {
                'v_or_h': 0,
                'duration': 0,
                'width': 0,
                'height': 0,
            }
            // 改变3
            for (var i in info) {
                video_info[i] = info[i]
            }
        }
        tv_id = tv_id.toLocaleLowerCase()
        if (tv_id != ".mov" && tv_id != ".mp4" && tv_id != ".rmvb" && tv_id != ".avi" && tv_id != ".ts") { //根据后缀，判断是否符合视频格式
            that.$message.error("不是指定视频格式,重新选择");
            $("#file").val('')
            canUploadBolFunTrue()
            return false
        }
        return true
    }

    //上传文件
    var uploadFile = function (client) {
        if (upfiles.length < 1)
            return;
        // 改变1 
        // upfile = upfiles[0];
        var upfile = upfiles[0];
        var file = upfile.file;
        // 判断
        var flag = checkTv(file.name)
        if (!flag) {
            return false
        }
        if (file["size"] > 1024 * 1024 * 1024) {
            console.log("文件不能大于1G", parseInt(file["size"] / (1024 * 1024)))
            that.$message.error("文件不能大于1G")
            $("#imgBoxLeftId").css({ 'display': 'none' })
            $(".noticeTextCss").css({ 'display': 'none' })
            canUploadBolFunTrue()
            $("#file").val('')

            return false
        }

        readFileMd5(file)
        get_video_info(file)
        //key可以自定义为文件名（例如file.txt）或目录（例如abc/test/file.txt）的形式，实现将文件上传至当前Bucket或Bucket下的指定目录。
        var key = basekey + '/ds/' + autoInfor["uid"] + '/' + upfile.name;
        return client.multipartUpload(key, file, {
            progress: function (p, cpt, res) {
                // console.log("p:", p);
                // console.log("cpt:", cpt);
                let checkpoint = cpt
                if (cpt != undefined) {
                    tempCheckpoint = cpt
                }

                // console.log(Math.floor(p * 100) + '%');
                // var bar = document.getElementById('progress-bar_' + upfile.num);
                // bar.style.width = Math.floor(p * 100) + '%';
                // bar.innerHTML = Math.floor(p * 100) + '%';
                var percentNum = Math.floor(p * 100)

                if (percentNum == 100) {
                    percentNum = 99
                }
                // bar.innerHTML = "<div style='font-weight: 600;font-size: 13px;top: 25px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>上传中</div><div style='top: 68px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>" + percentNum + "%</div>"

            },
        }).then(function (res) {
            console.log('upload success: ', res);
            // $('#progress-bar_' + upfile.num).css('width', '99%');
            // $('#progress-bar_' + upfile.num).text('99%');
            // $('#progress-bar_' + upfile.num).addClass('progress-bar-success');
            // $('#progress-bar_' + upfile.num).html("<div style='font-weight: 600;font-size: 13px;top: 25px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>上传中</div><div style='top: 68px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>99%</div>");
            getBaseUrlAndBaseKey()
            console.log('baseUrl', baseUrl)
            console.log('baseUrl upfile.name', upfile.name)
            video_info["source_url"] = baseUrl + upfile.name.replace(/\(/g, "%28").replace(/\)/g, "%29")
            add_video_source(upfile)
        }).catch(function (err) {

            console.log(err);
            canUploadBolFunTrue()
            that.$message.error('上传出错，请重试')
            // $("#file").val('')
            // 上传异常 show重新上传按钮
            showReUploadBtnFun()

            error(err);
        });
    };
    //断点续传
    window.reUploadFile = function (client) {
        if (upfiles.length < 1)
            return;
        var upfile = upfiles[0];
        var file = upfile.file;

        var key = basekey + '/ds/' + autoInfor["uid"] + '/' + upfile.name;
        console.log("tempCheckpoint", tempCheckpoint)
        return client.multipartUpload(key, file, {
            progress: function (p, cpt, res) {
                // console.log("p:", p);
                // console.log("cpt:", cpt);
                tempCheckpoint = cpt
                // console.log(Math.floor(p * 100) + '%');
                var bar = document.getElementById('progress-bar_' + upfile.num);
                if (bar) {
                    bar.style.width = Math.floor(p * 100) + '%';
                    bar.innerHTML = Math.floor(p * 100) + '%';

                }


            },
            checkpoint: tempCheckpoint
        }).then(function (res) {
            console.log('upload success: ', res);
            upfiles.shift();
            $('#progress-bar_' + upfile.num).css('width', '99%');
            $('#progress-bar_' + upfile.num).text('99%');
            $('#progress-bar_' + upfile.num).addClass('progress-bar-success');
            $('#progress-bar_' + upfile.num).text('上传中');
            getBaseUrlAndBaseKey()
            console.log('baseUrl', baseUrl)
            console.log('baseUrl upfile.name', upfile.name)
            video_info["source_url"] = baseUrl + upfile.name.replace(/\(/g, "%28").replace(/\)/g, "%29")
            add_video_source(upfile)
        }).catch(function (err) {
            console.log(err);
            error(err);
        });
    };

    //断点续传文件

    function error(err) {
        switch (err.status) {
            case 0:
                if (err.name == "cancel") { //手动点击暂停上传
                    return;
                }
                break;
            case -1: //请求错误，自动重新上传
                // 重新上传;
                return;
            case 203: //回调失败
                // 前端自己给后台回调;
                return;
            case 400:
                switch (err.code) {
                    case 'FilePartInterity': //文件Part已改变
                    case 'FilePartNotExist': //文件Part不存在
                    case 'FilePartState': //文件Part过时
                    case 'InvalidPart': //无效的Part
                    case 'InvalidPartOrder': //无效的part顺序
                    case 'InvalidArgument': //参数格式错误
                        // 清空断点;
                        // 重新上传;
                        return;
                    case 'InvalidBucketName': //无效的Bucket名字
                    case 'InvalidDigest': //无效的摘要
                    case 'InvalidEncryptionAlgorithmError': //指定的熵编码加密算法错误
                    case 'InvalidObjectName': //无效的Object名字
                    case 'InvalidPolicyDocument': //无效的Policy文档
                    case 'InvalidTargetBucketForLogging': //Logging操作中有无效的目标bucket
                    case 'MalformedXML': //XML格式非法
                    case 'RequestIsNotMultiPartContent': //Post请求content-type非法
                        // 重新授权;
                        // 继续上传;
                        return;
                    case 'RequestTimeout'://请求超时
                        // 重新上传;
                        return;
                }
                break;
            case 403: //授权无效，重新授权
            case 411: //缺少参数
            case 404: //Bucket/Object/Multipart Upload ID 不存在
                // 重新授权;
                // 继续上传;
                return;
            case 500: //OSS内部发生错误
                // 重新上传;
                return;
            default:
                break;
        }
    }
    //文件上传队列
    var upfiles = [];

    function changeShowBol() {
        console.log('开始上传', this)
        this.showProcessBol = true;
    }
    function canUploadBolFunTrue() {
        console.log('传完了');
        uploadVideo_open()
    }
    function canUploadBolFunFalse() {
        uploadVideo_close()
    }

    $(function () {
        $("#file").val('')
        // 将input file的选择的文件清空
        var obj = document.getElementById("file");
        if (obj) {
            obj.outerHTML = obj.outerHTML;
            console.log(obj,456789);
        }
        //初始化文件上传队列
        var fileDom = document.getElementById('file')
        // console.log(fileDom,99999);
        if (fileDom) {
            fileDom.onchange = function (e) {
            // window.onIptchange = function (e) {
                
                console.log(fileDom,999999);
                console.log('eeee onchange', e)
                var ufiles = $(this).prop('files');
                
                var htm = "";
                console.log( $(this),ufiles);
                // <dt>" + ufiles[i].name + "</dt>
                for (var i = 0; i < ufiles.length; i++) {
                    htm += "<div id='up_wrap' class='up_wrapCss' style='width: 100%;font-size:12px' ><dl><dd><div class=\"progress\"><div id=\"progress-bar_" + i + "\" class=\"progress-bar\" role=\"progressbar\" aria-valuenow=\"0\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"min-width: 1em;\"><div style='font-weight: 600;font-size: 13px;top: 25px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'>上传中</div><div style='top: 68px;position: absolute;color: #fff;width: 50%;left: 25%;text-align: center;'></div></div></div></dd></dl></div>";
                    upfiles = [{
                        num: i,
                        name: ufiles[i].name,
                        file: ufiles[i],
                        type: ufiles[i].type
                    }]
                }
                console.log('upfiles:', upfiles);
                console.log('upfiles:', upfiles[0].type.indexOf('image'));
                
                // 上传的是视频
                if (upfiles[0].type.indexOf('image') == -1) {

                    console.log('upfiles:', upfiles[0].name);
                    var reg = /[~!@#$%^&*/\|,《》￥<>?"';:+=\[\]{}]/   // 特殊字符
                    var regTwo = /\s/;   // 空格
                    if (reg.test(upfiles[0].name)) {
                        that.$message.error('视频名称不能包含特殊字符')
                        return false
                    } else if (regTwo.test(upfiles[0].name)) {
                        that.$message.error('视频名称不能包含空格')
                        return false
                    }
                   
                    canUploadBolFunFalse()
                    applyTokenDo(uploadFile);
                    // 上传视频 end
                } else {
                    that.$message.error('文件格式不对')
                }
            }
        }
        



        //上传
        $("#file-button").click(function () {
            applyTokenDo(uploadFile);
        });
        //续传
        $("#Continue-button").click(function () {
            console.log('点击了重新上传')
            applyTokenDo(reUploadFile);
        })
        $('#cancelAddLabelId').click(function () {
            $('.addLabel').css({ 'display': 'none' })
        })
        $('#sureAddLabelId').click(function () {
           
            if(!$('#labelTextId').val()){
                that.$confirm('请打标签，否则展示‘默认标签’', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    
                  }).then(() => {
                    uploadImg(upfiles[0].file)
                    console.log('添加了标签，然后上传图片')
                    cancelAddLabelFun()
                    setTimeout(() => {
                        $('.addLabel').css({ 'display': 'none' })
                    }, 100)
                  }).catch(() => {
                    that.$message({
                      type: 'info',
                      message: '已取消'
                    });          
                  });
            }else{
                uploadImg(upfiles[0].file)
                console.log('添加了标签，然后上传图片')
                cancelAddLabelFun()
                setTimeout(() => {
                    $('.addLabel').css({ 'display': 'none' })
                }, 100)
            }
            
        })
    })

}
export default videoFun