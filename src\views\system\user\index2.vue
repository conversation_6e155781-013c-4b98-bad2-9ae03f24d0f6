<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边部门数据-->
      <!-- <el-col :xs="9" :sm="6" :md="5" :lg="4" :xl="4">
        <div class="head-container">
          <el-input
            v-model="deptName"
            clearable
            size="small"
            placeholder="输入市场名称搜索"
            prefix-icon="el-icon-search"
            class="filter-item"
            @input="getDeptDatas"
          />
        </div>
        <el-tree
          :data="deptDatas"
          :load="getDeptDatas"
          :props="defaultProps"
          :expand-on-click-node="false"
          lazy
          @node-click="handleNodeClick"
        />
      </el-col> -->
      <!--用户数据-->
      <el-col :xs="15" :sm="18" :md="19" :lg="20" :xl="20">
        <!--工具栏-->
        <div class="head-container">
          <div v-if="crud.props.searchToggle">
            <!-- 搜索 -->
            <el-input v-model="query.blurry" clearable size="small" placeholder="输入名称搜索" style="width: 200px;"
              class="filter-item" @keyup.enter.native="crud.toQuery" />
            <el-select v-model="query.fromtype" clearable size="small" placeholder="营运类型" class="filter-item"
              style="width: 150px" @change="crud.toQuery">
              <el-option v-for="item in userTypes" :key="item.key" :label="item.display_name" :value="item.key" />
            </el-select>
            <date-range-picker valueFormat="yyyyMMdd" v-model="query.createTime" class="date-item" />
            <el-select v-model="query.enabled" clearable size="small" placeholder="状态" class="filter-item"
              style="width: 90px" @change="crud.toQuery">
              <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name"
                :value="item.key" />
            </el-select>
            <rrOperation />
          </div>
          <crudOperation show="" :permission="permission" />
        </div>
        <!--表单渲染-->
        <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
          :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="570px">
          <el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="68px">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" @keydown.native="keydown($event)" :disabled="form.id" />
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model.number="form.phone" :disabled="form.id" />
            </el-form-item>
            <!-- <el-form-item label="昵称" prop="nickName">
              <el-input v-model="form.nickName" @keydown.native="keydown($event)" />
            </el-form-item> -->
            <el-form-item label="邮箱" prop="email" style="margin-right:30px;">
              <el-input v-model="form.email" :disabled="form.id" />
            </el-form-item>
            <el-form-item label="数据范围" prop="dataScope">
              <el-select v-model="form.dataScope" style="width: 178px" placeholder="请选择数据范围" @change="changeScope">
                <el-option v-for="item in dateScopes" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="ismgmtuser && branchcode == 9999" style="margin-bottom: 0;" label="" prop="depts"
              v-show="form.dataScope === '市场'">
              <el-select v-model="deptList" style="width: 200px" multiple placeholder="请选择" @remove-tag="deleteDepts"
                @change="changeDepts">
                <el-option v-for="item in depts" :key="item.name" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item v-else label="" prop="dept.id" v-show="form.dataScope === '市场'">
              <treeselect v-model="form.dept.id" :options="depts" :load-options="loadDepts" style="width: 245px"
                placeholder="选择市场"  noChildrenText="暂无数据" />
            </el-form-item>
            <el-form-item label="" prop="datastpl[0]" v-show="form.dataScope === '自定义'">
              <el-select v-if="form.dataScope === '自定义'" v-model="form.datastpl[0].id" style="width: 178px"
                placeholder="请选择数据集" @change="changeDataScope">
                <el-option v-for="item in dataPartScopes" :key="item" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="岗位" prop="jobs">
              <el-select v-model="jobDatas" style="width: 178px" multiple placeholder="请选择" @remove-tag="deleteTag"
                @change="changeJob">
                <el-option v-for="item in jobs" :key="item.name" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="性别">
              <el-radio-group v-model="form.gender" style="width: 178px">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item> -->
            <el-form-item label="状态">
              <el-radio-group v-model="form.enabled" :disabled="form.id === user.id">
                <el-radio v-for="item in dict.user_status" :key="item.id" :label="item.value">{{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item style="margin-bottom: 0;" label="角色" prop="roles">
              <el-select v-model="roleDatas" style="width: 437px" multiple placeholder="请选择" @remove-tag="deleteTag"
                @change="changeRole">
                <el-option v-for="item in roles" :key="item.name" :disabled="level !== 1 && item.level <= level"
                  :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>

          <div slot="footer" class="dialog-footer" style="padding-top:90px">
            <el-button type="text" @click="crud.cancelCU">取消 123</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
          </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :data="crud.data" style="width: 100%;"
          @selection-change="crud.selectionChangeHandler">
          <!-- <el-table-column :selectable="checkboxT" type="selection" width="55" /> -->
          <el-table-column :show-overflow-tooltip="true" prop="username" label="用户名" />
          <el-table-column :show-overflow-tooltip="true" prop="nickName" label="姓名" />

          <!-- <el-table-column :show-overflow-tooltip="true" prop="nickName" label="昵称" /> -->
          <!-- <el-table-column prop="gender" label="性别" /> -->
          <!-- <el-table-column :show-overflow-tooltip="true" prop="phone" width="100" label="电话" /> -->
          <!-- <el-table-column :show-overflow-tooltip="true" prop="email" width="150" label="邮箱" /> -->
          <el-table-column :show-overflow-tooltip="true" prop="dept" label="数据范围">
            <template slot-scope="scope">
              <div>{{ scope.row.datastpl.length != 0 ? scope.row.datastpl[0]['name'] : scope.row.dept.name }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="enabled">
            <template slot-scope="scope">
              <div v-if="scope.row.enabled">激活</div>
              <div v-else>禁用</div>
              <!-- <el-switch
                v-model="scope.row.enabled"
                :disabled="user.id === scope.row.id"
                active-color="#409EFF"
                inactive-color="#F56C6C"
                @change="changeEnabled(scope.row, scope.row.enabled)"
              /> -->
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" prop="createdAt" width="135" label="创建日期" />
          <el-table-column v-if="checkPer([ 'perm.admin.user.edit', 'perm.admin.user.del'])" label="操作" width="115" align="center"
            fixed="right">
            <template slot-scope="scope">
              <udOperation :disabled-dle="scope.row.data_range == 9 || scope.row.id === user.id" :data="scope.row"
                :permission="permission" :disabledEdit="scope.row.data_range == 9" />
            </template>
          </el-table-column>
          <!-- <el-table-column v-if="checkPer(['admin', 'user:edit', 'user:del'])" label="操作" width="115" align="center"
            fixed="right">
            <template slot-scope="scope">
              <udOperation :disabled-dle="scope.row.data_range == 9 || scope.row.id === user.id" :data="scope.row"
                :permission="permission" :disabledEdit="scope.row.data_range == 9" />
            </template>
          </el-table-column> -->
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import crudUser from '@/api/system/user'
import { getTpls } from '@/api/system/dataTpls'
import { datas_filter_cond } from "@/api/commonInterface";
import { isvalidPhone } from '@/utils/validate'
import { getDepts, getDeptSuperior } from '@/api/system/dept'
import { simple_get_shop_struct, get_shop_count } from '@/api/systemCenter/structure'
import { get_mgmtuser_branchlist } from '@/api/device/device'
import { getAll, getLevel } from '@/api/system/role'
import { getAllJob } from '@/api/system/job'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import Treeselect from '@riophae/vue-treeselect'
import { mapGetters } from 'vuex'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect'
let userRoles = []
let userJobs = []
let deptList = []
const defaultForm = { id: null, username: null, nickName: null, gender: '男', email: null, enabled: 'false', dataScope: '市场', datastpl: [{ id: null }], roles: [], jobs: [], dept: { id: null }, phone: null, fromtype: '', depts: [{ id: null }] }
export default {
  name: 'User',
  components: { Treeselect, crudOperation, rrOperation, udOperation, pagination, DateRangePicker },
  cruds() {
    return CRUD({ title: '用户', url: 'api/users', crudMethod: { ...crudUser }, query: { "fromtype": 1 } })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['user_status'],
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        // callback(new Error('请输入电话号码'))
        callback()
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    return {
      height: document.documentElement.clientHeight - 180 + 'px;',
      deptName: '', depts: [], deptDatas: [], jobs: [], level: 3, roles: [],
      jobDatas: [], roleDatas: [], deptList: [],// 多选时使用
      defaultProps: { children: 'children', label: 'name', isLeaf: 'leaf' },
      dateScopes: ['市场', '自定义'],
      dataPartScopes: [{ "name": '上海运营', "id": 1 }, { "name": '西安运营店铺集合', "id": 2 }],
      dataScope: "",
      ismgmtuser: localStorage.getItem('ismgmtuser'),
      branchcode: localStorage.getItem('branchcode'),
      permission: {
        add: ['admin', 'user:add'],
        edit: ['admin', 'user:edit'],
        del: ['admin', 'user:del']
      },
      enabledTypeOptions: [
        { key: 'true', display_name: '激活' },
        { key: 'false', display_name: '禁用' }
      ],
      userTypes: [
        { key: 21, display_name: '营运架构员工' },
        { key: 1, display_name: '非营运架构员工' }
      ],
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
          { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { required: false, trigger: 'blur', validator: validPhone }
        ]
      },
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.crud.msg.add = '新增成功';

  },
  mounted: function () {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
    this.getSelectDataList()
  },
  methods: {
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "GroupTreeUsers", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then((res) => {
        this.operatingMarketList = res.data[0][1]; //运营市场下拉数据
        this.storeTypeList = res.data[0][2]; //门店类型下拉数据
        this.itMarketList = res.data[0][3]; // IT市场下拉数据
        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
        console.log(res.data[0]);
      });
    },
    // 禁止输入空格
    keydown(e) {
      if (e.keyCode === 32) {
        e.returnValue = false
      }
    },
    changeDataScope() {
      console.log("this.form.dataScopes", this.form.dataPartScope)
      if (this.form.dataScope === '自定义') {
        // this.getDepts()
        //todo 自定义需要重新按店铺选
      }
    },
    changeScope() {
      if (this.form.dataScope === '自定义') {
        if (this.form.datastpl.length == 0) {
          this.form.datastpl = [{ "id": null }]
        }
        this.getDepts()
        //todo 自定义需要重新按店铺选
      }
    },
    changeRole(value) {
      userRoles = []
      value.forEach(function (data, index) {
        const role = { id: data }
        userRoles.push(role)
      })
    },
    changeDepts(value) {
      deptList = []
      value.forEach(function (data, index) {
        const role = { id: data }
        deptList.push(role)
      })
    },
    changeJob(value) {
      userJobs = []
      value.forEach(function (data, index) {
        const job = { id: data }
        userJobs.push(job)
      })
    },
    deleteDepts(value) {
      deptList.forEach(function (data, index) {
        if (data.id === value) {
          deptList.splice(index, value)
        }
      })
    },
    deleteTag(value) {
      userRoles.forEach(function (data, index) {
        if (data.id === value) {
          userRoles.splice(index, value)
        }
      })
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      this.getDataTpls()
      this.getRoles()
      if (form.id == null) {
        this.getDepts()
      } else {
        console.log(this.storeTypeList,'storeTypeList');
        this.getSupDepts(form.dept.id)

      }
      this.getRoleLevel()
      this.getJobs()
      form.enabled = form.enabled.toString()
    },
    // 新增前将多选的值设置为空
    [CRUD.HOOK.beforeToAdd]() {
      this.jobDatas = []
      this.roleDatas = []
      this.deptList = []
    },
    // 初始化编辑时候的角色与岗位
    [CRUD.HOOK.beforeToEdit](crud, form) {
      this.getJobs(this.form.dept.id)
      this.jobDatas = []
      this.roleDatas = []
      this.deptList = []
      userRoles = []
      userJobs = []
      deptList = []
      const _this = this
      form.roles.forEach(function (role, index) {
        _this.roleDatas.push(role.id)
        const rol = { id: role.id }
        userRoles.push(rol)
      })
      form.depts.forEach(function (role, index) {
        _this.deptList.push(role.id)
        const rol = { id: role.id }
        deptList.push(rol)
      })
      form.jobs.forEach(function (job, index) {
        _this.jobDatas.push(job.id)
        const data = { id: job.id }
        userJobs.push(data)
      })
    },
    // 提交前做的操作
    [CRUD.HOOK.afterValidateCU](crud) {
      if (!crud.form.dept.id && this.dataScope == "市场") {
        this.$message({
          message: '市场不能为空',
          type: 'warning'
        })
        return false
      } else if (this.jobDatas.length === 100) {
        this.$message({
          message: '岗位不能为空',
          type: 'warning'
        })
        return false
      } else if (this.roleDatas.length === 0) {
        this.$message({
          message: '角色不能为空',
          type: 'warning'
        })
        return false
      }
      crud.form.roles = userRoles
      crud.form.jobs = userJobs
      crud.form.depts = deptList
      return true
    },
    getDataTpls() {
      console.log("getTpls", getTpls)
      getTpls().then((res) => {
        if (res.rst == 'ok') {
          let data = res.data[0]
          this.dataPartScopes = data["content"]
        }
      })
    },
    // 获取左侧部门数据
    getDeptDatas(node, resolve) {
      this.simple_get_shop_struct()
      const sort = 'id,desc'
      const params = { sort: sort }
      if (typeof node !== 'object') {
        if (node) {
          params['name'] = node
        }
      } else if (node.level !== 0) {
        params['pid'] = node.data.id
      }
      setTimeout(() => {
        getDepts(params).then(res => {
          if (resolve) {
            resolve(res.content)
          } else {
            this.deptDatas = res.content
          }
        })
      }, 100)
    },
    simple_get_shop_struct(group_id, callback, parentNode) {
      let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''
      let params = {
        user_id: user_id,
        group_id: localStorage.getItem('group_id'),
        direction: 0,
        sel_unit: 'shop',
      }
      if (group_id) {
        if (group_id.charAt(0) == 'g') {
          group_id = parseInt(group_id.split('g')[1])
        }
        params["group_id"] = group_id
      }
      if (this.ismgmtuser == 1) {
        get_mgmtuser_branchlist({}).then(res => {
          if (res.rst == 'ok') {
            sessionStorage.setItem('level_type_value', JSON.stringify(res.data[0].structure[0]))
            // console.log('12343546999999', res.data[0].structure)
            this.onlyStr = res.data[0].structure
            // console.log('res.data[0].tree_data', this.onlyStr)
            let tree = []
            this.onlyStr.forEach(item => {
              let treeInfo = {
                hasChildren: item["children"],
                id: item["id"],
                label: item["text"],
                name: item["text"],
                pid: item["id"],
                leaf: false
              }
              if (treeInfo.hasChildren) {
                treeInfo.children = null
              }
              console.log("treeInfo", treeInfo)
              tree.push(treeInfo)
            })
            // hasChildren: true
            // id: 2
            // label: "研发部"
            // leaf: false
            // name: "研发部"
            // pid: 7

            // children: true
            // id: "g112716"
            // li_attr: {class: ""}
            // text: "星巴克"
            // type: "L1"

            if (callback) {
              parentNode.children = tree
              setTimeout(() => {
                callback()
              }, 200)
            } else {
              this.depts = tree
            }
          }
        })
      } else {
        simple_get_shop_struct(params).then((res) => {
          if (res.rst == 'ok') {
            sessionStorage.setItem('level_type_value', JSON.stringify(res.data[0].structure[0]))
            // console.log('12343546999999', res.data[0].structure)
            this.onlyStr = res.data[0].structure
            // console.log('res.data[0].tree_data', this.onlyStr)
            let tree = []
            this.onlyStr.forEach(item => {
              let treeInfo = {
                hasChildren: item["children"],
                id: item["id"],
                label: item["text"],
                name: item["text"],
                pid: item["id"],
                leaf: false
              }
              if (treeInfo.hasChildren) {
                treeInfo.children = null
              }
              console.log("treeInfo", treeInfo)
              tree.push(treeInfo)
            })
            // hasChildren: true
            // id: 2
            // label: "研发部"
            // leaf: false
            // name: "研发部"
            // pid: 7

            // children: true
            // id: "g112716"
            // li_attr: {class: ""}
            // text: "星巴克"
            // type: "L1"

            if (callback) {
              parentNode.children = tree
              setTimeout(() => {
                callback()
              }, 200)
            } else {
              this.depts = tree
            }
          }
        })
      }
      // simple_get_shop_struct(params).then((res) => {
      //   if (res.rst == 'ok') {
      //     sessionStorage.setItem('level_type_value',JSON.stringify(res.data[0].structure[0]))
      //     // console.log('12343546999999', res.data[0].structure)
      //     this.onlyStr = res.data[0].structure
      //     // console.log('res.data[0].tree_data', this.onlyStr)
      //     let tree = []
      //     this.onlyStr.forEach(item=>{
      //       let treeInfo = {
      //         hasChildren: item["children"],
      //         id: item["id"],
      //         label: item["text"],
      //         name: item["text"],
      //         pid: item["id"],
      //         leaf: false
      //       }
      //       if (treeInfo.hasChildren) {
      //         treeInfo.children = null
      //       }
      //       console.log("treeInfo", treeInfo)
      //       tree.push(treeInfo)
      //     })
      //     // hasChildren: true
      //     // id: 2
      //     // label: "研发部"
      //     // leaf: false
      //     // name: "研发部"
      //     // pid: 7

      //     // children: true
      //     // id: "g112716"
      //     // li_attr: {class: ""}
      //     // text: "星巴克"
      //     // type: "L1"

      //     if(callback){
      //       parentNode.children = tree
      //       setTimeout(() => {
      //         callback()
      //       }, 200)
      //     }else{
      //        this.depts = tree
      //     }
      //   }
      // })
    },
    simple_get_shop_struct_child(ground_id, index, index2, index3) {
      console.log('这是第三个');
      let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''
      let params = {
        user_id: this.$store.state.user_id || user_id,
        group_id: ground_id,
        direction: 0,
        sel_unit: 'shop',
      }
      simple_get_shop_struct(params).then((res) => {
        if (res.rst == 'ok') {
          let structure = res.data[0].structure
          structure.forEach((item) => {
            item.children = []
            item.show = false
          })
          if (index !== undefined && index2 == undefined) {
            this.$emit('addBrandOne', index)
            this.treeList[0].children[index].children = structure
            console.log(structure, 33535);
            this.treeData.treeTwo = structure
            if (this.group_type !== 2) {
              this.$emit('setAllProvince')
            }
          } else if (index2 !== undefined && index3 == undefined) {
            this.$emit('addBrandTwo', index2)
            this.treeList[0].children[index].children[index2].children =
              structure
            this.treeData.treeThree = structure
          } else if (index3 !== undefined) {
            this.$emit('addBrandThree', index3)
            this.treeList[0].children[index].children[index2].children[
              index3
            ].children = structure
          }
          this.$emit('getTreeData', this.treeData)
          window.console.log('tree data treeData', this.treeData)
          console.log('tree data 111', this.treeList)
        }
      })
    },
    getDepts() {
      this.simple_get_shop_struct()
      // getDepts({ enabled: true }).then(res => {
      //   this.depts = res.content.map(function(obj) {
      //     if (obj.hasChildren) {
      //       obj.children = null
      //     }
      //     return obj
      //   })
      // })
    },
    getSupDepts(deptId) {
      this.simple_get_shop_struct()
      // getDeptSuperior(deptId).then(res => {
      //   const date = res.content
      //   this.buildDepts(date)
      //   this.depts = date
      // })
    },
    buildDepts(depts) {
      depts.forEach(data => {
        if (data.children) {
          this.buildDepts(data.children)
        }
        if (data.hasChildren && !data.children) {
          data.children = null
        }
      })
    },
    // 获取弹窗内部门数据
    loadDepts({ action, parentNode, callback }) {
      if (action === LOAD_CHILDREN_OPTIONS) {

        // this.simple_get_shop_struct_child()
        this.simple_get_shop_struct(parentNode.id, callback, parentNode)
        // getDepts({ enabled: true, pid: parentNode.id }).then(res => {
        //   parentNode.children = res.content.map(function(obj) {
        //     if (obj.hasChildren) {
        //       obj.children = null
        //     }
        //     return obj
        //   })
        //   setTimeout(() => {
        //     callback()
        //   }, 200)
        // })
      }
    },
    // 切换部门
    handleNodeClick(data) {
      if (data.pid === 0) {
        this.query.deptId = null
      } else {
        this.query.deptId = data.id
      }
      this.crud.toQuery()
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.user_status[val] + '" ' + data.username + ', 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudUser.edit(data).then(res => {
          this.crud.notify(this.dict.label.user_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(() => {
          data.enabled = !data.enabled
        })
      }).catch(() => {
        data.enabled = !data.enabled
      })
    },
    // 获取弹窗内角色数据
    getRoles() {
      getAll().then(res => {
        this.roles = res["data"][0]["content"]
      }).catch(() => { })
    },
    // 获取弹窗内岗位数据
    getJobs() {
      getAllJob().then(res => {
        this.jobs = res["data"][0].content
      }).catch(() => { })
    },
    // 获取权限级别
    getRoleLevel() {
      //todo update level by api
      this.level == 1
      // getLevel().then(res => {
      //   this.level = res.level
      // }).catch(() => { })
    },
    checkboxT(row, rowIndex) {
      return row.id !== this.user.id
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .vue-treeselect__control,
::v-deep .vue-treeselect__placeholder,
::v-deep .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}
</style>
