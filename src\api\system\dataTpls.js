import { post, uploadFile } from '@/utils/request'

export function getTpls() {
  const params = {
    'classModel': 'DataRangeTpl',
    'sort': '', // 非必要，排序规则，id,desc
    'createdAtS': '', // 非必要，查询创建时间开始日期
    'createdAtE': '', // 非必要，查询创建时间结束日期
    'page': 0, // 起始页码,
    'size': 1000 // 每页数据量,
  }
  return post('dmb/api/json', params, 'get_adm_datas')
}

export function add(params) {
  const data = {
    'shop_list': [],
    'dataTplName': '广东店铺集合',
    'id': ''
  }
  return post('dmb/api/json', params, 'rolemenumgr')
}

export function getDataTpl(params) {
  return post('dmb/api/json', params, 'get_adm_datas')
}

export default { getTpls }
