<template>
    <div class="resource_audit">
        <div class="resource_audit_header">
            <el-tabs v-model="activeTabName" @tab-click="handleChangeTab">
                <el-tab-pane :label="item.label" v-for="item in activeViews" :name="item.name" :key="item.name">
                    <span slot="label"><i :class="item.icon"></i> {{ item.label }}</span>
                    <component :is="item.component" v-if="activeTabName == item.name" @setImgPreview="setImgPreview"
                        @setVideoPreview="setVideoPreview" :ref="item.name">
                    </component>
                </el-tab-pane>
            </el-tabs>
        </div>
        <!-- 预览mask -->
        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask'>
        </previsualization>
    </div>
</template>
  
<script>
import picturAudit from './components/pictur_audit.vue';
import videoAudit from './components/video_audit.vue';
import previsualization from "@/components/communal/previsualization";
export default {
    components: {
        picturAudit,
        videoAudit,
        previsualization
    },
    data() {
        return {
            activeTabName: "images",
            activeViews: [
                {
                    name: 'images',
                    component: picturAudit,
                    label: '图片',
                    icon: 'el-icon-picture'
                },
                {
                    name: 'videos',
                    component: videoAudit,
                    label: '视频',
                    icon: 'el-icon-video-camera'
                }
            ],
            isPreviewMaskShow: false,
            PreviewSrc: '',
            PreviewType: ''
        };
    },
    computed: {

    },
    watch: {},
    created() {
    },
    mounted() {

    },
    destroyed() {
    },
    methods: {
        handleChangeTab() {

        },
        // 预览
        setImgPreview(val) {
            this.PreviewType = "image";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val) {
            this.PreviewType = "video";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = "";
        },
    }
};
</script>
  
<style lang='scss' scoped>
* {
    box-sizing: border-box;
}

.resource_audit {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    /* padding: 0 20px; */
}

.resource_audit_header {
    box-sizing: border-box;
    width: 100%;
    position: relative;
}

.operation_wrap {
    position: absolute;
    right: 0;
    top: 0;
    height: 40px;
    // width: 260px;
    width: 315px;
    /* border: 1px solid blue; */
}

.operation_wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #e4e7ed;
    z-index: 1;
}

::v-deep .preview_content {
    position: absolute;
    /* background: #fff; */
    width: 1920px;
    height: 1080px;
    transform: translate(-50%, -50%) scale(0.5);
}
</style>





<style>
/* tabs选中的样式 */
.resource_audit .is-active {
    color: var(--text-color) !important;
    background-color: var(--active-color) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.resource_audit #tab-images {
    padding: 0 20px !important;
}

.resource_audit #tab-videos {
    padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.resource_audit .el-tabs__active-bar {
    /* display: none; */
    background-color: var(--text-color) !important;
    width: 20% !important;
}

/* tabs鼠标移入样式 */
.resource_audit .el-tabs__item:hover {
    color: var(--text-color) !important;
}

/* tabs取消下边距 */
.resource_audit .el-tabs__header {
    margin-bottom: 0 !important;
    width: calc(100% - 260px);
}

.el-tabs__nav-wrap::after {
    display: none !important;
}
</style>