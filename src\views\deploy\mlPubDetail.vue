<template>
  <div class="newDatafiled">
    <div class="header">
      <i class="el-icon-back cursor" @click="back"></i>
      <div style=" width:100%; text-align: center;">
        <span>当前数据汇总</span>
      </div>
    </div>
    <div class="pubDataTable">
      <el-table :data="pubTableData" border style="width: 100%;" v-loading="loadingTotal" :header-cell-style="{
        background: '#24b17d', color: '#fff', 
        'font-size': '13px',
        'text-align': 'center',
      }" :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="name" label="发布名称" width=""></el-table-column>
        <!-- <el-table-column prop="storenum" label="门店数" width=""></el-table-column> -->
        <el-table-column prop="pre_pubscrcnt" label="屏幕数" width=""></el-table-column>
        <el-table-column prop="pre_pub_sche_cnt" label="内容数" width=""></el-table-column>
        <el-table-column prop="pub_ok_sche_cnt" label="下发数" width=""></el-table-column>
        <el-table-column prop="detail.notify_cnt" label="已通知终端数" width=""></el-table-column>
        <el-table-column prop="detail.api_cnt" label="屏幕获取数" width=""></el-table-column>
        <el-table-column prop="detail.download_cnt" label="屏幕下载数" width=""></el-table-column>
        <el-table-column prop="detail.play_cnt" label="屏幕播放数" width=""></el-table-column>
        <el-table-column prop="detail.pub_err_shop_cnt" label="门店失败数" width=""></el-table-column>
        <el-table-column prop="detail.pub_err_screen_cnt" label="屏幕失败数" width=""></el-table-column>
      </el-table>
    </div>
    <div style="background-color:rgba(229, 229, 229, 1); width:100%;height:11px; margin-top:20px;margin-bottom:20px;">
    </div>
    <div style="
        height: 56px;
        line-height: 56px;
        font-weight: bold;

        display: flex;
        align-items: center;
        justify-content: space-between;
      ">
      <div class="search">
        <el-form :inline="true" :model="queryList" class="demo-form-inline">
          <el-form-item>
            <span style="color:rgba(91, 91, 91, 1);margin-left:20px">查询类型:</span>
          </el-form-item>

          <el-form-item>
            <el-select v-model="queryList.marketname" clearable filterable placeholder="营运市场" size="small"
              style="width: 186px; margin-right: 12px">
              <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]" :value="item[0]">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input placeholder="请输入门店编号/名称" size="small" prefix-icon="el-icon-search"
              @keyup.enter.native="handleSearch" v-model="queryList.search" style="width: 186px; margin-right: 12px">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="Search" class="searchButton">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="right_export">
        <span @click="exportToExcel('all')">Excel导出全部</span>
      </div> -->
    </div>
    <!-- 列表 -->
    <div>
      <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
        style="width: 100%" v-loading="loading" :header-cell-style="{
          background: '#24b17d', color: '#fff', 
          'font-size': '13px',
          'text-align': 'center',
        }" :cell-style="{ 'text-align': 'center' }">
        <el-table-column prop="store_code" label="门店编号" width=""></el-table-column>
        <el-table-column prop="store_name" label="门店名称" width=""></el-table-column>
        <el-table-column prop="marketname" label="营运市场" width=""></el-table-column>
        <el-table-column prop="pub_screen_cnt" label="下发终端量" width=""></el-table-column>
        <el-table-column prop="pub_err_screen_cnt" label="失败终端量" width=""></el-table-column>
        <el-table-column :show-overflow-tooltip="true" label="操作" width="">
          <template slot-scope="scope">
            <div style="justify-content: center; display: flex;">
              <div class="event" style="margin-right: 10px;">
                <span @click="handleClick(scope.row)"> 查看 </span>
              </div>
              <!-- <div class="event" v-if="screen_error_cnt!==0">
                <span @click="Shiftout(scope.row)"> 重新下发 </span>
              </div> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--      分页-->
      <div class="block" style="float: right;margin-top: 20px;  ">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage3" :page-size="queryList.pageSize"
          layout="total,sizes,prev,pager, next, jumper" :total="totalNum" :page-sizes="[10, 20, 50, 100]" background>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { add_upt_del_data, get_data_detail } from "@/api/datafiled/datafiled";
import { get_adm_datas } from "@/api/shopManage/shop";
import { datas_filter_cond } from "@/api/commonInterface";
import { get_btpub_perform_status, get_btpub_shop_perform_list } from "@/api/quickHistory/quickHistory"

export default {
  components: {
  },
  data() {
    return {
      NewFormData: {
        DataName: "",
      },
      pubTableData: [],  //发布数据
      tableData: [], //列表数据
      operatingMarketList: [], //运营市场下拉数据
      storeTypeList: [], //门店类型下拉数据
      itMarketList: [], //IT市场下拉数据
      allEquipmentList: [], //全部设备下拉数据
      queryList: {
        search: "", //店铺名
        marketname: "", //运营市场
        storetypename: "", //门店类型
        itmarketname: "", //IT市场
        allEquipment: "", //全部设备
        currentPage: 1,
        pageSize: 10
      },
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      loading: true,
      loadingTotal: true,
      // 总数
      totalNum: "",
      platform: ""
    };
  },
  methods: {
    back() {
      this.$router.go(-1);
    },
    handleClick(row) {
      console.log("row", row)
      this.$router.push({
        path: "/deploy/mlpushscreen",
        query: { btpub_id: this.$route.query.btpub_id, shop_id: row.shop_id, platform: this.platform },
      });
    },
    get_btpub_shop_perform_list() {
      const params = { "btpub_id": this.$route.query.btpub_id, "opsmarket": this.queryList.marketname, "blurry": this.queryList.search,"page_num":this.queryList.currentPage-1,"page_size":this.queryList.pageSize }
      get_btpub_shop_perform_list(params).then((res) => {
        console.log(res);
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;
          this.totalNum = res.data[0].totalElements;
          this.loading = false;
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    get_btpub_perform_status() {
      const params = { "btpub_id": this.$route.query.btpub_id }
      get_btpub_perform_status(params).then((res) => {
        console.log(res);
        if (res.rst == "ok") {
          this.pubTableData = res.data;
          this.loadingTotal = false
        } else {
          this.$message.warning(res.error_msg);
          this.loadingTotal = false
        }
      });
    },
    newSaveData() {
      const params = {
        act: "add",
        name: this.NewFormData.DataName,
        dataAct: "new",
      };
      add_upt_del_data(params).then((res) => {
        console.log(res);
        if (res.rst == "ok") {
          this.dataId = res.data[0].id;
          this.BatchState = false;
          this.$message.success("新增成功");
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 350 + "px";
      this.autoHeight.heightNum = windowHeight - 230;
    },
    // 获取列表
    // 获取门店列表数据
    getTableData() {
      this.loading = true;
      const params = {
        classModel: "GroupShop",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.queryList.currentPage - 1, //起始页码,
        size: this.queryList.pageSize, //每页数据量,
        blurry: this.queryList.search, //店铺名
        opsmarket: this.queryList.marketname, //运营市场
        storetype: this.queryList.storetypename, //门店类型
        itmarket: this.queryList.itmarketname, //IT市场
      };
      get_adm_datas(params).then((res) => {
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;
          this.totalNum = res.data[0].totalElements;
          this.loading = false;
        } else {
          console.log("失败");
        }
      });
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "GroupShop", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then((res) => {
        this.operatingMarketList = res.data[0][1]; //运营市场下拉数据
        this.storeTypeList = res.data[0][2]; //门店类型下拉数据
        this.itMarketList = res.data[0][3]; // IT市场下拉数据
        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
        console.log(res.data[0]);
        console.log(this.operatingMarketList, "operatingMarketList");
      });
    },
    // 分页操作
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.queryList.pageSize = val;
      this.get_btpub_shop_perform_list();
    },
    handleCurrentChange(val) {
      this.queryList.currentPage = val;
      this.get_btpub_shop_perform_list();
    },
    // 搜索
    Search() {
      this.loading = true;
      this.get_btpub_shop_perform_list();
    },
    // 获取详情信息
    getDataDetail() {
      const params = {
        id: this.$route.query.id,
        classModel: "ItemDataRangeTpl",
        page: this.queryList.currentPage - 1,
        size: this.queryList.pageSize,
      };
      get_data_detail(params).then((res) => {
        this.totalNum = res.data[0].totalElements;
        this.loading = false;
        this.DetaliData = res.data[0].item_datatpl_info;
        this.tableData = res.data[0].content;
      });
    },
    Shiftout(row) {
      this.$confirm("是否确认重新下发内容?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            act: "upt",
            // name:
            id: this.DetaliData.id,
            name: this.DetaliData.name,
            dataAct: "del",
            range_shops: [row.shop_id],
          };
          add_upt_del_data(params).then((res) => {
            if (res.rst == "ok") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataDetail();
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消重新下发内容",
          });
        });
    },
    // 取消弹框
    handleClose() {
      this.editDialogState = false;
    },
    // 保存
    requestEditData(e) {
      const params = {
        act: "upt",
        name: e,
        id: this.Did,
        dataAct: "upt"
      }
      add_upt_del_data(params).then(res => {
        console.log(res);
        if (res.rst == "ok") {
          this.$message.success("修改成功");
          this.editDialogState = false;
          this.getDataDetail()
        } else {
          this.$message.warning(res.error_msg)
        }
      })
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.get_btpub_perform_status()
    this.get_btpub_shop_perform_list()
    this.getHeight();
    this.getSelectDataList()
    this.platform = this.$route.query.platform
    // this.getSelectDataList();
    // this.type = this.$route.query.type;
    // if (this.type == "new") {
    //   this.getTableData();
    // } else if (this.type == "edit") {
    //   this.Did = this.$route.query.id;
    //   this.getDataDetail();
    // }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style lang="scss" scoped>
.newDatafiled {
  padding: 0 25px 0 24px;

  .header {
    height: 52px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(229, 229, 229, 1);

    i {
      font-size: 24px;
      color: rgba(108, 178, 255, 1);
    }

    span {
      font-size: 14px;
      font-weight: bold;
      margin-left: 5px;
      padding-top: 3px;
    }
  }

  .newForm {
    height: 88px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(229, 229, 229, 1);

    .content {
      width: 480px;
      display: flex;

      button {
        width: 72px;
        height: 40px;
        color: rgba(255, 255, 255, 1);
        background-color: rgba(108, 178, 255, 1);
        font-size: 14px;
        border-radius: 0 6px 6px 0px;
      }
    }

    ::v-deep .el-input__inner {
      height: 40px !important;
      border-radius: 0;
    }
  }

  .search {
    height: 77px;
    margin-top: 25px;

    .searchButton {
      width: 88px;
      height: 31px;
      color: rgba(80, 80, 80, 1);
      background-color: var(--text-color);
      font-weight: bold;
      color: #fff;
    }
  }

  .bottom {

    // float: right;
    .batch {
      button {
        color: #fff;
        background-color: rgba(39, 177, 126,1);
        font-size: 14px;
      }
    }

    display: flex;
    justify-content: space-between;
  }
}

.addButton {
  width: 106px;
  height: 32px;
  color: #fff;
  background-color: var(--text-color);
  margin-right: 12px;
}

.event {
  span {
    cursor: pointer;
    color: rgba(42, 130, 228, 1)
  }
}

.right_export {
  width: 116px;
  height: 40px;
  display: flex;
  align-items: center;
  border: 1px solid var(--background-color);
  border-radius: 5px;

  span {
    display: inline-block;
    box-sizing: border-box;
    width: 106px;
    height: 100%;
    line-height: 40px;
    color: var(--background-color);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
  }

  :hover {
    color: rgba(212, 48, 48, .7);
  }
}
</style>