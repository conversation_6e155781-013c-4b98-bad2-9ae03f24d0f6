<template>
  <div class="ele">
    <el-input
      placeholder="请输入尺寸"
      class="search"
      prefix-icon="el-icon-search"
      v-model="searchValue"
    >
    </el-input>
    <!-- tab图片、视频 -->
    <div class="twoTabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="局部" name="first" class="part">
          <div class="tabBox">
                <ul class="lists">
                   <li v-for="(item, index) in lists" :key="index">{{ item }}</li>
              </ul>
          </div>
          <p class="el-icon-arrow-right" @click="nextList()"></p>
          <div class="recent">
            <h4>最近使用</h4>
            <ul>
              <li>
                <img src="../../assets/img/home_img/specialRec.png" />
              </li>
            </ul>
          </div>
          <div class="recommend">
            <h4>推荐</h4>
            <ul>
              <li v-for="(item, index) in 10" :key="index">
                <img src="../../assets/img/home_img/rotateLove.gif" />
              </li>
            </ul>
          </div>
        </el-tab-pane>
        <el-tab-pane label="全屏" name="second" class="part">
          <div class="sourcesDiv">这是全屏</div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 最近使用 -->
  </div>
</template>

<script>
import $ from "jquery"
export default {
  name: "contentEle",
  data() {
    return {
      searchValue: "",
      lists: [
        "圣诞",
        "新年",
        "爱心",
        "雪花",
        "春天",
        "巴拉巴拉",
        "呱呱呱",
        "哈哈哈哈",
        "哈哈哈",
        "嘟嘟嘟",
        "啦啦啦",
        "一大堆",
        "没尽头",
        "怎么办",
        "估计是半成半不成"
      ],
      activeName: "first",
      speed:0,
    };
  },
  methods: {
    //   tab切换按钮
    nextList() {
      this.speed = -60;
      // console.log($(".part .lists li").css("transform","translateX("+this.speed+"px"))
      $(".part .lists li").nextElementSibling().css("transform','translateX('+this.speed'+px)')");
    },
  },
};
</script>

<style scoped>
.ele {
  padding: 20px 15px;
}

.ele > .search {
  margin: 10px 0 20px;
}

.ele ::v-deep .el-tabs__content {
  width: 292px !important;
}

.ele > .search ::v-deep .el-input__inner {
  background: rgba(56, 56, 56, 1);
}

/* 局部  全屏 */
.ele > .twoTabs ::v-deep .el-tabs {
  flex-direction: column;
}

.ele > .twoTabs ::v-deep #tab-first,
.twoTabs ::v-deep #tab-second {
  color: #fff;
  font-size: 18px;
}

.ele > .twoTabs ::v-deep .el-tabs__nav-wrap::after {
  width: 0;
  height: 0;
}
.ele > .twoTabs .part .tabBox{
  width: 280px;
  overflow: hidden;
  white-space: nowrap;
}
.ele > .twoTabs .part .lists {
  list-style: none;
  height: 72px;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(72, 69, 69, 1);
}

.ele > .twoTabs .part .lists > li {
  width: 46px;
  height: 45px;
  color: rgba(229, 229, 229, 1);
  background-color: rgba(53, 53, 53, 1);
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 9px;
  font-size: 14px;
  text-align: center;
  margin: 10px 5px;
  line-height: 45px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  transform: translateX(0px);
}

.ele > .twoTabs .part  .el-icon-arrow-right {
  margin-top: 20px;
  font-size: 20px;
  cursor: pointer;
  position: absolute;
  top: 0;
  right: -3px;
}

.recommend > ul {
  overflow: auto;
  height: 192px;
}

.recent > h4,
.recommend > h4 {
  color: rgba(243, 243, 243, 1);
  font-size: 16px;
  text-align: left;
  margin: 10px 0;
}

.recent > ul,
.recommend > ul {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
}

.recent > ul > li {
  width: 100px;
  height: 100px;
  cursor: pointer;
}

.recommend > ul > li {
  width: 70px;
  height: 70px;
  margin: 9px;
  cursor: pointer;
}

.recent > ul > li > img,
.recommend > ul > li > img {
  width: 100%;
  height: 100%;
}

.recommend ::v-deep .tag_set .is-active {
  color: var(--text-color) !important;
}
</style>
