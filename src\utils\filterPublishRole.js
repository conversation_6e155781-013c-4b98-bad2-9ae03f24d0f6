import store from '@/store/index'
const filterPublishRole = function(value) {
    const filterArray = ['publistState', 'filterPublish']
    if (value && value instanceof Array && value.length > 0) {
        // const roles = store.getters && store.getters.roles;
        // roles = [...store.getters, ...filterArray]
        const roles = [...filterArray]
        const permissionRoles = value
        console.log(roles, 'roles')
        return roles.some(role => {
            if (role == 'admin') {
                return true
            } else {
                return permissionRoles.includes(role)
            }
        })
    } else {
        console.error(`need roles! Like v-permission="['admin','editor']"`)
        return false
    }
}

export {
    filterPublishRole
}
