<template>
  <div class='useShortEdit'>
    <div class='pageHead'>
      <span class='el-icon-back' @click.stop='goBack'></span>
      323上海上新
    </div>
    <el-button type="primary">播放编辑</el-button>
    <el-button type="primary">新增内容</el-button>
    <ul>
      <li>
        <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
        <div>内容类型：图片</div>
        <div>内容时长：10s</div>
        <div>播放顺序：1</div>
      </li>
      <li>
        <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
        <div>内容类型：图片</div>
        <div>内容时长：10s</div>
        <div>播放顺序：1</div>
      </li>
      <li>
        <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
        <div>内容类型：图片</div>
        <div>内容时长：10s</div>
        <div>播放顺序：1</div>
      </li>
      <li>
        <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
        <div>内容类型：图片</div>
        <div>内容时长：10s</div>
        <div>播放顺序：1</div>
      </li>
      <li>
        <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
        <div>内容类型：图片</div>
        <div>内容时长：10s</div>
        <div>播放顺序：1</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'UseShortEdit',
  methods: {
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>

<style scoped>
.useShortEdit{
  width: 100%;
  padding: 0 14px 0 13px;
  background-color: #f8f7f7;
}
.pageHead{
  height: 58px;
  line-height: 58px;
  font-size: 14px;
  color: #505050;
  font-weight: bold;
  border-bottom: 1px solid #ecebeb;
}
.el-icon-back{
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}
button{
  position: absolute;
  right: 110px;
  margin: 15px 15px;
}
.el-button--primary{
 right: 0;
}
ul{
  margin-top: 70px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 14px;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  list-style: none;
  padding: 10px 10px;
  flex-wrap: wrap;
  display: flex;
  align-content: flex-start;
}
ul>li{
  width: 292px;
  height: 284px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 15px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin:  10px 9px;
}
ul>li>img{
  width: 284px;
  height: 157px;
  margin: 5px 0;
}
ul>li>div{
  margin-top: 12px;
  color: rgba(80, 80, 80, 1);
  font-size: 12px;
  text-align: left;
  margin-left: 12px;
  font-weight: bold;
}
</style>
