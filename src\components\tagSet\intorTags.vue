<template>
  <div class="into" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <div class="shop_tags_title flex">
      <div>标签选择方式</div>
      <div class="add_btn" @click="addTags">+ 新增标签</div>
    </div>
    <!-- 列表区 -->
    <el-table header-row-class-name="intorTable" :data="tableData" :height="autoHeight.height"
      @selection-change="handleSelectionChange" :header-cell-style="{
        background: '#24b17d', color: '#fff', 
        'font-size': '13px',
        paddingLeft: '5px !important',
      }" ref="multipleTable" tooltip-effect="dark">
      <el-table-column type="selection" style="margin-left: 20px" width="55">
      </el-table-column>
      <el-table-column prop="tags_name" label="标签名称" width="" show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span class="table_cell_tags_name">{{ scope.row.value }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <!-- 暂时隐藏功能 -->
          <!-- <el-button
            @click.native.prevent="handleEdit(scope.row, tableData)"
            type="text"
            style="color: rgba(80, 80, 80, 1)"
            size="small"
            >修改</el-button
          > -->
          <el-button @click.native.prevent="handleDelete(scope.row, tableData)" type="text"
            style="color: var(--text-color)" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部页码区 -->

    <div class="shop_tags_footer">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
      </el-pagination>
    </div>
    <!-- 批量标签 -->
    <el-dialog :visible.sync="tagsDialogVisible" :close-on-click-modal="false" title="新增标签" width="520px"
      custom-class="batch_tags_dialog" :before-close="handleClose">
      <div style="display: flex; align-items: center; padding: 10px 0 40px">
        <el-form :model="newForm" label-width="85px" ref="newForm">
          <el-form-item label="新增标签:" prop="newInputTags" :rules="[
            { required: true, message: '请输入标签名称', trigger: 'blur' },
          ]">
            <el-input placeholder="请输入标签名称" v-model="newForm.newInputTags" maxlength="40" show-word-limit
              style="display: inline-block; width: 230px; ">
            </el-input>
          </el-form-item>

        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleAdd('newForm')" :loading="loadingbtn">确 定</el-button>
      </span>
    </el-dialog>

      <el-dialog :visible.sync="deleteDialogState" :close-on-click-modal="false" custom-class="batch_tags_dialog"
      width="400px">
      <p class="icon">
        <i class="el-icon-warning-outline"></i>
        <span>删除该标签</span>
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="CloseDeleteDialog">取 消</el-button>
        <el-button type="primary" @click="requestDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  get_adm_datas,
  get_screen_tags,
  create_or_delete_intorTags,
} from "@/api/system/label";
  // get_adm_datas,

  // get_adm_datas
export default {
  components: {},
  data() {
    return {
      loading: true,
      currentPage: 1, //页码
      pageSize: 10,
      totalNum: 0, //总数据数量
      tableData: [], //列表数据
      newInputTags: "",
      TagsForm: {
        // 新增表单
        name: "",
        act: "add",
        classModel: "SysShopMenuLabel"
      },
      timer: null,
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      tagsDialogVisible: false, //批量标签弹框
      deleteDialogState:false,
      activeName: "relation",
      newForm: {
        newInputTags: ""
      },
      loadingbtn:false
    };
  },
  computed: {},
  watch: {},
  created() {
    window.addEventListener("resize", this.getHeight);
    this.pageNum = this.pageSize;
    this.getHeight();
    // this.get_adm_datas();
    this.get_adm_datas()
  },
  mounted() { },
  methods: {
    // 获取列表数据
    get_adm_datas() {
      this.loading = true;
      const params = {
        classModel: "SysShopMenuLabel",
        page: this.currentPage - 1, //（int）页码
        size: this.pageSize, //（int）每页显示的数量
      };
      get_adm_datas(params).then((res) => {
        // console.log(res,'get_adm_datasres的');
        this.tableData = res.data[0].content;
        this.totalNum = Number(res.data[0].totalElements);
        this.loading = false;
      });
    },
    // 批量标签弹框确定按钮
    handleAdd(newForm) {
      console.log(this.TagsForm,'this.TagsForm');
      this.TagsForm.name = this.newForm.newInputTags;

      this.$refs[newForm].validate((valid) => {
        if (valid) {
          this.loadingbtn = true;
          if (this.TagsForm.name == "") {
          } else {
            create_or_delete_intorTags(this.TagsForm).then((res) => {
              if (res.rst == "ok") {
                console.log(res,'intor的res');
                if (this.timer) return;
                this.timer = setTimeout(() => {
                  this.timer = null;
                }, 2000);
                if (
                  this.tableData.filter((item) => item == this.TagsForm.name)
                    .length > 0
                ) {
                  if (!this.timer) return;
                  this.timer = setTimeout(() => {
                    this.timer = null;
                  }, 2000);
                  this.$message.warning("已有该标签，请重新新增标签");
                  this.newForm.newInputTags = "";
                } else {
                  this.$message.success("新增成功");
                  this.loadingbtn = false;
                  this.get_adm_datas();
                  this.handleClose();
                }
              } else {
                this.$message.warning("异常错误");
                this.loadingbtn = false;
                this.handleClose();
              }
            });
          }
        }else{
          
        }
      })

    },
    // 批量标签弹框取消按钮关闭弹框
    handleClose() {
      this.newForm.newInputTags = "";
      this.tagsDialogVisible = false;
    },
    // 列表编辑
    handleEdit(row) {
      console.log(row);
      this.$message.success("列表编辑");
    },
    // 列表删除
    handleDelete(row, item) {
      // console.log(row, item);
      // this.$message.warning("列表删除");
        this.TagsForm.name = row.name;
        this.TagsForm.act = "del";
        this.TagsForm.id = row.id;
        this.deleteDialogState = true;
        // const params = {
      //   shop_ids: ["60660"], // (List) 店铺id列表
      //   tags: ["yang_test"], // (List) 标签列表
      //   act: "del", // (String) 操作类型  新增:"add"; 删除:"del"
      //   shop_group_id: "104111" // (String) 店铺节点id
      // }
      // gshop_tags_mgmt(params).then(res=>{
      //   console.log(res)
      //   if(res.rst=='ok'){
      //     console.log(res,"删除")
      //   }
      // })
    },
    // 取消
    CloseDeleteDialog(){
      this.deleteDialogState = false;
    },
    requestDelete(){
        create_or_delete_intorTags(this.TagsForm).then((res) => {
        console.log(this.TagsForm, "删除");
        if (res.rst == "ok") {
          this.currentPage = this.currentPage;
          this.$message.closeAll()
          this.$message.success("删除成功");
          // this.get_adm_datas();
            this.get_adm_datas();
          if (this.tableData.length <= 0) {
            this.currentPage = 1;
            this.get_adm_datas();
            // this.get_adm_datas();
          }
          this.CloseDeleteDialog();
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 新增标签
    addTags() {
      this.tagsDialogVisible = true;
      // this.TagsForm.act = "adduser_tag";
    },
    //页码改变
    handleCurrentChange(val) {
      // this.$message.success(`现在是第${val}页`);
      this.currentPage = val;
      this.get_adm_datas();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.get_adm_datas();
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 208 + "px";
      this.autoHeight.heightNum = windowHeight - 233;
    },
    // 每页多少条
    handleChange(value) {
      this.pageSize = value;
      this.get_adm_datas();
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style scoped>
.into {
  padding: 0 20px;
}

.shop_tags_title {
  /* border: 1px solid red; */
  justify-content: space-between;
  align-items: center;
  height: 50px;
  font-size: 14px;
  padding-right: 5px;
}

.add_btn {
  background: var(--text-color);
  color: #fff;
  height: 32px;
  width: 106px;
  text-align: center;
  line-height: 32px;
  border-radius: 6px;
  cursor: pointer;
}

.add_btn:hover {
  background: rgba(211, 57, 57, 0.8);
}

.table_cell_tags_name {
  box-sizing: border-box;
  text-align: start;
  width: calc(100% - 24px);
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部页码区 */
.shop_tags_footer {
  box-sizing: border-box;
  width: 100%;
  height: 66px;
  font-size: 14px;
  padding-top: 17px;
  text-align: right;
  /* border: 1px solid red; */
}

/* 弹框 */
.relation_tags {
  display: flex;
  flex-wrap: wrap;
  height: 279px;
  margin-bottom: 20px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;
  font-size: 14px;
  border-radius: 24px;
  margin-right: 24px;
  margin-bottom: 18px;
  padding: 0 13px;
  border: 1px solid rgba(209, 209, 209, 1);
  cursor: pointer;
  /* 禁止文字选中 */
  /* -moz-user-select:none;
      -webkit-user-select:none;
      -ms-user-select:none;
      -khtml-user-select:none;
      user-select:none; */
}

.tag_active {
  color: rgba(108, 178, 255, 1);
  background-color: rgba(212, 232, 255, 1);
  border: 1px solid rgba(108, 178, 255, 1);
}
.icon{
  font-size: 18px;
  margin-top: 20px;
 
}
 .el-icon-warning-outline{
    color: rgba(255,170,0,1);
    font-weight: bold;
  }
.event {
  text-align: center;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.into .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.into .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px;
  /* border-radius: 50%; */
}

.into .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.into .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}

.into .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.batch_tags_dialog {
  margin-top: 0px !important;
  /* border-radius: 16px !important; */
}

.batch_tags_dialog .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
  font-size: 16px !important;
  font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
  margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 1px !important;
}

.into .batch_tags_dialog .el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
  background-color: #fff !important;
}

.batch_tags_dialog .el-button--primary {
  background: rgba(108, 178, 255, 1);
}

.into .el-tabs__header {
  margin-bottom: 0 !important;
  width: 100% !important;
}

.into .el-tabs__nav-scroll {
  padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .into .is-active {
  color: var(--text-color) !important;
  background-color: rgba(255, 255, 255, 0.3) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .into .el-tabs__nav {
  height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 0px !important;
}
</style>

