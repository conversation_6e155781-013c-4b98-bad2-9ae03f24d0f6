<template>
  <div class="flexParent flex hg-100">
    <div class="flex flexParent">
      <div class="flexBoxTwo pd-x-20">
        <div class="flexBoxTwoBox">
          <div class="flexBoxTwoTitle flex justify-between" >
            <div class="borderBottomCss">账号管理</div>
          </div>

          <div class="flex-1" style="padding-top: 6px;border-top:1px solid rgba(229, 229, 229, 1)">
            <div class="flex justify-between" style="line-height: 40px">
              <div class="flex-center justify-start">
                <div class="inputSearchParent position-relative" style="width: 260px">
                  <el-input  class="mg-right-10" size="small" v-model="user_nameSearch" @blur="user_nameSearch=$event.target.value.trim()"  style="width: 260px"
                    placeholder="请输入账号名称"></el-input>
                  <div class="
                      inputSearch
                      position-absolute
                      flex-center
                      cursor-pointer
                    " @click="get_g_role_user_list_fun_search">
                    <img src="../../assets/img/searchIcon.png" class="searchIconCss" alt="" />
                  </div>
                </div>
              </div>
              <div class="flexRight flex-center justify-end">
                <el-button v-show="group_type == 1" type="primary" size="small" style="height: 32px" @click="addAccountFun">+ 添加账号</el-button>
              </div>
            </div>
            <!-- 表格 start -->
            <div class="tableTopBorder" style="border-top: 1px solid #d7d7d7; margin-top: 5px" v-loading="loadingTree"
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)">
              <el-table :header-cell-style="{
                  background: '#EAF4FF',
                  color: '#666',
                  'font-size': '13px',
                }" :height="autoHeight.height" :data="tableData" style="width: 100%">
                <el-table-column prop="user_info" align="left" label="账号">
                  <template slot-scope="scope">
                    <div v-if="scope.row.user_info">
                      {{ scope.row.user_info.split(',')[1] }}
                    </div>
                    <div v-else></div>
                  </template>
                </el-table-column>
                <el-table-column prop="user_name" align="left" label="账号名称">
                </el-table-column>
                <el-table-column prop="admin_uid_type" width="200" align="left1" label="管理类型">
                  
                </el-table-column>

                <el-table-column prop="region" align="left" label="管理地区">
                </el-table-column>

                <el-table-column prop="datetime_to_format" align="left" label="注册时间">
                </el-table-column>
                <el-table-column prop="use_status" align="left" label="状态">
                </el-table-column>
                <el-table-column prop="name" align="center" label="操作" width="100">
                  <template slot-scope="scope">
                    <div class="flex justify-between">
                      <div class="opsBtn cursor-pointer" @click="editAccountFun(scope.row)">
                        编辑
                      </div>

                      <div class="opsBtn cursor-pointer cancelColor " @click="del_group_node_user_fun_(scope.row)">注销
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pd-10 flex justify-between" style="background: #fff">
                <div>
                  <el-button
                    type="primary"
                    size="small"
                    style="height: 32px"
                    @click="drawerFun"
                    >批量注销</el-button
                  >
                </div>
                <el-pagination background layout="prev, pager, next,total" @current-change="currentChangeFun"
                  @size-change="sizeChangeFun" :total="totalNum" :page-size="pageSize">
                </el-pagination>
              </div>
            </div>
            <!-- 表格 end -->
          </div>
        </div>
      </div>
    </div>
    <!-- 编辑门店 start -->
    <el-drawer title="我是标题" :visible.sync="editShopDrawer" :with-header="false">
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div class="flexBoxTwoTitle flex justify-between" style="align-items: center; border-bottom: 1px solid #ededed">
          编辑账号
          <img src="../../assets/img/closeIcon.png" class="closeCss cursor-pointer" @click="editShopDrawer = false"
            alt="" />
        </div>
        <div class="flex-1 overflowBoxCss">
          <!-- start -->
          <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select v-model="editAccountObj.regionOneValue" size="small" disabled style="width: 100px"
            placeholder="请选择" @change="change_regionOne_edit">
            <el-option v-for="item in regionOne" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>
          <el-select disabled v-model="editAccountObj.regionTwoValue" size="small" style="width: 100px"
            placeholder="请选择" @change="change_regionTwo_edit">
            <el-option v-for="item in regionTwo" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>
          <el-select disabled v-model="editAccountObj.regionThreeValue" size="small" style="width: 100px"
            placeholder="请选择" @change="change_regionThree_edit">
            <el-option v-for="item in regionThree" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>

          <!-- end -->
          
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            角色权限:
          </div>
          <div class="pd-left-10">
            <div class="font-smaller color-666">自定义：</div>
            <div>
              <el-checkbox-group v-model="addAccountObj.roleList_diy_check" @change="change_diy">
                <el-checkbox v-for="item in roleList_diy" :label="item.id" :value="item.id" :key="item.id + '4'">{{
                  item.text }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="font-smaller color-666 mg-top-5">系统默认：</div>
            <div>
              <el-checkbox-group v-model="addAccountObj.roleList_default_check" @change="change_default">
                <el-checkbox disabled v-for="item in roleList_default" :value="item.id" :label="item.id"
                  :key="item.id + '3'">{{ item.text }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            登录账号:
          </div>
          <el-input readonly class="" size="small" v-model="editAccountObj.user_phone" style="width: 80%"></el-input>
          <div class="drawerTitle mg-top-10"><span class="spanMust">*</span> 登录密码:</div>
          <div class="pd-left-10 flex" style="line-height: 32px">
            ******
            <div class="editBtn flex-center mg-left-30 cursor-pointer" style="width: 85px"
              v-show="!changePassword.showBol && group_type == 1" @click="changePassword.showBol = true">
              修改密码
            </div>
          </div>
          <!-- 修改密码 -->
          <div class="changePassword mg-10 pd-10" style="border: 1px solid #ececec; margin-top: 0; width: 300px"
            v-if="changePassword.showBol">
            <div class="text-center font-small mg-bottom-10">修改账号密码</div>
            <div class="flex">
              <div class="font-smaller" style="width: 60px; line-height: 32px">
                新密码：
              </div>

              <el-input class="" size="small" type="password" v-model="changePassword.password" style="width: 60%"
                placeholder="请输入新密码" @blur="checkPassword"></el-input>
            </div>
            <div class="flex">
              <div class="font-smaller" style="width: 60px; line-height: 32px"></div>
              <div class="spanMust font-smaller">{{ checkMes }}</div>
            </div>
            <div class="flex mg-top-10">
              <div class="font-smaller" style="width: 60px; line-height: 32px">
                确认密码：
              </div>

              <el-input class="" size="small" type="password" v-model="changePassword.new_password" style="width: 60%"
                placeholder="请确认密码"></el-input>
            </div>
            <div class="editBtn flex-center mg-top-10 cursor-pointer" style="width: 85px; margin-left:113px"
              @click="changePasswordRequest" v-show="!saveLoading">
              确认修改
            </div>
            <div class="editBtn flex-center mg-top-10" style="width: 85px; margin-left: 60px" v-show="saveLoading">
              修改中...
            </div>
          </div>

        </div>
        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto; margin-right: 10px">
            <el-button type="default" size="small" style="width: 80px" @click="editShopDrawer = false">取消</el-button>
            <el-button :disabled='!isCanClick' type="primary" size="small" style="width: 80px; height: 32px"
              @click="editAccountSave">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 编辑门店 end -->
    <!-- 添加角色 start -->
    <el-drawer title="我是标题" :visible.sync="drawerAddRole" :with-header="false">
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div class="flexBoxTwoTitle flex justify-between" style="align-items: center; border-bottom: 1px solid #ededed">
          添加账号
          <img src="../../assets/img/closeIcon.png" class="closeCss cursor-pointer" @click="drawerAddRole = false"
            alt="" />
        </div>
        <div class="flex-1 overflowBoxCss">
          <!-- start -->
          <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select v-model="regionOneValue" size="small" style="width: 100px" placeholder="请选择"
            @change="change_regionOne">
            <el-option v-for="item in regionOne" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>
          <el-select v-model="regionTwoValue" size="small" style="width: 100px" placeholder="请选择"
            @change="change_regionTwo">
            <el-option v-for="item in regionTwo" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>
          <el-select v-model="regionThreeValue" size="small" style="width: 100px" placeholder="请选择"
            @change="change_regionThree">
            <el-option v-for="item in regionThree" :key="item.id" :label="item.text" :value="item.id">
            </el-option>
          </el-select>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            门店角色权限:
          </div>
          <div class="pd-left-10">
            <div class="font-smaller color-666">自定义：</div>
            <div>
              <el-checkbox-group v-model="addAccountObj.roleList_diy_check">
                <el-checkbox v-for="item in roleList_diy" :label="item.id" :key="item.id + '4'">{{ item.text }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="font-smaller color-666 mg-top-5">系统默认：</div>
            <div>
              <el-checkbox-group v-model="addAccountObj.roleList_default_check">
                <el-checkbox disabled v-for="item in roleList_default" :label="item.id" :key="item.id + '3'">{{
                  item.text }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            用户名称
          </div>
          <el-input class="mg-right-10" size="small" v-model="addAccountObj.admin_name" style="width: 300px"
            placeholder="请输入名称"></el-input>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            登录账号:
          </div>
          <el-input class="" size="small" v-model="addAccountObj.signin_phone" style="width: 150px"
            placeholder="请输入手机号"></el-input>
          <el-input class="" size="small" v-model="addAccountObj.admin_email" style="width: 150px" placeholder="请输入邮箱">
          </el-input>

          <!-- end -->
          <div class="font-smaller color-999">手机号、邮箱至少填写一个</div>
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            密码:
          </div>
          <el-input class="mg-right-10" size="small" type="password" v-model="addAccountObj.signin_passwd"
            style="width: 300px" placeholder="请输入登录密码"></el-input>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            确认密码:
          </div>
          <el-input class="mg-right-10" size="small" type="password" v-model="addAccountObj.confirm_passwd"
            style="width: 300px" placeholder="请确认登录密码"></el-input>

          <!-- end -->
        </div>
        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <el-button :disabled='!isCanClick' type="primary" size="small" style="
              width: 80px;
              height: 32px;
              margin-left: auto;
              margin-right: 10px;
            " @click="addAccountSave">保存</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 添加角色 end -->
  </div>
</template>
<script>
  export default {
    data() {
      return {
        group_type: '',
        isCanClick: true,
        pageSize: 15,
        pageNumber: 1,
        searchRoleName: '',
        dataTree: [],
        regionOne: [],
        regionTwo: [],
        regionThree: [],
        regionOneValue: '',
        regionTwoValue: '',
        regionThreeValue: '',
        loadingTree: false,
        drawerAddRole: false,
        editShopDrawer: false,
        changePassword: {
          showBol: false,
          password: '',
          new_password: '',
        },
        data: [
          {
            id: 1,
            label: '一级 1',
            children: [
              {
                id: 4,
                label: '二级 1-1',
                children: [
                  {
                    id: 9,
                    label: '三级 1-1-1',
                  },
                  {
                    id: 10,
                    label: '三级 1-1-2',
                  },
                ],
              },
            ],
          },
          {
            id: 2,
            label: '一级 2',
            children: [
              {
                id: 5,
                label: '二级 2-1',
              },
              {
                id: 6,
                label: '二级 2-2',
              },
            ],
          },
          {
            id: 3,
            label: '一级 3',
            children: [
              {
                id: 7,
                label: '二级 3-1',
              },
              {
                id: 8,
                label: '二级 3-2',
              },
            ],
          },
        ],
        defaultProps: {
          children: 'children',
          label: 'label',
        },

        autoHeight: {
          height: '',
        },
        totalNum: 0,
        pageSize: 15,
        tableData: [

        ],
        user_nameSearch: '',
        roleList_diy: [],
        roleList_default: [],
        editAccountObj: {
          regionOneValue: '',
          regionTwoValue: '',
          regionThreeValue: '',
          req_node_id: '', //所创建或编辑管理员节点id,
          role_list: [], //---->管理员权限id列表

          signin_phone: '', //手机号,
          signin_passwd: '', //登录密码,
          admin_email: '', //邮箱,
          shop_name: '',
          admin_name: '',
        },
        addAccountObj: {
          req_node_id: '', //所创建或编辑管理员节点id,
          role_list: [], //---->管理员权限id列表
          user_name: '', //管理员名称,
          signin_phone: '', //手机号,
          signin_passwd: '', //登录密码,
          admin_email: '', //邮箱,
          roleList_diy_check: [],
          roleList_default_check: ['1796', '2409', '2396'],
          category: '',
          admin_name: '',
        },
      }
    },
    mounted() {
      this.group_type = parseInt(localStorage.getItem('group_type'))
      this.simple_get_shop_struct_fun()
      this.get_g_role_user_list_fun(this.pageSize, this.pageNumber)
      if (this.group_type == 2) {
        this.get_g_role_user_list_fun_role()
      } else {
        this.get_g_role_list_fun()
      }

    },
    methods: {
      get_g_role_user_list_fun_search() {
        this.get_g_role_user_list_fun(this.pageSize, this.pageNumber)
      },
      get_g_role_user_list_fun_role() {
        let params = {
          one_part_user: JSON.parse(
            localStorage.getItem('device_info')
          ).uid + ''
        }
        this.$api.get_g_role_user_list(params).then((res) => {
          if (res.rst == 'ok') {
            let data = res.data[0]
            data.forEach((item) => {

              if (item[4] == 1) {
                // 自定义
                let tempObj = {
                  id: item[0],
                  type: item[1],
                  text: item[2],
                  tiem: item[3],
                }
                this.roleList_diy.push(tempObj)
              } else {
                this.addAccountObj.roleList_default_check.push(item[0])
                // 系统默认
                let tempObj = {
                  id: item[0],
                  type: item[1],
                  text: item[2],
                  tiem: item[3],
                }
                this.roleList_default.push(tempObj)
              }
            })
            console.log(8888, this.roleList_diy, this.roleList_default)
          }
        })
      },
      del_group_node_user_fun_(val) {
        this.$confirm('确认注销?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.del_group_node_user_fun(val)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消注销',
            })
          })

      },
      del_group_node_user_fun(val) {
        console.log('val88888', val)
        let parmas = {
          user_id: val.user_info.split(',')[0]

        }
        this.loadingTree = true
        this.$api.del_group_node_user(parmas).then(res => {
          this.loadingTree = false
          if (res.rst == 'ok') {
            this.$message.success('注销成功')
            this.get_g_role_user_list_fun(this.pageSize, this.pageNumber)

          } else {
            this.$message.error('注销失败')
          }
        })
      },
      currentChangeFun(val) {
        this.pageNumber = val
        this.get_g_role_user_list_fun(this.pageSize, this.pageNumber)
      },
      change_default() { },
      addAccountSave() {
        this.addAccountObj.role_list =
          this.addAccountObj.roleList_diy_check.concat(
            this.addAccountObj.roleList_default_check
          )
        this.simple_perm_role_get_or_edit_fun('add')
      },
      addAccountFun() {
        this.drawerAddRole = true
        this.editAccountObj = {
          regionOneValue: '',
          regionTwoValue: '',
          regionThreeValue: '',
          req_node_id: '', //所创建或编辑管理员节点id,
          role_list: [], //---->管理员权限id列表

          signin_phone: '', //手机号,
          signin_passwd: '', //登录密码,
          admin_email: '', //邮箱,
          shop_name: '',
          admin_name: '',
        }
        // this.addAccountObj = {
        //   req_node_id: '', //所创建或编辑管理员节点id,
        //   role_list: [], //---->管理员权限id列表
        //   user_name: '', //管理员名称,
        //   signin_phone: '', //手机号,
        //   signin_passwd: '', //登录密码,
        //   admin_email: '', //邮箱,
        //   roleList_diy_check: [],
        //   roleList_default_check: [],
        //   category: '',
        //   admin_name: '',
        // }
        this.addAccountObj.req_node_id = ''
        this.addAccountObj.role_list = []
        this.addAccountObj.user_name = ''
        this.addAccountObj.signin_phone = ''
        this.addAccountObj.signin_passwd = ''
        this.addAccountObj.admin_email = ''
        this.addAccountObj.roleList_diy_check = []
        this.addAccountObj.category = ''
        this.addAccountObj.admin_name = ''


        this.regionOneValue = ''
        this.regionTwoValue = ''
        this.regionThreeValue = ''
      },
      getArrEqual(arr1, arr2) {
        let newArr = []
        for (let i = 0; i < arr2.length; i++) {
          for (let j = 0; j < arr1.length; j++) {
            if (arr1[j] === arr2[i]) {
              newArr.push(arr1[j])
            }
          }
        }
        return newArr
      },
      editAccountFun(val) {
        console.log('777777', val)
        this.editShopDrawer = true
        this.changePassword = {
          showBol: false,
          password: '',
          new_password: '',
        }
        this.editAccountObj = {
          admin_name: '',
          regionOneValue: '',
          regionTwoValue: '',
          regionThreeValue: '',
          user_name: '',
          user_phone: '',
          req_node_id: '',
        }
        // this.addAccountObj = {
        //   req_node_id: '', //所创建或编辑管理员节点id,
        //   role_list: [], //---->管理员权限id列表
        //   user_name: '', //管理员名称,
        //   signin_phone: '', //手机号,
        //   signin_passwd: '', //登录密码,
        //   admin_email: '', //邮箱,
        //   roleList_diy_check: [],
        //   roleList_default_check: [],
        //   category: '',
        // }

        this.addAccountObj.req_node_id = ''
        this.addAccountObj.role_list = []
        this.addAccountObj.user_name = ''
        this.addAccountObj.signin_phone = ''
        this.addAccountObj.signin_passwd = ''
        this.addAccountObj.admin_email = ''
        this.addAccountObj.roleList_diy_check = []
        this.addAccountObj.category = ''
        this.addAccountObj.admin_name = ''

        let roleList_diyId = []
        this.roleList_diy.forEach((item) => {
          roleList_diyId.push(item.id)
        })
        let roleList_defaultId = []
        this.roleList_default.forEach((item) => {
          roleList_defaultId.push(item.id)
        })
        // console.log(*********, roleList_diyId, roleList_defaultId)
        this.addAccountObj.roleList_diy_check = this.getArrEqual(
          roleList_diyId,
          val.roleIdList
        )
        this.addAccountObj.roleList_default_check = this.getArrEqual(
          roleList_defaultId,
          val.roleIdList
        )

        console.log('this.addAccountObj 666', this.addAccountObj.roleList_diy_check)
        //  this.addAccountObj = {
        //   roleList_diy_check: val.user_roles.split(','),
        //   roleList_default_check: val.user_roles.split(','),
        //  }
        this.editAccountObj = {
          admin_name: val.admin_uid_type,
          regionOneValue: val.region,
          regionTwoValue: val.province,
          regionThreeValue: val.city,
          user_name: val.user_name,
          user_phone: val.user_info.split(',')[1],
          req_node_id: val.user_info.split(',')[0],
        }

      },
      editAccountSave() {
        let tempArr = this.addAccountObj.roleList_diy_check.concat(
          this.addAccountObj.roleList_default_check
        )

        this.editAccountObj.role_list = []
        tempArr.forEach((item) => {
          if (this.editAccountObj.role_list.indexOf(item) == -1) {
            this.editAccountObj.role_list.push(item)
          }
        })

        this.simple_perm_role_get_or_edit_fun('edit')
      },
      change_regionThree(val) {
        console.log('市级id', val)
        this.addAccountObj.req_node_id = val
      },
      change_regionTwo(val) {
        this.regionTwo.forEach((item) => {
          if (val == item.id) {
            this.regionThree = item.children
            this.addAccountObj.req_node_id = item.id
          }
        })
      },
      changePasswordRequest() {
        let reg = /^(?=.*[0-9])(?=.*[a-zA-Z0-9])(.{8,30})$/
        let resg2 = new RegExp(
          "[`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？ ]"
        )
        if (!this.changePassword.password) {
          this.$message.error('请输入新密码')
        } else if (!reg.test(this.changePassword.password)) {
          this.$message.error('至少为8位由字母和数字组成的字符,包含字母大小写')
          this.checkMes = '至少为8位由字母和数字组成的字符,包含字母大小写'
        } else if (resg2.test(this.changePassword.password)) {
          this.$message.error('密码不能包含特殊字符')
          this.checkMes = '密码不能包含特殊字符'
        } else if (!this.changePassword.new_password) {
          this.$message.error('请确认密码')
        } else if (
          this.changePassword.password !== this.changePassword.new_password
        ) {
          this.$message.error('两次密码不一致')
        } else {
          this.reset_shop_password_fun()
        }
      },
      reset_shop_password_fun() {
        let params = {
          user_id: this.editAccountObj.req_node_id, //---->用户id
          // CurrPW: this.passwordObj.oldPassword,
          NewPW: this.changePassword.password,
          ConfirmPW: this.changePassword.new_password,
          InPutVerifyCode: '123456',
          myVerifyCode: '123456',
          root_flag: 1,
        }
        // console.log(params)
        // this.saveLoading = true
        this.$api.reset_shop_password(params).then((res) => {
          this.saveLoading = false
          if (res.rst == 'ok') {
            this.$message.success('修改成功')
            this.changePassword.showBol = false
            this.showChangeBox = false
          } else {
            this.$message.error('修改失败')
          }
        })
      },
      change_regionOne(val) {
        console.log(4444444, val)
        this.regionOne.forEach((item) => {
          if (val == item.id) {
            this.regionTwo = item.children
            this.addAccountObj.category = item.text
            this.addAccountObj.req_node_id = item.id
          }
        })
      },
      change_regionThree_edit(val) {
        console.log('市级id', val)
        this.editAccountObj.req_node_id = val
      },
      change_regionTwo_edit(val) {
        this.regionTwo.forEach((item) => {
          if (val == item.id) {
            this.regionThree = item.children
            this.editAccountObj.req_node_id = item.id
          }
        })
      },
      change_regionOne_edit(val) {
        console.log(4444444, val)
        this.regionOne.forEach((item) => {
          if (val == item.id) {
            this.regionTwo = item.children
            this.editAccountObj.category = item.text
            this.editAccountObj.req_node_id = item.id
          }
        })
      },
      get_g_role_list_fun() {
        let params = {
          // group_id:
          //   this.$store.state.group_id || localStorage.getItem('group_id'),

          pgid: this.$store.state.group_id || localStorage.getItem('group_id'),
          role_name: this.searchRoleName ? this.searchRoleName : '',
        }
        this.$api.get_g_role_list(params).then((res) => {
          if (res.rst == 'ok') {
            let data = res.data[0]
            data.forEach((item) => {

              if (item[4] == 1) {
                // 自定义
                let tempObj = {
                  id: item[0],
                  type: item[1],
                  text: item[2],
                  tiem: item[3],
                }
                this.roleList_diy.push(tempObj)
              } else {
                this.addAccountObj.roleList_default_check.push(item[0])
                // 系统默认
                let tempObj = {
                  id: item[0],
                  type: item[1],
                  text: item[2],
                  tiem: item[3],
                }
                this.roleList_default.push(tempObj)
              }
            })
            console.log(8888, this.roleList_diy, this.roleList_default)
          }
        })
      },

      formTreeData(tree) {
        let treeTempData = []
        for (let i = 0; i < tree.length; i++) {
          tree[i].children = []
          // if (tree[i].type !== 'h' && tree[i].type !== 'L0') {
          //   tree[i].children = []
          // }
          const id = tree[i].id
          for (let j = 0; j < tree.length; j++) {
            if (id == tree[j].parent) {
              if (tree[i].children) {
                tree[i].children.push(tree[j])
              }
            }
          }
        }
        tree.forEach((item) => {


          if (item.type == 'L0' && item.type !== 'h' && item.type !== 'v') {
            treeTempData.push(item)
          } else if (
            item.type == 'L1' &&
            item.type !== 'h' &&
            item.type !== 'v'
          ) {
            if (treeTempData.length == 0) treeTempData.push(item)
          } else if (
            item.type == 'L2' &&
            item.type !== 'h' &&
            item.type !== 'v'
          ) {
            if (treeTempData.length == 0) treeTempData.push(item)
          } else if (
            item.type == 'L3' &&
            item.type !== 'h' &&
            item.type !== 'v'
          ) {
            if (treeTempData.length == 0) treeTempData.push(item)
          } else if (
            item.type == 'L4' &&
            item.type !== 'h' &&
            item.type !== 'v'
          ) {
            if (treeTempData.length == 0) treeTempData.push(item)
          }
        })
        console.log('tree[1]', treeTempData)
        return treeTempData
      },
      simple_get_shop_struct_fun() {
        let user_id = JSON.parse(
          localStorage.getItem('device_info')
        ).uid + ''

        let params = {
          purpose: 'load_pub_tree',
          group_id:
            this.$store.state.group_id || localStorage.getItem('group_id'),
          need_screen: 1,
        }

        this.$api.simple_get_shop_struct(params).then((res) => {
          if (res.rst == 'ok') {
            const tree = res.data[0].tree_data.tree
            this.dataTree = this.formTreeData(tree)
            console.log('dataTree', this.dataTree)
            this.regionOne = this.dataTree[0].children
          }
        })
      },
      getNumGroundId(val) {
        let temp_ground_id = val
        let last_ground_id = ''
        if (temp_ground_id.charAt(0) == 'g') {
          last_ground_id = parseInt(temp_ground_id.split('g')[1])
        }
        return last_ground_id
      },
      simple_perm_role_get_or_edit_fun(strType) {
        let params = {}
        if (strType == 'add') {
          if (!this.addAccountObj.req_node_id) {
            this.$message.error('请选择地区')
          }

          if (this.addAccountObj.role_list.length == 0) {
            this.$message.error('请选择角色')
            return
          }
          if (!this.addAccountObj.admin_name) {
            this.$message.error('请输入用户名称')
            return
          }
          if (!this.addAccountObj.signin_phone && !this.addAccountObj.admin_email) {
            this.$message.error('请填写登录账号')
            return
          }
          if (!this.addAccountObj.signin_passwd) {
            this.$message.error('请填写登录密码')
            return
          }
          if (!this.addAccountObj.confirm_passwd) {
            this.$message.error('请确认登录密码')
            return
          }
          if (this.addAccountObj.confirm_passwd !== this.addAccountObj.signin_passwd) {
            this.$message.error('两次密码输入不一致')
            return
          }

          params.type = 'add_user'
          params.group_id =
            this.$store.state.group_id || localStorage.getItem('group_id')
          params.p_g = this.getNumGroundId(this.addAccountObj.req_node_id)
          params.role_list = this.addAccountObj.role_list
          params.signinPass = this.addAccountObj.signin_passwd
          params.signinPhone = this.addAccountObj.signin_phone
          params.adminEmail = this.addAccountObj.admin_email
          params.adminUname = this.addAccountObj.admin_name
          // params.adminUname = '沈阳幸福分店'
          params.confirmPass = this.addAccountObj.confirm_passwd
        } else {
          // 编辑

          params.type = 'edit_user'
          params.group_id =
            this.$store.state.group_id || localStorage.getItem('group_id')
          params.req_node_id = this.editAccountObj.req_node_id
          params.role_list = this.editAccountObj.role_list
          params.adminUname = this.editAccountObj.admin_name
          // params.signin_passwd = this.editAccountObj.signin_passwd
          // params.signin_phone = this.editAccountObj.signin_phone
          // params.admin_email = this.editAccountObj.admin_email
        }
        this.isCanClick = false
        this.$api.simple_perm_role_get_or_edit(params).then((res) => {
          this.isCanClick = true
          if (res.rst == 'ok') {
            if (strType == 'add') {
              this.$message.success('添加成功')
              this.drawerAddRole = false
            } else {
              this.$message.success('编辑成功')
              this.editShopDrawer = false
            }
            this.get_g_role_user_list_fun(this.pageSize, this.pageNumber)
          } else {
            this.$message.error(res.error_msg)
          }
        })
      },
      get_g_role_user_list_fun(limitVal, offsetVal) {
        let params = {
          limit: limitVal,
          offset: (offsetVal - 1) * this.pageSize,
          user_name: this.user_nameSearch,
          pgid: this.$store.state.group_id || localStorage.getItem('group_id'),
        }
        this.loadingTree = true
        this.$api.get_g_role_user_list(params).then((res) => {
          this.loadingTree = false
          if (res.rst == 'ok') {
            let data = res.data[0].user_list
            this.totalNum = res.data[0].all_count
            data.forEach((item) => {
              item.roleIdList = []
              item.isShowZhuXiao = item.user_info.split(',')[0] == localStorage.getItem('user_id') ? false : true
              item.user_roles.forEach((itemChild) => {
                item.roleIdList.push(itemChild.key)
              })
              if (item.user_roles.length > 0) {
                item.user_roles_one = item.user_roles[0].name + '......'

              } else {
                item.user_roles_one = ''
              }

            })
            this.tableData = []
            data.forEach(item => {
              if (item.isShowZhuXiao) {
                this.tableData.push(item)
              }
            })

            console.log('this.tableData 777', this.tableData)
          }
        })
      },
      toAddAccount() {
        this.$router.push('/addAccount')
      },

      // handleSelectionChange(val) {
      //   console.log(val, 999999)
      // },
      getHeight() {
        let windowHeight = parseInt(window.innerHeight)
        this.autoHeight.height = windowHeight - 200 + 'px'

      },
    },
    created() {
      window.addEventListener('resize', this.getHeight)
      this.getHeight()
    },
    destroyed() {
      window.removeEventListener('resize', this.getHeight)
    },
  }
</script>
<style scoped>
  .flexParent {
    width: 100%;
    height: 100%;
    
  }

  .flexBoxTwo {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  .flexBoxTwoTitle {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    height: 40px;
    line-height: 40px;
    width: 100%;
    background-color: #fff;
    z-index: 100;
  }

  .releaseIconCss {
    vertical-align: middle;
    width: 15px;
    margin-bottom: 3px;
  }

  .flexBottom {
    flex-direction: column;
  }

  .flexBottomBox {
    height: 40px;
    line-height: 40px;
    text-align: center;
    padding-left: 15px;
  }

  .topBox {
    height: 45px;
    line-height: 45px;
    border-bottom: 1px solid #ccc;
    font-weight: 600;
    text-align: center;
  }

  .activeCss {
    background-color: #90c1ec;
    color: #fff;
  }

  .rightBoxTop {
    height: 35px;
    line-height: 35px;
    background-color: #90c1ec;
    color: #fff;
    font-size: 14px;
    padding-left: 10px;
  }

  .flexBoxTwoBox {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }

  .flexBoxTwoBox::-webkit-scrollbar {
    width: 0 !important;
    display: none;
  }

  .borderBottomCss {
    /* border-bottom: 3px solid #326ffa; */
    color: rgba(108, 178, 255, 1);
  }

  .inputSearch {
    height: 30px;
    width: 30px;
    background: #eaf4ff;
    right: 1px;
    top: 7px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .searchIconCss {
    width: 12px;
  }

  .flexLeft {
    width: 300px;
    border-right: 1px solid #ececec;
  }

  .editBtn {
    width: 50px;
    height: 30px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #326ffa;
    font-size: 13px;
    color: #326ffa;
  }

  .opsBtn {
    font-size: 13px;
    color: #326ffa;
  }

  .cancelColor {
    color: #f73146;
  }

  /* drawer start */
  .closeCss {
    width: 15px;
    height: 15px;
  }

  .drawerTitle {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .spanMust {
    color: #f73146;
  }

  .saveBtnParent {
    height: 40px;
    text-align: right;

    border-top: 1px solid #ececec;
    background: #fff;
  }

  .overflowBoxCss {
    overflow-y: scroll;
  }

  .overflowBoxCss::-webkit-scrollbar {
    width: 0;
  }

  /* drawer end */
</style>