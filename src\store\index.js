import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'

Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules,
  getters,
  state: {
    zoom: 0.47,
    direction: 'across',
    // 右击状态
    menuShow: false,
    // 右击弹出框位置
    menuTop: 0,
    menuLeft: 0,
    deployCfInfo:{},
    // 屏幕标签
    // ScreenTags:[]
    deployDataFilters:[]

  },
  mutations: {
    changeDirection(state, value) {
      state.direction = value
    },
    changeZoom(state, value) {
      state.zoom = value
    },
    // 右键事件
    // 打开右键弹出
    showContexeMenu(state, { top, left }) {
      state.menuShow = true
      state.menuTop = top
      state.menuLeft = left
    },

    // 隐藏右键弹出
    hideContexeMenu(state) {
      state.menuShow = false
    },
    changeDeployInfo(state, deployCfInfo){
      state.deployCfInfo = deployCfInfo
    },
    changeDataFilters(state, dataFilters){
      state.deployDataFilters = dataFilters

    }
    // 删除事件
    // deleteComponent(state) {
    //   state.componentData.splice(state.curComponentZIndex, 1)
    //   state.classifyData.splice(state.curComponentZIndex, 1)
    // },

    // 上移
    // upComponent({ componentData, curComponentZIndex }) {
    //   // 上移图层 zIndex，表示元素在数组中越往后
    //   if (curComponentZIndex < componentData.length - 1) {
    //     swap(componentData, curComponentZIndex, curComponentZIndex + 1)
    //   } else {
    //     toast('已经到顶了', 'error')
    //   }
    // },

    // 下移
    // downComponent({ componentData, curComponentZIndex }) {
    //   // 下移图层 zIndex，表示元素在数组中越往前
    //   if (curComponentZIndex > 0) {
    //     swap(componentData, curComponentZIndex, curComponentZIndex - 1)
    //   } else {
    //     toast('已经到底了', 'error')
    //   }
    // },
    // 置顶
    // topComponent({ componentData, curComponentZIndex }) {
    //   if (curComponentZIndex < componentData.length - 1) {
    //     swap(componentData, curComponentZIndex, componentData.length - 1)
    //   } else {
    //     toast('已经到顶了', 'error')
    //   }
    // },
    // bottomComponent({ componentData, curComponentZIndex }) {
    //   // 置底
    //   if (curComponentZIndex > 0) {
    //     swap(componentData, curComponentZIndex, 0)
    //   } else {
    //     toast('已经到底了', 'error')
    //   }
    // }
    // setScreenTags(state,value){
    //   state.ScreenTags = value
    // }
  }
})

export default store
