<template>
  <div class="live">
    <div class="live_title">直播管理</div>
    {{ test }}

    <el-tabs v-model="tabsName" type="card" @tab-click="handleClick">
      <el-tab-pane label="直播设置" name="first">
        <el-form ref="form" class="live_form" label-width="130px">
          <el-form-item label="直播开始时间:">
            <el-date-picker v-model="LiveFromData.start_date" value-format="yyyy-MM-dd" type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="直播结束时间:">
            <el-date-picker v-model="LiveFromData.end_date" value-format="yyyy-MM-dd" type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="每日直播时间段:">
            <el-time-picker is-range v-model="everyday_live_time" format="HH:mm" value-format="HH:mm"
              range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围"></el-time-picker>
          </el-form-item>
          <el-form-item label="直播链接:">
            <el-input v-model="LiveFromData.stream_url" placeholder="请输入内容" style="width:400px"></el-input>
          </el-form-item>
          <el-form-item label="开启定时任务:">
            <el-switch v-model="LiveFromData.plan_active" :active-value="1" :inactive-value="0" active-color="#13ce66"
              inactive-color="#ff4949"></el-switch>
          </el-form-item>
          <el-form-item label="是否激活直播任务:">
            <el-switch v-model="LiveFromData.active_usage_type" :active-value="1" :inactive-value="0"
              active-color="#13ce66" inactive-color="#ff4949"></el-switch>
            <span>(直播和录播同一时间只能激活其中一项)</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onLiveSubmit">确认直播</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="录播设置" name="second">
        <el-form ref="form" class="live_form" label-width="130px">
          <el-form-item label="录播开始时间:">
            <el-date-picker v-model="LiveFromData.start_date" value-format="yyyy-MM-dd" type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="录播结束时间:">
            <el-date-picker v-model="LiveFromData.end_date" value-format="yyyy-MM-dd" type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="每日录播时间段:">
            <el-time-picker is-range v-model="everyday_live_time" format="HH:mm" value-format="HH:mm"
              range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围"></el-time-picker>
          </el-form-item>
          <el-form-item label="录播链接:">
            <el-input v-model="LiveFromData.stream_url" placeholder="请输入内容" style="width:400px"></el-input>
          </el-form-item>
          <el-form-item label="开启定时任务:">
            <el-switch v-model="LiveFromData.plan_active" :active-value="1" :inactive-value="0" active-color="#13ce66"
              inactive-color="#ff4949"></el-switch>
          </el-form-item>
          <el-form-item label="是否激活录播任务:">
            <el-switch v-model="LiveFromData.active_usage_type" :active-value="2" :inactive-value="0"
              active-color="#13ce66" inactive-color="#ff4949"></el-switch>
            <span>(直播和录播同一时间只能激活其中一项)</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onLiveSubmit">确认直播</el-button>
          </el-form-item>

        </el-form>
      </el-tab-pane>
    </el-tabs>



  </div>
</template>

<script>
import {
  change_live_status,
  dslive_plan_and_config,
  dslive_planinfo
} from "@/api/live/live";

import { mapState } from "vuex";

console.log(mapState, 'mapState');
export default {
  data() {
    return {
      LiveFromData: {
        start_date: "",
        end_date: "",
        start_dttime: "",
        end_dttime: "",
        stream_url: "",
        plan_active: 0
      },
      everyday_live_time: null,
      tabsName: "first",

    };
  },
  computed: {
    ...mapState({
      sum: state => state.test.num,
    })
  },
  methods: {
    load(tree, treeNode, resolve) {
      setTimeout(() => {
        resolve([
          {
            id: 31,
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄'
          }, {
            id: 32,
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄'
          }
        ])
      }, 1000)
    },
    onLiveSubmit() {
      this.LiveFromData.start_dttime = this.everyday_live_time[0];
      this.LiveFromData.end_dttime = this.everyday_live_time[1];
      console.log(this.LiveFromData);
      let usage_type = this.tabsName == "first" ? 1 : 2;
      let active_usage_type = this.tabsName == "first" ? 1 : 2;
      this.LiveFromData.usage_type = usage_type;
      this.LiveFromData.active_usage_type = active_usage_type;
      dslive_plan_and_config(this.LiveFromData).then(res => {
        if (res.rst == "ok") {
          this.$message.success("设置成功");
        } else {
          this.$message.error(res["error_message"]);
        }
      });
    },
    handleClick(e) {
      console.log(this.tabsName, "eeee");
      this.getLiveManage();
    },
    getLiveManage() {
      let usage_type = this.tabsName == "first" ? 1 : 2;
      dslive_planinfo({
        usage_type: usage_type
      }).then(res => {
        console.log(res, "detali");
        if (res["rst"] == "ok") {
          this.LiveFromData = res["data"][0];
          this.everyday_live_time = [
            res["data"][0]["start_dttime"],
            res["data"][0]["end_dttime"]
          ];
        } else {
          this.$message.error(res["error_msg"]);
        }
      });
    },
    newDevices() { },

  },
  created() {
    this.getLiveManage();
    console.log(this.$store.state);
  },
  mounted() {
  }
};
</script>

<style lang="scss" scoped>
.live {
  padding: 30px;

  .live_title {
    padding-left: 10px;
    font-size: 20px;
    padding-bottom: 10px;
    font-weight: bold;
  }

  .live_form {
    margin-top: 20px;

    ::v-deep .el-form-item__content {
      width: 380px !important;
    }
  }
}

.transition-box {
  margin-bottom: 10px;
  width: 200px;
  height: 100px;
  border-radius: 4px;
  background-color: #409EFF;
  text-align: center;
  color: #fff;
  padding: 40px 20px;
  box-sizing: border-box;
  margin-right: 20px;
}

.el-table__row--level-1{
  animation: el-table__row--level-1 2s;
}


@keyframes el-table__row--level-1 {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
