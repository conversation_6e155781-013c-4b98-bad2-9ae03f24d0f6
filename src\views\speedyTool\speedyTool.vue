<template>
  <div class="tool">
    <p class="soo">内容检索</p>
    <div class="search">
      <div class="left">
        <!-- suffix-icon="el-icon-search" -->
        <el-input placeholder="输入中台推动内容包名称" v-model="zipName" @change="SearchZip"></el-input>
      </div>

      <div class="right">
        <el-button class="soldout" @click="SearchZip">搜索</el-button>
      </div>
    </div>
    <div class="table table_wrap flex-1">
      <h4 class="tabletitle">zip包</h4>
      <el-table
        :data="tableData"
        row-key="shop_id"
        style="width: 100%"
        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <!-- <el-table-column type="selection" width="50"></el-table-column> -->
        <el-table-column prop="zip_name" label="文件包名称"></el-table-column>
        <el-table-column prop="shop_cnt" label="店铺数"></el-table-column>
        <el-table-column prop="screen_cnt" label="屏幕数"></el-table-column>
        <el-table-column prop="itmarketname" label="缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.thumb_url" alt style="width:50px;height:50px" />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="event">
              <span
                style="cursor: pointer;color: rgb(64, 158, 255);"
                @click="openSelectDialog(scope.row,'replaceImages')"
              >替换</span>
              <span
                style="cursor: pointer;color: rgb(64, 158, 255);"
                @click="openUndercarriage(scope.row,'undercarriage')"
              >下架</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="table table_wrap flex-1" v-show="HistoryData.length > 0">
      <h4 class="tabletitle">历史记录</h4>
      <el-table
        :data="HistoryData"
        row-key="shop_id"
        style="width: 100%"
        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <!-- <el-table-column type="selection" width="50"></el-table-column> -->
        <el-table-column prop="task_id" label="任务id"></el-table-column>
        <el-table-column prop="zip_name" label="zip名称"></el-table-column>
        <el-table-column prop="itmarketname" label="zip缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.thumb_url" alt style="width:50px;height:50px" />
          </template>
        </el-table-column>
        <el-table-column prop="act_job_label" label="任务类型"></el-table-column>
        <el-table-column label="替换图片">
          <template slot-scope="scope">
            <div v-if="scope.row.content_info">
              <img :src="scope.row.content_info.photo_url" alt style="width:50px;height:50px" />
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="act_status_label" label="任务状态"></el-table-column>
        <el-table-column prop="job_finish_tm" label="完成时间"></el-table-column>
        <el-table-column prop="target_shop_cnt" label="目标门店数"></el-table-column>
        <el-table-column prop="done_shop_cnt" label="完成门店数"></el-table-column>
      </el-table>
    </div>
    <!-- 底部以及页码 -->
    <div class="shop_footer flex flex-1" v-show="HistoryData.length != 0">
      <div class="left_button_wrap flex-1"></div>
      <div class="right_page_wrap">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[5, 20, 50, 100]"
          layout="total,sizes,prev,pager, next, jumper"
          :total="totalNum"
        ></el-pagination>
      </div>
    </div>

    <el-dialog
      title="新增内容"
      :visible.sync="showAlertBox"
      width="61%"
      custom-class="tabsDatat"
      @close="closeBox"
      :before-close="showAlertBox"
    >
      <!--      <div class='top'>-->
      <!--        <h3>新增内容</h3>-->
      <!--        <p @click.stop='showAlertBox=false'>×</p>-->
      <!--      </div>-->
      <div class="threeParts tag_set">
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="图片" name="first">
            <picture-resources
              :tagsList="tagsList"
              :delTagsList="delTagsList"
              @checkedList="checkedList"
              @setImgPreview="setImgPreview"
              ref="picture"
            ></picture-resources>
          </el-tab-pane>
          <!-- <el-tab-pane label="视频" name="second">
                        <video-resources @setVideoPreview='setVideoPreview' @checkedList='checkedList' ref="video">
                        </video-resources>
                    </el-tab-pane>
                    <el-tab-pane label="H5" name="third">
                        <h5-resources @setH5Preview='setH5Preview' @checkedList='checkedList' ref="h5"></h5-resources>
          </el-tab-pane>-->
        </el-tabs>
      </div>
      <div class="btns dialog_btns">
        <el-button type="primary" @click.stop="cancel">取消</el-button>
        <el-button type="primary" @click.stop="alertOk">确定</el-button>
      </div>
    </el-dialog>
    <!-- 预览mask -->
    <previsualization
      :isPreviewMaskShow="isPreviewMaskShow"
      :PreviewSrc="PreviewSrc"
      :PreviewType="PreviewType"
      @closePreviewMask="closePreviewMask"
    ></previsualization>
  </div>
</template>

<script>
import pictureResources from "@/components/ContentCenter/pictureResources";
import videoResources from "@/components/ContentCenter/videoResources";
import h5Resources from "@/components/ContentCenter/h5Resources";
import previsualization from "@/components/communal/previsualization";

import {
  search_zip_pub_datas,
  create_zip_ct_handle_task,
  get_zip_ct_handle_task_list
} from "@/api/speedytool/speedytool";

export default {
  data() {
    return {
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      currentPage: 0, //页码
      totalNum: 0, //总数据数量
      pageSize: 5,
      PreviewType: "",
      PreviewSrc: "",
      showAlertBox: false,
      activeName: "first",
      zipName: "",
      tableData: [],
      ZipChange: "",
      ZipFormData: {
        zip_id: "",
        act_job: "",
        content: null
      },
      HistoryData: [],
      pageNumer: 0
    };
  },
  components: {
    pictureResources,
    videoResources,
    h5Resources,
    previsualization
  },
  methods: {
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 230 + "px";
      this.autoHeight.heightNum = windowHeight - 230;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getHistoryRecord();
    },
    handleCurrentChange(val) {
      this.pageNumer = val - 1;
      this.getHistoryRecord();
    },
    setImgPreview(val) {
      this.PreviewType = "image";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setVideoPreview(val) {
      this.PreviewType = "video";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setH5Preview(val) {
      this.PreviewType = "h5";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    },
    openSelectDialog(row, changeName) {
      this.ZipChange = changeName;
      this.ZipFormData.zip_id = row.zip_id;
      this.ZipFormData.act_job = 2;
      this.showAlertBox = true;
    },
    openUndercarriage(row) {
      this.ZipFormData.zip_id = row.zip_id;
      this.ZipFormData.act_job = 1;
      this.ZipFormData.content = null;
      this.$confirm("此操作将下架Zip包内容, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          create_zip_ct_handle_task(this.ZipFormData).then(res => {
            if (res.rst == "ok") {
              this.$message.success("下架成功");
              this.SearchZip();
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消下架"
          });
        });
    },
    cancel() {
      this.showAlertBox = false;
    },
    closeBox() {
      this.showAlertBox = false;
    },
    alertOk() {
      if (this.imgList.length == 0) {
        this.$message.info("请选择替换图片");
      } else {
        this.$confirm("此操作将替换此Zip包内容, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.ZipFormData.content = this.imgList[0]["file_id"];
            create_zip_ct_handle_task(this.ZipFormData).then(res => {
              if (res.rst == "ok") {
                this.$message.success("替换成功");
                this.showAlertBox = false;
                this.SearchZip();
              } else {
                this.$message.closeAll();
                this.$message.warning(res.error_msg);
                this.showAlertBox = false;
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消替换"
            });
          });
      }
    },
    checkedList(imgList) {
      this.$refs.picture.isContent = true;
      console.log(imgList, "imgLists");
      this.imgList = imgList;
      console.log(this.imgList);
      //   this.$emit("selectImage", imgUrl);
    },

    // search
    SearchZip() {
      if (this.zipName) {
        search_zip_pub_datas({
          zip_name: this.zipName,
          page_num: 0,
          page_size: 10
        }).then(res => {
          if (res.rst == "ok") {
            this.tableData = res["data"][0]["content"];
            console.log(this.tableData);
          }
          this.getHistoryRecord();
        });
      } else {
        this.$message.warning("请填写包名称");
      }
    },
    getHistoryRecord() {
      get_zip_ct_handle_task_list({
        page_num: this.pageNumer,
        page_size: this.pageSize
      }).then(res => {
        console.log(res, "res");
        this.HistoryData = res["data"][0]["content"];
        this.totalNum = res["data"][0]["totalElements"];
      });
    }
  },
  created() {
    // 列表区高度自适应
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getHistoryRecord();
  }
};
</script>

<style lang="scss">
.dialog_btns {
  height: 50px;
  button {
    margin: 0 20px 0 0 !important;
  }
}
</style>
<style lang='scss' scoped>
.tool {
  padding: 22px 29px 0 32px;

  .soo {
    color: rgba(56, 56, 56, 1);
    font-size: 16px;
  }

  .search {
    height: 95px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(166, 166, 166, 1);

    .left {
      .el-input__inner {
        width: 210px;
        border-radius: 20px !important;
      }
    }

    .center {
      margin-left: 56px;
    }

    .right {
      display: flex;
      margin-left: 50px;

      .soldout {
        width: 110px;
        background-color: var(--text-color);
        color: #fff;
        border-radius: 4px;
      }

      .replace {
        width: 110px;
        background-color: rgba(108, 178, 255, 1);
        color: #fff;
        border-radius: 4px;
      }
    }
  }

  .table {
    margin-top: 15px;

    .tabletitle {
      width: 64px;
      color: rgba(108, 178, 255, 1);
      font-size: 16px;
      text-align: left;
      font-weight: bold;
      margin-bottom: 15px;
    }
  }
}

.table_wrap .el-checkbox__inner {
  height: 19px !important;
}

.shop_footer {
  height: 89px;
  align-items: center;
  padding-left: 24px;
  padding-right: 22px;
}

.btns {
  display: flex;
  margin-top: -12px;
  padding-bottom: 10px;
  justify-content: flex-end;
  width: 100%;
}
</style>