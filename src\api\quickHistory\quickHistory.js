import { post } from '@/utils/request'

/**
 * 获取 发布历史
 * @param page_num  页码
 * @param page_size  10
 */
const get_btpub_perform_list = p =>
    post('/dmb/api/json', p, 'get_btpub_perform_list')

/**
 * 获取 发布任务/计划 - 详情状态汇总
 * @param btpub_id // 任务id
 */
const get_btpub_perform_status = p =>
    post('/dmb/api/json', p, 'get_btpub_perform_status')

/**
 * 发布任务/计划 - 门店发布列表
 * @param btpub_id  任务id
 * @param page_num  页码
 * @param page_size  10
 */
const get_btpub_shop_perform_list = p =>
    post('/dmb/api/json', p, 'get_btpub_shop_perform_list')

/**
 * 发布任务/计划 - 屏幕发布列表
 * @param btpub_id 任务id
 * @param shop_id   门店id
 * @param page_num  页码
 * @param page_size  10
 */
const get_btpub_screen_perform_list = p =>
    post('/dmb/api/json', p, 'get_btpub_screen_perform_list')

/**
 * 发布任务/计划 - 屏幕内容列表
 * @param btpub_id  任务id
 * @param screen_id 屏幕id
 * @param page_num  页码
 * @param page_size  10
 */
const get_btpub_screen_perform_detail = p =>
    post('/dmb/api/json', p, 'get_btpub_screen_perform_detail')

/**
 * check删除
 * @param {btpub_id} 任务id
 */
const check_for_delete_btpub = p => post('/dmb/api/json', p, 'check_for_delete_btpub')

/**
 * 执行删除
 * @param {btpub_id} 任务id
 */
const delete_btpub = p => post('/dmb/api/json', p, 'delete_btpub')

/**
 * 复制
 * @param {btpub_id} 任务id
 * @param {new_bt_name} 发布任务名称
 */
const copy_btpub = p => post('/dmb/api/json', p, 'copy_btpub')

export {
    get_btpub_perform_list,
    get_btpub_perform_status,
    get_btpub_shop_perform_list,
    get_btpub_screen_perform_list,
    get_btpub_screen_perform_detail,
    check_for_delete_btpub,
    delete_btpub,
    copy_btpub
}
