<template>
  <div class="box">

    <!-- v-if="$route.query.isLook" -->
    <div class="top">
      <div style="display: flex; height: 100%; align-items: center">
        <span class="el-icon-back" @click.stop="goBack" style="cursor: pointer;z-index: 9999;"></span>
        <div>
          <el-tabs style="" v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane name="img">
              <span slot="label"><i class="el-icon-picture"></i> 图片</span>
            </el-tab-pane>
            <el-tab-pane name="video">
              <span slot="label"><i class="el-icon-video-camera-solid"></i> 视频</span>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <!-- :class="item.content.display.v_or_h == 'horizontal' ? 'herLi' : 'verLi'"> -->
<!-- {{ dataList }}  -->
    <ul v-if="file_type == 'img' && !$route.query.isLook">
      <li v-for="(item, index) in dataList" :key="index"
        :class="item.content.display.v_or_h == 'horizontal' ? isV == true ? 'herLiV' : 'herLi' : 'verLi'">
        <div class="styAlign">
          <img :src="item.file_url" :preview-src-list="previewUrl" @click="clickShowImg(item.file_url)" ref="preview"
            class="styImg"
            :style="[item.content.display.v_or_h == 'horizontal' ? (item.more_info ? JSON.stringify(item.more_info) == '{}' ?
              $route.query.all_displayinfo[$route.query.displaynuminfo[0]] : $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]]) == 'vertical' ? rota : '' : '']" />
          <div class="play" :data-imgurl="item.file_url" @click="show(item.file_url, item)"
            :class="(item.more_info ? JSON.stringify(item.more_info) == '{}' ?
              $route.query.all_displayinfo[$route.query.displaynuminfo[0]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]]) == 'vertical' ? '' : 'view'">
            <span class="el-icon-view"></span>
            预览
          </div>
          <div class="tptTime">
            <span>截取时间：</span>
            <span>{{ item.tpt_time ? item.tpt_time : "" }}</span>
            <!-- <span>( {{ item.more_info ? JSON.stringify(item.more_info) == '{}' ?
                `当前截取为${$route.query.displaynuminfo[0]}屏` : item.more_info.dual_type
                  == 1 ? `当前截取为${$route.query.displaynuminfo[0]}屏` : `当前截取为${$route.query.displaynuminfo[1]}屏` :
                `当前截取为${$route.query.displaynuminfo[0]}屏`
            }} )</span> -->
            <span>
              当前截取为{{ reverseDisplayNumber[index % reverseDisplayNumber.length] }}屏 
            </span>
          </div>
        </div>
      </li>
    </ul>
    <!-- isLook -->
    <!-- 查看截图 -->
    <ul v-if="file_type == 'img' && $route.query.isLook">
      <li v-for="(item, index) in dataList" :key="index"
        :class="item.content.display.v_or_h == 'horizontal' ? isV == true ? 'herLiV' : 'herLi' : 'verLi'">
        <div class="styAlign">
          <img :src="item.file_url" :preview-src-list="previewUrl" @click="clickShowImg(item.file_url)" ref="preview"
            class="styImg" style="transform:rotate(0deg)"
            :style="[item.content.display.v_or_h == 'horizontal' ? (item.more_info ? JSON.stringify(item.more_info) == '{}' ?
              $route.query.all_displayinfo[$route.query.displaynuminfo[0]] : $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]]) == 'vertical' ? rota : '' : '']" />
          <div class="play" :data-imgurl="item.file_url" @click="show(item.file_url, item)"
            :class="(item.more_info ? JSON.stringify(item.more_info) == '{}' ?
              $route.query.all_displayinfo[$route.query.displaynuminfo[0]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]] :
              $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]]) == 'vertical' ? '' : 'view'">
            <span class="el-icon-view"></span>
            预览
          </div>
          <div class="tptTime">
            <span>截取时间：</span>
            <span>{{ item.tpt_time ? item.tpt_time : "" }}</span>
            <span v-if="Object.keys(item.more_info).length > 0">
              <!-- {{ item.more_info }} -->
                
              当前截取为{{ reverseDisplayNumber[index % reverseDisplayNumber.length] }}屏 
            </span>
            <!-- <span>( {{ item.more_info ? JSON.stringify(item.more_info) == '{}' ?
                `当前截取为${$route.query.displaynuminfo[0]}屏` : item.more_info.dual_type
                  == 1 ? `当前截取为${$route.query.displaynuminfo[0]}屏` : `当前截取为${$route.query.displaynuminfo[1]}屏` :
                `当前截取为${$route.query.displaynuminfo[0]}屏`
            }} )</span> -->
          </div>
        </div>
      </li>
    </ul>
    <ul v-else-if="file_type == 'video'" class="video_box">
      <!-- {{dataList}} -->
      <li v-for="(item, index) in dataList" :key="index" :class="dataInfo == '竖屏' ? (item.more_info ? JSON.stringify(item.more_info) == '{}' ?
        $route.query.all_displayinfo[$route.query.displaynuminfo[0]] : $route.query.all_displayinfo[$route.query.displaynuminfo[item.more_info.dual_type - 1]] :
        $route.query.all_displayinfo[$route.query.displaynuminfo[0]]) == 'vertical' ? 'verLi' : 'herLi' : ''">
        <div>
          <video :src="item.file_url"></video>
          <div class="videoPlay" @click="openVideo(item.file_url)">
            <span class="el-icon-view"></span>
            预览
          </div>
        </div>
        <div style="margin-bottom:20px" class="time">
          <span>截取时间：</span>

          <span>{{ item.tpt_time ? item.tpt_time : "" }}</span>
         
            <span v-if="item.more_info && Object.keys(item.more_info).length > 0">
              当前截取为{{ reverseDisplayNumber[index % reverseDisplayNumber.length] }}屏 
            </span>
        </div>
      </li>
    </ul>

    <!--    大图-->
    <el-dialog title="预览视频" :visible.sync="dialogVideo" width="60%" :before-close="handleClose">
      <div class="video_preview">
        <video :src="previewVideoUrl" controls></video>
      </div>
    </el-dialog>

    <div class="mask" v-show="isBgShow">
      <!-- showH -->
      <!-- {{ dataInfo }} -->
      <div :class="dataInfo == '竖屏' ? 'showH' : 'showV'">
        <img :src="bgImg" class="bgImg" style="transform:rotate(0deg)">
      </div>
      <p class="bgClose" @click="isBgShow = false">
        <span class="el-icon-close"></span>
      </p>
    </div>

    <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
      @closePreviewMask='closePreviewMask'>
    </previsualization>
  </div>
</template>

<script>
import { screen_capture_or_record } from "@/api/screen/screen";
import previsualization from "@/components/communal/previsualization"

export default {
  name: "ShotRecord",
  data() {
    return {
      screen_id: "",
      isBgShow: false,
      // 截图或录屏数据
      dataList: [],
      dataInfo: "",
      // 预览
      previewUrl: [""],
      // 是否是截图还是录屏
      file_type: "img",
      LookState: 0,
      // 切换
      activeName: "img",
      // 预览视频
      dialogVideo: false,
      // 视频地址
      previewVideoUrl: "",
      isPreviewMaskShow: false,
      PreviewSrc: "",
      more_info: {},
      rota: {
        transform: 'rotate(-90deg)',
        margin: '0 auto',
        width: '159px',
        height: '266px',
        display: 'block'
      },
      norota: {
        transform: 'rotate(0deg)',
      },
      allDisplayinfo: [],
      isV: false,
      reverseDisplayNumber: []
    };
  },
  components: {
    previsualization
  },
  methods: {
    //点击预览
    clickShowImg(url) {
      this.previewUrl[0] = url;
    },
    goBack() {
      // this.$router.replace({ path: "/deviceManage/screenGroup" });
      this.$router.go(-1);
    },
    get_screen() {
      const params = {
        screen_id: this.screen_id, // (Int) 屏幕id
        file_type: this.file_type, // (String) "img":图片; "video":视频
        limit: 10,
        offset: 0,
      };
      screen_capture_or_record(params).then((res) => {
        console.log(res, 'res');
        this.dataList = res.data[0].data.snap_list;
        this.dataInfo = res.data[0].data.screen_info.v_or_h;
        console.log(this.allDisplayinfo, 'allDisplayinfo');
        console.log(this.dataList, 'dataList');
        this.allDisplayinfo.forEach(item => {
          console.log(item, 'item');
          if (item.display.v_or_h == "vertical") {
            if (item.hdmi_port == '0') {
              this.isV = true;
            }
          }
        })
        this.dataList.forEach(item1 => {
          if (item1.more_info) {
            if (this.allDisplayinfo.length == 1) {
              item1['content'] = this.allDisplayinfo[0];
            } else {
              if (item1.more_info.dual_type == 2) {
                item1['content'] = this.allDisplayinfo[1];
              } else {
                item1['content'] = this.allDisplayinfo[0];
              }
            }
          } else {
            item1['content'] = this.allDisplayinfo[0];
          }
        })

      });
    },
    show(url, imgs) {
      this.previewUrl[0] = url;
      this.isBgShow = true;
      this.bgImg = url;
      console.log(this.isV, 'isV');
      console.log(imgs, 'imgs');
      if (this.isV == true && imgs.content.display.v_or_h == "horizontal") {
        document.getElementsByClassName("bgImg")[0].style.transform = 'rotate(-90deg)'
      } else {
        document.getElementsByClassName("bgImg")[0].style.transform = 'rotate(0deg)'
      }
      // document.getElementsByClassName("bgImg")[0].style.transform = document.getElementsByClassName("styImg")[index].style.transform == 'rotate(0deg)' ? 'rotate(0deg)' : 'rotate(-90deg)'
    },

    // 切换
    handleClick() {
      this.dataList = []
      switch (this.activeName) {
        case "img":
          this.getImageData();
          return;
        case "video":
          this.getVideoData();
          return;
      }
    },
    // 获取图片列表
    getImageData() {
      this.file_type = "img";
      this.get_screen();
      console.log(this.allDisplayinfo, 'xxx');

    },
    // 获取视频列表
    getVideoData() {
      this.file_type = "video";
      this.get_screen();
    },
    // 视频预览
    openVideo(url) {
      this.PreviewType = 'video'
      this.PreviewSrc = url
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = '';
    },
  },
  created() {
    this.screen_id = this.$route.query.screen_id;
    // console.log(this.$route.query.all_displayinfo, 'mmmall_displayinfo');
    // this.$route.query.all_displayinfo = JSON.parse(this.$route.query.all_displayinfo)
    console.log(this.$route.query.all_displayinfo, 'this.$route.query.all_displayinfo');
    this.allDisplayinfo = JSON.parse(this.$route.query.all_displayinfo)
    console.log(this.$route.query,'this.$route.query');

    this.reverseDisplayNumber =  this.$route.query.displaynuminfo
    if(typeof(this.reverseDisplayNumber) == 'string'){
      
      this.reverseDisplayNumber = this.reverseDisplayNumber.split(',')
      this.reverseDisplayNumber = this.reverseDisplayNumber.reverse()
    }else if(typeof(this.reverseDisplayNumber) == 'object'){
      this.reverseDisplayNumber =  this.$route.query.displaynuminfo.reverse() 
    }


    if (this.$route.query.LookState) {
      this.LookState = this.$route.query.LookState;
      if (this.LookState == 1) {
        this.activeName = "img";
        this.getImageData();
      } else if (this.LookState == 2) {
        this.activeName = "video";
        this.getVideoData();
      }
    } else {
      this.get_screen();
      // this.dataList
    }
  },
};
</script>

<style scoped>
.box {
  width: 100%;
  height: 100%;
  background-color: #fff;
}

/*  */
.mask {
  width: 100%;
  height: 100%;
  background-color: #e2e2e2b7;
  position: absolute;
  top: 0;
  left: 0;
  transition: 5s;
  z-index: 99;
}

.mask div {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  width: 300px;
  height: 200px;

}

.mask .showH {
  /* transform: rotate(-90deg); */
  width: 356px;
  height: 625px;
}

.mask img {
  display: block;
  width: 100%;
  height: 100%;
}

.mask .showV {
  width: 600px;
  height: 355px;
}

.mask .bgClose {
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 50%;
  color: #fff;
  background-color: #a8a8aaec;
  position: absolute;
  right: 50px;
  top: 50px;
}

.mask .el-icon-close {
  font-size: 20px;
  width: 20px;
  height: 20px;
  display: block;
  text-align: center;
  margin: auto;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.top {
  height: 48px;
  width: 100%;
  padding: 0 18px 0 13px;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
  border-bottom: 1px solid #ecebeb;
}

.el-icon-back {
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
  display: block;
}

ul {
  display: flex;
  padding: 17px 24px;
  flex-wrap: wrap;
  position: relative;
  list-style: none;
  /* justify-content: space-between; */
  /* overflow: scroll; */
  margin-top: -40px !important;
  align-items: center !important;
}

ul>li {
  width: 17%;
  /* height: 200px; */
  margin: 14px 25px;
  /* margin-right: 25px; */
  position: relative;
  margin-top: 34px !important;
}

ul>.verLi {
  width: 10%;
  /* width: 200px; */
  margin: 60px 2%;
  height: 340px;
  margin-top: 70px !important;
}

ul>.herLi {
  margin: 14px 1%;
  width: 20% !important;
  height: 235px !important;
}

ul>.herLiV img {
  transform: rotate(-90deg) !important;
  width: 200px !important;
  margin-left: 70px;
}

ul>li>div {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  margin-top: 10px;
  position: relative;
}


ul li .rota {
  transform: rotate(-90deg);
  margin: 0 auto;
  width: 159px;
  height: 266px;
  display: block;
}

ul li .rota .el-image {
  display: block;
  width: 100%;
  height: 100%;
}

ul>li>div:hover .play {
  display: block;
  cursor: pointer;
}

ul>li>div:hover .videoPlay {
  display: block;
  cursor: pointer;
}

ul>li>div>.tptTime {
  line-height: 0 !important;
}

ul>li>div>.tptTime>span {
  margin-top: 20px;
  display: inline-block;
  /* text-align: center; */

}

/* ul>li>div>.tptTime>span:nth-last-child(1) {
  margin-left: 20px;
} */
ul .boxvertical {
  height: 300px;
  width: 100%;
  /* width: 250px; */
  margin-left: 6%;
  /* transform: rotate(270deg); */

}

ul .boxher {
  height: 300px !important;
  transform: rotate(-90deg);
  width: 300px !important;
  /* width: 250px; */
  /* transform: rotate(270deg); */
  /* margin-left: 6%; */
}

ul .vertical {
  height: 300px;
  width: 65%;
  /* width: 248px; */
}

ul .view {
  /* top: -15px;
  left: -25%; */
  margin: auto;
  top: 0;
  left: 8%;
  bottom: 23%;
  right: 0;
}

.play {
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 30px;
  font-size: 14px;
  color: #fff;
  border: rgba(255, 255, 255, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 17px;
  top: -66px;
  margin: auto;
  right: 0;
  bottom: 0;
  display: none;
}

.play>span {
  font-size: 24px;
  display: block;
  margin-top: 10px;
  color: rgba(255, 255, 255, 1);
}

.videoPlay {
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 30px;
  font-size: 14px;
  color: #fff;
  border: rgba(255, 255, 255, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 17px;
  top: -5px;
  margin: auto;
  right: 0;
  bottom: 0;
  display: none;
}

.videoPlay>span {
  font-size: 24px;
  display: block;
  margin-top: 10px;
  color: rgba(255, 255, 255, 1);
}

.sideStep>div>img:hover~.play {
  display: block;
}

.bigBg {
  width: 900px;
  height: 506px;
  position: absolute;
  left: 50%;
  object-fit: contain;
  margin-left: -450px;
}

.bigBg>img {
  width: 100%;
  height: 100%;
}

.bigBg>p {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: #ffff;
  text-align: center;
  position: absolute;
  line-height: 50px;
  top: -16px;
  right: -20px;
  z-index: 20;
  font-size: 30px;
  background-color: #111111;
  cursor: pointer;
}

.el-tabs {
  margin-bottom: -17px !important;
}

::v-deep .el-tabs--card>.el-tabs__header .el-tabs__nav {
  border: 0 !important;
  margin-bottom: 5px !important;
}

::v-deep .el-tabs__item {
  border-left: 0 !important;
  height: 100% !important;
  border-bottom: 0;
  margin-top: 15px !important;
}

::v-deep .el-tabs--card>.el-tabs__header {
  border-bottom: 0;
}

::v-deep .is-active {
  color: var(--text-color) !important;
  border-bottom: 3px solid var(--text-color) !important;
  background-color: var(--active-color);
}

.video_box video {
  width: 196px;
  height: 345px;
}

::v-deep .el-message {
  min-width: 1380px !important;
}

::v-deep .el-message-box__wrapper .el-message {
  width: 1380px !important;
}

.video_preview {
  margin: 0 auto;
}

.video_preview video {
  width: 100%;
  height: 100%;
}

::v-deep .time {
  margin-bottom: 20px !important;
  width: 101%;
}

.el-image {
  display: block !important;
}

.styAlign {
  width: 100% !important;
  height: 100% !important;
}

.styAlign img {
  width: 100% !important;
  height: 100% !important;
}
</style>
