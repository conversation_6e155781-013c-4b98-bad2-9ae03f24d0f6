<template>
  <div class="deploy">
    <div class="top">
      <div>
        <!--      市场运营-->

        <el-input placeholder="策略名称" style="width: 150px" prefix-icon="el-icon-search" v-model="queryList.name"
          clearable>
        </el-input>

        <el-select v-model="queryList.platform" placeholder="发布类型" clearable>
          <el-option label="餐牌组投放" value="2"></el-option>
          <el-option label="普通投放" value="3"></el-option>
          <el-option label="联屏投放" value="4"></el-option>
        </el-select>
        <!--         搜索按钮-->
        <el-button type="danger" style="background: var(--background-color)" @click="searchStrategy">搜索</el-button>
      </div>
      <div>
        <el-button type="danger" style="background: var(--background-color)" @click="openNewStrategy">新建策略</el-button>
      </div>
    </div>
    <div class="title_info">
      <span>快捷发布策略</span>
      <span>规则数量：{{ totalNum }}</span>
    </div>
    <!--      表格-->
    <el-table :height="autoHeight.height" :data="tableData"
      :header-cell-style="{ background: 'var( --text-color-light);' }" style="width: 100%">
      <!-- <el-table-column prop="num" label="序号" align="center" width="">
      </el-table-column> -->
      <!-- <el-table-column prop="status" align="center" label="状态" width="">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value='1' :inactive-value='0' active-color="#13ce66"
            inactive-color="#dcdfe6">
          </el-switch>
        </template>
</el-table-column> -->
      <el-table-column prop="name" label="策略名称" align="center" width="">
      </el-table-column>
      <el-table-column align="center" label="发布类型" width="">
        <template slot-scope="scope">
          <span v-if="scope.row.platform == 3">普通投放</span>
          <span v-else-if="scope.row.platform == 2">餐牌组投放</span>
          <span v-else-if="scope.row.platform == 4">联屏投放</span>
        </template>
      </el-table-column>
      <el-table-column prop="daypart" label="经营时段类型" align="center" width="">
        <template slot-scope="scope">
          <div>
            {{ scope.row.play_info.daypartgroup ? scope.row.play_info.daypartgroup : '/' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="dmb_spec" label="餐牌组规格" align="center" width="">
        <template slot-scope="scope">
          <div style="display:flex;justify-content: center;" v-if="scope.row.dmb_spec">
            {{ splitStr(scope.row.dmb_spec).labelX ? splitStr(scope.row.dmb_spec).labelX : '' +
              splitStr(scope.row.dmb_spec).labelY ?
              splitStr(scope.row.dmb_spec).labelY : '' }}
            <!-- <div v-for="item in scope.row.dmb_spec">
              <span v-if="item == 0"> 横</span>
              <span v-else> 竖 </span>
            </div> -->
            <!-- {{scope.row.dmb_spec? '1*' + scope.row.dmb_spec.length:'/'}} -->
          </div>
          <div v-else> / </div>
        </template>
      </el-table-column>

      <el-table-column prop="usage_type" label="屏幕类型" align="center" width="">
        <template slot-scope="scope">
          <div v-for="item in mylist[3].options">
            <span v-if="item[0] == scope.row.usage_type"> {{ item[1] }} </span>
            <!-- {{scope.row.usage_type?scope.row.usage_type:'/'}} -->
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="vs_spec" label="联屏规格" align="center" width="">
        <template slot-scope="scope">
          <div>
            {{ scope.row.vs_spec ? scope.row.vs_spec : '/' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="play_style" label="播放类型" align="center" width="">
        <template slot-scope="scope">
          <div v-if="scope.row.play_style == 1">轮播</div>
          <div v-else-if="scope.row.play_style == 2">独占</div>
        </template>
      </el-table-column>

      <el-table-column label="营运市场" align="center" width="">
        <template slot-scope="scope">
          <div v-if="scope.row.pub_info.pub_json">
            <span v-for="item in scope.row.pub_info.pub_json.opsmarket_cn">
              {{ item }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <!-- <div style="margin-bottom:2px">
          </div> -->
          <div style="display: flex;">
            <el-button @click="handleUse(scope.row)" type="primary" size="mini">使用</el-button>
            <el-button class="event_text" @click="handleSee(scope.row)" type="text" size="small">查看</el-button>
            <el-button class="event_text" @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
            <el-button class="event_text" @click="handleDel(scope.row)" type="text" size="small">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--      分页-->
    <div class="block">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage3" :page-size="queryList.page_size" layout="total,sizes,prev,pager, next, jumper"
        :total="totalNum" :page-sizes="[10, 20, 50, 100]" background>
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { get_strategy_list, quick_delete_btplan } from "@/api/strategy/strategy"
import { datas_filter_cond } from '@/api/commonInterface'

export default {
  name: "TimeFrame",
  data() {
    return {
      inputValue: "",
      //  表格
      tableData: [
      ],
      queryList: {
        page_num: 0,
        page_size: 10,
        name: "",
        platform: ""
      },


      //  表格区域高度
      autoHeight: {
        height: "",
        heightNum: "",
      },
      // 获取表单条件
      pageSize: 10,
      pageNum: 0,
      totalNum: 0,
      mylist: null
    };
  },
  methods: {
    getTableData() {
      get_strategy_list(this.queryList).then(res => {
        console.log(res);
        if (res.rst == "ok") {
          this.tableData = res['data'][0]['content'];
          this.totalNum = res['data'][0]['totalElements']
        }
      })

    },
    // 新建策略
    openNewStrategy() {
      this.$router.push({
        path: "/deploy/openNewStrategy",
        // query: { batch_id:  row.batch_id},
      });
    },
    //使用
    handleUse(row) {
      console.log('使用', row);
      this.$router.push({
        path: "/deploy/useStrategy",
        query: {
          platform: row.platform,
          btplan_id: row.btplan_id
        }
      })
      // this.$router.push({
      //     path: "/deploy/openNewStrategy",
      //     // query: { batch_id:  row.batch_id},
      // });
    },
    // 查看
    handleSee(row) {
      console.log('查看', row);
      this.$router.push({
        path: "/deploy/quickDpSee",
        query: { btplan_id: row.btplan_id, platform: row.platform },
      });
    },
    // 编辑
    handleEdit(row) {
      this.$router.push({
        path: "/deploy/quickDpEdit",
        query: { btplan_id: row.btplan_id },
      });
      console.log(row);
      // if (row.has_content == 1) {
      //   this.$message.warning("有内容不可编辑")
      // }
    },
    // 删除
    handleDel(row) {
      console.log('删除', row);
      this.$confirm(row.has_content == 1 ? '此策略有内容,是否删除' : '此操作将永久删除该策略, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        quick_delete_btplan({
          btplan_id: row.btplan_id
        }).then(res => {
          if (res.rst == 'ok') {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getTableData()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    //  列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 235 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    handleSizeChange(val) {
      this.queryList.page_size = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage3 = val;
      this.queryList.page_num = val - 1;
      this.getTableData();
    },
    // 跳转详情
    toDetail() {
      this.$router.push({
        path: "/deploy/pubFail",
      });
    },
    searchStrategy() {
      this.getTableData()
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: 'ContentPub', //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      }
      datas_filter_cond(params).then(res => {
        this.mylist = res['data'][0];
        console.log(this.mylist, 'this.mylist ');
      })
    },
    splitStr(str) {
      let obj = str.split("").reduce(function (x, y) {
        return (x[y]++ || (x[y] = 1), x);
      }, {});
      for (let i in obj) {
        if (i == 0) {
          obj.labelX = obj[i] + '横'
        } else if (i == 1) {
          obj.labelY = obj[i] + '竖'
        }
      }
      console.log(obj);
      return obj
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getSelectDataList()
    this.getHeight();
    this.getTableData();
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style scoped lang='scss'>
.deploy {
  padding: 0 20px 0 13px;
  width: 100%;
}

.top {
  display: flex;
  height: 40px;
  align-items: center;
  padding: 27px 0 30px 0;
  margin-bottom: 10px;
  justify-content: space-between;
}

.top>div {
  margin-right: 8px;
}

.top>.queryTime {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: left;
}

.top>.queryTime {
  display: flex;
  align-items: center;
}

.top>.queryTime>span {
  width: 62px;
}

::v-deep .el-select {
  width: 150px;
}

/*日历*/
.el-input__inner {
  width: 243px;
  height: 32px;
  margin-right: 16px;
}

::v-deep .el-range-separator {
  line-height: 23px;
}

::v-deep .el-range__icon {
  line-height: 17px !important;
}

::v-deep .el-date-editor .el-range-separator {
  width: 6%;
  padding: 0;
}

.top>button {
  width: 88px;
  height: 32px;
  line-height: 9px;
}

/*表格*/
::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-table__body {
  table-layout: auto;
}

/*分页*/
.el-pagination {
  height: 0.32rem;
  margin-top: 35px;
  text-align: right;
}

.look {
  cursor: pointer;
  color: rgba(42, 130, 228, 1);
  width: 60px;
  background-color: rgba(227, 241, 255, 1);
  height: 26px;
  border-radius: 3px;
  line-height: 26px;
  text-align: center;
  position: absolute;
  right: 4%;
  top: 50%;
}

.tou {
  margin-bottom: 10px;
}

.my-popover {
  padding: 20px;
}

.progress {
  position: relative;
  padding: 10px;
}

::v-deep .el-progress-bar {
  width: 75% !important;
}

.title_info {
  margin-bottom: 15px;

  span:nth-of-type(1) {
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    font-weight: bold;
    margin-right: 10px;
  }

  span:nth-of-type(2) {
    color: rgba(166, 166, 166, 1);
    font-size: 14px;
  }
}

.event_text {
  color: #276042 !important;
}
</style>