<template>
    <div class="product-center">
        <div class="goods_info_wrap">
            <div class="search_bar">
                <div class="search_item">
                    <el-input v-model="searchInfo.spu_blurry" style="width:220px" placeholder="请输入商品名称/编码"
                        clearable></el-input>
                </div>
                <div class="search_item">
                    <el-input v-model="searchInfo.pos_key" style="width:220px" placeholder="请输入商品名称pos_key"
                        clearable></el-input>
                </div>
                <div class="search_item">
                    <el-select v-model="searchInfo.active_status" style="width:220px" placeholder="请选择商品状态" clearable>
                        <el-option v-for="item in activeStatusOptions" :key="'sold_out_' + item[0]" :label="item[1]"
                            :value="item[0]"></el-option>
                    </el-select>
                </div>
                <div class="search_item">
                    <el-button type="primary" @click="search">搜索</el-button>
                    <el-button @click="reset">重置</el-button>
                </div>
            </div>
            <div class="info_item_panel">
                <el-table :data="tableData" height="100%" border v-loading="loading"
                    header-row-class-name="table_header" row-class-name="table_row" style="width: 100%;height:100%"
                    row-key="id" :tree-props="{
                        children: 'sku_list',
                        hasChildren: 'hasChildren'  // 对应数据中的布尔值字段
                    }" :load="loadChildData" lazy>
                    <!-- <el-table-column width="50">
                        <template #default="scope">
                            <i v-if="scope.row.hasChildren" class="el-icon-arrow-right custom-expand-icon"
                                :class="{ expanded: scope.row.expanded }" @click="handleExpandClick(scope.row)"></i>
                        </template>
</el-table-column> -->
                    <el-table-column prop="spu_code" label="SPU Code" align="center" width="120"></el-table-column>
                    <el-table-column prop="spu_name" label="商品名称" align="center" min-width="150"></el-table-column>
                    <el-table-column prop="code" label="商品状态" align="center" min-width="150">
                        <template slot-scope="scope">
                            <span v-if="scope.row.active_status == 9"> 在售 </span>
                            <span v-if="scope.row.active_status == 4"> 已下架 </span>
                            <span v-else> </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="cate_type_display" label="商品分类" align="center"
                        min-width="150"></el-table-column>
                    <el-table-column prop="sku_code" label="SKU Code" align="center" min-width="150"></el-table-column>
                    <el-table-column prop="unit_label" label="SKU 名称" align="center" min-width="150"></el-table-column>
                    <el-table-column prop="soldout_status_display" label="售罄状态" align="center" min-width="150">
                        <template slot-scope="scope">
                            <span v-if="scope.row.active_status == 9"> 在售 </span>
                            <span v-if="scope.row.active_status == 4"> 已下架 </span>
                            <span v-else> </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="last_time" label="更新时间" align="center" min-width="150"></el-table-column>
                    <el-table-column label="数据来源" prop="data_source" align="center" min-width="150">

                    </el-table-column>
                    <el-table-column prop="date" label="操作" align="center" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="small" type="primary" @click="handlePrice(scope.row)">价格</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="info_pages">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                
                    :current-page="pageNumber" :page-sizes="pageSizes" :page-size="pageSize"

                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import echarts from 'echarts'
import * as XLSX from 'xlsx'
import FileSaver from 'file-saver'
import { get_adm_datas, get_pc_spu_list, dmb_sellprod_mgmt, datas_filter_cond } from '@/api/goods/goods'
export default {
    components: {

    },
    data() {
        return {
            loading: false,
            pageSize: 10,
            pageNumber: 1,
            total: 0,
            active_product_id: '',  // 当前选中的商品id, 编辑、查看
            pageSizes: [10, 20, 30, 50, 100],
            chart: '',
            dialogShow: false,
            drawerShow: false,
            drawer_state: 'show',
            searchInfo: {
                spu_blurry: '', // 模糊搜索 商品编号/名称
                pos_key: '',
                soldout_status: '', // 售罄状态
                active_status: '', // 上架/下架状态
                cate_type: '', // 分类
            },
            soldouStatusOptions: [], //售罄状态
            activeStatusOptions: [], //上架/下架状态
            CateTypeOptions: [], //分类
            PubFromOptions: [], //商品售罄来源
            addDrawerinfo: {
                name: '',
                code: '',
                cate_type: '',
                active_status: '',
                thumb_img_key: ''
            },
            details_page_info: {
                pageSize: 10,
                pageNumber: 1,
                total: 0,
                pageSizes: [10, 20, 30, 40, 50],
            },
            example_data: [
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '售罄', category: '奶茶' },
            ],
            tableData: [],
            details_drawer_list: [],     // 商品详情 已售罄门店列表
            edit_item_info: {}, // 编辑商品信息
            add_rules: {
                name: [
                    { required: true, message: '请输入商品名称', trigger: 'blur' },
                ],
                active_status: [
                    { required: true, message: '请选择商品状态', trigger: 'change' }
                ],
                cate_type: [
                    { required: true, message: '请选择商品分类', trigger: 'change' }
                ],
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
        this.getfilterCond();
        this.getProductList();
    },
    methods: {
        getfilterCond() {
            const params = {
                classModel: 'SellProds'
            }
            datas_filter_cond(params).then(res => {
                if (res.rst == 'ok') {
                    res.data[0].forEach(item => {
                        switch (item.filterkey) {
                            case 'soldout_status':
                                this.soldouStatusOptions = item.options;
                                break;
                            case 'active_status':
                                this.activeStatusOptions = item.options;
                                break;
                            case 'cate_type':
                                this.CateTypeOptions = item.options;
                                break;
                            case 'pub_from':
                                this.PubFromOptions = item.options;
                                break;
                        }
                    })
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        search() {
            this.pageNumber = 1;
            this.getProductList();
        },
        reset() {
            this.searchInfo.spu_blurry = '';
            this.searchInfo.pos_key = '';
            this.searchInfo.active_status = '';
            this.searchInfo.cate_type = '';
            this.pageNumber = 1;
            this.getProductList();
        },
        processData(data) {
            return data.map((item, index) => {
                // 父节点ID（spu_code + 随机数保证唯一性）
                const parentId = `spu_${item.spu_code}_${index}`;

                // 递归处理子节点
                if (item.sku_list) {
                    item.sku_list = item.sku_list.map(sku => ({
                        ...sku,
                        id: `sku_${sku.sku_code}_${index}`, // 子节点唯一ID
                        parentId: parentId                  // 关联父节点
                    }));
                }

                return {
                    ...item,
                    id: parentId,
                    expanded: false,                 // 父节点唯一ID
                    hasChildren: item.sku_list.length > 0   // 标记是否有子节点
                };
            });
        },
        getProductList() {
            this.loading = true;
            const params = {
                page_num: this.pageNumber - 1,
                page_size: this.pageSize,
                spu_blurry: this.searchInfo.spu_blurry,              // 模糊查询，商品编号或者商品名称
                pos_key: this.searchInfo.pos_key,      // 1 非售罄，无售罄门店，6： 售罄。表示已经下发门店售罄
                active_status: this.searchInfo.active_status,       // 是否在dmb系统上架，5：已下架， 9： 在售
            }
            get_pc_spu_list(params).then(res => {
                if (res.rst == 'ok') {
                    // this.tableData = res.data[0]['content'];
                    const tableData = res.data[0]['content'].map(item => ({
                        id: item.spu_code,         // 唯一标识（父节点用spu_code）
                        spu_name: item.spu_name,
                        spu_code: item.spu_code,
                        create_time: item.create_time,
                        active_status: item.active_status,
                        last_time: item.last_time,
                        data_source: 'PC>ESB',
                        hasChildren: true,         // 必须字段
                        children: [],              // 初始为空数组
                        sku_list: item.sku_list     // 保留原始数据（可选）
                    }));
                    this.tableData = this.processData(tableData);
                    console.log(res, 'res')
                    this.total = res.data[0]['total_elements'];
                } else {
                    this.$message.error(res.error_msg)
                }
                this.loading = false;
            }).catch(err => {
                this.loading = false;
                console.log('get goods list error');
            })
        },


        handleCurrentChange(val) {
            this.pageNumber = val;
            this.getProductList();
        },

        handleSizeChange(val) {
            this.pageSize = val;
            this.handleCurrentChange(1);
        },

        async fetchSkuData(spuCode) {
            // 从原始数据中找到对应SPU的SKU列表
            console.log(this.tableData, 'this.tableData')
            const spu = this.tableData.find(item => item.id == spuCode);
            return spu ? spu.sku_list : [];
        },
        async loadChildData(row, treeNode, resolve) {
            try {
                // 模拟异步请求（实际应调用API）
                console.log(row.id, 'iiii')
                const res = await this.fetchSkuData(row.id);

                // 转换子节点数据格式
                const children = res.map(sku => ({
                    id: sku.sku_code,      // 唯一标识（子节点用sku_code）
                    unit_label: sku.unit_label,
                    sku_code: sku.sku_code,
                    hasChildren: false,    // 叶子节点无子节点
                    parentId: row.id,      // 关联父节点ID
                    parentRow: row         // 保存父节点引用
                }));

                console.log(children, 'children')
                // 更新数据并展开
                row.children = children;
                resolve(children);
            } catch (error) {
                console.error('加载失败:', error);
                resolve([]);
            }
        },
        handleExpandClick(row) {
            console.log(row, 'row')
            if (row.children.length > 0) {
                row.expanded = !row.expanded;
                // 已加载子节点：直接切换状态
                this.$refs.elTable.toggleRowExpansion(row, row.expanded);
            } else {
                // 未加载子节点：触发懒加载
                this.loadChildData(row, null, (children) => {
                    row.expanded = true; // 加载后自动展开
                });
            }
        },
        // 查找父级行的方法
        findParentRow(childRow) {
            // 如果子行有直接的父行引用，直接返回
            if (childRow.parentRow) {
                return childRow.parentRow;
            }

            // 如果有parentId，通过parentId查找父行
            if (childRow.parentId) {
                return this.tableData.find(item => item.id === childRow.parentId);
            }

            // 如果没有parentId，通过遍历查找包含该子行的父行
            for (let parentRow of this.tableData) {
                if (parentRow.children && parentRow.children.length > 0) {
                    const foundChild = parentRow.children.find(child => child.id === childRow.id);
                    if (foundChild) {
                        return parentRow;
                    }
                }
            }

            return null;
        },

        handlePrice(row) {
            console.log(row, 'row')

            // 判断是否为父级行（SPU）
            if (row.sku_list && row.sku_list.length > 0) {
                console.log('父级行 (SPU):', row)
                // 处理父级行的价格逻辑
                // 可以选择第一个SKU或者显示所有SKU的价格
                const firstSku = row.sku_list[0];
                if (firstSku) {
                    this.$router.push({
                        path: "/goodsManage/goods_price",
                        query: {
                            sku_code: firstSku.sku_code,
                            spu_code: row.spu_code,
                            row_info: JSON.stringify(row)
                        }
                    })
                }
            } else {
                // 子级行（SKU）
                console.log('子级行 (SKU):', row)

                // 获取父级行
                const parentRow = this.findParentRow(row);
                if (parentRow) {
                    console.log('找到父级行:', parentRow)
                    // 跳转到价格页面，传递SKU和SPU信息
                    this.$router.push({
                        path: "/goodsManage/goods_price",
                        query: {
                            sku_code: row.sku_code,
                            spu_code: parentRow.spu_code
                        }
                    })
                } else {
                    console.log('未找到父级行')
                    // 如果找不到父级，只传递SKU信息
                    this.$router.push({
                        path: "/goodsManage/goods_price",
                        query: {
                            sku_code: row.sku_code
                        }
                    })
                }
            }
        }
    },
};
</script>

<style scoped lang="scss">
.product-center {
    width: 100%;
    padding: 0 !important;
    height: 100%;
    background-color: var(--light-gray);
    box-sizing: border-box;

    .dark-btn {
        background-color: var(--btn-background-dark);
        border-color: var(--btn-background-dark);

        &:active {
            filter: brightness(0.9);
        }
    }

    .center_title {
        font-size: 24px;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: center;
    }

    .show_panel {
        display: flex;
        border: 2px solid var(--dev-border);
        width: 450px;
        border-radius: 30px;
        margin-top: 20px;
        font-weight: 600;
        margin-bottom: 20px;

        .chart_wrap {
            padding: 0 20px;
            // border: 1px solid red;
            width: 200px;
            height: 200px;

            .chart_title {
                height: 40px;
                line-height: 40px;
                font-size: 18px;
            }

            .chart {
                width: 150px;
                height: 150px;
            }
        }

        .panel_info {
            // border: 1px solid blue;
            vertical-align: bottom;
            display: grid;
            place-items: end;
            padding-bottom: 10px;
            margin-left: 10px;

            .info_item_wrap {
                .info_item {
                    margin: 15px 0;
                    font-size: 15px;

                    span:first-child {
                        display: inline-block;
                        width: 90px;
                    }
                }
            }
        }
    }

    .goods_info_wrap {
        display: flex;
        flex-direction: column;
        height: 100%;

        .goods_info_top {
            display: flex;
            padding-right: 20px;

            .info_btns {
                flex: 1;
                display: flex;
                margin-left: 30px;

                button {
                    align-self: flex-start;
                }
            }
        }

        .search_bar {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;

            .search_item {
                display: flex;
                align-items: center;
                margin: 10px 20px 10px 0;

                &>span {
                    margin-right: 10px;
                }
            }
        }

        .info_item_panel {
            margin-top: 10px;
            // height: calc(100vh - 475px);
            flex: 1;
            min-height: 450px;
            // border-top-left-radius: 20px;
            // border-top-right-radius: 20px;
            // border:2px solid ;
            // border-bottom: none;
            // border-color: var(--dev-border);
            overflow: hidden;

            ::v-deep .table_header {
                // background-color: red !important;
                height: 50px;
                font-size: 14px;

                th.el-table__cell {
                    color: #fff;
                    background-color: var(--table-header-bg-color) !important;
                }
            }

            ::v-deep .table_row {
                height: 120px;
                font-size: 14px;
            }

            ::v-deep .el-table__body-wrapper {
                &::-webkit-scrollbar {
                    width: 8px !important;
                    height: 8px !important;
                }
            }
        }

        .info_pages {
            height: 60px;
            // border-top: 1px solid var(--gray);
            background-color: #fff;
            overflow: hidden;
            // border-bottom-left-radius: 20px;
            // border-bottom-right-radius: 20px;
            // border:2px solid ;
            // border-top: none;
            // border-color: var(--dev-border);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding-right: 20px;
        }
    }

    ::v-deep .drawer {
        min-width: 550px !important;
    }

    .drawer {

        .img_box {
            width: 200px;
            height: 200px;
            margin-right: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
            background-color: var(--light-gray);
        }

        .upload_box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .upload_btn {
                font-size: 50px;
                color: var(--event-text-color);
                cursor: pointer;
                margin-bottom: 5px;

                &:hover {
                    filter: brightness(1.5);
                }

                &:active {
                    filter: brightness(1.2);
                }
            }

            .upload_title {
                color: var(--text-gray);
                font-size: 16px;
            }
        }

        .goods_img {
            position: relative;


        }

        .drawer_content {
            height: calc(100% - 60px);
            overflow-y: auto;
            box-sizing: border-box;
            padding: 20px 30px;

            .drawer_image,
            .drawer_badge {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 20px;
            }

            h2 {
                margin-bottom: 15px;
            }

            .info_item {
                font-size: 17px;
                margin: 30px 0;
            }

            ::v-deep .el-form-item label {
                font-size: 17px !important;
            }
        }

        .drawer_footer {
            height: 60px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            box-sizing: border-box;
            padding-right: 50px;
            border-top: 1px solid var(--gray);
        }


        .goods_info_drawer_content {
            height: 100%;
            padding: 25px;
            box-sizing: border-box;

            .t1 {
                margin-bottom: 35px;
            }

            .t2 {
                margin-bottom: 20px;
            }

            .info_content_wrap {
                height: calc(100% - 250px);
                margin-bottom: 10px;
                overflow-y: auto;

                .info_content {
                    background-color: var(--light-gray);
                    border: 1px solid transparent;
                    padding: 10px;

                    .info_item {
                        margin-bottom: 20px;
                        display: flex;
                        flex-wrap: wrap;
                        border-bottom: 1px solid #ccc;
                        font-size: 15px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        div {
                            margin-right: 10px;
                            margin-bottom: 12px;
                            width: 320px;
                        }
                    }
                }
            }

            .footer {
                height: 50px;

                button {
                    float: right;
                }
            }
        }
    }



    ::v-deep .el-dialog__header {
        padding: 0 !important;
    }

    .dialog_header {
        padding-left: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            position: relative;
            font-size: 18px;
            font-weight: 600;

            .line {
                position: absolute;
                width: 100%;
                bottom: -12px;
                height: 2px;
                background-image: linear-gradient(to right,
                        var(--table-header-bg-color) 0%,
                        var(--table-header-bg-color) 80%,
                        transparent 50%);
                background-size: 28px 1px;
                background-repeat: repeat-x;
            }
        }

        .close_btn {
            cursor: pointer;
            font-size: 26px;

            &:hover {
                filter: brightness(1.4);
            }
        }
    }

    .dialog_info {
        margin-top: 20px;
        margin-bottom: 40px;
        padding-left: 20px;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;

        span {
            color: red;
        }
    }

    .dialog_table {
        // padding: 0 30px 0 20px;
        margin: 0 30px 0 20px;
        border-radius: 10px 5px;
        overflow: hidden;

        ::v-deep .table_cell {
            height: 50px !important;
        }
    }

    .dialog_footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 60px;
        box-sizing: border-box;
        padding: 0 20px;
        margin-top: 35px;

        .btn_dange {
            background-color: var(--btn-danger-color);
        }

        .btn_primary {
            background-color: var(--btn-background-dark);
        }

        .btn_wrap {
            cursor: pointer;
            color: #fff;
            width: 155px;
            height: 40px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 0 15px;
            margin-right: 30px;

            &:hover {
                filter: brightness(1.2);
            }

            .btn_l {
                display: flex;
                align-items: center;

                img {
                    width: 18px;
                    margin-right: 8px;
                }
            }
        }
    }

    .up_ipt {
        position: fixed;
        width: 0px;
        top: -500px;
        left: -500px;
        z-index: -10;
    }
}

/* 隐藏默认展开箭头 */
::v-deep .el-table__expand-icon {
    // display: none !important;
}

/* 自定义图标样式 */
.custom-expand-icon {
    cursor: pointer;
    color: #606266;
    font-size: 16px;
    transition: transform 0.2s;
}

.custom-expand-icon.expanded {
    transform: rotate(90deg);
}
</style>
