<template>
    <div class="pushlishing_setting flex">
        <!-- 左侧详情 -->
        <div class="left">
            <div class="left_header">
                设置详情
            </div>
            <div class="left_content">
                <p class="left_details">
                    <span style="width:79px">发布名称：</span>
                    <span>{{ issueMessage.name }}</span>
                </p>
                <p class="left_details">
                    <span style="width:93px">发布周期：</span>
                    <span>{{ issueMessage.start_time }}--{{ issueMessage.end_time }} </span>
                </p>
                <!-- {{issueMessage.usage_type}} -->
                <p v-if="issueMessage.platform == 3" class="left_details">
                    <span style="width:78px">屏幕类型：</span>
                    <!-- <span v-if="issueMessage.usage_type == 'dmb'"> DMB </span>
                    <span v-else-if="issueMessage.usage_type == 'trailers'"> 品宣屏 </span>
                    <span v-else-if="issueMessage.usage_type == 'children'"> 儿童屏 </span> -->
                    <span v-for="item in typesKey">
                        <span v-if="issueMessage.usage_type == item[0]">{{ item[1] }}</span>
                    </span>
                </p>

                <p v-else class="left_details">
                    <span style="width:81px">屏幕规格：</span>
                    <span v-if="issueMessage.platform == 2">
                        <span v-for="item in issueMessage.dmb_spec" :key="item"
                            :class="item == 0 ? 'screen_h' : 'screen_v'"></span>
                        <span v-if="issueMessage.platform == 2" style="margin-left:5px">餐牌组
                            1*{{ issueMessage.dmb_spec.length }}</span>
                    </span>
                    <span v-else-if="issueMessage.platform == 4">
                        <span v-for="item in issueMessage.vs_spec.xCount" :key="item"
                            :class="vs_prec.v_or_h == 'h' ? 'screen_h' : 'screen_v'"></span>
                        <span style="margin-left:5px">联屏 {{ vs_prec.yCount }}* {{ vs_prec.xCount }}</span>
                    </span>
                </p>

                <p class="left_details">
                    <span style="width:80px">播放类型：</span>
                    <span v-if="issueMessage.play_style == 1">轮播</span>
                    <span v-else>独占</span>
                </p>
                <p class="left_details" style="margin-top:20px" v-show="issueMessage.platform == 2">
                    <span style="width:95px">经营时段: </span>
                    <span>{{ issueMessage.play_info.daypartgroup }}</span>
                </p>
                <p v-if="issueMessage.platform == 3" class="left_details">
                    <span style="width:78px">播放详情：</span>
                    <span v-if="issueMessage.play_info.play_mode == 'full_day'">全天</span>
                    <span
                        v-else-if="issueMessage.play_info.play_mode == 'week_day' || issueMessage.play_info.play_mode == 'week_range'">按星期</span>
                    <span v-else-if="issueMessage.play_info.play_mode == 'single_use_range'">按指定时段</span>

                </p>
                <!-- <p v-if="issueMessage.platform == 4" class="left_details">
                    <span style="width:80px">播放间隔：</span>
                    <span> {{ issueMessage['play_info']['waitting_time'] }} 秒 </span>
                </p> -->
            </div>
            <div v-if="issueMessage.platform == 2" class="daypart">
                <div class="daypart_header">
                    经营时段详情
                </div>
                <div v-for="(item, index) in timeframe" :key="item.name" class="daypart_info">
                    <span class="num">{{ index + 1 }}</span>
                    <span>时段名称： {{ item.name }} <span> {{ item.starttime }} ~ {{ item.endtime }} </span> </span>
                </div>
            </div>
        </div>
        <!-- 右侧筛选 -->
        <div class="flex right flex-1">
            <!-- 按市场门店筛选 -->
            <div class="search_wrap">
                <div class="flex flex-1">
                    <div>
                        <span style="color:rgba(56, 56, 56, 1)">营运市场：</span>
                        <el-select v-model="queryList.marketname" clearable multiple filterable placeholder="请选择投放市场"
                            size="small" collapse-tags style="width:166px;margin-right:12px">
                            <el-option v-for="item in options" :key="item[1]" :label="item[1]" :value="item[0]">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <!-- <span style="color:rgba(56, 56, 56, 1)">按门店：</span>
                        <el-input placeholder="门店ID/名称" size="small" prefix-icon="el-icon-search"
                            @keyup.enter.native='handleSearch' v-model="queryList.search"
                            style="width:166px;margin-right:12px"></el-input> -->
                    </div>
                </div>
                <!-- <div class="reset" @click="reset(1)">
                    重置
                </div> -->
                <el-button size="small" style="float:right" @click="reset(1)">重置</el-button>
            </div>
            <!-- 按标签筛选 -->
            <div class="tags_filtering">
                <div>
                    <!-- <span style="color:rgba(56, 56, 56, 1)">标签筛选：</span>
                    <el-select v-model="queryList.changeCondition"  filterable placeholder="" size="small" style="width:166px;margin-right:12px">
                        <el-option v-for="item in condition" :key="item.value" :label="item.label" :value="item.value"></el-option> -->
                    <!-- </el-select> -->
                    <span style="color:rgba(56, 56, 56, 1)">门店类型：</span>

                    <el-select v-model="queryList.shopname" clearable multiple collapse-tags filterable
                        placeholder="请选择投放市场" size="small" @change="selectTags" style="width:206px;margin-right:12px">
                        <el-option v-for="item in shopOptions" :key="item[1]" :label="item[1]" :value="item[0]">
                        </el-option>
                    </el-select>
                    <el-button size="small" style="float:right" @click="searchShops">查询</el-button>
                </div>
                <div class="tags_wrap flex" v-show="issueMessage.platform == 3 || issueMessage.platform == 2">

                    <!-- <div style="margin-top:6px">选中标签：</div>
                    <div class="tags_list flex flex-1">
                        <div v-for="(item, index) in tagsList" class="every_tag" :key="index">
                            <img src="../../assets/img/gray_tag_icon.svg" style="width:18px;height:18px;margin-top:-2px"
                                alt="">
                            <span class="tags_name">{{ item.value }}</span>
                            <i class="el-icon-close remove_tag" @click="removeTag(item, index)"></i>
                        </div>
                    </div> -->
                    <span style="color:rgba(56, 56, 56, 1);padding-top: 8px;">门店标签:</span>
                    <el-select v-model="screenTagsValue" placeholder="请选择屏幕类型"
                        style="width:206px;margin-left: 15px;margin-bottom: 10px;" multiple="true">
                        <el-option v-for="item in screenTages" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                    <!-- <el-button size="small" style="float:right" @click="reset(2)">重置</el-button> -->
                </div>
            </div>
            <!-- 选中的门店 -->
            <div class="shops_wrap flex" v-loading="shoploading">
                <div class="shop_title flex">
                    <div v-if="totalShop != 0"></div>
                </div>
                <div class="shop_content flex ">
                    <div v-for="(item, index) in shopsList" class="every_shop flex" :key="index">
                        <el-checkbox v-model="item.checked" style="margin-right:5px" @change="selectCheckout(item)">
                        </el-checkbox>
                        <img src="../../assets/img/home_img/shop.png"
                            style="width:30px;height:30px;margin-top:-2px" alt="">
                        <!-- <div class="flex-1 shop_info" style="width:65%;"> -->
                            <div class="flex-1 shop_info" style="width:65%;overflow:hidden;" :title='item.storecode'>
                            <p>{{ item.storecode }}</p>
                            <p class="text_overflow" :title="item.storename">{{ item.storename }}</p>
                        </div>
                    </div>
                </div>
                <div class="paginat" v-show="shopsList.length != 0">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                        :total="ShopTotal">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { get_adm_datas } from "@/api/shopManage/shop";
    import { get_daypart_data } from "@/api/contentdeploy/contentdeploy"
    import { get_screen_tags } from "@/api/system/label"

    import { get_strategy_data } from "@/api/strategy/strategy"

    export default {
        components: {

        },
        data() {
            return {
                queryList: {
                    shopname: '',
                    marketname: '',
                    changeCondition: 1,
                },
                options: [],
                shopOptions: [],
                condition: [
                    {
                        label: '且(包含全部已选标签,标签选择不能超过十个)',
                        value: 2
                    },
                    {
                        label: '或(包含任何一个已选标签)',
                        value: 1
                    },
                    {
                        label: '非(不包含任何已选标签)',
                        value: 0
                    }],
                tagsList: [],
                shopsList: [],
                dsusage_type: "",
                vs_spec: "",
                dmb_spec: "",
                daypartgroup: "",
                totalShop: 0,
                timeframe: [],
                ShopTotal: "",
                page: 0,
                pagesize: 10,
                issueMessage: {},
                optionsType: [],
                types: [],
                typesKey: [],
                noSelectShops: [],
                shoploading: false,
                screenTages: [],
                screenTagsValue: []
            };
        },
        props: {

        },
        computed: {

        },
        watch: {

        },
        created() {
            // console.log(this.types,this.typesKey,"123");
        },
        mounted() {
            setTimeout(() => {
                this.options = this.$store.state.deployDataFilters[6]['options']
                console.log(this.options, 'this.optionsthis.options');
                this.shopOptions = this.$store.state.deployDataFilters[5]['options']
                this.optionsType = this.$store.state.deployDataFilters[3]['options'];
                this.optionsType.forEach(item => {
                    this.typesKey.push(item)
                })
                this.getScreenTags()
            }, 500);
            this.get_btpub_detail_info()

        },
        methods: {
            reset(val) {
                console.log(val);
                switch (val) {
                    case 1:
                        this.queryList.marketname = [];
                        console.log(this.queryList.marketname, 'queryList.marketname');
                        break
                    case 2:
                        this.tagsList = [];
                        this.queryList.shopname = [];
                        break
                    case 3:
                        this.shopsList = [];
                        break
                }
                this.shopsList = [];
                this.totalShop = 0
            },
            // 移除选中的标签
            removeTag(val, index) {
                this.queryList.shopname.forEach((item, index1) => {
                    if (val.key == item) {
                        this.queryList.shopname.splice(index1, 1)
                    }
                })
                this.tagsList.splice(index, 1)
            },
            getDayPart() {
                const params = {
                    classModel: "SysDayPart",
                    page: 0,
                    size: 100
                }
                get_daypart_data(params).then(res => {
                    console.log("res1", res)
                    res.data[0].content.forEach((item, index) => {
                        if (item.group_name == this.daypartgroup) {
                            this.timeframe = item.attr_list
                        }
                    })
                })
            },
            selectTags(val) {
                this.tagsList = []
                val.forEach(item => {
                    this.shopOptions.forEach(item1 => {
                        if (item == item1[0]) {
                            // console.log(item1);
                            this.tagsList.push({ key: item1[0], value: item1[1] })
                        }
                    })

                })
            },
            searchShops() {
                this.shoploading = true;
                let params = {
                    "classModel": "GroupShop",
                    "sort": "",//非必要，排序规则，storecode,createdAtS
                    "page": this.page, //起始页码,
                    "size": this.pagesize, //每页数据量,
                    "blurry": this.queryList.search,//搜索门店编号或者名称模糊查找　
                    "opsmarkets": this.queryList.marketname,//营运市场编号,多个市场
                    "itmarket": "",//it市场
                    "storetypes": this.queryList.shopname,//门店类型
                    "daypart_groupname": this.daypartgroup,//系统门店day类型
                    "dmb_spec": this.dmb_spec,//门店dmb规格
                    "dsusage_type": this.dsusage_type,//门店屏幕类型
                    "screen_tags": this.screenTagsValue,//门店便签 list
                    "filtertags_type": "",//门店便签#"must> AND, must_not> OR should > NOT
                    "v_or_h": this.v_or_h,
                    "vs_spec": this.vs_spec
                    // "":""//其他参数参考　datas_filter_cond　定义　　　　　　　
                }
                console.log(params, '0params');
                get_adm_datas(params).then(res => {
                    console.log(res.data[0].content, 'res8');
                    res.data[0].content.forEach(item => {
                        item.checked = false;
                    })
                    this.shopsList = res.data[0].content
                    console.log("this.shopsList", this.shopsList);
                    this.ShopTotal = res["data"][0]["totalElements"]
                    this.$emit("SearchState", this.shopsList)
                    this.totalShop = res.data[0].totalElements;
                    console.log(this.noSelectShops);
                    this.shopsList.forEach(item => {
                        if (this.noSelectShops.length != 0) {
                            this.noSelectShops.forEach(item1 => {
                                if (item.shop_id == item1) {
                                    item.checked = false;
                                } else {
                                    item.checked = true;
                                }
                            })
                        } else {
                            item.checked = true;
                        }
                    })
                    this.shoploading = false;
                })
            },
            handleSizeChange(val) {
                console.log("val", val);
                this.pagesize = val;
                this.searchShops()
            },
            handleCurrentChange(val) {
                console.log("val", val);
                this.page = val - 1;
                this.searchShops()
            },
            get_btpub_detail_info() {
                const params = {
                    btplan_id: sessionStorage.getItem("btplan_id"),
                    range: 'simple'
                }
                // console.log(this.issueMessage.play_info.daypartgroup,'this.issueMessage.play_info.daypartgroup');
                get_strategy_data(params).then(data => {
                    if (data.rst == 'ok') {
                        console.log("dadatadatadatadatadatadatadatadatata", data);
                        this.issueMessage = data["data"][0];
                        console.log(this.issueMessage, 'this.issueMessage');
                        if (this.issueMessage.platform == 2) {
                            this.daypartgroup = this.issueMessage.play_info.daypartgroup;
                            this.dmb_spec = this.issueMessage.dmb_spec
                            this.getDayPart()
                        } else if (this.issueMessage.platform == 3) {
                            // console.log(this.issueMessage.usage_type,'this.issueMessage.usage_type');
                            this.v_or_h = this.issueMessage['play_info']['v_or_h'];
                            this.dsusage_type = this.issueMessage.usage_type
                        } else if (this.issueMessage.platform == 4) {
                            this.vs_spec = this.issueMessage.vs_spec;
                            const vs_spec = {}
                            vs_spec["v_or_h"] = this.vs_spec[0]
                            vs_spec["yCount"] = this.vs_spec[2]
                            vs_spec["xCount"] = this.vs_spec.split("*")[1] * 1
                            // this.vs_spec = vs_spec;
                            this.vs_prec = vs_spec
                        }
                    } else {

                    }
                })
            },
            selectCheckout(item) {
                if (item.checked == false) {
                    this.noSelectShops.push(item.shop_id)
                } else {
                    let index = this.noSelectShops.indexOf(item.shop_id)
                    this.noSelectShops.splice(index, 1)
                }
                console.log(this.noSelectShops);
            },
            getScreenTags() {
                get_screen_tags({
                    page: 0,
                    size: 30
                }).then(res => {
                    console.log('res', res);
                    this.screenTages = res['data'][0]['tags_list'];
                    console.log(this.screenTages);
                })
            }
        },
    };
</script>

<style scoped lang="scss">
    .pushlishing_setting {
        margin-top: 10px;
        height: calc(100vh - 196px);
    }

    .left {
        display: flex;
        flex-direction: column;
        width: 27%;
        max-width: 338px;
        border-right: 1px solid rgba(236, 235, 235, 1);

        .left_header {
            color: rgba(80, 80, 80, 1);
            font-size: 14px;
            font-weight: bold;
            height: 45px;
            line-height: 45px;
            padding-left: 15px;
            border-bottom: 1px solid rgba(236, 235, 235, 1);
        }

        .left_content {
            height: 330px;
            overflow: auto;
            border-bottom: 1px solid rgba(236, 235, 235, 1);

            .left_details {
                display: flex;
                align-items: center;
                margin-top: 15px;
                padding-left: 15px;
                font-size: 14px;

                .screen_v {
                    display: inline-block;
                    width: 16px;
                    height: 26px;
                    font-size: 13px;
                    color: rgba(80, 80, 80, 1);
                    background-color: rgba(42, 130, 228, 0.16);
                    text-align: center;
                    line-height: 26px;
                    border: rgba(125, 176, 233, 1) solid 1px;
                    margin-left: -1px;
                }

                .screen_h {
                    display: inline-block;
                    width: 25px;
                    font-size: 13px;
                    height: 18px;
                    color: rgba(80, 80, 80, 1);
                    background-color: rgba(42, 130, 228, 0.16);
                    text-align: center;
                    line-height: 18px;
                    border: rgba(125, 176, 233, 1) solid 1px;
                    margin-left: -1px;
                }
            }
        }

        .daypart {
            flex: 1;
            padding: 10px 0 10px 15px;
            overflow: auto;
            color: rgba(80, 80, 80, 1);

            .daypart_header {
                font-weight: bold;
            }

            .daypart_info {
                margin-top: 15px;
                display: flex;
                align-items: center;
                font-size: 14px;

                .num {
                    display: inline-block;
                    width: 19px;
                    height: 19px;
                    border-radius: 50%;
                    overflow: hidden;
                    text-align: center;
                    line-height: 19px;
                    margin-right: 13px;
                    color: rgba(140, 139, 139, 1);
                    background-color: rgba(229, 229, 229, 1);
                    font-size: 12px;
                }
            }
        }
    }

    .right {
        flex-direction: column;

        .reset {
            color: rgba(80, 80, 80, 1);
            width: 30px;
            font-size: 13px;
            cursor: pointer;
        }

        .reset:hover {
            color: rgba(80, 80, 80, .8);
        }

        .search_wrap {
            width: 100%;
            border-bottom: 1px solid rgba(236, 235, 235, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 45px;
            padding: 0 17px 0 10px;
        }

        .tags_filtering {
            padding: 10px 17px 3px 10px;
            border-bottom: 1px solid rgba(236, 235, 235, 1);

            .tags_wrap {
                margin-top: 10px;

                .tags_list {
                    flex-wrap: wrap;
                    max-height: 70px;
                    overflow-y: auto;
                    margin-right: 18px;

                    .every_tag {
                        display: flex;
                        align-items: center;
                        // width: 118px;
                        padding: 0 8px;
                        height: 31px;
                        box-sizing: border-box;
                        border: rgba(166, 166, 166, 1) solid 1px;
                        color: rgba(80, 80, 80, 1);
                        border-radius: 24px;
                        margin-bottom: 8px;
                        margin-right: 8px;
                        overflow: hidden;

                        .tags_name {
                            color: rgba(128, 128, 128, 1);
                            font-size: 14px;
                            margin-right: 3px;
                        }

                        .remove_tag {
                            font-size: 16px;
                            cursor: pointer;
                        }
                    }
                }
            }
        }

        .shops_wrap {
            padding: 0px 17px 0 10px;
            flex-direction: column;
            max-height: calc(100% - 180px);

            // flex: 1;
            .shop_title {
                height: 40px;
                line-height: 40px;
                justify-content: space-between;

                div:nth-of-type(1) {
                    font-size: 14px;
                    color: rgba(166, 166, 166, 1);
                }
            }

            .shop_content {
                height: calc(100% - 40px);
                flex-wrap: wrap;
                overflow-y: auto;

                .every_shop {
                    box-sizing: border-box;
                    width: 163px;
                    height: 48px;
                    align-items: center;
                    padding: 0px 7px;
                    margin-right: 15px;
                    margin-bottom: 20px;
                    border: 1px solid rgba(108, 178, 255, 1);

                    .shop_info {
                        padding: 5px 0;
                        margin-left: 5px;

                        p {
                            font-size: 12px;
                            height: 50%;
                            line-height: 17px;
                            // border: 1px solid red;
                        }

                    }
                }
            }
        }
    }

    .text_overflow {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<style>
    /* 把复选框改为红色 */
    .pushlishing_setting .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background: var(--base-color) !important;
        border-color: var(--base-color) !important;
    }

    .pushlishing_setting .el-checkbox__inner:hover {
        border-color: var(--base-color) !important;
    }

    .pushlishing_setting .el-checkbox__input.is-focus .el-checkbox__inner {
        border-color: var(--base-color) !important;
    }

    .pushlishing_setting .el-checkbox__inner {
        /* border-color:red !important; */
        width: 18px !important;
        height: 18px !important;
        border-radius: 50%;
    }

    .pushlishing_setting .el-checkbox__inner::after {
        left: 6px !important;
        top: 3px !important;
    }

    .pushlishing_setting .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
        left: 0px !important;
        top: 7px !important;
    }

    .paginat {
        margin-top: 20px;
    }

    .el-pagination {
        float: right;
    }
</style>