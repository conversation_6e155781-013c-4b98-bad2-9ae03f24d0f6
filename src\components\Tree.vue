<template>
  <div>
    <div class="tree" v-if="group_type == 1">
      <div class="flex">
        <div class="tree-text flex-center cursor-pointer" style="top: 10px; left: 10px">
          {{ group_type == 2 ? onlyStr : '总部' }}
        </div>
        <!-- <el-button v-show="group_type !== 2" type="default" style="margin-left: 10px" @click="creatRegion">添加品牌
        </el-button> -->
      </div>

      <div class="tree-group tree-group-animation" style="margin-left: 230px">
        <div class="tree-children" v-for="(children, index) in treeList[0].children" :style="getTreetextStyle(index)">
          <div class="flex">
            <div class="tree-text position-relative">
              <div class="tree-btn" @click="firstLayerClick(children, index)" v-if="!children.show && children.type!='shop'">
                +
              </div>
              <div class="tree-btn" @click="firstLayerClick(children, index)" v-if="children.show && children.type!='shop'">
                -
              </div>
              
              <div class="flex-1  hg-100 cursor-pointer more_text" :title="children.text" style="overflow:hidden;text-overflow:ellipsis;" @click="showAddBrand(children, index)">
                <span>{{ children.text }}</span>
              </div>
              
              <div class="position-absolute shop-count-num">{{shopCountList.length>0?shopCountList[index]:0}}</div> <!-- :style="{padding:shopCountList[index]<10?'0px':''}" -->
            </div>
            <!-- <div class="mg-left-10" style="height:40px;width:120px;line-height:40px;">门店数量：{{shopCountList.length>0?shopCountList[index]:0}}</div> -->
            <!-- <el-button type="default" style="margin-left: 15px" @click="showAddBrandAddShop(children, index)">添加门店
            </el-button> -->
          </div>

          <div class="tree-group" v-if="children.show" :style="getTreeGroupStyle(index)">
            <div class="tree-children" v-for="(children2, index2) in children.children">
              <div class="tree-text" :style="getTreetextStyle(index2)">
                <div class="tree-btn" @click="firstLayerClick(children2, index, index2)" v-if="!children2.show && children2.type!='shop'">
                  +
                </div>
                <div class="tree-btn" @click="firstLayerClick(children2, index, index2)" v-if="children2.show && children2.type!='shop'">
                  -
                </div>
                <div class="flex-1  hg-100 cursor-pointer more_text" :title="children2.text" @click="showAddBrand(children2, index, index2)">
                  <span>{{ children2.text }}</span>
                </div>
              </div>
              <div class="tree-group" v-if="children2.show" :style="getTreeGroupStyle(index2)">
                <div class="tree-children" v-for="(children3, index3) in children2.children">
                  <div class="tree-text" :style="getTreetextStyle(index3)">
                    <div class="tree-btn" @click="firstLayerClick(children3, index, index2, index3)"
                      v-if="!children3.show && children3.type!='shop'">
                      +
                    </div>
                    <div class="tree-btn" @click="firstLayerClick(children3, index, index2, index3)"
                      v-if="children3.show && children3.type!='shop'">
                      -
                    </div>
                    <div class="flex-1  hg-100 cursor-pointer more_text" :title="children3.text"
                      @click="showAddBrand(children3, index, index2, index3)">
                      <span>{{ children3.text }}</span>
                    </div>
                  </div>
                  <div class="tree-group" v-if="children3.show" :style="getTreeGroupStyle(index3)">
                    <div class="tree-children cursor-pointer" v-for="(children4, index4) in children3.children"
                      @click="clickShop(children4)" :key="index4">
                      <div class="tree-text more_text" :title="children4.text" :style="getTreetextStyle(index4)" style="width:160px;padding:0 10px 0 15px">
                        <span>{{ children4.text }}</span>
                      </div>
                      <div class="line" :class="
                          'line-fourthlayer' + index + index2 + index3 + index4
                        " :style="
                          getLayerLineStyle(
                            index4,
                            'line-fourthlayer' +
                              index +
                              index2 +
                              index3 +
                              index4
                          )
                        "></div>
                    </div>
                  </div>

                  <div :class="'line-thirdlayer' + index + index2 + index3" class="line" :style="
                      getLayerLineStyle(
                        index3,
                        'line-thirdlayer' + index + index2 + index3
                      )
                    "></div>
                </div>
              </div>
              <div :class="'line-secondlayer' + index + index2" class="line" :style="
                  getLayerLineStyle(index2, 'line-secondlayer' + index + index2)
                "></div>
            </div>
          </div>
          <div :class="'line-firstlayer' + index" class="line"
            :style="getLayerLineStyle(index, 'line-firstlayer' + index)"></div>
        </div>
      </div>
    </div>
    <div class="tree" v-if="group_type !== 1">
      <div class="flex">
        <div class="tree-text flex-center cursor-pointer" style="top: 10px; left: 10px"
          @click="showAddBrand(onlyStr, 0)">
          {{ group_type == 2 ? onlyStr.text : '总部' }}
        </div>
      </div>

      <div class="tree-group tree-group-animation" style="margin-left: 230px">
        <div class="tree-children" v-for="(children, index) in treeList[0].children" :style="getTreetextStyle(index)">
          <div class="flex">
            <div class="tree-text">
              <div class="tree-btn" @click="firstLayerClick(children, index)" v-if="!children.show && children.type!='shop'">
                +
              </div>
              <div class="tree-btn" @click="firstLayerClick(children, index)" v-if="children.show && children.type!='shop'">
                -
              </div>
              <div class="flex-1  hg-100 cursor-pointer more_text" :title="children.text" @click="showAddBrand(children, index)" :style="{width:children.type=='shop'?'160px':'',padding:children.type=='shop'?'0 10px 0 15px':''}">
                <span>{{ children.text }}</span>
              </div>
            </div>
          </div>

          <div class="tree-group" v-if="children.show" :style="getTreeGroupStyle(index)">
            <div class="tree-children" v-for="(children2, index2) in children.children">
              <div class="tree-text" :style="getTreetextStyle(index2)">
                <div class="tree-btn" @click="firstLayerClick(children2, index, index2)" v-if="!children2.show && children2.type!='shop'">
                  +
                </div>
                <div class="tree-btn" @click="firstLayerClick(children2, index, index2)" v-if="children2.show && children2.type!='shop'">
                  -
                </div>
                <div class="flex-1  hg-100 cursor-pointer more_text" :title="children2.text" @click="showAddBrand(children2, index, index2)">
                  <span>{{ children2.text }}</span>
                </div>
              </div>
              <div class="tree-group" v-if="children2.show" :style="getTreeGroupStyle(index2)">
                <div class="tree-children" v-for="(children3, index3) in children2.children">
                  <div class="tree-text" :style="getTreetextStyle(index3)">
                    <div class="tree-btn" @click="firstLayerClick(children3, index, index2, index3)"
                      v-if="!children3.show && children3.type!='shop'">
                      +
                    </div>
                    <div class="tree-btn" @click="firstLayerClick(children3, index, index2, index3)"
                      v-if="children3.show && children3.type!='shop'">
                      -
                    </div>
                    <div class="flex-1  hg-100 cursor-pointer more_text" :title="children3.text"
                      @click="showAddBrand(children3, index, index2, index3)">
                      <span>{{ children3.text }}</span>
                    </div>
                  </div>
                  <div class="tree-group" v-if="children3.show" :style="getTreeGroupStyle(index3)">
                    <div class="tree-children cursor-pointer" v-for="(children4, index4) in children3.children"
                      @click="clickShop(children4)" :key="index4">
                      <div class="tree-text more_text" :style="getTreetextStyle(index4)" :title="children4.text" style="width:160px;padding:0 10px 0 15px">
                        <span>{{ children4.text }}</span>
                      </div>
                      <div class="line" :class="
                          'line-fourthlayer' + index + index2 + index3 + index4
                        " :style="
                          getLayerLineStyle(
                            index4,
                            'line-fourthlayer' +
                              index +
                              index2 +
                              index3 +
                              index4
                          )
                        "></div>
                    </div>
                  </div>

                  <div :class="'line-thirdlayer' + index + index2 + index3" class="line" :style="
                      getLayerLineStyle(
                        index3,
                        'line-thirdlayer' + index + index2 + index3
                      )
                    "></div>
                </div>
              </div>
              <div :class="'line-secondlayer' + index + index2" class="line" :style="
                  getLayerLineStyle(index2, 'line-secondlayer' + index + index2)
                "></div>
            </div>
          </div>
          <div :class="'line-firstlayer' + index" class="line"
            :style="getLayerLineStyle(index, 'line-firstlayer' + index)"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {simple_get_shop_struct,get_shop_count} from '@/api/systemCenter/structure'
  export default {
    props: {
      treeList: {
        type: Array,
        default: [
          {
            text: '大陆',
            children: [
              {
                text: '品牌1',
                show: true,
                children: [
                  {
                    text: '陕西',
                    show: false,
                    children: [
                      {
                        text: '西安',
                        show: false,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                      {
                        text: '宝鸡',
                        show: false,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                      {
                        text: '宝鸡',
                        show: false,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                    ],
                  },
                  {
                    text: '山西',
                    show: true,
                    children: [
                      {
                        text: '西安',
                        show: true,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                      {
                        text: '西安',
                        show: true,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                text: '品牌2',
                show: false,
                children: [
                  {
                    text: '陕西',
                    show: true,
                    children: [
                      {
                        text: '宝鸡',
                        show: true,
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                    ],
                  },
                  {
                    text: '陕西',
                    show: false,
                    children: [
                      {
                        text: '宝鸡',
                        children: [
                          { text: '门店1' },
                          { text: '门店2' },
                          { text: '门店2' },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                text: '品牌3',
                show: true,
                children: [],
              },
            ],
          },
        ],
      },
      treeList: {
        type: Array,
        default: [
          {
            text: '大陆',
            children: [{}],
          },
        ],
      },
    },

    data() {
      return {
        level_type_value: '',
        onlyStr: '',
        group_type: '',
        width: 160,
        height: 40, //高
        marginX: 60, //左右间距
        marginY: 60, //上下间距
        result: {
          width: '170px',
          left: '-170px',
          height: '30px',
          top: '-10px',
        },
        firstLayer: true,
        treeData: {
          treeOne: [],
          treeTwo: [],
          treeThree: [],
        },
        shopCountList:[]
      }
    },
    created() {
      this.simple_get_shop_structOnly()
    },
    mounted() {
      this.group_type = parseInt(localStorage.getItem('group_type'))
      // console.log('this.group_type 111', this.group_type)
      this.simple_get_shop_struct_fun()
    },
    methods: {
      addShop() {
        // 添加门店
        this.$emit('addShop', '点击总部')
      },
      creatRegion() {
        // 创建分区
        this.$emit('creatRegion')
      },
      clickZongBu() {
        this.$emit('clickZongBu', '点击总部')
        this.$emit('setAllProvince')
        //  this.$emit('notSetAllProvince')
      },
      simple_get_shop_structOnly() {
        let params = {
          group_id: '#',
          direction: 0,
          sel_unit: 'shop',
        }
        simple_get_shop_struct(params).then((res) => {
          if (res.rst == 'ok') {
            sessionStorage.setItem('level_type_value',JSON.stringify(res.data[0].structure[0]))
            // console.log('12343546999999', res.data[0].structure[0])
            this.onlyStr = res.data[0].structure[0]
            // console.log('res.data[0].tree_data', this.onlyStr)
          }
        })
      },
      simple_get_shop_struct_fun() {
        let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''
        let params = {
          user_id: this.$store.state.user_id || user_id,
          group_id:
            this.$store.state.group_id || localStorage.getItem('group_id'),
          direction: 0,
          sel_unit: 'shop',
        }
        simple_get_shop_struct(params).then((res) => {
          if (res.rst == 'ok') {
            let structure = res.data[0].structure
            structure.forEach((item) => {
              item.children = []
              item.show = false
            })
            this.treeData = {
              treeOne: [],
              treeTwo: [],
              treeThree: [],
            }
            
            this.treeList[0].children = structure
            // console.log(this.treeList);
            this.getShopCpunt(this.treeList[0].children)
            this.treeData.treeOne = structure
            this.$emit('getTreeData', this.treeData)
            // console.log('tree data', this.treeData)
          }
        })
      },
      simple_get_shop_struct_child(ground_id, index, index2, index3) {
          // console.log('这是第三个');
        let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''
        let params = {
          user_id: this.$store.state.user_id || user_id,
          group_id: ground_id,
          direction: 0,
          sel_unit: 'shop',
        }
        simple_get_shop_struct(params).then((res) => {
          if (res.rst == 'ok') {
            let structure = res.data[0].structure
            structure.forEach((item) => {
              item.children = []
              item.show = false
            })

            if (index !== undefined && index2 == undefined) {
              this.$emit('addBrandOne', index)
              this.treeList[0].children[index].children = structure
              // console.log(structure,33535);
              this.treeData.treeTwo = structure
              // if(structure.length == 0){
              //   // 地区设置所有的省
              //   this.$emit('setAllProvince')
              // }else{
              //   this.$emit('notSetAllProvince')
              // }
              if (this.group_type !== 2) {
                this.$emit('setAllProvince')
              }
            } else if (index2 !== undefined && index3 == undefined) {
              this.$emit('addBrandTwo', index2)
              this.treeList[0].children[index].children[index2].children =
                structure
              this.treeData.treeThree = structure
            } else if (index3 !== undefined) {
              this.$emit('addBrandThree', index3)
              this.treeList[0].children[index].children[index2].children[
                index3
              ].children = structure
            }
            this.$emit('getTreeData', this.treeData)
            // window.console.log('tree data treeData', this.treeData)
            // console.log('tree data 111', this.treeList)
          }
        })
        // this.$emit('addBrand')
      },

      getTreetextStyle(index) {
        // let result = {}
        return `margin-top:10px;`
      },
      getTreeGroupStyle(index) {
        // let result = {}
        return `margin-left:230px;`
      },
      getLayerLineStyle(index, className) {
        this.level_type_value = JSON.parse(
          sessionStorage.getItem('level_type_value')
        ).type
        // console.log('level_type_value', this.level_type_value)

        if (index == 0) {
          return this.result
        } else {
          this.$nextTick(() => {

            const currentLine = document.querySelectorAll('.' + className)[0]

            if (currentLine) {
              const lastParentNode = currentLine.parentNode.previousSibling
              const height = parseInt(getComputedStyle(lastParentNode).height) + 10
              currentLine.style.height = height + 'px'
              currentLine.style.width = this.result.width
              currentLine.style.left = this.result.left
              currentLine.style.top = -(height - this.height / 2 - index )  + 'px'
            }
          })
        }
      },
      getSecondLayerLineStyle(index, className) {
        if (index == 0) {
          return this.result
        } else {
          this.$nextTick(() => {
            const currentLine = document.querySelectorAll('.' + className)[0]
            if (currentLine) {
              const lastParentNode = currentLine.parentNode.previousSibling
              const height =
                parseInt(getComputedStyle(lastParentNode).height) + 10
              currentLine.style.height = height + 'px'
              currentLine.style.width = this.result.width
              currentLine.style.left = this.result.left
              currentLine.style.top = -(height - this.height / 2 - index) + 'px'
            }
          })
        }
      },
      getLineStyle(index) {
        return this.result
      },
      firstLayerClick(item, index, index2, index3) {
        // console.log(item,index,index2,index3,99999999999);
        if (item.type == 'shop') {
          this.clickShop(item)
        }

        item.show = !item.show
        let temp_ground_id = item.id
        let last_ground_id = ''
        if (temp_ground_id.charAt(0) == 'g') {
          last_ground_id = parseInt(temp_ground_id.split('g')[1])
        }
        // if (item.show) {
        this.simple_get_shop_struct_child(last_ground_id, index, index2, index3)
        // }
      },
      showAddBrandAddShop(item, index, index2, index3) {
        if (index !== undefined) {
          this.$emit('addShop', '点击品牌')
          this.$emit('addBrandOne', index)
        }
        this.firstLayerClick(item, index, index2, index3)
      },
      clickShop(item) {
        this.$emit('shopDetailBolFun', item)
      },
      showAddBrand(item, index, index2, index3) {
        this.firstLayerClick(item, index, index2, index3)
        // this.$emit('showDrawer')
        if (index !== undefined && index2 == undefined) {
          if (this.group_type == 1) {
            this.$emit('brandDetailBolFun', '点击品牌', item)
          }
          this.$emit('addBrandOne', index)
        } else if (index2 !== undefined && index3 == undefined) {
          this.$emit('addBrandTwo', index2)
        } else if (index3 !== undefined) {
          this.$emit('addBrandThree', index3)
        }
      },
      // 获取品牌下门店数量
      getShopCpunt(list){
        let arr = [];
        list.forEach(item=>{
          let ids = item.id.split('g')[1]
          arr.push(ids)
        })
        let params = {
          group_list:arr
        }
        get_shop_count(params).then(res=>{
          if(res.rst == 'ok'){
              // console.log(44444);
              res.data[0].count_list.forEach(item=>{
              let num = item[Object.keys(item)[0]];
              this.shopCountList.push(num)
            })
          }
        })
      }
    },
  }
</script>
<style scoped>
  .tree {
    position: relative;
    top: 20px;
    left: 20px;
  }

  .line {
    position: absolute;
    background: #fff;
    top: 0;
    border-left: 1px dashed #83a8fc;
    border-bottom: 1px dashed #83a8fc;
  }

  .tree-text {
    /* position: absolute; */
    /* width: 130px; */
    width: 150px;
    height: 40px;
    background: #f1f1f1;
    border-radius: 6px;
    display: flex;
    /* justify-content: center; */
    align-items: center;
    /* padding:3px 0; */
  }

  .tree-children {
    position: relative;
  }

  .tree-btn {
    width: 30px;
    height: 40px;
    background: #e6e6e6;
    border-radius: 6px 0px 0px 6px;
    display: flex;
    justify-content: center;
    color: #999;
    align-items: center;
    font-size: 25px;
    cursor: pointer;
  }

  .tree-group-animation-open {
    animation: move 1s;
    /* transition:0.5s */
  }

  .tree-group-animation-close {
    animation: close 1s;
  }

  @keyframes close {
    0% {
      transform: translateY(-0px);
    }

    100% {
      transform: translateY(-10px);
      display: none;
    }
  }

  @keyframes move {
    0% {
      transform: translateY(-5px);
    }

    100% {
      transform: translateY(0px);
    }
  }
  .shop-count-num{
    /* color: red; */
    min-width: 23px;
    height: 23px;
    text-align: center;
    line-height: 23px;
    font-size: 14px;
    top: -10px;
    right: -10px;
    background-color: #e40012;;
    border-radius: 50px;
    color: #ffffff;
    cursor: default;
    overflow: hidden;
    padding: 0 5px;
  }
  .more_text{
    font-size: 16px;
    height: 40px;
   	line-height: 23px;
		display: flex;
		align-items: center;
    padding-left:8px;
		/* justify-content: center; */
  }
  .more_text span{
    word-break: break-all;
    display:-webkit-box;
    -webkit-line-clamp:2;
    -webkit-box-orient:vertical;
    overflow:hidden;
    text-overflow:ellipsis;
  }
</style>