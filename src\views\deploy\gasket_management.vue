<template>
    <div class="gasket_management">
        <div class="title">
            <p>垫片管理设置</p>
        </div>
        <div class="spec_wrap">
            <p class="spec_title">选择联屏规格</p>
            <div class="spec_list">
                <div class="spec_item_wrap" v-for="(item,index) in specList" :key="'spec_'+index" 
                :class="[item.dir == 'h' ? 'spec_item_h' : 'spec_item_v', item.value == specActiveInfo.value ? 'spec_item_actived' : '']">
                    <div class="spec_item" @click="activeChange(item)">
                        <div class="spec_item_row" v-for="row in item.x" :key="'spec_row_'+row">
                            <div class="spec_item_col" v-for="col in item.y" :key="'spec_col_'+ col"></div>
                        </div>
                    </div>
                    <div class="spec_info">
                        <input type="radio" name="spec" :value="item.value" :id="'spec_'+item.value" v-model="specActiveInfo.value" @input="activeChange(item)">
                        <label >{{item.name}}</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="spec_gasket_wrap">
            <div class="search_region">
                <div class="tips">选择对应屏幕规格，对应设置图片/视频，点击加号进行图片/视频添加</div>
                <div class="search">
                    <div class="search_item">
                        <span>设置门店类型</span>
                        <el-select v-model="storeSelected" @change="selectChange('store')">
                            <el-option v-for="(item,index) in storeSelectList" :key="'store_op_' + index" :value="item.value" :label="item.name"></el-option>
                        </el-select>
                    </div>
                    <div class="search_item">
                        <span>设置播放动线</span>
                        <el-select v-model="storeplaylayoutSelected" @change="selectChange('layout')">
                            <el-option v-for="(item,index) in storeplaylayoutSelectList" :key="'layout_op_' + index" :value="item.value" :label="item.name"></el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="spec_setting" 
            v-loading="loading"
            :element-loading-text="loadingText"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)">
                <div 
                class="spec_setting_item " 
                :class="[specActiveInfo.dir == 'h' ? 'spec_setting_item_h' : 'spec_setting_item_v']"
                v-for="(item,index) in specActiveInfo.list" 
                :key="'spec_setting_item_'+index">
                    <div class="add_btn" @click="addSpec(item.key)">
                        <i class="el-icon-circle-plus"></i>
                    </div>
                    <img :src="item['img_url']" alt="" v-if="item['img_url']">
                    <!-- <div style="position: absolute;z-index: 9999;color: red;left: 0;top: 0;">{{ item }}</div> -->
                </div>
            </div>
            <div class="btns">
                <el-button type="primary" size="large" @click="specConfirm">保 存</el-button>
            </div>
        </div>

        <el-dialog title="设置垫片内容" :visible.sync="dialogVisible" width="60%" :before-close="handleClose" custom-class="gasket_dialog">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="图片" name="image"></el-tab-pane>
                <el-tab-pane label="视频" name="video"></el-tab-pane>
            </el-tabs>
            <div class="image_center">
                <picture-resources v-if="dialogVisible && activeName=='image'" @checkedList='checkedImgList' @setImgPreview='setImgPreview' ref="picture" ></picture-resources>
                <video-resources v-if="dialogVisible && activeName=='video'" @checkedList='checkedVideoList' @setVideoPreview='setVideoPreview' ref="video"></video-resources>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="dialogConfirm">确认</el-button>
            </div>
        </el-dialog>


        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask'>
        </previsualization>
    </div>
</template>

<script>
import { datas_filter_cond } from '@/api/commonInterface'
import { ds_dmb_default_play_schedule_datas,ds_dmb_default_play_schedule_set } from '@/api/systemCenter/gasket_manage'
import pictureResources from '@/components/ContentCenter/pictureResources'
import videoResources from '@/components/ContentCenter/videoResources'
import previsualization from "@/components/communal/previsualization";
export default {
    components: {
        pictureResources,
        videoResources,
        previsualization
    },
    data() {
        return {
            activeName:'image',
            dialogVisible:false,
            loading:false,
            loadingText:'拼命加载中',
            specList:[],
            storeSelectList:[],
            storeSelected:'',
            storeplaylayoutSelectList:[],
            storeplaylayoutSelected:'',
            specActived:'',
            specActiveInfo:{
                dir:'',
                spec:'',
                x:0,
                y:0,
                name:'',
                value:'',
                list:[]
            },
            addSpecIndex:0,
            checkedImg:[],
            checkedVideo:[],
            isPreviewMaskShow: false,
            PreviewSrc:'',
            PreviewType:''
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.getPubInfo()
    },
    mounted() {
        
    },
    methods: {
        getPubInfo(){
            const parmas = {
                classModel:"ContentPub"
            }
            datas_filter_cond(parmas).then(res=>{
                if(res.rst == 'ok'){
                    let spec_data = [],
                        store_data = [],
                        layout_data = [];
                    res.data[0].forEach(item=>{
                        switch (item.filterkey) {
                            case 'storetype':
                                store_data = item;
                                break;
                            case 'vsspec':
                                spec_data = item;
                                break;
                            case 'storeplaylayout':
                                layout_data = item;
                                break;
                        }
                    });
                    this.filterSpecList(spec_data);
                    this.filterStoreList(store_data);
                    this.filterPlayLayoutList(layout_data);
                }
            })
        },
        filterSpecList(data){
            let list = [];
            data.options.forEach(item=>{
                if(item[0] && item[0] != ''){
                    const dir = item[0].split('_')[0];
                    const spec = item[0].split('_')[1];
                    const x = Number(spec.split('*')[0]);
                    const y = Number(spec.split('*')[1]);
                    const name = item[1];
                    const value = item[0];
                    let arr = [];
                    
                    for(let i = 0;i<x * y;i++){
                        arr.push({
                            key:i+1,
                            img_key:'',  
                            img_url:'',
                            video_key:'',
                            video_thumb_url:''
                        })
                    }
                    const obj = {
                        dir,
                        spec,
                        x,
                        y,
                        name,
                        value,
                        list:arr
                    };
                    list.push(obj);
                }
            })
            this.specList = list;
        },
        filterStoreList(data){
            let list = [];
            data.options.forEach(item=>{
                const obj = {
                    value:item[0],
                    name:item[1]
                };
                list.push(obj);
            })
            this.storeSelectList = list;
            this.storeSelected = list[0].value;
        },
        filterPlayLayoutList(data){
            const list = []
            data.options.forEach(item=>{
                const obj = {
                    value:item[0],
                    name:item[1]
                };
                list.push(obj);
            })
            this.storeplaylayoutSelectList = list;
            this.storeplaylayoutSelected = list[0].value;
        },
        getPlaySchedule(){
            this.loading = true;
            this.loadingText = '正在获取垫片数据';
            this.addSpecIndex = 0;
            const params = {
                dmb_spec:this.specActiveInfo.value,
                storetype:this.storeSelected,
                storeplaylayout:this.storeplaylayoutSelected
            }
            ds_dmb_default_play_schedule_datas(params).then(res=>{
                if(res.rst == 'ok'){
                    const total = res.data[0]['totalElements'];
                    // console.log(this.specActiveInfo,'this.specActiveInfo')
                    this.specActiveInfo.list.forEach(item=>{
                        item.img_key = '';
                        item.img_url = '';
                        item.video_key = '';
                        item.video_thumb_url = '';
                    })
                    if(total == 0){
                        this.loading = false;
                        return
                    };

                    const data = res.data[0]['content'][0];
                    // console.log(data,'获取的数据');
                    const info = data.dmb_spec_attr;
                    let list = [];
                    for(let i in info){
                        // console.log(info[i],'info[i]');
                        const obj = {
                            key:i,
                            img_key:info[i].img_key,
                            video_key:info[i].video_key,
                        }
                        if(info[i].img_url || info[i].video_thumb_url){
                            obj.img_url = info[i].img_url || info[i].video_thumb_url;
                        }else{
                            obj.img_url = '';
                        }
                        list.push(obj);
                    }
                    this.specActiveInfo.list = list;

                    // console.log(this.specActiveInfo,'this.specActiveInfo');
                }else{
                    this.$message.error(res.error_msg)
                }
                this.loading = false;
            }).catch(err=>{
                this.loading = false;
            })
        },
        activeChange(item){
            if(item.value != this.specActiveInfo.value){
                this.specActived = item.value;
                this.specActiveInfo = item;
                this.getPlaySchedule();
            }
        },
        selectChange(value){
            
            if(!this.specActiveInfo.value || this.specActiveInfo.value == '') return;

            this.getPlaySchedule();
        },
        specConfirm(){
            this.loading = true;
            this.loadingText = '垫片设置中';
            let params = {
                act:'add',
                storetype:this.storeSelected,
                storeplaylayout:this.storeplaylayoutSelected,
                dmb_spec:this.specActiveInfo.value,
                dmb_spec_attr:{}
            }
            
            if(this.specActiveInfo.list.length == 0){
                for(let i = 0;i<this.specActiveInfo.x * this.specActiveInfo.y;i++){
                    const key = i + 1;
                    const obj = {
                        img_key:'',
                        img_url:'',
                        video_key:'',
                        video_thumb_url:''
                    }
                    params.dmb_spec_attr[key] = obj;
                }
            }else{
                this.specActiveInfo.list.forEach((item,index)=>{
                    const key = item.key;
                    const obj = {
                        img_key:item.img_key,
                        video_key:item.video_key
                    }
                    params.dmb_spec_attr[key] = obj;
                })
            }
            
            // console.log(params,'params');
            
            ds_dmb_default_play_schedule_set(params).then(res=>{
                if(res.rst == 'ok'){
                    console.log(res);
                    this.$message.success('垫片设置成功');
                }else{
                    this.$message.error(res.error_msg);
                }
                this.loading = false;
            }).catch(err=>{
                this.loading = false;
            })
        },
        handleClose(){
            this.dialogVisible = false;
            this.addSpecIndex = 0;
        },
        addSpec(value){
            
            this.dialogVisible = true;
            
            this.addSpecIndex = value;
            setTimeout(() => {
                this.$refs.picture.isContent = true;
                this.$refs.video.isContent = true;
            }, 100);
        },
        checkedImgList(list){
            this.checkedImg = list;
        },
        checkedVideoList(list){
            this.checkedVideo = list;
            console.log(list);
        },
        dialogConfirm(){
            // if(this.checkedImg.length == 0 || this.checkedVideo.length == 0){
            //     this.$message.warning('请先选择要设置的图片/视频');
            //     return;
            // }
            if(this.activeName =='image'){
                if(this.checkedImg.length == 0){
                    this.$message.warning('请先选择要设置的图片');
                    return;
                }
            }else if(this.activeName =='video'){
                if(this.checkedVideo.length == 0){
                    this.$message.warning('请先选择要设置的视频');
                    return;
                }
            }
            this.specActiveInfo.list.forEach((item,index)=>{
                if(item.key == this.addSpecIndex){
                    if(this.activeName =='image'){
                        item.video_key = "";
                        item.img_key = this.checkedImg[0].file_key;
                        item.img_url = this.checkedImg[0].photo_url;
                    }else if(this.activeName =='video'){
                        item.img_key = "";
                        item.video_key = this.checkedVideo[0].file_id;
                        item.img_url = this.checkedVideo[0].thumb;
                    }
                    
                }
            })
            this.dialogVisible = false;
        },
        closePreviewMask(){
            this.isPreviewMaskShow = false;
            this.PreviewSrc = "";
        },
        setImgPreview(val){
            this.PreviewType = "image";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val){
            console.log(val,'视频');
            this.PreviewType = "video";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        }
    },
};
</script>

<style scoped lang="scss">
 .gasket_management{
    box-sizing: border-box;
    padding: 20px;
    color: var(--text-gray);
    .title{
        color: var(--text-color);
        font-size: 16px;
        font-weight: bold;
        margin: 15px 0;
    }
    .spec_wrap{
        margin-bottom: 20px;
        .spec_title{
            font-size: 16px;
            color: var(--text-gray);
            margin-bottom: 20px;
        }
        .spec_list{
            display: flex;
            flex-wrap: wrap;
            .spec_item_wrap{
                margin-bottom: 20px;
                .spec_item{
                    cursor: pointer;
                    width: 140px;
                    height: 80px;
                    border: 1px solid var(--gray);
                    margin: 0px 15px 10px 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 5px;
                    --long-side:20px;
                    .spec_item_row{
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .spec_item_col{
                        background-color: #e5e5e5;
                        box-sizing: border-box;
                        border: 1px solid #bcbcbc;
                        border-radius: 1px;
                    }
                }
            }
            .spec_item_actived{
                // --border-color:var(--gray);
                .spec_item{
                    border-color: var(--dev-border) !important;
                    .spec_item_col{
                        background-color: #fff !important;
                        border-color: var(--dev-border) !important;
                    }
                }
            }
            .spec_item_h{
                .spec_item_col{
                    aspect-ratio: 1.5/1;
                    width: var(--long-side);
                    height: calc(var(--long-side) / 1.5);
                }
            }
            .spec_item_v{
                .spec_item_col{
                    aspect-ratio: 1/1.5;
                    height: var(--long-side);
                    width: calc(var(--long-side) / 1.5);
                }
            }
            .spec_info{
                display: flex;
                justify-content: center;
                align-items: center;
                input{
                    margin-right: 5px;
                }
                label{
                    font-weight: normal;
                    color: var(--text-gray);
                }
                input:checked + label{
                    color: var(--text-color-light);
                }
                input[type="radio"]:checked{
                    accent-color: var(--radio-color); /* 选中时改变背景颜色 */  
                }
            }
        }
    }
    .spec_gasket_wrap{
        width: 100%;
        box-sizing: border-box;
        padding-right: 30px;
        .search_region{
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            .tips{
                margin-bottom: 15px;
            }
            .search{
                display: flex;
                align-items: center;
                .search_item{
                    margin-left: 20px;
                }
            }
        }
        .spec_setting{
            background-color: #f2f2f2;
            width: 100%;
            min-height: 300px;
            border-radius: 20px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            box-sizing: border-box;
            padding: 40px 40px 0 ;
            .spec_setting_item{
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                border-radius: 20px;
                margin: 0 30px 40px 0;
                position: relative;
                .add_btn{
                    position: relative;
                    z-index: 5;
                    font-size: 30px;
                    color: var(--text-color);
                    &:hover{
                        cursor: pointer;
                        filter: brightness(1.4);
                    }
                    &:active{
                        filter: brightness(0.8);
                    }
                }
                img{
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
            .spec_setting_item_h{
                width: 350px;
                height: 200px;
            }
            .spec_setting_item_v{
                width: 200px;
                height: 350px;
            }
        }
        .btns{
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 30px;
        }
    }
    ::v-deep .gasket_dialog{
        color: red;
        margin-top: 5vh !important;
        .el-dialog__body{
            padding-top: 0px !important;
            .el-tabs{
                margin-bottom: 0 !important;
            }
            .lattice_header{
                display: none;
            }
            .btns{
                display: none;
            }
            .item_status{
                display: none;
            }
            .hidden_mask{
                display: none !important;
            }
        }
    }




 }
</style>
