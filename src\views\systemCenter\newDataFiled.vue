<template>
  <div class="newDatafiled">
    <div class="header">
      <i class="el-icon-back cursor" @click="back"></i>
      <span>数据范围</span>
    </div>
    <div class="newForm">
      <div class="content" v-if="type == 'new'">
        <el-input placeholder="请输入数据名称" v-model="NewFormData.DataName"></el-input>
        <el-button type="primary" @click="newSaveData" class="request" :disabled="SaveDisabled">确定</el-button>
      </div>
      <div class="content" style="font-weight: bold;" v-else>数据范围名称: {{ DetaliData.name }}
        <span style="margin-left:10px;color:rgba(42, 130, 228, 1)" class="cursor" @click="editDataName">编辑</span>
      </div>
    </div>
    <div class="search" v-if="type == 'new'">
      <el-form :inline="true" :model="queryList" class="demo-form-inline">
        <el-form-item>
          <el-input placeholder="请输入门店编号/名称" size="small" prefix-icon="el-icon-search" @keyup.enter.native="handleSearch"
            v-model="queryList.search" style="width: 186px; margin-right: 12px">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="queryList.marketname" clearable filterable placeholder="营运市场" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]" :value="item[0]">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item>
          <el-select
            v-model="queryList.storetypename"
            clearable
            filterable
            placeholder="门店类型"
            size="small"
            style="width: 186px; margin-right: 12px"
          >
            <el-option
              v-for="item in storeTypeList.options"
              :key="item[1]"
              :label="item[1]"
              :value="item[0]"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-select v-model="queryList.itmarketname" clearable filterable placeholder="IT市场" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="item in itMarketList.options" :key="item[1]" :label="item[1]" :value="item[0]">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item>
          <el-select
            v-model="queryList.allEquipment"
            clearable
            placeholder="全部设备"
            size="small"
            style="width: 186px; margin-right: 12px"
          >
            <el-option
              v-for="item in allEquipmentList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button @click="Search" class="searchButton">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="
          height: 56px;
          line-height: 56px;
          font-weight: bold;
          border-left: 1px solid rgba(229, 229, 229, 1);
          border-right: 1px solid rgba(229, 229, 229, 1);
          display: flex;
          align-items: center;
          justify-content: space-between;
        " v-else>
      <span style="margin-left: 12px; color: rgba(80, 80, 80, 1); font-size: 14px">
        已创建数据范围门店
      </span>
      <el-button class="addButton" @click="addData">增加数据</el-button>
    </div>
    <!-- 列表 -->
    <div>
      <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
        style="width: 100%" v-loading="loading" :header-cell-style="{
          background: '#24b17d',
          color: '#fff',
          'font-size': '13px',
          'text-align': 'center',
        }" :cell-style="{ 'text-align': 'center' }">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="storecode" label="门店编号" width=""></el-table-column>
        <el-table-column prop="storename" label="门店名称" width=""></el-table-column>
        <el-table-column prop="marketname" label="营运市场" width=""></el-table-column>
        <el-table-column prop="itmarketname" label="IT市场" width=""></el-table-column>
        <el-table-column prop="storetypename" label="类型" width=""></el-table-column>
        <el-table-column prop="data_from" :show-overflow-tooltip="true" label="门店来源" width=""></el-table-column>
        <!-- <el-table-column prop="data_from" :show-overflow-tooltip="true" label="门店来源" width="">
        </el-table-column> -->
        <el-table-column :show-overflow-tooltip="true" label="操作" width="" v-if="type == 'edit'">
          <template slot-scope="scope">
            <div class="event">
              <span @click="Shiftout(scope.row)"> 移出 </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottom">
      <div class="batch">
        <el-button :disabled="SelectState != false || BatchState != false" @click="batchAddData" v-if="type == 'new'">
          批量创建数据</el-button>
        <el-button @click="batchDelete" v-else>批量移出</el-button>
      </div>
      <div class="paginat">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage4"
          :page-sizes="[10, 20, 30, 40]" :page-size="10" layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum">
        </el-pagination>
      </div>
    </div>
    <Dialog :editDialogState="editDialogState" @handleClose="handleClose" :editDataNameInput="editDataNameInput"
      @requestEditData="requestEditData"></Dialog>
  </div>
</template>

<script>
import { add_upt_del_data, get_data_detail } from "@/api/datafiled/datafiled";
import { get_adm_datas } from "@/api/shopManage/shop";
import { datas_filter_cond } from "@/api/commonInterface";
import Dialog from "./component/dialog.vue"
export default {
  components: {
    Dialog
  },
  data() {
    return {
      NewFormData: {
        DataName: "",
      },
      tableData: [], //列表数据
      operatingMarketList: [], //运营市场下拉数据
      storeTypeList: [], //门店类型下拉数据
      itMarketList: [], //IT市场下拉数据
      allEquipmentList: [], //全部设备下拉数据
      queryList: {
        search: "", //店铺名
        marketname: "", //运营市场
        storetypename: "", //门店类型
        itmarketname: "", //IT市场
        allEquipment: "", //全部设备
      },
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      loading: true,
      // 页码
      currentPage: 1,
      pageSize: 10,
      // 总数
      totalNum: "",
      // 批量按钮
      BatchState: true,
      // 新增返回数据条id
      NewDataId: [],
      // 新增id
      dataId: null,
      // 多选选中
      SelectState: true,
      // 是否是新增
      type: "",
      // id
      Did: "",
      // 详情
      DetaliData: {},

      detaliCode: false,
      // 新增确定按钮
      SaveDisabled: false,
      // 修改弹框
      editDialogState: false,
      editDataNameInput: null
    };
  },
  methods: {
    back() {
      this.$router.go(-1);
    },
    newSaveData() {
      const params = {
        act: "add",
        name: this.NewFormData.DataName,
        dataAct: "new",
      };
      add_upt_del_data(params).then((res) => {
        console.log(res);
        if (res.rst == "ok") {
          this.dataId = res.data[0].id;
          this.BatchState = false;
          this.$message.success("新增成功");
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 320 + "px";
      this.autoHeight.heightNum = windowHeight - 230;
    },
    // 获取列表
    // 获取门店列表数据
    getTableData() {
      this.loading = true;
      console.log(this.queryList,'queryList');
      const params = {
        classModel: "GroupShop",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize, //每页数据量,
        blurry: this.queryList.search.toUpperCase(), //店铺名
        opsmarket: this.queryList.marketname, //运营市场
        storetype: this.queryList.storetypename, //门店类型
        itmarket: this.queryList.itmarketname, //IT市场
      };
      get_adm_datas(params).then((res) => {
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;
          this.totalNum = res.data[0].totalElements;
          this.loading = false;
        } else {
          console.log("失败");
        }
      });
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "GroupShop", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then((res) => {
        this.operatingMarketList = res.data[0][1]; //运营市场下拉数据
        this.storeTypeList = res.data[0][2]; //门店类型下拉数据
        this.itMarketList = res.data[0][3]; // IT市场下拉数据
        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
        console.log(res.data[0]);
      });
    },
    // 分页操作
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      // this.getTableData();
      console.log(this.type,'type');
      if(this.type == 'edit'){
        this.getDataDetail()
      }else{
        this.getTableData();
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      if(this.type == 'edit'){
        this.getDataDetail()
      }else{
        this.getTableData();
      }
    },
    // 搜索
    Search() {
      this.currentPage = 1;
      console.log( this.currentPage,' this.currentPage');
      this.getTableData();
    },
    // 多选框
    handleSelectionChange(item) {
      console.log(item);
      if (item.length != 0) {
        this.SelectState = false;
        this.NewDataId = item.map((item) => {
          return item.shop_id;
        });
        console.log(this.NewDataId);
      } else {
        this.NewDataId = [];
        this.SelectState = true;
      }
    },
    // 批量新增
    batchAddData() {
      console.log(this.NewDataId);
      if (this.NewDataId.length != 0) {
        if (this.detaliCode == false) {
          let params = {
            act: "upt",
            name: this.NewFormData.DataName,
            dataAct: "new",
            id: this.dataId,
            range_shops: this.NewDataId,
          };
          add_upt_del_data(params).then((res) => {
            console.log(res);
            if (res.rst == "ok") {
              this.$message.success("新增成功");
              this.$router.push("/systemCenter/dataField");
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        } else if (this.detaliCode == true) {
          let params = {
            act: "upt",
            name: this.NewFormData.DataName,
            dataAct: "new",
            id: this.Did,
            range_shops: this.NewDataId,
          };
          add_upt_del_data(params).then((res) => {
            console.log(res);
            if (res.rst == "ok") {
              this.$message.success("新增成功");
              this.$router.push("/systemCenter/dataField");
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        }
      } else {
        this.$message.warning("未选择门店")
      }

    },
    // 编辑 增加数据
    addData() {
      this.type = "new";
      this.NewFormData.DataName = this.DetaliData.name;
      this.BatchState = false;
      this.getTableData();
      this.detaliCode = true;
      this.SaveDisabled = true;
      // this.newSaveData();
    },
    // 获取详情信息
    getDataDetail() {
      const params = {
        id: this.$route.query.id,
        classModel: "ItemDataRangeTpl",
        page: this.currentPage - 1,
        size: this.pageSize,
      };
      get_data_detail(params).then((res) => {
        this.totalNum = res.data[0].totalElements;
        this.loading = false;
        this.DetaliData = res.data[0].item_datatpl_info;
        this.tableData = res.data[0].content;
      });
    },
    Shiftout(row) {
      this.$confirm("此操作将删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            act: "upt",
            // name:
            id: this.DetaliData.id,
            name: this.DetaliData.name,
            dataAct: "del",
            range_shops: [row.shop_id],
          };
          add_upt_del_data(params).then((res) => {
            if (res.rst == "ok") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getDataDetail();
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    batchDelete() {
      if (this.NewDataId.length != 0) {
        const params = {
          act: "upt",
          // name:
          id: this.DetaliData.id,
          name: this.DetaliData.name,
          dataAct: "del",
          range_shops: this.NewDataId,
        };
        console.log(this.NewDataId);
        add_upt_del_data(params).then((res) => {
          if (res.rst == "ok") {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getDataDetail();
          } else {
            this.$message.warning(res.error_msg);
          }
        });
      } else {
        this.$message.warning("请选择")
      }
    },
    // 修改数据范围名称
    editDataName() {
      this.editDialogState = true;
      this.editDataNameInput = this.DetaliData.name
      console.log(this.editDataNameInput);
    },
    // 取消弹框
    handleClose() {
      this.editDialogState = false;
    },
    // 保存
    requestEditData(e) {
      const params = {
        act: "upt",
        name: e,
        id: this.Did,
        dataAct: "upt"
      }
      add_upt_del_data(params).then(res => {
        console.log(res);
        if (res.rst == "ok") {
          this.$message.success("修改成功");
          this.editDialogState = false;
          this.getDataDetail()
        } else {
          this.$message.warning(res.error_msg)
        }
      })
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getSelectDataList();
    this.getHeight();
    this.type = this.$route.query.type;
    if (this.type == "new") {
      this.getTableData();
    } else if (this.type == "edit") {
      this.Did = this.$route.query.id;
      this.getDataDetail();
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style lang="scss" scoped>
.newDatafiled {
  padding: 0 25px 0 24px;

  .header {
    height: 52px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(229, 229, 229, 1);

    i {
      font-size: 24px;
      color: var(--text-color);
    }

    span {
      color: var(--text-color);
      font-size: 14px;
      font-weight: bold;
      margin-left: 5px;
      padding-top: 3px;
    }
  }

  .newForm {
    height: 88px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(229, 229, 229, 1);

    .content {
      width: 480px;
      display: flex;

      button {
        width: 72px;
        height: 40px;
        color: rgba(255, 255, 255, 1);
        background-color: rgba(108, 178, 255, 1);
        font-size: 14px;
        border-radius: 0 6px 6px 0px;
      }
    }

    ::v-deep .el-input__inner {
      height: 40px !important;
      border-radius: 0;
    }
  }

  .search {
    height: 77px;
    margin-top: 25px;

    .searchButton {
      width: 88px;
      height: 38px;
      color: rgba(80, 80, 80, 1);
      background-color: var(--text-color);
      font-weight: bold;
      color: #fff;
    }
  }

  .bottom {

    // float: right;
    .batch {
      button {
        color: #fff;
        background-color: rgba(39, 177, 126,1);
        font-size: 14px;
      }
    }

    display: flex;
    justify-content: space-between;
  }
}

.addButton {
  width: 106px;
  height: 32px;
  color: #fff;
  background-color: var(--text-color);
  margin-right: 12px;
}

.event {
  span {
    cursor: pointer;
    color: var(--text-color);
  }
}

.request{
  background-color: var(--btn-background-color) !important;
  border-color: var(--btn-background-color);
  color: var(--btn-color) !important;
}
</style>