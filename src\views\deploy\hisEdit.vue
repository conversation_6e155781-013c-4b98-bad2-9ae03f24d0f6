<template>
  <div class='hisEdit'>
    <div class='top'>
      <div>
        <span class='el-icon-back' @click.stop='backPrePage'></span>
        历史投放
      </div>
      <el-button type="danger">更新投放</el-button>
    </div>
    <main>
      <div class='left'>
        <div class='title'>
          <img src='../../assets/img/home_img/little_edit.svg'>
          <span>321上海市场上新</span>
        </div>
        <ul>
          <li>
            <span>投放范围:</span>
            <div>总部-上海市场-徐家汇</div>
          </li>
          <li>
            <span>投放周期:</span>
            <div>2022年3月15日-2022年3月21日</div>
          </li>
          <li>
            <span>播放模式:</span>
            <div>轮播</div>
          </li>
          <li>
            <span>播放形式:</span>
            <div>单屏播放</div>
          </li>
          <li>
            <span>门店标签:</span>
            <div>
              <img src='../../assets/img/home_img/little_label.svg'>
              <p>常规餐厅</p>
              </div>
          </li>
          <li>
            <span>经营时段:</span>
            <div>9</div>
          </li>
          <li>
            <span>包含门店:</span>
            <div>
              <img src='../../assets/img/home_img/shop.png'>
              186
              <span>查看</span>
            </div>
          </li>
          <li>
            <span>投放成功:</span>
            <div>
              <img src='../../assets/img/home_img/shop.png'>
              186
            </div>
          </li>
          <li>
            <span>投放失败:</span>
            <div>
              <img src='../../assets/img/home_img/shop.png'>
              186
              <span @click.stop='view'>查看</span>
            </div>
          </li>
        </ul>
      </div>
      <table>
        <tr  class='timeSpace'>
          <th>时段</th>
          <td colspan='2'>
            <div class='two'>
              <div>
                <p>1</p>
                <p>2</p>
              </div>
              <p>ID：2345612</p>
            </div>
          </td>
          <td colspan='2'>
            <div class='two'>
              <div>
                <p>3</p>
                <p>4</p>
              </div>
              <p>ID：2345612</p>
            </div>
          </td>
        </tr>
        <tr>
          <th>工作日早餐</th>
          <td @mouseover='showFourBtn()' @mouseout='showFourBtns=false'>
            <!-- <img src='../../assets/img/home_img/breakfast.png' :class="showBg?'alertBgImg':[]"> -->
            <div class='blackClose' v-show='showBg' @click.stop='showBg=false'>×</div>
            <div class='imgBtns' v-show='showFourBtns'>
              <div class='fourBtn' @click.stop='fourPreview'>
                <i class='iconfont icon-icon_yulan'></i>
                预览
              </div>
              <div class='fourBtn' @click.stop='fourClear'>
                <i class='iconfont icon-qingkong'></i>
                清空
              </div>
              <div class='fourBtn' @click.stop='fourEdit'>
                <i class='iconfont icon-bianji'></i>
                编辑
              </div>
              <div  class='fourBtn' @click.stop='fourAdd'>
                <i class='iconfont icon-xinzeng-chuangjian-faqi-05'></i>
                新增
              </div>

            </div>
          </td>
          <td>
            <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          </td>
          <td>
            <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          </td>
          <td>
            <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          </td>
        </tr>
        <tr>
          <th>早午餐过渡1</th>
          <td>
            <div class='add' @click.stop='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
        </tr>
        <tr>
          <th>早午餐过渡2</th>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
        </tr>
        <tr>
          <th>午餐</th>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
        </tr>
        <tr>
          <th>下午茶</th>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
          <td>
            <div class='add'>
              <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
              新增
            </div>
          </td>
        </tr>
      </table>
    </main>
  </div>
</template>

<script>
export default {
  name: 'HisEdit',
  methods:{
    //返回
    backPrePage(){
      this.$router.go(-1);
    },
    //查看
    view(){
      this.$router.push({
        path:"/deploy/pubFail"
      })
    }
  }
}
</script>

<style scoped>
.hisEdit{
  width: 100%;
}
.top{
  height: 60px;
  display: flex;
  padding: 0 18px 0 13px;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
}
.el-icon-back{
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}
.top>button{
  width: 88px;
  height: 32px;
  line-height: 8px;
  text-align: center;
}
main{
  display: flex;
}
main>.left{
  width: 283px;
  height: 454px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  font-size: 12px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin: 0 14px 0 26px;
}
.left>.title{
  padding: 10px 0 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  color: rgba(80, 80, 80, 1);
}
.left>.title>img{
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-right: 9px;
}
.left>.title>span{
  font-size: 14px;
}
.left>ul{
  padding: 21px 0 0 19px;
}
.left>ul>li{
  display: flex;
  align-items: center;
  color: rgba(80, 80, 80, 1);
  font-size: 12px;
  text-align: left;
  font-weight: bold;
  margin-bottom: 19px;
}
.left>ul>li>span{
  width: 63px;
  height: 18px;
}
.left>ul>li>div>img{
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-right: 7px;
}
.left>ul>li>div>p{
  display: inline-block;
  font-weight: 500;
}
.left>ul>li>div>span{
  color: var(--text-color);
  margin-left: 10px;
}
/*main>.right{*/
/*  width: 950px;*/
/*  height: 694px;*/
/*  background-color: rgba(255, 255, 255, 1);*/
/*  border: rgba(198, 198, 198, 1) solid 1px;*/
/*}*/
table{
  font-size: 0.14rem;
  width: 9.5rem;
  height: 7.72rem;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 1);
  border-spacing: 0;
}
table>tr{
  width: 0.95rem;
  height: 0.85rem;
  text-align: center;
}
th{
  border: 1px solid rgba(224, 224, 224, 0.99);
  width: 1rem;
  font-weight: 500;
}
td{
  border: 0.01rem solid rgba(224, 224, 224, 0.99);
}
table>.timeSpace{
  height: 0.3rem;
}
td>.two{
  display: flex;
  justify-content: center;
  align-items: center;
}
td>.two>div>p{
  width: 0.28rem;
  height: 0.18rem;
  display: inline-block;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 0.14rem;
  border: rgba(39, 177, 126,1) solid 0.01rem;
  text-align: center;
}
td>.two>p{
  color: rgba(166, 166, 166, 1);
  font-size: 0.12rem;
  font-weight: bold;
  margin-left: 0.2rem;
}
td>img{
  width: 2.06rem;
  height: 1.2rem;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  right: 0;
}
td>.imgBtns{
  z-index: 5;
  position: absolute;
  margin: auto;
  height: 41px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  /*display: none;*/
}
/*编辑的大图*/
td>.alertBgImg{
  width: 938px;
  height: 524px;
  position: absolute;
  left: -257px;
  top: 400px;
  z-index: 10;
  object-fit: contain;
}
.blackClose{
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(17, 17, 17, 1);
  border-radius: 20px;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top:-6px;
  right: -480px;
  z-index: 10;
}
/*编辑、新增*/
td>.imgBtns>.fourBtn{
  width: 41px;
  height: 41px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border: rgba(255, 255, 255, 1) solid 1px;
  border-radius: 21px;
  color: #fff;
  font-size: 10px;
  float: left;
  margin: 0 4px;
}
/*td:hover .imgBtns{*/
/*  display: block;*/
/*}*/
td>.imgBtns>div>.iconfont{
  width: 17px;
  height: 17px;
  display: block;
  margin: 3px 0 0 12px;
  color: #fff;
  font-size: 17px;
  color: rgba(255, 255, 255, 1);
}
td{
  width: 210px;
  position: relative;
}
.add{
  width: 0.51rem;
  height: 0.51rem;
  color: rgba(80, 80, 80, 1);
  border-radius: 0.26rem;
  font-size: 0.12rem;
  border: rgba(229, 229, 229, 1) solid 0.01rem;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  line-height: 0.7rem;
  font-weight: bold;
  font-size: 0.12rem;
  color: rgba(229, 229, 229, 1);
}
.add>.addImg{
  width: 0.2rem;
  height: 0.2rem;
  position: absolute;
  margin: auto;
  top: 0.05rem;
  left: 0;
  right: 0;
}
</style>
