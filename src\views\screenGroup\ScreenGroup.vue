<template>
  <div class="box">
    <div class="top">
      <div class="left">
        <!-- 筛选条件 -->
        <div class="conditio_search flex-1">
          <el-input placeholder="请输入门店编号/名称" size="small" prefix-icon="el-icon-search" v-model="SearchFrom.blurry"
            style="width: 186px; margin-right: 12px">
            <!-- @keyup.enter.native="handleSearch" -->
          </el-input>
          <el-select v-model="SearchFrom.opsmarket" clearable placeholder="运营市场" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]"
              :value="item[0]"></el-option>
          </el-select>
          <el-select v-model="SearchFrom.storetype" clearable placeholder="门店类型" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="item in storeTypeList.options" :key="item[1]" :label="item[1]"
              :value="item[0]"></el-option>
          </el-select>
          <el-select v-model="dir_spec" clearable placeholder="联屏规格" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="item in ScreenSettingList" :key="item.title" :label="item.title"
              :value="item.value" ></el-option>
          </el-select>
          <el-select v-model="SearchFrom.ky_flag" clearable placeholder="标签条件" size="small"
            style="width: 186px; margin-right: 12px">
            <el-option v-for="(item,index) in ky_flag_options" :key="'tag_flag'+index" :label="item.label"
              :value="item.value" ></el-option>
          </el-select>
          <el-select v-model="SearchFrom.tags_list" clearable placeholder="标签" size="small"
            style="width: 186px; margin-right: 12px" multiple collapse-tags>
            <el-option v-for="(item,index) in searchTagOptions" :key="'tag_'+index" :label="item"
              :value="item" ></el-option>
          </el-select>

          <!-- <div class="search_btn">确认</div> -->
          <el-button size="small" style="height: 32px; border-radius: 6px" class="btn_color"
            @click="handleSearch">搜索</el-button>
        </div>
      </div>
      <el-button @click.stop="newScreenGroup" class="btn_color">新建联屏组</el-button>
    </div>
    <!-- row-key="shop_id" -->
    <el-table :data="tableData" @selection-change="handleSelectionChange" style="width: 100%"
      :height="autoHeight.height"
      :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
      :cell-style="{ 'text-align': 'center' }" v-loading="loadingState">


      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="date" align="center" label="联屏组" width="180">
        <template slot-scope="scope">
          <div>
            <span v-for="item in scope.row.screen_count" :key="item.sg_id"
              :class="scope.row.item_v_or_h == 'h' ? '' : 'devShu'" style="height: 0.18rem"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="storecode" label="门店编号" align="center"></el-table-column>
      <el-table-column prop="storename" align="center" label="门店名称"></el-table-column>
      <el-table-column prop="label" align="center" label="联屏组标签">
        <template slot-scope="scope">
          <div class="flex" style="flex-wrap: wrap;align-items:center">
            <div class="flex" style="flex-direction:column;width:150px" v-if="scope.row.tags.length > 2">
              <div style="display:flex;align-items:center">
                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                {{ scope.row.tags[0] }}
              </div>
              <div style="display:flex;align-items:center">
                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                {{ scope.row.tags[1] }}
              </div>
              <el-popover placement="top-start" title="设备标签" popper-class="popperOptions" width="200" trigger="hover">
                <div v-for="item in scope.row.tags" :key="item" style="display:flex;align-items:center">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  <span>
                    {{
                      item
                    }}
                  </span>
                </div>
                <span class="cursor" slot="reference">...</span>
              </el-popover>
            </div>
            <div class="flex" style="flex-direction:column;width:150px" v-else>
              <div style="display:flex;align-items:center" v-for="item in scope.row.tags" :key="item">
                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                {{ item }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="last_modified_time" align="center" label="绑定时间"></el-table-column>
      <!-- <el-table-column prop="content_status" align="center" label="在播内容">
      </el-table-column>-->
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button @click="toLook(scope.row)" type="text" size="small">查看</el-button>

          <el-button type="text" size="small" style="color: #f56c6c" @click="unbundle(scope.row)"
            v-if="checkPer(['dm.vs.control'])">解绑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <Drawer ref="drawer" :drawer="drawer" @closeDrawer="closeDrawer" @searchShop="searchShop" :searchTable="searchTable"
              @addShop="addShop" :shopData="shopData" :ScreenSettingList="ScreenSettingList" :ShopLoading="ShopLoading"
    @cancel="cancel" @bindingScroll="bindingScroll" @unbindingScroll="unbindingScroll" @saveTags="saveTags" />-->
    <Drawer ref="drawer" :drawer="drawer" @closeDrawer="closeDrawer" @searchShop="searchShop" :searchTable="searchTable"
      @addShop="addShop" :shopData="shopData" :ScreenSettingList="ScreenSettingList" :ShopLoading="ShopLoading"
      @cancel="cancel" @bindingScroll="bindingScroll" @unbindingScroll="unbindingScroll" @saveTags="saveTags" />

    <!--    按钮+分页-->
    <!-- <div class="bottom">
             <el-button>批量增加标签</el-button>
              <div class="block">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                  :current-page.sync="currentPage3" :page-size="SearchFrom.size" :page-sizes="[10, 20, 50, 100]"
                  :pager-count='5' layout="total,sizes,prev,pager, next, jumper" :total="screenGroupTotal" background>
                </el-pagination>
              </div>
    </div>-->
    <!-- 底部以及页码 -->
    <div class="bottom_footer flex flex-1">
      <div class="left_button_wrap flex-1">
        <!-- <el-button type="primary" size="small" @click="setDaypart">批量DayPart设置</el-button> -->
        <el-button class="btn_color" size="small" @click="setBatchTag">批量标签设置</el-button>
      </div>
      <div class="right_page_wrap">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage3" :page-size="SearchFrom.size" :pager-count="5"
          :page-sizes="[10, 20, 50, 100]" layout="total,sizes,prev,pager, next, jumper"
          :total="screenGroupTotal"></el-pagination>
      </div>
    </div>
    <!-- 批量修改标签 -->
    <Dialog :tagsDialog="tagsDialog" @handleCloseDialog="handleCloseDialog" :ShopTagList="tagsList"
      @saveTags="saveScreenTags" :delTagsList="delShowList"></Dialog>
  </div>
</template>

<script>
import {
  get_screen_group,
  search_screen_group,
  get_shop_data,
  bind_shop,
  unbind_shop,
  bind_shop_tags
} from "@/api/screen_group/screen_group";
import { datas_filter_cond } from "@/api/commonInterface";
import { get_tags_list, create_or_delete_group_tags } from "@/api/system/label";
// import Drawer from "./component/drawer";
import Drawer from "./component/drawer";
import Dialog from "./component/Dialog.vue";
import { removeDuplicateObj } from "@/utils/setArray";
export default {
  name: "NewConnectScreen",
  components: {
    // Drawer,
    Dialog,
    Drawer
  },
  data() {
    return {
      // 联屏列表数据Search条件
      SearchFrom: {
        group_id: localStorage.getItem("group_id"), // 屏幕id
        page: 0, //  页码,从1开始
        size: 10, // 每页显示个数
        blurry: "",
        classModel: "VsGroup",
        dire: null, //  0:横屏;1:竖屏
        tags_list: null, // (List) 标签查询
        ky_flag: 1, //  0:非查询; 1:或查询 2:且查询
        spec:'',
        tags_list:[]
      },
      dir_spec:'',
      queryList: {
        search: "", //店铺名
        marketname: "", //运营市场
        storetypename: "", //门店类型
        itmarketname: "", //IT市场
        allEquipment: "" //全部设备
      },
      operatingMarketList: [], //运营市场下拉数据
      storeTypeList: [], //门店类型下拉数据
      itMarketList: [], //IT市场下拉数据
      allEquipmentList: [], //全部设备下拉数

      // 表格总数
      screenGroupTotal: "",
      //  表格数据
      tableData: [],
      //  抽屉
      drawer: false,
      //抽屉方向
      direction: "rtl",
      // 查询后table表格
      searchTable: [],
      // 查询后店铺数据
      shopData: {},
      // 联屏设定数据
      ScreenSettingList: [],
      // 查询loading效果
      ShopLoading: false,
      loadingState: true,
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      // 弹框状态
      tagsDialog: false,
      // size
      pageSize: 100,
      // 标签列表
      tagsList: [],
      // 多选屏幕ids
      screen_ids: [],
      delTagsList: [],
      delShowList: [],
      vsspecList:[],
      searchTagOptions:[],
      ky_flag_options:[
        {
          label: "且(包含全部已选标签,标签选择不能超过十个)",
          value: 2
        },
        {
          label: "或(包含任何一个已选标签)",
          value: 1
        },
        {
          label: "非(不包含任何已选标签)",
          value: 0
        }
      ]
    };
  },
  methods: {
    /**
     * @description 获取联屏列表数据
     * @param 具体参数见 "@/api/screen_group/screen_group.js"
     */
    getScreenGroup() {
      this.SearchFrom.blurry = this.SearchFrom.blurry.toUpperCase();
      let v_h = '';
      if(!this.dir_spec || this.dir_spec == ''){
        this.SearchFrom.dire = null;
        this.SearchFrom.spec = '';
      }else{
        v_h = this.dir_spec.indexOf('h') != -1 ? 'h' : 'v';
      }
      let spec = '';
      
      if(v_h == 'h'){
        this.SearchFrom.dire = 0;
        spec = this.dir_spec.replace('h_','')
      }else if(v_h == 'v'){
        this.SearchFrom.dire = 1;
        spec = this.dir_spec.replace('v_','')
    }

      this.SearchFrom.spec = spec;
      get_screen_group(this.SearchFrom).then(res => {
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;

          this.screenGroupTotal = res.data[0].totalElements;
          this.loadingState = false;
          sessionStorage.removeItem("ContentScreenSearch");
        }
      });
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "VsGroup" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        this.operatingMarketList = res.data[0][1]; //运营市场下拉数据
        this.storeTypeList = res.data[0][2]; //门店类型下拉数据
        this.itMarketList = res.data[0][3]; // IT市场下拉数据
        this.vsspecList = res.data[0][5]; // 规格下拉数据
        const screens = res.data[0][5].options;
        for (let i = 1; i < screens.length; i++) {
          this.ScreenSettingList.push({
            title: screens[i][1],
            value: screens[i][0],
            disable: false,
            selectValue: ""
          });
        }
        console.log(this.ScreenSettingList, "ScreenSettingList");
        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
      });
    },
    // 搜索
    handleSearch() {
      this.loadingState = true;
      console.log(this.SearchFrom, 'xx');
      this.SearchFrom.page = 0;

      this.getScreenGroup();
    },
    //表格查看按钮
    toLook(row) {
      this.$router.push({
        path: "/deviceManage/connectDetail",
        query: {
          dmakey: row.sg_key,
          storecode: row.storecode,
          shop_id: row.shop_id,
          specification: row.specification
        }
      });
      let SearchString = JSON.stringify(this.SearchFrom);
      sessionStorage.setItem("ContentScreenSearch", SearchString);
    },
    //  表格第一行样式
    // 表格头第一行上色
    rowClass({ row, rowIndex }) {
      return "background:#d7e7fa";
    },
    //新建联屏组
    newScreenGroup() {
      this.drawer = true;
      this.$refs.drawer.IsShowButton = false;
    },
    // 关闭联屏抽屉
    closeDrawer() {
      this.drawer = false;
    },
    // 查询事件
    searchShop(searchFrom) {
      this.ShopLoading = true;
      searchFrom.shop_name = searchFrom.shop_name.toUpperCase();
      console.log(searchFrom, "searchFrom");
      search_screen_group(searchFrom).then(res => {
        if (res.rst == "ok") {
          this.searchTable = res.data[0];
          this.ShopLoading = false;
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 点击添加进入查询当前店铺详情
    addShop(shopId) {
      const params = {
        g_group_id: localStorage.getItem("group_id"),
        shop_id: shopId,
        dataSource: "dmb"
      };
      get_shop_data(params).then(res => {
        if (res.rst == "ok") {
          this.shopData = res.data[0];
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    //绑定屏幕
    // bindingScroll(bindScrollData, shop_id) {
    //   this.ShopLoading = true;
    //   this.loadingState = true;
    //   bind_shop(bindScrollData).then((res) => {
    //     if (res.rst == "ok") {
    //       this.$message.success("绑定成功");
    //       this.$refs.drawer.addShop(shop_id);
    //       this.getScreenGroup();
    //       setTimeout(() => {
    //         this.ShopLoading = false;
    //       }, 500);
    //     } else {
    //       this.$message.warning(res.rst);
    //     }
    //   });
    // },
    // 解绑屏幕
    unbindingScroll(sgKey, shop_id) {
      this.ShopLoading = true;
      const params = {
        sg_key: sgKey
      };
      unbind_shop(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("解绑成功");
          setTimeout(() => {
            this.$refs.drawer.addShop(shop_id);
            setTimeout(() => {
              this.ShopLoading = false;
            }, 500);
          }, 1000);
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 保存tags
    saveTags(params) {
      this.loadingState = true;
      // this.bindingScroll()
      bind_shop_tags(params).then(res => {
        if (res.rst == "ok") {
          this.drawer = false;
          // this.$message.success("保存成功");
          this.$refs.drawer.searchState = false;
          this.$refs.drawer.ShopLoading = false;
          this.$refs.drawer.shopState = false;
          this.$refs.drawer.SearchFrom.shop_name = "";

          this.getScreenGroup();
        } else {
          // this.$message.warning(res.rst);
        }
      });
    },
    // 取消
    cancel() {
      this.drawer = false;
    },
    // 解绑
    unbundle(row) {
      const params = {
        sg_key: row.sg_key
      };
      this.$confirm("是否将此设备解绑?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loadingState = true;
        unbind_shop(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("解绑成功");
            this.getScreenGroup();
          } else {
            this.$message.warning(res.rst);
          }
        });
      });
    },
    // 页码改变
    handleCurrentChange(val) {
      this.SearchFrom.page = val - 1;
      this.getScreenGroup();
    },
    handleSizeChange(val) {
      this.SearchFrom.size = val;
      this.getScreenGroup();
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 230 + "px";
      this.autoHeight.heightNum = windowHeight - 200;
    },
    // 多选
    handleSelectionChange(val) {
      this.screen_ids = [];
      this.delTagsList = [];
      this.delShowList = [];

      val.forEach(item => {
        this.screen_ids.push(item.sg_id);
        item.screen_tags = [];
        if (item.tags.length != 0) {
          item.tags.forEach(item1 => {
            item.screen_tags.push(item1);
            this.delTagsList.push(item1);
          });
        }
      });
    },
    // 获取tags
    getScreenTags() {
      this.tagsList = [];
      const params = {
        tag_type: "sngroup",
        page: 0,
        size: 999,
        dmbSource: true
      };
      get_tags_list(params).then(res => {
        this.searchTagOptions = res.data[0]['tags_list'];
        res.data[0].tags_list.forEach(item => {
          this.tagsList.push({
            name: item,
            active: false
          });
        });
      });
    },
    // 打开弹框
    setBatchTag() {
      if (this.screen_ids.length != 0) {
        this.getScreenTags();
        this.delTagsList.forEach(item => {
          this.delShowList.push({
            active: false,
            name: item
          });
        });
        this.delShowList = removeDuplicateObj(this.delShowList);
        this.tagsDialog = true;
      } else {
        this.$message.closeAll();
        this.$message.warning("请选择联屏组");
      }
    },
    // 保存批量
    saveScreenTags(tags, message) {
      const params = {
        sg_ids: this.screen_ids,
        tags: tags,
        action: "add"
      };

      if (message == "relation") {
        params.action = "add";
        create_or_delete_group_tags(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("批量增加标签成功");
            this.getScreenGroup();
            this.tagsDialog = false;
          } else {
            this.$message.warning(res.error_msg);
          }
        });
      } else {
        params.action = "del";
        create_or_delete_group_tags(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("批量删除标签成功");
            this.getScreenGroup();
            this.tagsDialog = false;
          } else {
            this.$message.warning(res.error_msg);
          }
        });
      }
    },
    // 关闭
    handleCloseDialog() {
      this.tagsDialog = false;
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);

    this.getSelectDataList();
    this.getHeight();
    this.getScreenTags()
    if (sessionStorage.getItem("ContentScreenSearch")) {
      let SearchSn = JSON.parse(sessionStorage.getItem("ContentScreenSearch"));
      console.log( SearchSn,"SearchSn");

      if(SearchSn.spec && SearchSn.spec !== ""){
        let v_h = '';
        if(SearchSn.dire == 0){
            v_h = 'h'
        }else if(SearchSn.dire == 1){
            v_h = 'v'
        }
        this.dir_spec = v_h + '_' + SearchSn.spec;
      }

      this.SearchFrom = SearchSn;
      this.handleSearch();
    } else {
      this.getScreenGroup();
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .warning-row {
  background: #f0f0f0;
}

::v-deep .el-table .el-table__cell {
  padding: 0.05rem 0;
}

::v-deep .el-table .success-row {
  background: #f0f0f0;
}

// 竖向
.devShu {
  width: 25px !important;
  height: 30px !important;
  display: block !important;
}

.box {
  width: 100%;
  font-size: 0.14rem;
  background-color: #fff;
}

.box>.top {
  height: 0.6rem;
  justify-content: space-between;
  align-items: center;
  display: flex;
  padding: 0 0.2rem;
}

.box>.top>.left {
  display: flex;
  align-items: center;
}

.box>.top .query {
  width: 1.65rem;
  height: 0.4rem;
  position: relative;
  border: 0.01rem solid rgba(204, 204, 204, 1);
}

.box>.top .query>img {
  width: 0.24rem;
  height: 0.24rem;
  margin: 0.1rem;
}

.box>.top .query>input {
  position: absolute;
  right: 0;
  top: 0;
  width: 80%;
  height: 100%;
  border: none;
  color: rgba(128, 128, 128, 1);
}

.left>.el-select {
  margin: 0 0.1rem;
}

.cell>div {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell>div>span {
  width: 0.28rem;
  /* height: 0.18rem; */
  display: block;
  background-color: var(--screen-color);
  border: 0.01rem solid #fff;
}

table {
  text-align: center;
  margin-top: 0.2rem;
}

/*分页*/
.bottom {
  position: relative;
  margin-top: 0.2rem;
  width: 100%;
}

.bottom_footer {
  margin-top: 20px;
  padding: 0 20px;
}

.bottom>button {
  margin-left: 0.2rem;
  width: 1.06rem;
  height: 0.32rem;
}

::v-deep .el-button--primary {
  line-height: 0.05rem;
}

.bottom>.block {
  width: 4.85rem;
  height: 0.32rem;
  position: absolute;
  right: 0.1rem;
}

.el-pagination {
  float: right;
}

// ::v-deep .btn-prev {
//   width: 0.4rem;
//   border: 1px solid #cfcfcf;
// }
// ::v-deep .number {
//   margin: 0 0.05rem;
//   border: 1px solid #cfcfcf;
// }
/* .el-pager>.number{
  width: 0.4rem;
  height: 0.28rem;
  border: 0.01rem solid #ddd;
} */
/* ::v-deep .el-pagination__jump>.el-input{
  width: 0.35rem;
} */
/* .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon{
  width: 0.38rem;
  display: inline-block;
  height: 0.26rem;
  line-height: 0.26rem;
  border: 0.01rem solid #ddd;
  vertical-align: middle;
} */
/*抽屉*/
.box ::v-deep section {
  padding: 0.1rem 0.1rem 0.5rem 0.1rem !important;
  position: relative;
}

section>.top {
  display: flex;
  justify-content: space-between;
}

section>.top>button {
  height: 0.32rem;
  line-height: 0.08rem;
}

section ul>li {
  display: flex;
  margin-top: 0.29rem;
}

section ul>li>span {
  width: 0.7rem;
  height: 0.21rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  font-weight: bold;
}

.littleBox {
  display: block;
  width: 0.28rem;
  height: 0.18rem;
  line-height: 0.18rem;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 0.14rem;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}

/*联屏设置*/
.set {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.23rem;
}

.set>button {
  background-color: var(--text-color);
}

/*选择屏幕*/
.btns {
  display: flex;
  margin-top: 0.2rem;
}

.btns>.btn {
  width: 0.9rem;
  height: 0.48rem;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(39, 177, 126,1);
  font-size: 0.14rem;
  color: #fff;
  line-height: 0.48rem;
  border: rgba(0, 0, 0, 1) solid 1px;
}

.btns>.el-select {
  width: 1.5rem;
  height: 0.4rem;
}

::v-deep el-input__inner {
  height: 0.5rem;
}

.label>h4 {
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  font-weight: bold;
  margin: 0.3rem 0;
}

.label {
  /* padding-left: 0.2rem; */
}

.label_text {
  height: 100%;
  margin-left: 2px;
  border: 0 !important;
  border-radius: 10px;
  color: #3c6bf9;
}

.close,
.save {
  position: absolute;
  right: 1.08rem;
  bottom: 0.3rem;
}

.save {
  right: 0.2rem;
}

.search {
  width: 88px;
  height: 32px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 6px;
}

.btn_color {
  background-color: var(--btn-background-color);
  color: #fff;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.box .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.box .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.box .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.box .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}


</style>