<template>
    <div class="strategy">
        
        <div class="Specification">
            <h3 class="content_title">选择联屏规格</h3>
            <div class="gp">
                <div v-for="(item, index) in speci" class="speci" :key="item">
                    <div class="specic" @click="eventActive(index, item)">
                        <div v-for="item1 in item.value" :class="item1 == 0 ? 'across' : 'vertical'" :key="item1"></div>
                    </div>
                    <div class="alternative">
                        <el-radio v-model="radio" :label="item.name" @change="changeRadio(index, item)"></el-radio>
                    </div>
                </div>
            </div>
        </div>
        <div class="set_play_rule">
            <h3 class="content_title">播放时长规则设置</h3>
            <div class="select_wrap">
                <div class="select_info">
                    <span>设置完整动画确保屏幕：</span>
                    <div class="s_t_wrap">
                        <el-select v-model="paly_rules.complete_ani_screen" >
                            <el-option v-for="(item,index) in select_screen_optios" :key="'comp_' + index" :label="item.s_number" :value="item.s_number"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="select_info">
                    <span>延迟屏幕：</span>
                    <div class="s_t_wrap">
                        <el-select v-model="paly_rules.delay_screen" style="width: 230px;">
                            <el-option v-for="(item,index) in select_screen_optios" :key="'delay_' + index" :label="item.s_number" :value="item.s_number"></el-option>
                        </el-select>
                        <span class="select_tips" v-if="paly_rules.delay_screen && paly_rules.delay_screen != ''">延迟屏幕{{ paly_rules.delay_screen }}:对应屏幕起始延迟{{ paly_rules.delay_time }}秒</span>
                    </div>
                </div>
                <div class="select_info">
                    <span>对应屏幕起始延迟时间：</span>
                    <el-input v-model="paly_rules.delay_time" style="width:50px;margin-right: 10px;"></el-input>
                    <span>秒</span>
                </div>
                <div class="select_info">
                    <el-button type="primary" size="small" @click="savePlayRule">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { datas_filter_cond } from "@/api/commonInterface";
import { removeDuplicateObj } from "@/utils/setArray";
export default {
    name: 'connectStrategy',
    data() {
        return {
            dialogVisible: false,
            speciData: [],
            speci: [],
            radio: "00",
            active: 0,
            dmb_spec: '',
            select_screen_optios:[],
            paly_rules:{
                complete_ani_screen:'',
                delay_screen:'',
                delay_time:0
            }
        }
    },
    methods: {
        getSelectDataList() {
            const params = {
                classModel: "ContentPub" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
            };
            datas_filter_cond(params).then(res => {
                console.log(res["data"], "res");
                this.speciData = res['data'][0][0]['options']
                this.speciData.forEach(item => {
                    if (item[1].indexOf("组合") == -1) {
                        this.speci.push({
                            name: item[1],
                            value: item[0],
                            order: item[1][0] == "横" ? 0 : 1
                        });
                    }
                });

                this.speci = removeDuplicateObj(this.speci);
            });
        },
        eventActive(index, item) {
            this.activeChange(index,item);
        },
        changeRadio(index, item) {
            this.activeChange(index,item);
        },
        activeChange(index,item){
            this.active = index;
            this.radio = item.name;
            this.dmb_spec = item.value;
            const col_num = Number(item.name.split("*")[1]);
            let list = new Array(col_num).fill(0).map((col,idx)=>{
                return {
                    s_number:idx + 1
                }
            });
            this.select_screen_optios = list;
            this.paly_rules.complete_ani_screen = '';
            this.paly_rules.delay_screen = '';
            this.paly_rules.delay_time = 0;
        },
        savePlayRule(){
            this.checkCanSave().then(res=>{
                if(res.status == 'pass'){
                    console.log(12);
                }else{
                    this.$message.warning(res.str);
                }
            })
        },
        checkCanSave(){
            return new Promise((resolve,reject)=>{
                let str = '';
                let status = 'deny';
                if(!this.dmb_spec || this.dmb_spec == ''){
                    str = '请选择联屏规格';
                    status = 'deny';
                }else if(!this.paly_rules.complete_ani_screen || this.paly_rules.complete_ani_screen == ''){
                    str = '请选择完整动画确保屏幕';
                    status = 'deny';
                }else if(!this.paly_rules.delay_screen || this.paly_rules.delay_screen == ''){
                    str = '请选择延迟屏幕';
                    status = 'deny';
                }else if(!this.paly_rules.delay_time || this.paly_rules.delay_time == ''){
                    str = '请输入延迟时间';
                    status = 'deny';
                }else{
                    status = 'pass'
                }

                resolve({status,str})
            })
        }
    },
    mounted() {
        this.getSelectDataList()
    }
}

</script>

<style scoped lang="scss">
.strategy {
    padding: 20px;
    .content_title{
        color: var(--text-color-light);
        padding-left: 10px;
        margin-bottom: 20px;
    }
    .header {
        height: 50px;
        display: flex;
        align-items: center;
        // margin-bottom: 40px;
        // padding: 15px;
        .haeder_btn {
            background-color: var(--btn-background-color);
            color: var(--btn-color);
        }
    }
    
    .Specification {
        // flex-wrap: wrap;
        margin-bottom: 40px;
        width: 100%;
        .gp {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
        }

        .speci {
            width: 138px !important;
            // height: 100px;
            margin-left: 10px;
            // margin-top: 32px;
            cursor: pointer;

            .specic {
                height: 80px;
                border: rgba(215, 215, 215, 1) solid 1px;
                border-radius: 3px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 10px;
            }

            .alternative {
                height: 30px;
                display: flex;
                align-items: center;
                margin-top: 5px;
                justify-content: center;
            }

            .across {
                width: 26px;
                height: 16px;
                background-color: rgba(229, 229, 229, 1);
                border: rgba(166, 166, 166, 1) solid 1px;
            }

            .vertical {
                width: 16px;
                height: 26px;
                background-color: rgba(229, 229, 229, 1);
                border: rgba(166, 166, 166, 1) solid 1px;
            }

        }
    }

    .set_play_rule{
        width: 100%;
        .select_wrap{
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;
            width: 100%;
            padding-left: 10px;
            .select_info{
                margin-right: 30px;
                margin-bottom: 40px;
                display: flex;
                align-items: center;
                .s_t_wrap{
                    position: relative;
                    .select_tips{
                        position: absolute;
                        top: 40px;
                        left: 0;
                        color: var(--text-color-light);
                    }
                }
                ::v-deep .el-input__inner{
                    color: var(--text-color-light);
                }
            }
        }
    }
}
</style>