<template>
    <div class='box'>
      <ul>
        <li :class="bgIndex==index?'radiusBlue':''" v-for='(item,index) in 5' :key='index' @click.stop='clickChange(index)'>
         <div>
           <span class='el-icon-first-aid-kit'></span>
           <p>网络设置</p>
         </div>
        </li>
        <el-button type="primary" style='background-color: var(--btn-background-color)' @click.stop='close'>关闭</el-button>
      </ul>
    </div>
</template>

<script>
export default {
  name: 'SetRemoteDev',
  data(){
    return {
      bgIndex:0,
    }
  },
  methods:{
    clickChange(idx){
      this.bgIndex = idx;
    },
  //  关闭
    close(){
      this.$router.push({
        path:"/ScreenDevice"
      })
    }
  }
}
</script>

<style scoped>
.box{
  width: 100%;
  height: 100%;
  background-color: #383838;
  position: relative;
}
ul{
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  width: 843px;
  height: 494px;
  background-color: #242422;
  flex-wrap: wrap;
  display: flex;
  padding: 5px 0 0;
}
ul>li{
  width: 241px;
  height: 206px;
  text-align: center;
  color: #fff;
  padding: 20px 20px;
  font-size: 14px;
  position: relative;
  border-bottom: 1px solid #2b2b2a;
}
.radiusBlue{
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
  background: linear-gradient(to top, #1b5b7c,#1d4f67, #203d4a);
}
ul>li>div{
  border-right: 1px solid #2b2b2a;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 10px;
  bottom: 10px;
}
ul>li>div>span{
  font-size: 80px;
  margin-top: 50px;
}
button{
  position: absolute;
  top: 0;
  right: -85px;
}
</style>
