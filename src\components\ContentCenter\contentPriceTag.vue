<template>
  <div class="tag">
    <!-- 添加价签 -->
    <!-- v-show="!isColorShow" -->
    <div class="addTags" v-if="isColorShow == true">
      <div class="price_text"> 价格文字 <el-input v-model="price_text" placeholder="请输入价格示意数字" @input="getPriceText">
        </el-input>
      </div>
      <div class="price_assist" style="margin-top:40px">辅助内容
        <div v-for="item in assistList" @click="setAssist(item)">
          {{item}}
        </div>
      </div>
      <div class="price_assist" style="margin-top:40px">价格组建库</div>
      <div class="price_bank">
        <div class="price_one" @click="addTags(0)">
          <span class="symbol">¥</span>
          <span class="price">5.5</span>
          <span class="unit">元</span>
        </div>
        <!-- <div class="price_tow" @click="addTags(1)">
          <div> <span class="symbol">大杯</span> <span class="price">00</span> <span class="unit">元</span> </div>
          <div> <span class="symbol">小杯</span> <span class="price">00</span> <span class="unit">元</span> </div>
        </div> -->
      </div>
      <div class="price_position" style="margin-top:40px">价签位置
        <div class="setting">
          <div v-for="item in PositionSetting" class="seting_div">
            <div style="display:flex;align-items: center; position: relative;">{{item.name}}
              <el-input-number v-model="item.value" @change="changeValue(item)" size="small" label="描述文字">
              </el-input-number>
            </div>
          </div>
        </div>
      </div>
      <div class="price_its" style="margin-top:40px">价签对齐方式
        <div class="its_three">
          <div v-for="item in itsList" @click="itsPrice(item)">
            <img :src="item.icon" alt="" style="width:20px;">
          </div>
        </div>
      </div>
    </div>

    <div class="color" style="margin-top:20px;text-align: left;" v-else-if="isColorShow == 'F'">
      色值设置
      <photoshop-picker v-model="colors" @input="changeColor" style="margin-top:10px"></photoshop-picker>
      <div style="margin-top:10px;"> 色值 : {{ colors.hex }}</div>
      <div style="display:flex;justify-content: start;align-items: center;margin-top: 15px;">
        <span style="height:40px;line-height: 40px;">输入色值:</span>
        <el-input type="text" style="height:40px;width:150px;margin-left: 10px;" v-model="colors.hex" />
      </div>
      <div style="display:flex;align-items: center;justify-content: center;margin-top: 20px;">
        <el-button style="height:40px;width:120px;font-size:18px;" type="primary" @click="changeColor">确认</el-button>
      </div>
    </div>

    <div class="negative" v-else-if="isColorShow == false">
      <div>
        <div> 圆角</div>
        <el-input-number v-model="borderValue" @change="changeBorderValue" size="small" label="描述文字"
          style="margin-left:40px">
        </el-input-number>
      </div>
      <!-- <div>
        <div> 阴影 </div>
        <el-input-number v-model="shadowValue" @change="changeShadowValue" size="small" label="描述文字"
          style="margin-left:40px">
        </el-input-number>
      </div> -->
      <div>
        <div> 描边 </div>
        <el-color-picker v-model="outlineValue" style="margin-left:12px" @active-change="ChangeoutlineValue">
        </el-color-picker>
        <div style="font-size:30px;margin-left: 155px;cursor: pointer;" @click="clearoutLine"> <img src="@/assets/icons/fu.png" alt=""
            style="width:20px">
        </div>
      </div>
      <div>
        <div> 描边大小 </div>
        <el-input-number v-model="lineSize" @change="changeLineSize" size="small"  label="描述文字"  style="margin-left:60px"></el-input-number>
      </div>
      <div style="border-bottom:3px solid rgba(80,80,80,1)">
        <div> 颜色 </div>
        <el-color-picker v-model="colorValue" style="margin-left:12px" @active-change="ChangeColorValue">
        </el-color-picker>
        <div style="font-size:30px;margin-left: 155px;cursor: pointer;" @click="clearcolor"> <img src="@/assets/icons/fu.png" alt=""
            style="width:20px">
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { Photoshop } from 'vue-color'
export default {
  name: "contentPriceTag",
  data() {
    return {
      price_text: '',
      isColorShow: true,
      assistList: [
        '¥', '$','无'
      ],
      // '/份', '/杯',
      colors: {
        hex: '#FFFFFF'
      },
      isColorFontShow: false,

      PositionSetting: [
        {
          name: "X",
          value: null
        },
        {
          name: "Y",
          value: null
        },
        {
          name: "W",
          value: null
        },
        {
          name: "H",
          value: null
        },
      ],
      selectPriceClass: '',
      selectChangeWath: {

      },
      itsList: [
        {
          icon: require("@/assets/icons/bottom.png"),
          type: 'top'
        },
        {
          icon: require("@/assets/icons/center.png"),
          type: 'center'
        },
        {
          icon: require("@/assets/icons/top.png"),
          type: 'bottom'
        },
      ],
      borderValue: 0,
      shadowValue: 0,
      outlineValue: '#FF0000',
      lineSize:'1',
      colorValue: ""
    };
  },
  components: {
    'photoshop-picker': Photoshop
  },
  methods: {
    addTags(type) {
      this.$emit("addTags", type)
    },
    changeColor() {
      this.$emit("changeColor", this.colors.hex)
    },
    setAssist(assiset) {
      this.$emit("setAssist", assiset)
    },
    getPriceText(e) {
      this.price_text = e.replace(/[^\d]/g, ""); // 只能输入数字
      this.price_text = this.price_text.replace(/^0+(\d)/, "$1"); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      if (e == '') {
        this.price_text = 0;
      }
      this.$emit("getPriceText", this.price_text)
    },
    changeValue(item) {
      this.selectChangeWath = item;
      this.selectChangeWath.className = this.selectPriceClass;
      this.$emit("changePricePosition", this.selectChangeWath)
    },
    itsPrice(item) {
      this.$emit("itsPrice", item)
    },
    changeBorderValue() {
      this.$emit("changeBorderValue", this.borderValue)
    },
    changeShadowValue() {
      this.$emit("changeShadowValue", this.shadowValue)
    },
    ChangeoutlineValue(e) {
      // console.log(e);
      this.outlineValue = e;
      // console.log(this.outlineValue);
      this.$emit("ChangeoutlineValue", this.outlineValue)
    },
    changeLineSize(e){
      this.$emit("changeLineSize",this.lineSize)
    },
    ChangeColorValue(e) {
      this.colorValue = e;
      // console.log(e);
      this.$emit("ChangeColorValue", this.colorValue)
    },
    clearoutLine() {
      this.$emit("clearoutLine")
    },
    clearcolor() {
      this.$emit("clearcolor")
    },
  },
  mounted() {
    var that = this;
    // 监听子页面想父页面的传参
    window.addEventListener('message', function (event) {
      //此处执行事件
      if (event.data.type == 'classPrice') {
        that.PositionSetting[0].value = Number(event.data.left);
        that.PositionSetting[1].value = Number(event.data.top);
        that.PositionSetting[2].value = Number(event.data.width);
        that.PositionSetting[3].value = Number(event.data.height);
        that.selectPriceClass = event.data.className;
      }
    });

  }
};
</script>

<style lang="scss" scoped>
.tag {
  text-align: center;
  padding: 20px 15px;
}

.addTags {
  display: flex;
  flex-direction: column;

  .price_text {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .el-input {
      width: 70% !important;

      ::v-deep .el-input__inner {
        background-color: rgba(255, 255, 255, 0.****************) !important;
        border: 0 !important;
        color: rgba(166, 166, 166, 1) !important;
      }
    }
  }

  .price_assist {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 10px;

    div {
      width: 30px;
      height: 30px;
      color: rgba(80, 80, 80, 1);
      background-color: rgba(255, 255, 255, 0.****************);
      color: #fff;
      line-height: 30px;
      text-align: center;
      border-radius: 5px;
      margin-left: 30px;
      cursor: pointer;
    }
  }

  .price_bank {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;

    .price_one {
      width: 135px;
      height: 135px;
      box-sizing: border-box;
      padding: 0 20px;
      border: 1px solid rgba(128, 128, 128, 1);
      border-radius: 6px;
      cursor: pointer;
    }

    .price_tow {
      width: 135px;
      height: 135px;
      box-sizing: border-box;
      padding: 0 20px;
      border: 1px solid rgba(128, 128, 128, 1);
      border-radius: 6px;
      cursor: pointer;
    }

    .price_one {
      display: flex;
      align-items: center;
      justify-content: center;

      .symbol {
        color: rgba(229, 229, 229, 1);
        font-size: 21px;
      }

      .price {
        color: rgba(229, 229, 229, 1);
        font-size: 53px;
        font-weight: bold;
      }

      .unit {
        color: rgba(255, 255, 255, 1);
        font-size: 21px;
      }
    }

    .price_tow {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      div {
        display: flex;
        width: 103%;
        align-items: center;
        justify-content: space-between;

        .symbol {
          color: rgba(229, 229, 229, 1);
          font-size: 18px;
        }

        .price {
          color: rgba(229, 229, 229, 1);
          font-size: 31px;
          font-weight: bold;
        }

        .unit {
          color: rgba(255, 255, 255, 1);
          font-size: 18px;
        }
      }
    }
  }

  .price_position {
    text-align: left;


    .setting {
      display: flex;
      flex-wrap: wrap;
      margin-left: -20px;

      .seting_div {
        width: 125px;
        margin-top: 20px;
        margin-left: 20px;
        height: 41px;
        line-height: 41px;
        padding-left: 10px;
        background-color: rgba(255, 255, 255, 0.****************);
        border-radius: 5px;
        display: flex;
        align-items: center;
      }
    }
  }

  .price_its {
    text-align: left;

    .its_three {
      width: 100%;
      height: 41px;
      margin-top: 15px;
      background-color: rgba(255, 255, 255, 0.****************);
      border-radius: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;

      div {
        cursor: pointer;
      }
    }
  }
}

::v-deep .el-input-number__increase {
  display: none;
}

::v-deep .el-input-number__decrease {
  display: none;
}

::v-deep .el-input-number {
  position: absolute;
  left: 15px;
  width: 90px;
}

::v-deep .el-input-number .el-input__inner {
  background-color: #535353;
  border: 0;
  padding: 0 0 !important;
  color: #fff;
}

.tag .search {
  margin: 10px 0 20px;
}

.tag .search ::v-deep .el-input__inner {
  background: rgba(56, 56, 56, 1);
}



.tag .color .colorsLi {
  display: flex;
  flex-wrap: wrap;
}

.tag .color>h4 {
  text-align: left;
  color: rgba(166, 166, 166, 1);
  margin: 10px;
}

.tag .color .colorsLi li {
  width: 45px;
  height: 45px;
  margin: 10px 5px;
  color: rgba(80, 80, 80, 1);
  /* background-color: rgba(245, 76, 101, 1); */
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  list-style: none;
  cursor: pointer;
}

.tag .color .last>h4 {
  text-align: left;
  color: rgba(166, 166, 166, 1);
  margin: 10px;
}

.tag .color .last ::v-deep .el-input-number .el-input {
  position: absolute;
  top: -37px;
  right: 0;
}

.tag .color .last ::v-deep .el-input-number__increase,
.tag .color .last ::v-deep .el-input-number__decrease {
  top: -37px;
}

.tag .color .last ::v-deep .el-slider__runway.show-input {
  margin-top: 20px;
  width: 280px;
}

::v-deep .vc-ps-controls {
  display: none;
}

::v-deep .vc-photoshop {
  width: 100% !important;
}

::v-deep .font_select .el-input__inner {
  font-size: 20px !important;
}

.negative {
  display: flex;
  flex-direction: column;

  div {
    height: 70px;
    display: flex;
    align-items: center;
    border-top: 3px solid rgba(80, 80, 80, 1);
  }
}
</style>
