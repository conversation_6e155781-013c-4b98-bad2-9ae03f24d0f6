<template>
    <div class="branch_dialog">
        <el-dialog title="分配品牌" :visible.sync="branchStateDialog" width="30%" :before-close="handleClose"
            :close-on-click-modal="false">

            <el-form :model="branchForm" class="demo-form-inline" :rules="rules" ref="form">
                <el-form-item label="品牌:" label-width="88px" placeholder="请选择品牌" prop="branchcode">
                    <el-select v-model="branchForm['branchcode']" style="width:240px;margin-right:12px" clearable
                        @change="getBranch">
                        <el-option v-for="item in branchList" :key="item.id" :label="item.text" :value="item.branchcode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="item.placeholder + ':'" label-width="88px" v-for="item in mylist"
                    :key="item.placeholder" :prop="item.filterkey + 'code'">
                    <el-input v-if="item.type == 'input'" :placeholder="item.placeholder" size="small"
                        prefix-icon="el-icon-search" @keyup.enter.native='handleSearch'
                        v-model="branchForm[item.filterkey]" style="width:240px;margin-right:12px">
                    </el-input>
                    <el-select v-if="item.type == 'select'" v-model="branchForm[item.filterkey + 'code']" clearable
                        filterable :placeholder="item.placeholder" size="small" style="width:240px;margin-right:12px"
                        @change="selectBranch(item)">
                        <el-option v-for="items in item.options" :key="items[1]" :label="items[1]" :value="items[0]">
                        </el-option>
                    </el-select>
                </el-form-item>

            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" style="height:35px" @click="saveBranch('form')">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { datas_filter_cond } from '@/api/commonInterface'
import { get_mgmtuser_branchlist } from '@/api/device/device'
export default {
    data() {
        return {
            branchForm: {
                branchcode: ""
            },
            mylist: [],
            queryList: {},
            branchList: [],
            rules: {
                branchcode: [
                    { required: true, message: '请选择品牌', trigger: 'change' },
                ],
                opsmarketcode: [
                    { required: true, message: '请选择营运市场', trigger: 'change' },
                ],
                storetypecode: [
                    { required: true, message: '请选择门店类型', trigger: 'change' },
                ],
                itmarketcode: [
                    { required: true, message: '请选择IT市场', trigger: 'change' },
                ],
            }
        }
    },
    props: {
        branchStateDialog: {
            type: Boolean
        }
    },
    watch: {
        branchStateDialog(newValue, oldValue) {
            console.log("newValue", newValue);
            if (newValue == true) {
                this.getSelectDataList()
                this.get_mgmtuser_branchlist()
            }
        }
    },
    methods: {
        handleClose() {
            this.$emit("closeBranch")
        },
        // 获取下拉数据
        getSelectDataList() {
            const params = {
                classModel: 'GroupShop', //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
                brandcode: this.branchForm.branchcode
            }
            datas_filter_cond(params).then(res => {
                console.log(res, '[res1]');
                this.mylist = res.data[0];
                this.mylist.shift()
            })
        },
        get_mgmtuser_branchlist() {
            get_mgmtuser_branchlist({}).then(res => {
                console.log(res);
                this.branchList = res['data'][0]['structure'];
                this.branchList.forEach((item, index) => {
                    if (item.id == 'g137880') {
                        this.branchList.splice(index, 1)
                    }
                })
            })
        },
        getBranch() {
            console.log(this.branchForm);
            this.getSelectDataList()
        },
        selectBranch(e) {
            console.log(e);
            if (e.placeholder == "营运市场") {
                e.options.forEach(item => {
                    if (item[0] == this.branchForm.opsmarketcode) {
                        this.branchForm.opsmarketname = item[1]
                    }
                })
            } else if (e.placeholder == "门店类型") {
                e.options.forEach(item => {
                    if (item[0] == this.branchForm.storetypecode) {
                        this.branchForm.storetypename = item[1]
                    }
                })
            } else if (e.placeholder == "IT市场") {
                e.options.forEach(item => {
                    if (item[0] == this.branchForm.itmarketcode) {
                        this.branchForm.itmarketname = item[1]
                    }
                })
            }
        },
        saveBranch(form) {
            this.$refs[form].validate((valid) => {
                if (valid) {
                    this.$emit("saveBranch", this.branchForm)
                } else {
                    return false;
                }
            });
        }
    },
    created() {

    }
}
</script>

<style lang="scss" scoped>
</style>