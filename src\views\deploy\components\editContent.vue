<template>
    <div class="contentCreate">
        <el-button @click="newScene" class="newScene" v-if="pub_flow_status_code != 4">新增场景</el-button>
        <div class="scene">
            <div class="table" v-show="templateScene.length != 0">
                <div class="header">
                    <div> 联屏组内容 </div>
                </div>
                <div class="content">
                    <div v-for="item in templateScene" :key="item" style="display:flex;height:200px">
                        <div class="left">
                            <div class="ordering">
                                {{ item.ordering }}
                                <input type="text" v-model="item.ordering" :disabled="pub_flow_status_code == 4"
                                    @blur="setOrder($event, item)">
                            </div>
                            <div class="duration" @click="setDuration(item)" v-if="pub_flow_status_code != 4">
                                {{ item.duration }} s
                            </div>
                            <div class="duration" v-else>
                                {{ item.duration }} s
                            </div>
                        </div>
                        <div class="right" v-for="(item1, index) in item.tile_groups" :style="{ flex: item1.x_length }"
                            :key="index">
                            <div class="right_content">
                                <div style="height:30px;color:var(--btn-color);line-height: 30px;position: absolute;left: 5px;z-index: 999;"
                                    class="ptrex_text">
                                    {{ item1.percent_text }} </div>
                                <div class="addImage" @click.stop="openSelectImages(item, item1)"
                                    v-if="item1.percent_text == '等待添加内容...'">
                                    +
                                </div>
                                <div class="addText" v-if="item1.percent_text == '等待添加内容...'"
                                    @click.stop="openSelectImages(item, item1)"> 新增</div>
                                <div v-else style="width:100%;height:100%;box-sizing: border-box;padding: 3px;"
                                    class="event_show">
                                    <div class="event" v-if="pub_flow_status_code != 4">
                                        <div class="edit" @click.stop="openSelectImages(item, item1)">
                                            <i class="el-icon-edit-outline"></i>
                                        </div>
                                    </div>
                                    <img :src="item1.cs_list[0].thumb_url" alt="" style="width:100%;height:100%">
                                </div>
                            </div>
                        </div>
                        <div class="delete" v-if="pub_flow_status_code != 4">
                            <span @click="deleteScene(item)"> 删除 </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <el-dialog title="新建场景" :visible.sync="dialogVisible" width="40%" :before-close="handleClose"
            :close-on-click-modal="false">
            <div class="center">
                <div v-for="(item, index) in layoutArray" class="center_content" @click="selectTempl(item, index)"
                    :key="index">
                    <i class="el-icon-success iconr" :class="activeIndex == index ? 'active' : ''"></i>

                    <div style="display:flex;width:100px;">
                        <div v-for="item1 in item" :class="arrange == 'h' ? 'arcoss' : 'vision'" class="selectTemplate"
                            :key="item1" :style="{ flex: item1 }">
                        </div>

                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="addScene">确 定</el-button>
            </span>
        </el-dialog>


        <!--    点击后出现-->
        <el-dialog title="新增内容" :visible.sync="showAlertBox" width="61%" custom-class="tabsDatat"
            :before-close="showAlertBox">
            <div class='threeParts tag_set'>
                <!--        tab栏切换-->
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="图片" name="first">
                        <picture-resources :tagsList='tagsList' :delTagsList='delTagsList' @checkedList='checkedList'
                            @setImgPreview='setImgPreview' ref="picture"></picture-resources>

                    </el-tab-pane>
                    <el-tab-pane label="视频" name="second">
                        <video-resources @setVideoPreview='setVideoPreview' @checkedList='checkedList' ref="video">
                        </video-resources>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class="btns dialog_btns">
                <el-button type='primary' @click.stop='showAlertBox = false' slot="right: 100px;">取消</el-button>
                <!-- <el-button type='primary' @click.stop='alertOk'>确定</el-button> -->
                <el-button type='primary' v-debounce.stop='alertOk'>确定</el-button>
            </div>
        </el-dialog>
        <el-dialog title="修改时长" :visible.sync="durationState" width="20%" :before-close="handleClose">
            时长: <el-input type="text" style="width:200px" v-model="durationValue" autocomplete="off"></el-input>
            <span style="margin-left:3px">s</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="durationState = false">取 消</el-button>
                <el-button type="primary" @click="requestDuration">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 预览mask -->
        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask'>
        </previsualization>


    </div>
</template>

<script>
import { summation, summation1 } from "@/utils/resolveNum"
import { api_create_page, api_get_vs_cf_info, api_add_content, api_delete_page, get_btpub_detail, api_set_page_ordering, api_set_page_duration } from "@/api/contentdeploy/contentdeploy"
import pictureResources from '@/components/ContentCenter/pictureResources'
import videoResources from '@/components/ContentCenter/videoResources'
import h5Resources from '@/components/ContentCenter/h5Resources'
import previsualization from '@/components/communal/previsualization'
export default {
    data() {
        return {
            vs_content_spec: '',
            dialogVisible: false,
            layoutArray: [],
            arrange: 'h',
            activeIndex: null,
            ref_id: null,
            SceneTem: null,
            templateScene: [],
            showAlertBox: false,
            requestImages: [],
            addContentParams: {
                page_id: "",
                tile_index: "",
                content_type: "",
                content_id: ""
            },
            activeName: "first",
            ContentData: null,
            EditContent: null,
            durationState: false,
            durationValue: "",
            setDuraion: {},
            PreviewType: '',
            PreviewSrc: '',
            pub_flow_status_code: ''
        }
    },
    components: {
        pictureResources,
        videoResources,
        h5Resources,
        previsualization
    },
    props: {

        btpub_id: {
            type: String
        }
    },
    watch: {

    },
    methods: {
        // 预览
        setImgPreview(val) {
            this.PreviewType = 'image'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val) {
            this.PreviewType = 'video'
            this.PreviewSrc = val
            this.isPreviewMaskShow = true;
        },
        Preview(type, val) {
            if (type == "image") {
                this.setImgPreview(val.thumb_url)
            } else if (type == "video") {
                this.setVideoPreview(val.video_info.source_url)
            }
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = '';
        },
        newScene() {
            this.dialogVisible = true;
        },
        selectTempl(item, index) {
            this.SceneTem = item;
            this.activeIndex = index
        },
        addScene() {
            const params = {
                vslength: this.vs_content_spec[this.vs_content_spec.length - 1],
                spec: this.SceneTem,
                cf_id: this.ref_id
            }
            console.log(params);
            api_create_page(params).then(res => {
                console.log(res);
                if (res.rst == "ok") {
                    this.$message.success("新建场景成功");
                    this.dialogVisible = false;
                    this.getContentInfo()
                } else {
                    this.$message.warning(res.error_msg)
                }
            })
        },
        getContentInfo() {
            const params = {
                cf_id: this.ref_id
            }
            api_get_vs_cf_info(params).then(res => {
                console.log(res);
                this.ContentData = this.templateScene = res.data[0]
                this.templateScene = res.data[0].cf_info.pages;
                this.$parent.loading = false;
                console.log(this.templateScene);
            })
        },
        openSelectImages(item, item1) {
            console.log(item);
            this.addContentParams.page_id = item.id;
            this.addContentParams.tile_index = item1.index;
            this.showAlertBox = true;
            setTimeout(() => {
                this.$refs.picture.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.video.checkedList.forEach(item => {
                    item.checked = false;
                })
                this.$refs.picture.batchState = true;
                this.$refs.video.batchState = true;
                this.$refs.picture.isContent = true;
                this.$refs.video.isContent = true;
            }, 500);
        },
        checkedList(imgList) {
            imgList.forEach(item => {
                item.thumb_url = item.thumb
            })
            console.log("imgList", imgList);
            this.requestImages = imgList;
            //  this.addContentParams.tile_index = index;
        },
        alertOk() {
            if (this.requestImages.length == 0) {
                this.$message.warning("请选择图片/视频")
            } else {
                if (this.activeName == "first") {
                    this.addContentParams.content_type = "photo"
                    this.addContentParams.content_id = this.requestImages[0].file_key;

                } else if (this.activeName == "second") {
                    this.addContentParams.content_type = "video"
                    this.addContentParams.content_id = this.requestImages[0].file_id;
                }
                api_add_content(this.addContentParams).then(res => {
                    console.log(res);
                    if (res.rst == "ok") {
                        this.$message.success("添加内容成功");
                        this.getContentInfo()

                        this.showAlertBox = false;
                    } else {
                        this.$message.warning(res.error_msg)
                    }
                })

            }
        },
        //删除场景
        deleteScene(item) {
            console.log(item);
            const params = {
                page_id: item.id
            }
            this.$confirm('此操作将永久删除此场景, 是否继续?', '删除场景', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                api_delete_page(params).then(res => {
                    if (res.rst == "ok") {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getContentInfo()
                    }
                })

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });

        },
        getContent() {
            get_btpub_detail({
                btpub_id: this.btpub_id
            }).then(res => {
                console.log(res, "1re");
                if (res.rst == "ok") {
                    this.ref_id = res["data"][0]["contents"][0]["ref_id"]
                    this.vs_spec = res["data"][0]["vs_spec"];
                    this.DeployForm = res["data"][0]['pub_info'] ? res["data"][0]['pub_info']['pub_json'] : {}
                    this.EditContent = res["data"][0]
                    this.vs_content_spec = this.vs_spec;
                    this.layoutArray = summation1(this.vs_content_spec[this.vs_content_spec.length - 1]);
                    this.$parent.contents = res["data"][0]["contents"]
                    this.getContentInfo()
                }
            })
        },
        setOrder(e, item) {
            console.log(item);
            const parmas = {
                page_id: item.id,
                ordering: e.target.value
            }
            api_set_page_ordering(parmas).then(res => {
                console.log(res);
                if (res.rst == "ok") {
                    this.$message.success("调整成功")
                    this.getContentInfo()
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        setDuration(item) {
            this.durationState = true;
            this.setDuraion.page_id = item.id;
            this.durationValue = ""
        },
        requestDuration() {
            this.setDuraion.duration = this.durationValue;
            console.log(this.setDuraion);
            api_set_page_duration(this.setDuraion).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("设置时长成功");
                    this.getContentInfo()
                } else {
                    this.$message.error(res.error_msg)
                }
            })
            this.durationState = false;
        }
    },
    created() {
        // this.layoutArray = summation(3);
        this.getContent()
        this.pub_flow_status_code = this.$route.query.pub_flow_status_code
    },
    mounted() {

    }
}
</script>

<style lang="scss" scoped>
.dialog_btns {
  height: 50px;
  button {
      margin: 0 20px 0 0 !important;
  }
}
.contentCreate {
    padding: 20px 0;
}

.center_content {
    width: 200px;
    padding: 20px 0;
    height: 140px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    background-color: #f3f6fa;
    cursor: pointer;
    position: relative;

    .selectTemplate {
        height: 20px;
        width: 20px !important;
        border: 1px solid #d3dce9;
        background-color: #ffffff;
    }
}

.center {
    width: 95%;
    height: 375px;
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    margin-left: -20px;
    justify-content: flex-start;
    align-items: flex-start;
}

.arcoss {
    display: flex;
    background-color: var(--text-color-light) !important;
    color:var(--btn-color);
}

.vision {
    height: 50px !important;
    color:var(--btn-color);
}

.iconr {
    position: absolute;
    right: 20px;
    top: 10px;
    color: green;
    display: none;
    font-size: 20px;
}

.active {
    display: block;
}

.newScene {
    width: 106px;
    height: 30px;
    background-color: var(--background-color);
    color: white;
    float: right;
}

.scene {
    width: 100%;
    display: flex;
    justify-content: center;

    .table {
        width: 90%;
        border: 1px solid #ccc;
        margin-top: 10px;

        .header {
            height: 70px;
            line-height: 70px;
            border: 0;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }

        .content {

            .left {
                border: 1px solid #ccc;
                text-align: center;
                line-height: 200px;
                border-bottom: 0;
                border-left: 0;
                // flex: 1;
                width: 160px;
                border-right: 0;
                position: relative;

                .ordering {
                    width: 100%;
                    height: 100%;
                    position: relative;

                    input {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        text-align: center;
                        font-size: 25px;
                    }
                }

                .duration {
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    height: 45px;
                    background-color: var(--text-color-light);;
                    color: #fff;
                    font-weight: bold;
                    line-height: 45px;
                    font-size: 16px;
                    cursor: pointer;
                }
            }

            .right {
                border: 1px solid #ccc;
                border-right: 0;
                border-bottom: 0;
                display: flex;

                .right_content {
                    flex: 1;
                    text-align: center;
                    line-height: 200px;
                    position: relative;

                    .addImage {
                        width: 25px;
                        height: 25px;
                        color: #ffffff;
                        background-color: #e5e5e5;
                        line-height: 25px;
                        text-align: center;
                        position: absolute;
                        left: 45%;
                        top: 45%;
                        font-size: 17px;
                        color: black;
                        border-radius: 50%;
                    }

                    .addText {
                        color: #000;
                        position: absolute;
                        left: 45%;
                        top: 16%;
                    }

                    .event_show {
                        position: relative;

                        &:hover {
                            .event {
                                display: block;
                                display: flex;
                            }
                        }

                        .event {
                            display: flex;
                            flex-direction: column;
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            justify-content: center;
                            align-items: center;
                            display: none;

                            .edit {
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                background-color: #ccc;
                                color: #fff;
                                line-height: 35px;
                                text-align: center;
                                margin-bottom: 10px;
                                cursor: pointer;

                                i {
                                    font-size: 15px;
                                }
                            }

                            // .delete {
                            //     width: 35px;
                            //     height: 35px;
                            //     border-radius: 50%;
                            //     background-color: #ccc;
                            //     color: #fff;
                            //     margin-top: 10px;
                            //     line-height: 35px;
                            //     text-align: center;
                            //     cursor: pointer;

                            //     i {
                            //         font-size: 15px;
                            //     }
                            // }
                        }
                    }
                }
            }

            .delete {
                width: 50px;
                text-align: center;
                height: 100%;
                line-height: 200px;
                border-left: 1px solid #ccc;
                border-top: 1px solid #ccc;
                color: var(--text-color-light);;

                span {
                    cursor: pointer;
                }
            }


        }
    }
}

.btns {
    display: flex;
    margin-top: -12px;
    padding-bottom: 10px;
    justify-content: flex-end;
    width: 100%;
}

.ptrex_text {
    background: rgba($color: #000000, $alpha: 0.4);
    padding: 0 10px;
    top: 3px;
}</style>