export default {
    state: {
        ossInfo:null,            // 初始化阿里云配置
        uploadCompleted:0,       // 是否已经上传完成 0:没有完成 1：全部完成
        videoTimer:null,        // 视频timer
        uploadNum:3,            // 可同时上传的数量
        videoUploadList:[],      // 需要上传的视频列表
        uploadingVideo:[],      // 正在上传的视频列表
        successNum:0,           // 上传成功数量
        failNum:0,              // 上传失败数量
        uploadType:['mp4','mov','avi'],
        maxSize:500 * 1024 * 1024,
    },
    getters:{
        getUploadList:state=>{
            return state.videoUploadList.videoUploadList;
        }
    },
    mutations: {
        SET_VIDEO_UPLOAD_LIST(state,value){
            state.videoUploadList = value;
        },
        RESET_LIST(state){
            clearInterval(state.videoTimer);
            state.videoTimer = null;
            state.videoUploadList = [];
            state.uploadingVideo = [];
            state.successNum = 0;
            state.failNum = 0;
            state.uploadCompleted = 0;
        },
        SET_ALI_INFO(state,ossInfo){
            state.ossInfo = ossInfo;
        }
    },
    actions:{
        videoInputChange({commit},list){
            commit('SET_VIDEO_UPLOAD_LIST',list);
        },
        resetList({commit}){
            commit('RESET_LIST');
        },
        setOssInfo({commit},ossInfo){
            commit('SET_ALI_INFO',ossInfo)
        }
    }
}