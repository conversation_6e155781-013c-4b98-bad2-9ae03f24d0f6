<template>
  <div class="content_right">
    <div class="right_header">
      <div class="font" style="display: flex;" v-if="isReq">
        <el-select class="font_select" v-model="FontValue" placeholder="请选择" style="margin-left: 8px;width:128px;"
          @change="changeFontFamily">
          <el-option v-for="item in selectFontOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </div>
      <div class="font" style="margin-left:10px;text-align: left;" v-if="isReq">
        <el-input-number v-model="fontNum" @change="ChangeFontSize" :min="1" :max="250"
          label="价签字体大小"></el-input-number>
      </div>
      <div class="font" style="margin-left:10px;text-align: left;display: flex;align-items: center;" v-if="isReq">
        <div style="font-size:16px">价签旋转角度</div>
        <el-input-number v-model="fontAngle" style="margin-left:10px" @change="ChangeFontAngle" :min="0" :max="360"
          label="价签旋转角度"></el-input-number>
      </div>
      <div class="fontevent" style="display:flex;align-items: center;" v-if="isReq">
        <div v-for="item in FontEvent" style="margin-left:20px;cursor: pointer;" @click="settingFontType(item)">
          <img :src="item.event ? item.activeIcon : item.icon" alt :style="{ width: item.width }" />
        </div>
      </div>
      <div class="decimal" style="display:flex;align-items: center;margin-left: 50px;" v-if="isReq">
        开启小数点
        <el-switch v-model="openDecimal" active-color="#13ce66" inactive-color="#ff4949" style="margin-left:10px"
          @change="openDecimalevent"></el-switch>
        <div style="margin-left:20px">
          小数点后保留
          <el-input v-model="DecimalNum" style="width:50px" label="描述文字" @input="setingDecimal"></el-input>
          <span style="margin-left:10px">位置</span>
        </div>
        <el-button type="primary" class="requestBtn" @click="requestFont">确认</el-button>
        <!-- <el-button type="primary" class="requestBtn" @click="SetGroup">编组</el-button> -->
      </div>

      <div style="width:100%;display:flex;align-items: center;justify-content: start; margin-left: 50px;"
        v-if="isReq == false">
        <div style="display:flex;margin-right: 20px;align-items: center;">
          <span style="width:100px;margin-left: 20px;">link ID</span>
          <el-input v-model="LinkIDPrice"></el-input>
          <el-button type="primary" class="requestBtn1" style="margin-left:20px" @click="requestPrice">确认设置Link
            ID</el-button>
        </div>
        <div style="display:flex;align-items: center;margin-right: 20px;">
          <span style="width:100px;">H5命名:</span>
          <el-input v-model="h5_name"></el-input>
        </div>
        <el-button type="primary" class="requestBtn1" @click="openLinkId"
          v-if="(openLink == false)">显示linkId</el-button>
        <el-button type="primary" class="requestBtn1" @click="closeLinkId" v-else>隐藏linkId</el-button>
        <el-button type="primary" class="requestBtn1" @click="reload">价签设置</el-button>
        <el-button type="primary" class="requestBtn1" @click="saveTemplate"
          v-if="checkPer(['cm.cs.add'])">保存</el-button>
      </div>
      <!-- <el-button type="primary" class="saveBtn" @click="saveTemplate"
      v-if="checkPer([ 'cs:add'])">保存</el-button>-->
    </div>
    <div class="right_center" ref="right_center">
      <!-- scrolling = 'no' -->
      <!-- <div class="center_div" :style="{
        width: iframeWidth,
        height: iframeHeight
      }" :class="$store.state.direction == 'across' ? 'iframe' : 'iframe1'" ref="center_div">-->
      <iframe :src="iframeUrl" name="iframe" ref="iframe" id="iframe" frameborder="0"
        :class="$store.state.direction == 'across' ? 'iframe' : 'iframe1'" scrolling="no"></iframe>
      <!-- </div> -->
      <!-- :class="direction == 'across' ? 'iframe' : 'iframe1'" -->
      <!-- style="transform: scale(0.49);" -->
    </div>

    <div class="setting_zoom">
      <el-input-number v-model="iframeZoom" :step="25" :min="70" step-strictly
        @change="changeIframeZoom"></el-input-number>
    </div>

    <el-dialog title="文字修改" :visible.sync="dialogFormVisible">
      <el-form :model="changeFormText">
        <el-input v-model="changeFormText.text" autocomplete="off"></el-input>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="request_change_word">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 右击菜单 -->
    <ContextMenu @deleteComponent="deleteComponent" />
  </div>
</template>

<script>
import ContextMenu from "../../components/Menu/ContextMenu.vue";
import {
  set_group_cs,
  add_group_cs,
  edit_group_cs
} from "@/api/template/index";
import $ from "jquery";
export default {
  data() {
    return {
      iframeUrl: "",
      iframeDom: "",
      iframe: null,
      isresource: false,
      imgIndex: null,
      imags: null,
      className: [],
      cs_id: null,
      activeClassName: "BackgroundImg",
      requestImg: {},
      group_cs_id: "",
      iframeWidth: "",
      iframeHeight: "",
      dialogFormVisible: false,
      changeFormText: {
        text: ""
      },
      zoom: 0.5,
      price_color1: "",
      current: "",
      FontValue: "DIN-Black-Seal",
      selectFontOptions: [
        {
          value: "DIN-Black-Seal",
          label: "DIN-Black-Seal"
        },
        {
          value: "SimSun",
          label: "宋体"
        },
        {
          value: "SimHei",
          label: "黑体"
        },
        {
          value: "Microsoft YaHei",
          label: "微软雅黑"
        },
        {
          value: "Microsoft JhengHei",
          label: "微软正黑体"
        },
        {
          value: "PMingLiU",
          label: "新细明体"
        },
        {
          value: "DFKai-SB",
          label: "标楷体"
        },
        {
          value: "STKaiti",
          label: "华文楷体"
        },
        {
          value: "STFangsong",
          label: "华文仿宋"
        }
        // {
        //   value: 'boringCrispy',
        //   label: 'boringCrispy'
        // }
      ],
      fontNum: 79,
      FontEvent: [
        {
          icon: require("@/assets/icons/B.png"),
          activeIcon: require("@/assets/icons/B1.png"),
          label: "B",
          event: false,
          width: "20px"
        },
        {
          icon: require("@/assets/icons/FontColor.png"),
          activeIcon: require("@/assets/icons/FontColor1.png"),
          label: "F",
          event: false,
          width: "28px"
        },
        {
          icon: require("@/assets/icons/incline.png"),
          activeIcon: require("@/assets/icons/incline1.png"),
          label: "I",
          event: false,
          width: "25px"
        },
        {
          icon: require("@/assets/icons/Under.png"),
          activeIcon: require("@/assets/icons/Under1.png"),
          label: "U",
          event: false,
          width: "25px"
        }
      ],
      currentEvent: null,
      openDecimal: false,
      DecimalNum: 1,
      DecimalClass: "",
      isReq: true,
      LinkIDPrice: "",
      priceId: "",
      fontAngle: "",
      iframeZoom: 100,
      openLink: false,
      h5_name: ""
    };
  },
  components: {
    ContextMenu
  },
  props: {
    imgsList: {
      type: Object
    },
    fileType: {
      type: String
    },
    templateOnePrice: {
      type: Object
    },
    priceTagType: {
      type: String
    },
    templateType: {
      type: Number
    },
    tpl_id: {
      type: String
    },
    // iframeUrl: {
    //   type: String
    // },
    direction: {
      type: String
    },
    price_color: {
      type: String
    },
    assist: {
      type: String
    },
    priceText: {
      type: [Number, String]
    }
  },

  watch: {
    imgsList(newValue, oldValue) {
      console.log("newValue", newValue, "oldValue", oldValue);
      let dom = null;
      // this.className = Array.from(new Set(this.className))
      this.$set(this.requestImg, this.activeClassName, { 'file_id': newValue.file_id, 'file_identify': newValue.file_identify });
      console.log(this.activeClassName, this.fileType);
      if (this.activeClassName == "VideoPart") {
        if (this.fileType == "photo") {
          this.$message.warning("当前是视频模板 请选择视频");
        } else if (this.fileType == "video") {
          this.className.forEach((item, index) => {
            if (item === this.activeClassName) {
              dom = this.activeClassName;
              this.$refs.iframe.contentWindow.setImgs(dom);
            }
          });
        }
      } else {
        if (this.fileType == "video") {
          this.$message.warning("当前是图片模板 请选择图片");
        } else {
          dom = this.activeClassName;
          console.log(dom, "dom");
          this.$refs.iframe.contentWindow.setImgs(dom);
          // this.className.forEach((item, index) => {
          //   if (item === this.activeClassName) {
          //     dom = this.activeClassName;
          //     this.$refs.iframe.contentWindow.setImgs(dom);
          //   }
          // })
        }
      }
    },
    fileType(newValue, oldValue) { },
    templateOnePrice(newValue, oldValue) {
      this.$refs.iframe.contentWindow.SetPrice();
    },
    priceTagType(newValue, oldValue) { },
    templateType(newValue, oldValue) { },
    tpl_id(newValue, oldValue) { },
    iframeUrl(newValue, oldValue) { },
    price_color(newValue, oldValue) {
      this.price_color1 = newValue;
      this.$refs.iframe.contentWindow.getPriceTagsColor();
    },
    price_size(newValue, oldValue) {
      this.$refs.iframe.contentWindow.getPriceFontSize();
    },

    // 辅助内容
    assist(newValue, oldValue) {
      if (this.current == "current") {
        this.$refs.iframe.contentWindow.setSymbol();
      }
    },
    priceText() {
      if (this.current == "current_price") {
        this.$refs.iframe.contentWindow.setPriceText();
      }
    }
  },
  computed: {
    /**
     * 返回vuex数据 模板排列方式
     */
    getStateDirection() {
      return this.$store.state.direction;
    }
  },
  created() {
    sessionStorage.removeItem("html_url");
  },

  methods: {
    toResource(imgIndex) {
      this.$emit("toResource", imgIndex);
    },
    changeFontFamily(FontValue) {
      this.FontValue = FontValue;
      this.$refs.iframe.contentWindow.getPriceFontFamily();
    },
    ChangeFontSize(fontSize) {
      this.fontNum = fontSize;
      this.$refs.iframe.contentWindow.getPriceFontSize();
    },
    ChangeFontAngle(fontAngle) {
      this.fontAngle = fontAngle;
      this.$refs.iframe.contentWindow.getPriceFontAngle();
    },

    settingFontType(item) {
      item.event = !item.event;
      if (item.label == "F") {
        if (item.event) {
          this.$parent.$refs.ContentPriceTag.isColorShow = "F";
        } else {
          this.$parent.$refs.ContentPriceTag.isColorShow = true;
        }
      } else {
        this.currentEvent = item;
        this.$parent.$refs.ContentPriceTag.isColorShow = true;
        this.$refs.iframe.contentWindow.settingPriceType();
      }
    },

    openDecimalevent() {
      if (this.DecimalClass != "") {
        this.$refs.iframe.contentWindow.settingDecimal();
      } else {
        this.openDecimal = false;
        this.$message.warning("请选择价签");
      }
    },
    setingDecimal() {
      if (this.openDecimal == true) {
        this.$refs.iframe.contentWindow.settingDecimal();
      }
    },

    requestFont() {
      this.isReq = false;
      this.$parent.$refs.ContentPriceTag.isColorShow = false;
    },
    reload() {
      this.isReq = true;
      this.$parent.$refs.ContentPriceTag.isColorShow = true;
    },
    openLinkId() {
      this.$refs.iframe.contentWindow.openLinkId();
      this.openLink = true;
    },

    request_change_word() {
      this.dialogFormVisible = false;
      console.log(this.changeFormText.text);
      this.$refs.iframe.contentWindow.get_change_word(this.changeFormText.text);
    },

    closeLinkId() {
      this.$refs.iframe.contentWindow.closeLinkId();
      this.openLink = false;
    },
    getPrice(link_id) {
      return link_id;
    },

    SetGroup() {
      this.$refs.iframe.contentWindow.SetGroup();
    },

    requestPrice() {
      this.priceId = this.getPrice(this.LinkIDPrice);
      if (this.priceId == undefined) {
        this.priceId = "";
      }
      this.$refs.iframe.contentWindow.settingLInkId();
    },

    saveTemplate() {
      const paramsNew = {
        shop_group_id: localStorage.getItem("group_id"),
        act_type: "n2n_scence",
        cs_list: {
          tpl_id: this.tpl_id
        }
      };

      var savePrice = this.$refs.iframe.contentWindow.savePrice();
      let params = {
        group_cs_id: "",
        file_identify: "",
        html_str: savePrice
      };
      let reObject = {};
      console.log(this.requestImg, 'this.requestImg');
      for (const key in this.requestImg) {
        if (Object.hasOwnProperty.call(this.requestImg, key)) {
          const element = this.requestImg[key];
          if (key == "VideoPart") {
            console.log(this.requestImg, 'this.requestImg');
            params.file_identify = this.requestImg[key]['file_identify'];
          } else {
            // delete params.file_identify;
            reObject[key] = element.file_id;
          }
          console.log(reObject, 'reObject');
          for (const keyChild in reObject) {
            if (Object.hasOwnProperty.call(reObject, keyChild)) {
              let length = Object.keys(reObject).length;
              if (length == 1) {
                if (params.file_identify != "") {
                  params.ref_photo_info = {
                    [keyChild]: reObject[keyChild]
                  };
                } else {
                  params.ref_photo_key = reObject[keyChild];
                }
              } else {
                params.ref_photo_info = reObject;

                delete params.ref_photo_key;
              }
            }
          }
        }
      }
      console.log(params, "params");

      params["group_cs_id"] = this.group_cs_id;
      // params.ref_photo_info = params.ref_photo_info.filter(item => item);
      delete params.ref_photo_info[""];
      // params.ref_photo_info = {
      //   BackgroundImg: params.ref_photo_info.BackgroundImg
      // };
      // params.ref_photo_info = JSON.stringify(params.ref_photo_info)
      if (this.changeFormText.text) {
        let key = this.changeFormText.className;
        params.template_vars = {
          // key: this.changeFormText.text,
          WordLine1: this.changeFormText.text
        };
        // `${this.changeFormText.className}`:this.changeFormText.text
      }
      params.h5_name = this.h5_name;
      console.log(params, "params");
      edit_group_cs(params).then(res => {
        if (res.rst == "ok") {
          this.$refs.iframe.contentWindow.closeLinkId();
          this.$message.success("保存成功");
          this.$router.push("/contentCenter/srcfiles");
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },

    changeIframeZoom(e) {
      console.log(e, "ex");
      // console.log(this.$refs.center_div.style, 'xxxx');
      // this.$refs.center_div.style['transform'] = `scale(${this.iframeZoom / 100})`
      // console.log(this.direction, '');
      // console.log(this.$refs.right_center.style['width']);
      // if (this.direction == 'across') {
      //   console.log(1920 * 0.7 * (this.iframeZoom / 100), '1920 * 0.7 * (this.iframeZoom / 100)');
      //   // this.$refs.center_div.style['width'] = (1920 * 0.7 * (this.iframeZoom / 100)) + 'px'
      //   // this.$refs.center_div.style['height'] = (1080 * 0.7 * (this.iframeZoom / 100)) + 'px'
      //   this.$refs.iframe.style['width'] = (1920 * 0.7 * (this.iframeZoom / 100)) + 'px'
      //   this.$refs.iframe.style['height'] = (1080 * 0.7 * (this.iframeZoom / 100)) + 'px'
      // } else {
      //   // this.$refs.center_div.style['width'] = (1080 * 0.4 * (this.iframeZoom / 100)) + 'px'
      //   // this.$refs.center_div.style['height'] = (1920 * 0.4 * (this.iframeZoom / 100)) + 'px'
      //   this.$refs.iframe.style['width'] = (1080 * 0.4 * (this.iframeZoom / 100)) + 'px'
      //   this.$refs.iframe.style['height'] = (1920 * 0.4 * (this.iframeZoom / 100)) + 'px'
      // }
      this.$refs.iframe.style["zoom"] = this.iframeZoom / 100;
      this.$refs.iframe.style["zoom"] = this.iframeZoom / 100;

      console.log(this.iframeZoom, "iframeZoom");
      // console.log(this.$refs.iframe.contentWindow)
      this.$refs.iframe.contentWindow.changeIframeZoomF();
    }
  },

  mounted() {
    var that = this;

    setTimeout(() => {
      const paramsNew = {
        shop_group_id: localStorage.getItem("group_id"),
        act_type: "n2n_scence",
        cs_list: {
          tpl_id: this.tpl_id
        }
      };
      add_group_cs(paramsNew).then(res => {
        if (res.rst == "ok") {
          console.log(res, "res");
          this.iframeUrl = res["data"][0]["static_url"];
          this.group_cs_id = res["data"][0]["group_cs_id"];
        }
      });
    }, 1000);

    this.iframe = document.getElementById("iframe");
    this.iframe.onload = function () {
      // iframe加载完成后要进行的操作
    };
    window.onresize = () => {
      // console.log(this.$store.state.d);
      return (() => {
        // console.log(this.$store.state.direction);
        // if (this.$store.state.direction == 'across') {
        //   this.zoom = $('#app').width() / 1920 / 1.5
        //   this.iframeWidth = 1920 + "px";
        //   this.iframeHeight = 1080 + "px";
        // } else {
        //   this.zoom = $('#app').width() / 1080 / 1.5
        //   this.iframeWidth = 1080 + "px";
        //   this.iframeHeight = 1920 + "px";
        // }
        // console.log(this.$refs.iframe.style['transform'] = `scale(${this.zoom})`);
      })();
    };

    // 监听子页面想父页面的传参
    window.addEventListener("message", function (event) {
      //此处执行事件
      if (event.data.className != undefined) {
        that.toResource(1);
        that.className.push(event.data.className);
        that.cs_id = event.data.cs_id;
        that.activeClassName = event.data.className;

        if (event.data.className.indexOf("WordLine") != -1) {
          that.dialogFormVisible = true;
          that.changeFormText.text = event.data.word_text;
          that.changeFormText.className = event.data.className;
        }
      } else {
      }

      if (event.data.type == "current") {
        that.current = event.data.type;
        that.fontNum = Number(event.data.fontSize.split("px")[0]);
        if (event.data.fontWeight != "700") {
          that.FontEvent[0].event = false;
        } else {
          that.FontEvent[0].event = true;
        }
        if (event.data.fontStyle != "italic") {
          that.FontEvent[2].event = false;
        } else {
          that.FontEvent[2].event = true;
        }
        if (event.data.textDecoration != "underline solid rgb(0, 0, 0)") {
          that.FontEvent[3].event = false;
        } else {
          that.FontEvent[3].event = true;
        }

        if (event.data.className) {
          that.DecimalClass = event.data.className;
        } else {
          that.DecimalClass = "";
        }
      } else if (event.data.type == "current_price") {
        that.current = event.data.type;
        that.fontNum = Number(event.data.fontSize.split("px")[0]);
        if (event.data.fontWeight != "700") {
          that.FontEvent[0].event = false;
        } else {
          that.FontEvent[0].event = true;
        }
        if (event.data.fontStyle != "italic") {
          that.FontEvent[2].event = false;
        } else {
          that.FontEvent[2].event = true;
        }
        if (event.data.textDecoration != "underline solid rgb(0, 0, 0)") {
          that.FontEvent[3].event = false;
        } else {
          that.FontEvent[3].event = true;
        }

        if (event.data.open) {
          if (event.data.open == "none") {
            that.openDecimal = false;
          } else {
            that.openDecimal = true;
          }
        } else {
          that.openDecimal = false;
        }
        if (event.data.openlength) {
          if (event.data.openlength >= 3) {
            that.DecimalNum = 1;
          } else {
            that.DecimalNum = event.data.openlength;
            if (that.DecimalNum < 0) {
              that.DecimalNum = 1;
            }
          }
        }
        if (event.data.className) {
          that.DecimalClass = event.data.className;
        } else {
          that.DecimalClass = "";
        }
      } else {
        that.current = event.data.type;
        // that.fontNum = Number(event.data.fontSize.split("px")[0]);
        if (event.data.fontWeight != "700") {
          that.FontEvent[0].event = false;
        } else {
          that.FontEvent[0].event = true;
        }
        if (event.data.fontStyle != "italic") {
          that.FontEvent[2].event = false;
        } else {
          that.FontEvent[2].event = true;
        }
        if (event.data.textDecoration != "underline solid rgb(0, 0, 0)") {
          that.FontEvent[3].event = false;
        } else {
          that.FontEvent[3].event = true;
        }

        if (event.data.className) {
          that.DecimalClass = event.data.className;
        } else {
          that.DecimalClass = "";
        }
      }

      if (event.data.linkId) {
        that.LinkIDPrice = event.data.linkId;
      } else {
        that.LinkIDPrice = "";
      }
    });

    window.SetBackgroundImage = function () {
      if (that.fileType == "photo") {
        return that.imgsList.original_url;
      } else {
        return that.imgsList.thumb;
      }
    };

    window.setPriceType = function () {
      return that.priceTagType;
    };

    // window.getprice = function (link_id) {
    //   var a = { "100": "20", "101": "30" };
    //   return a[link_id];
    // }

    window.PriceTagsColor = function () {
      return that.price_color1;
    };

    window.priceSizeNum = function () {
      return that.fontNum;
    };

    window.priceAngle = function () {
      return that.fontAngle;
    };

    window.priceFontFamily = function () {
      return that.FontValue;
    };

    window.showPriceStyle = function () {
      that.$parent.$refs.ContentPriceTag.isColorFontShow = true;
    };

    window.setSymbolStyle = function () {
      return that.assist;
    };

    window.setPriceTextParent = function () {
      return that.priceText;
    };

    window.settingPriceX = function () {
      return that.currentEvent;
    };

    window.settingDecimalx = function () {
      return {
        open: that.openDecimal,
        DecimalNum: that.DecimalNum,
        className: that.DecimalClass,
        type: that.current
      };
    };

    window.settingLinkData = function () {
      // if (that.current == 'classPrice') {
      return {
        priceId: that.priceId,
        current: that.current
      };
      // }
    };

    window.changeZoom = function () {
      return that.iframeZoom;
    };

    // window.getType = function () {
    //   return "新增"
    // }
  },

  // 自定义事件
  //自定义指令
  directives: {
    drag: {
      // 指令的定义q
      bind: function (el, binding, vnode) {
        let oDiv = el; // 获取当前元素
        let firstTime = "",
          lastTime = "";
        oDiv.onmousedown = e => {
          // 算出鼠标相对元素的位置
          oDiv.setAttribute("flag", false);
          firstTime = new Date().getTime();
          let dom;
          let disX = e.clientX - oDiv.offsetLeft;
          let disY = e.clientY - oDiv.offsetTop;
          document.onmousemove = e => {
            // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
            let left = e.clientX - disX;
            let top = e.clientY - disY;

            oDiv.style.left = left + "px";
            oDiv.style.top = top + "px";
          };

          document.onmouseup = e => {
            document.onmousemove = null;
            document.onmouseup = null;
            // onmouseup 时的时间，并计算差值
            lastTime = new Date().getTime();
            if (lastTime - firstTime < 200) {
              oDiv.setAttribute("flag", true);
            }
          };
        };
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.content_right {
  width: 100%;
  height: 100%;
  background-color: #eeeeee;

  .right_header {
    // width: 100%;
    width: 100%;
    height: 62px;
    background-color: #fff;
    display: flex;
    align-items: center;

    .saveBtn {
      margin-right: 14px;
      letter-spacing: 2px;
      color: rgba(255, 255, 255, 1);
      background-color: rgba(39, 177, 126,1);
      border: 0;
      text-align: center;
    }
  }

  .right_center {
    // width: 1920px;
    // height: 1080px;
    width: calc(100% - 390px);
    // width: 1400px;
    // height: 760px;
    // height: 100%;
    height: calc(100% - 65px);
    // display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    // padding: .5rem;
    // position: relative;
    overflow: scroll;

    iframe {
      // width: 100%;
      // height: 100%;

      ::v-deep div {
        width: 100% !important;
        height: 100% !important;
      }

      ::v-deep ol {
        width: 100% !important;
        height: 100% !important;
      }

      ::v-deep li {
        width: 100% !important;
        height: 100% !important;
      }

      // margin-left: -2rem;
      // position: absolute;
    }
  }
}

.active_border {
  border: 1px solid var(--text-color-light);;
}

::v-deep .el-input--small .el-input__inner {
  // height: 40px !important;
}

.iframe {
  width: 1920px;
  height: 1080px;
  transform: scale(0.7);
  // margin: -1rem 0 0 -2rem;
  position: relative;
  transform-origin: 10px 10px;

  iframe {
    position: absolute;
    // left: 45%;
    // top: 20%;
    left: 0;
    top: 0;
  }
}

.iframe1 {
  width: 1080px !important;
  height: 1920px !important;
  left: 5% !important;
  transform: scale(0.4) !important;
  // margin: -4rem 0 0;
  position: relative;
  transform-origin: 10px 10px;

  iframe {
    position: absolute;
    // left: 45%;
    // top: 20%;
    left: 0;
    top: 0;
  }
}

::v-deep .font_select .el-input__inner {
  font-size: 17px !important;
}

::v-deep .decimal {
  .el-input__inner {
    text-align: center !important;
    margin: 0 0 0 10px;
  }
}

.requestBtn {
  margin-right: 14px;
  letter-spacing: 2px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(39, 177, 126,1);
  border: 0;
  text-align: center;
  height: 40px;
  position: absolute;
  right: 0;
}

.requestBtn1 {
  margin-right: 14px;
  letter-spacing: 2px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(39, 177, 126,1);
  border: 0;
  text-align: center;
  height: 40px;
}

.setting_zoom {
  position: fixed;
  bottom: 10px;
  right: 10px;
}

.right_center::-webkit-scrollbar {
  width: 10px;
  /* display: none; */
  height: 10px;
}

::v-deep .content_right ::-webkit-scrollbar {
  width: 10px;
  /* display: none; */
  display: none !important;
  height: 10px;
}
</style>