<template>
    <div class="template_resources" @keydown.esc="closeMask" v-loading="loading">
        <div class="content">
            <!-- 搜索区 -->
            <div class="content_header flex">
                <!-- 左侧筛选条件 -->
                <div class="content_search flex flex-1">
                    <el-select v-model="queryList.desc" clearable size="small" style="width:151px;margin-right:7px">
                        <el-option v-for="item in template_tags" :key="item.key" :label="item.name"
                            :value="item.key"></el-option>
                    </el-select>

                    <el-select v-model="v_or_h" size="small" disabled style="width:151px;margin-right:7px">
                        <el-option label="横" value="0"></el-option>
                        <el-option label="竖" value="1"></el-option>
                    </el-select>
                    <el-input v-model="queryList.name" clearble size="small" placeholder="请输入模板名称"
                        style="width:151px;margin-right:7px"></el-input>
                    <div class="search_btn" @click="handleSearch">
                        <span>搜索</span>
                    </div>
                </div>
                <!-- 右侧切换格子/列表 -->
                <div class="content_tabs flex">
                    <div class="tabs_btn" @click="contentShow = 0" :class="contentShow == 0 ? 'tabs_checked' : ''">
                        <span>格子</span>
                    </div>
                    <div class="tabs_btn" style="margin-left:-1px" @click="contentShow = 1"
                        :class="contentShow == 1 ? 'tabs_checked' : ''">
                        <span>列表</span>
                    </div>
                </div>
            </div>
            <!-- 内容区 -->
            <div class="h5_content flex-1">
                <!-- 格子 -->
                <div class="lattice flex" v-show="contentShow == 0">
                    <div class="lattice_content flex flex-1">
                        <div class="lattice_item" v-for="item in dataList" :key="item">
                            <div class="item_image">
                                <img :src="item.thumb_url" style="width: 100%;height:100%;object-fit:contain" alt />
                                <div class="edit_mask flex">
                                    <div class="mask_btn" @click="handleEdit(item)"
                                        v-if="isContent == false && checkPer(['cm.cs.edit'])">
                                        <p>
                                            <i class="el-icon-edit"></i>
                                        </p>
                                        <p>编辑</p>
                                    </div>
                                    <div class="mask_btn" style @click="handleShow(item)">
                                        <p>
                                            <i class="el-icon-view"></i>
                                        </p>
                                        <p>预览</p>
                                    </div>
                                    <!-- <div class="mask_btn" @click="handleDelete(item)"
                                        v-if="isContent == false && checkPer(['cm.cs.del'])">
                                        <p><i class=" el-icon-delete-solid"></i></p>
                                        <p>删除</p>
                                    </div> -->
                                </div>
                            </div>
                            <div class="item_size" :title="item.name" style="display: flex;align-items: center;"> 名称:
                                <span
                                    style="width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                                    {{ item.name }} </span>
                                <img src="../../assets/icons/edit.png" style="width:20px;cursor: pointer;" alt="">
                            </div>
                            <div class="up_date flex" style>
                                <div class="flex-1 txt_ellipsis">上传日期：{{ item.last_modified_human }}</div>
                            </div>
                            <!-- <div class="item_size">尺寸：{{ item.n_tpl_size }}</div> -->
                            <div class="item_tags flex">
                                <div class="each_tags flex" v-for="val in item.tags" :key="val">
                                    <img src="../../assets/img/tags.png" style="width:24px;height:24px" />
                                    <span style="display: inline-block;height: 24px;line-height: 24px;">{{ val }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 列表 -->
                <div class="lists" v-show="contentShow == 1">
                    <el-table :data="dataList" height="100%" ref="table_list" @selection-change="handleSelectionChange"
                        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px' }">
                        <el-table-column width="50" align="center">
                            <template slot="header">
                                <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox>
                            </template>
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.checked" @change="checkOne"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="img_url" label="图片预览" width="110" align="center">
                            <template slot-scope="scope">
                                <img :src="scope.row.thumb_url" style="width: 73px;height:73px;object-fit:contain"
                                    alt />
                            </template>
                        </el-table-column>
                        <el-table-column prop="n_tpl_size" label="尺寸" width show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="size" label="大小" width show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="tags" label="标签" width show-overflow-tooltip="true" align="center">
                            <template slot-scope="scope">
                                <div class="flex">
                                    <div v-for="(item, index) in scope.row.tags" :key="index"
                                        class="flex align-items-center" style="margin-right:2px">
                                        <img src="../../assets/img/tags.png" style="width:24px;height:24px;" alt />
                                        <span>{{ item }}</span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="last_modified_human" label="上传日期" width show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="account" label="账户" width show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column fixed="right" label="操作" width="130" align="center">
                            <template slot-scope="scope">
                                <el-button @click.native.prevent="handleShow(scope.row)" type="text"
                                    style="color:#409eff" size="small">预览</el-button>
                                <el-button @click.native.prevent="handleEdit(scope.row)" type="text"
                                    style="color:#409eff" size="small"
                                    v-if="isContent == false && checkPer(['cm.cs.edit'])">编辑
                                </el-button>
                                <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:red"
                                    size="small" v-if="isContent == false && checkPer(['cm.cs.del'])">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <!-- 底部操作以及页码 -->
        <div class="footer flex" style="justify-content: right;">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum"></el-pagination>
        </div>
    </div>
</template>

<script>
import { get_gongfang_h5cs_list, delete_gongfang_h5cs_list } from "@/api/files/h5Resources";
import { get_template_cs_tags, search_tpl_list } from "@/api/template";
export default {
    components: {},
    props: {
        v_or_h: {
            type: String,
            default: "0"
        }
    },
    watch: {
    },
    data() {
        return {
            activeName: "relation",
            contentShow: 0, //显示的是列表还是格子
            currentPage: 1, //页码
            pageSize: 10,
            totalNum: null, //总数据数量
            colorList: [
                { color: "rgba(248, 231, 28, 1)", checked: false, label: "黄" },
                { color: "rgba(154, 220, 83, 1)", checked: false, label: "绿" },
                { color: "rgba(42, 130, 228, 1)", checked: false, label: "蓝" },
                { color: "rgba(121, 212, 191, 1)", checked: false, label: "青" },
                { color: "rgba(245, 112, 129, 1)", checked: false, label: "粉" },
                { color: "rgba(222, 181, 230, 1)", checked: false, label: "紫" }
            ],
            queryList: {
                v_or_h: "0",
                offset: 0,
                limit: 10,
                name: '',
                desc: ''
            },
            screenSizeList: [
                { value: "1920*1080", label: "横屏1920*1080" },
                { value: "1080*1920", label: "竖屏1080*1920" },
                { value: "qita", label: "其他尺寸" }
            ],
            marketList: [
                { value: "quanguo", label: "全国市场" },
                { value: "shanghai", label: "上海市场" },
                { value: "beijing", label: "北京市场" },
                { value: "chengdu", label: "成都市场" }
            ],
            isAllChecked: false,
            dataList: [],
            tagsList: [
                { active: false, label: "24小时" },
                { active: false, label: "清真餐厅" },
                { active: false, label: "社区店" },
                { active: false, label: "甜品站" }
            ],
            checkedList: [],
            autoHeight: {
                //列表区高度
                height: "",
                heightNum: ""
            },
            batchState: false,
            isContent: false,
            isDMB: false,
            html_url: "",
            loading: true,
            v_h: '',
            template_tags: []
        };
    },
    computed: {},
    watch: {},
    created() {
        window.addEventListener("resize", this.getHeight);
        this.getHeight();
        this.getDataList();
    },
    mounted() {
        get_template_cs_tags({}).then(res => {
            console.log(res, 'template_cs');
            this.template_tags = res['data'][0]['tags_info']
        })
    },
    methods: {
        // 搜索
        handleSearch() {
            // console.log(this.queryList);
            this.loading = true;
            this.getDataList();
        },
        // 获取列表数据
        getDataList() {
            this.queryList.v_or_h = this.v_or_h
            const parmas = this.queryList;
            parmas.offset = (this.currentPage - 1) * this.pageSize;
            search_tpl_list(parmas).then(res => {
                console.log(res, 'res');
                if (res.rst == "ok") {
                    this.dataList = res["data"][0]["data"];
                    this.dataList.forEach(item => {
                        this.$set(item, "checked", false);
                    });
                    this.loading = false;
                    this.totalNum = res["data"][0]["cnt"];
                }
            });
            // this.dataList = list;
            // this.loading = false;
        },
        handleSelectionChange(val) {
            // console.log(val);
        },
        // 全选
        checkAll() {
            this.dataList.forEach(item => {
                item.checked = this.isAllChecked;
            });
            if (this.isAllChecked) {
                this.checkedList = JSON.parse(JSON.stringify(this.dataList));
            } else {
                this.checkedList = [];
            }
        },
        //单选
        checkOne() {
            // this.checkedList = this.dataList.filter(item=>item.checked)
            // if(this.checkedList.length == this.dataList.length){
            //     this.isAllChecked = true;
            // }else{
            //     this.isAllChecked = false;
            // }
            if (this.isContent == false) {
                // no 联屏
                this.checkedList = this.dataList.filter(item => item.checked);
                if (this.checkedList.length == this.dataList.length) {
                    this.isAllChecked = true;
                } else {
                    this.isAllChecked = false;
                }
                // add by Ethan
                this.$emit("checkedTemplate", this.checkedList);
            } else if (this.isDMB == true) {
                this.checkedList = this.dataList.filter(item => item.checked);
                if (this.checkedList.length == this.dataList.length) {
                    this.isAllChecked = true;
                } else {
                    this.isAllChecked = false;
                }
                // add by Ethan
                this.$emit("checkedTemplate", this.checkedList);
            } else {
                // data.checked = true;
                this.dataList.forEach(item => {
                    item.checked = false;
                });
                this.$set(data, "checked", true);
                this.isAllChecked = false;
                this.$emit("checkedTemplate", [data]);
            }
        },
        // 预览
        handleShow(val) {
            console.log(val, 'val');
            // this.PreviewMaskSrc = val.dynamic_url;
            // sessionStorage.setItem("html_url", val.html_str);
            // // console.log(val,'val');
            // if (val.tpl_v_or_h == 'vertical') {
            //   this.v_h = 1;
            // } else {
            //   this.v_h = 0;
            // }
            // // console.log(this.v_h,'this.v_h');
            // this.$emit("setH5Preview", val.dynamic_url, val.html_str, this.v_h);

            this.PreviewMaskSrc = val.thumb_url;
            this.$emit('setImgPreview', val.thumb_url)


        },
        // 编辑
        handleEdit(val) {
            // this.$message.success('编辑')
            if (this.queryList.v_h == 0) {
                this.$store.commit("changeDirection", 'across')
            } else {
                this.$store.commit("changeDirection", 'vertical')
            }
            this.$router.push({
                path: "/contentCenter",
                query: {
                    title: "编辑",
                    templateUrl: val.dynamic_url,
                    // pricehtml: val.html_str,
                    cs_id: val.cs_id,
                    tpl_v_or_h: val.tpl_v_or_h,
                    tpl_id: val.tpl_id,
                    cs_id: val.cs_id,
                    h5_name: val.h5_name
                }
            });
            // console.log("编辑", val);
        },
        // 删除
        handleDelete(val) {
            this.$confirm("是否删除该内容?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    let params = {
                        group_cs_id: val.cs_id
                    }
                    delete_gongfang_h5cs_list(params).then(res => {
                        if (res.rst == 'ok') {
                            this.$message.success("删除成功");
                            this.getDataList()
                        } else {
                            this.$message.error(res.error_msg)
                        }
                    })
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除"
                    });
                });
        },

        // 批量删除
        batchDelete() {
            if (this.checkedList.length == 0) {
                this.$message.warning("请先选择需要删除的内容");
                return;
            }
            this.$confirm("是否删除已选内容?", "", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.$message.success("删除成功");
                    // console.log(this.checkedList);
                })
                .catch(() => {
                    this.$message.info("已取消删除");
                });
        },
        //页码改变
        handleCurrentChange(val) {
            // console.log(`现在是第${val}页`);
            this.queryList.offset = val - 1;
            this.loading = true;
            this.getDataList()
            // this.currentPage = val;
        },
        handleSizeChange(val) {
            // console.log(`每页${val}条`);
            this.queryList.limit = val;
            this.loading = true;
            this.getDataList()
            // this.pageSize = val;
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 162 + "px";
            this.autoHeight.heightNum = windowHeight - 162;
        }
    },
    destroyed() {
        window.removeEventListener("resize", this.getHeight);
        window.removeEventListener("keyup", this.closeScreenFull);
    }
};
</script>

<style scoped>
* {
    box-sizing: border-box;
}

.template_resources {
    width: 100%;
    padding: 0 14px;
    background: #eeeeee;
}

.content {
    /* border: 1px solid red; */
    display: flex;
    flex-direction: column;
}

.content_header {
    height: 67px;
}

.content_search {
    align-items: center;
}

.content_tabs {
    align-items: center;
}

.tabs_btn {
    border: 1px solid var(--btn-background-color);
    font-size: 14px;
    padding: 6px 15px;
    cursor: pointer;
    color: var(--text-color-light);
    background: #fff;
    white-space: nowrap;
}

.tabs_btn:hover {
    border: 1px solid var(--btn-background-color);
}

.tabs_checked {
    color: #fff;
    background: var(--btn-background-color)
}

.h5_content {
    width: 100%;
    /* height: calc(100% - 67px); */
    /* overflow-y:auto ; */
    /* border: 1px solid pink; */
}

.color_disc {
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 50%;
    cursor: pointer;
    margin-right: 10px;
}

.color_disc_active {
    /* background: black !important; */
}

/* 格子 */
.lattice {
    /* height: 100%; */
    flex-direction: column;
}

.lattice_header {
    width: 100%;
    height: 42px;
    line-height: 42px;
    background-color: var(--text-color-light);
    padding-left: 10px;
    font-size: 14px;
    color: #fff;
}

.lattice_content {
    flex-wrap: wrap;
    overflow-y: auto;
}

.lattice_item {
    width: 240px;
    height: 350px;
    margin-right: 16px;
    margin-top: 15px;
    font-size: 13px;
    color: rgba(56, 56, 56, 1);
    background-color: #fff;
}

.item_image {
    position: relative;
    width: 100%;
    height: 240px;
    padding: 3px;
}

.item_image:hover .edit_mask {
    display: flex;
}

.edit_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 240px;
    background-color: rgba(0, 0, 0, 0.335);
    color: #fff;
    justify-content: center;
    align-items: center;
}

.mask_btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.564);
    border-radius: 50%;
    font-size: 11px;
    margin: 0 10px;
}

.mask_btn:hover {
    background-color: rgba(0, 0, 0, 0.464);
}

.each_tags {
    align-items: center;
    margin-right: 11px;
}

.up_date,
.item_size,
.item_tags {
    margin-top: 10px;
    align-items: center;
    padding: 0 9px;
}

.item_tags {
    margin-top: 13px;
    padding: 0 4px;
}

/* 列表 */
.lists {
    width: 100%;
    height: 100%;
}

.footer {
    height: 72px;
    align-items: center;
    justify-content: space-between;
}

.txt_ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 弹框 */
.relation_tags {
    display: flex;
    flex-wrap: wrap;
    height: 279px;
    margin-bottom: 20px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    font-size: 14px;
    border-radius: 24px;
    margin-right: 24px;
    margin-bottom: 18px;
    padding: 0 13px;
    border: 1px solid rgba(209, 209, 209, 1);
    cursor: pointer;
    /* 禁止文字选中 */
    /* -moz-user-select:none;
          -webkit-user-select:none;
          -ms-user-select:none;
          -khtml-user-select:none;
          user-select:none; */
}

.tag_active {
    color: rgba(108, 178, 255, 1);
    background-color: rgba(212, 232, 255, 1);
    border: 1px solid rgba(108, 178, 255, 1);
}

.search_btn {
    height: 30px;
    top: 99px;
    color: #fff;
    background-color: var(--text-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    margin-top: -1px;
    padding: 0 20px;
    margin-left: 17px;
    white-space: nowrap;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.template_resources .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: var(--base-color) !important;
    border-color: var(--base-color) !important;
}

.template_resources .el-checkbox__inner {
    /* border-color:red !important; */
    width: 18px;
    height: 18px;
    border-radius: 50%;
}

.template_resources .el-checkbox__inner::after {
    left: 6px !important;
    top: 3px !important;
}

.template_resources .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    left: 0px !important;
    top: 7px !important;
}

.template_resources .el-dialog__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.batch_tags_dialog {
    margin-top: 0px !important;
    border-radius: 16px !important;
}

.batch_tags_dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
    margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 1px !important;
}

.batch_tags_dialog .el-tabs__item {
    height: 50px !important;
    line-height: 50px !important;
}

.batch_tags_dialog .el-button--primary {
    background: rgba(108, 178, 255, 1);
}

.template_resources .el-tabs__header {
    margin-bottom: 0 !important;
    width: 100% !important;
}

.template_resources .el-tabs__nav-scroll {
    padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .template_resources .is-active {
    color: var(--text-color) !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .template_resources .el-tabs__nav {
    height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 0px !important;
}
</style>