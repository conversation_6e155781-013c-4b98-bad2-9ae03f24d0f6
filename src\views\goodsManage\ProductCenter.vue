<template>
    <div class="product-center">
        <!-- <div class="center_title">
            商品概况
        </div>
        <div class="show_panel">
           <div class="chart_wrap">
                <div class="chart_title">商品状态</div>
                <div ref="chart" class="chart"></div> 
           </div>
           <div class="panel_info">
                <div class="info_item_wrap">
                    <div class="info_item">
                        <span>商品总数</span>
                        <span>{{total}}</span>
                        <span>个</span>
                    </div>
                    <div class="info_item">
                        <span>在售</span>
                        <span>0</span>
                        <span>个</span>
                    </div>
                    <div class="info_item">
                        <span>售罄</span>
                        <span>0</span>
                        <span>个</span>
                    </div>
                </div>
           </div>
        </div> -->
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="MOP渠道" name="MOP">
                <div class="goods_info_wrap" v-show="activeName == 'MOP'">
                    <div class="goods_info_top">
                        <div class="center_title">
                            商品资料
                        </div>
                        <div class="info_btns">
                            <el-button type="primary" class="dark-btn" style="margin-left: auto;"
                                @click="openDrawer('add')">新增商品信息</el-button>
                            <!-- <el-button type="primary" class="dark-btn"  @click="dialogShow = true">手动导入商品信息</el-button> -->
                        </div>
                    </div>
                    <div class="search_bar">
                        <div class="search_item">
                            <el-input v-model="searchInfo.blurry" style="width:220px" placeholder="请输入商品名称/编码"
                                clearable></el-input>
                        </div>
                        <div class="search_item">
                            <el-select v-model="searchInfo.soldout_status" style="width:220px" placeholder="请选择售罄状态"
                                clearable>
                                <el-option v-for="item in soldouStatusOptions" :key="'sold_out_' + item[0]"
                                    :label="item[1]" :value="item[0]"></el-option>
                            </el-select>
                        </div>
                        <div class="search_item">
                            <el-select v-model="searchInfo.active_status" style="width:220px" placeholder="请选择商品状态"
                                clearable>
                                <el-option v-for="item in activeStatusOptions" :key="'active_status_' + item[0]"
                                    :label="item[1]" :value="item[0]"></el-option>
                            </el-select>
                        </div>
                        <div class="search_item">
                            <el-select v-model="searchInfo.cate_type" style="width:220px" placeholder="请选择商品分类"
                                clearable>
                                <el-option v-for="item in CateTypeOptions" :key="'cate_type_' + item[0]"
                                    :label="item[1]" :value="item[0]"></el-option>
                            </el-select>
                        </div>
                        <div class="search_item">
                            <el-button type="primary" @click="search">搜索</el-button>
                            <el-button @click="reset">重置</el-button>
                        </div>
                    </div>
                    <div class="info_item_panel">
                        <el-table :data="tableData" height="100%" border v-loading="loading"
                            header-row-class-name="table_header" row-class-name="table_row"
                            style="width: 100%;height:100%">
                            <el-table-column prop="prod_id" label="商品ID" align="center" width="100"></el-table-column>
                            <!-- <el-table-column prop="thumb_img_url" label="商品图" align="center" min-width="150">
                        <template slot-scope="scope">
                            <span v-if="!scope.row.thumb_img_url || scope.row.thumb_img_url == ''">暂无商品图</span>
                        </template>
</el-table-column> -->
                            <el-table-column prop="name" label="名称" align="center" min-width="150"></el-table-column>
                            <el-table-column prop="code" label="商品编码" align="center" min-width="150">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.code ? scope.row.code : '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="active_status_display" label="商品状态" align="center"
                                min-width="150"></el-table-column>
                            <el-table-column prop="cate_type_display" label="分类" align="center"
                                min-width="150"></el-table-column>
                            <el-table-column prop="soldout_status_display" label="售罄状态" align="center" min-width="150">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.soldout_status_display }} </span>
                                    <!-- ({{ scope.row.soldout_status == 1 ? '无售罄门店' : '已下发门店售罄' }}) -->
                                </template>
                            </el-table-column>
                            <el-table-column prop="soldout_shop_cnt" label="已售罄门店数" align="center"
                                min-width="150"></el-table-column>
                            <el-table-column prop="obj_from_display" label="数据来源" align="center" width=""
                                min-width="150"></el-table-column>
                            <el-table-column prop="date" label="操作" align="center" width="400" fixed="right">
                                <template slot-scope="scope">
                                    <el-button size="small" type="primary"
                                        @click="openDrawer('show', scope.row)">详情</el-button>
                                    <el-button size="small" type="primary"
                                        @click="openDrawer('edit', scope.row)">编辑</el-button>
                                    <el-button size="small" type="primary"
                                        @click="toGoodsManage('sold_out', scope.row)">售罄</el-button>
                                    <el-button size="small" type="primary"
                                        @click="toGoodsManage('cancel_sold_out', scope.row)">取消售罄</el-button>
                                    <el-button size="small" type="danger" @click="handleDel(scope.row)">删除</el-button>

                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="info_pages">
                        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="pageNumber" :page-sizes="pageSizes" :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="total">
                        </el-pagination>
                    </div>

                    <!-- --------------------------------------  Drawer  ------------------------------------------------ -->
                    <el-drawer :visible.sync="drawerShow" :wrapperClosable="false" :before-close="cancel"
                        custom-class="drawer" :with-header="false">
                        <!-- ----------------------------------  编辑 ---------------------------------- -->
                        <div class="drawer_content" v-if="drawer_state == 'edit'">
                            <!-- <h2>商品图</h2>
                    <div class="drawer_image">
                        <div class="img_box goods_img">
                            
                        </div>
                        <div class="img_box upload_box">
                            <div class="upload_btn">
                                <i class="el-icon-circle-plus-outline"></i>
                            </div>
                            <div class="upload_title">
                                上传图片
                            </div>
                        </div>
                    </div> -->

                            <div class="info_item">
                                <span>商品&nbsp;&nbsp;ID：&nbsp;</span>
                                <span>{{ edit_item_info.prod_id }}</span>
                            </div>
                            <div class="info_item">
                                <span>商品编码：</span>
                                <span>{{ edit_item_info.code ? edit_item_info.code : '--' }}</span>
                            </div>
                            <div class="info_item">
                                <span>商品名称：</span>
                                <!-- <span v-if="edit_item_info.obj_from != 2">{{edit_item_info.name}}</span> -->
                                <el-input size="small" style="width: 300px;" v-model="edit_item_info.name"></el-input>
                            </div>

                            <div class="info_item">
                                <span>商品状态：</span>
                                <!-- <span v-if="edit_item_info.obj_from != 2">{{edit_item_info.active_status_display}}</span> -->
                                <el-select size="small" style="width: 300px;" v-model="edit_item_info.active_status">
                                    <el-option v-for="item in activeStatusOptions"
                                        :key="'edit_active_status_' + item[0]" :label="item[1]"
                                        :value="item[0]"></el-option>
                                </el-select>
                            </div>
                            <div class="info_item">
                                <span>商品分类：</span>
                                <span v-if="edit_item_info.obj_from != 2">{{ edit_item_info.cate_type_display }}</span>
                                <el-select v-else size="small" style="width: 300px;" v-model="edit_item_info.cate_type">
                                    <el-option v-for="item in CateTypeOptions" :key="'edit_cate_type_' + item[0]"
                                        :label="item[1]" :value="item[0]"></el-option>
                                </el-select>
                            </div>
                            <div class="info_item">
                                <span>数据来源：</span>
                                <span>{{ edit_item_info.obj_from_display }}</span>
                            </div>

                        </div>

                        <!-- ----------------------------------  新增 ---------------------------------- -->
                        <div class="drawer_content" v-if="drawer_state == 'add'">
                            <!-- <h2>商品图</h2>
                    <div class="drawer_image">
                        <div class="img_box goods_img">
                            
                        </div>
                        <div class="img_box upload_box">
                            <div class="upload_btn">
                                <i class="el-icon-circle-plus-outline"></i>
                            </div>
                            <div class="upload_title">
                                上传图片
                            </div>
                        </div>
                    </div> -->
                            <el-form ref="add_form" :rules="add_rules" :model="addDrawerinfo">
                                <el-form-item label="商品名称：" prop="name">
                                    <el-input size="small" style="width: 300px;" placeholder="请输入商品名称"
                                        v-model="addDrawerinfo.name"></el-input>
                                </el-form-item>
                                <el-form-item label="商品编码：" prop="code" style="margin-left:10px">
                                    <el-input size="small" style="width: 300px;" placeholder="请输入商品编码"
                                        v-model="addDrawerinfo.code"></el-input>
                                </el-form-item>
                                <el-form-item label="商品状态：" prop="active_status">
                                    <el-select size="small" style="width: 300px;" placeholder="请选择商品状态"
                                        v-model="addDrawerinfo.active_status">
                                        <el-option v-for="item in activeStatusOptions" :key="'active_status_' + item[0]"
                                            :label="item[1]" :value="item[0]"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="商品分类：" prop="cate_type">
                                    <el-select size="small" style="width: 300px;" placeholder="请选择商品分类"
                                        v-model="addDrawerinfo.cate_type">
                                        <el-option v-for="item in CateTypeOptions" :key="'cate_type_' + item[0]"
                                            :label="item[1]" :value="item[0]"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>

                        </div>

                        <!-- ----------------------------------  确认按钮 ---------------------------------- -->
                        <div class="drawer_footer" v-if="drawer_state == 'edit' || drawer_state == 'add'">
                            <el-button size="medium" style="margin-right: 20px;"
                                @click="cancel(drawer_state)">取消</el-button>
                            <el-button size="medium" type="primary" class="dark-btn" @click="infoConfirm">保存</el-button>
                        </div>

                        <!-- ----------------------------------  查看 ---------------------------------- -->
                        <div v-if="drawer_state == 'show'" class="goods_info_drawer_content">
                            <h2 class="t1">产品名称：{{ prodName }} </h2>
                            <h3 class="t2">已售罄门店列表</h3>
                            <div class="info_content_wrap">
                                <div class="info_content">
                                    <div v-if="details_drawer_list.length == 0"
                                        style="height:100px;line-height:100px;text-align:center;color:#999">
                                        暂无已售罄门店
                                    </div>
                                    <div class="info_item" v-for="item in details_drawer_list"
                                        :key="'detail_item' + item.id">
                                        <div>
                                            <span>分区：</span>
                                            <span>{{ item.marketname }}</span>
                                        </div>
                                        <div>
                                            <span>门店编码：</span>
                                            <span>{{ item.storecode }}</span>
                                        </div>
                                        <div>
                                            <span>门店名称：</span>
                                            <span>{{ item.storename }}</span>
                                        </div>
                                        <div>
                                            <span>售罄时间：</span>
                                            <span>{{ item.createdtime }}</span>
                                        </div>
                                        <div>
                                            <span>数据来源：</span>
                                            <span>{{ item.pub_from_display }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="page"
                                style="height:100px;display:flex;justify-content: flex-end;align-items: center;">
                                <el-pagination background @size-change="detailSizeChange"
                                    @current-change="detailCurrentChange" :current-page="details_page_info.pageNumber"
                                    :page-sizes="details_page_info.pageSizes" :page-size="details_page_info.pageSize"
                                    layout="total, sizes, prev, pager, next, jumper" :total="details_page_info.total">
                                </el-pagination>
                            </div>
                            <div class="footer">
                                <el-button size="medium" @click="cancel('show')">关闭</el-button>
                            </div>
                        </div>

                    </el-drawer>
                </div>
            </el-tab-pane>
            <el-tab-pane label="POS渠道" name="POS">
                <ProductPos></ProductPos>
            </el-tab-pane>
        </el-tabs>




        <!-- -----------------------------  手动导入  ----------------------------- -->
        <el-dialog :close-on-click-modal="false" :show-close="false" :visible.sync="dialogShow" width="50%"
            :before-close="dialogClose">
            <div class="dialog_header">
                <div class="title">
                    文件格式要求如下表格(文件名称如：*xls/*.xlsX)：
                    <div class="line"></div>
                </div>
                <div class="close_btn" @click="dialogClose">
                    <i class="el-icon-close"></i>
                </div>
            </div>
            <div class="dialog_info">
                <p>
                    <span>以*开头列属于必填项，每个文件最多5001行。</span>
                </p>

                <p style="margin-top: 5px">
                    状态列只能输入“<span>在售</span>”和“<span>售罄</span>”，其他字段不识别。
                </p>

                <p style="margin-top: 5px">
                    文件上传以后，请5分钟后在商品资料列表及时查看，文件行数太多，需要更多时间。
                </p>
            </div>
            <div class="dialog_table">
                <el-table :data="example_data" id="tableId" ref="tableId" stripe border cell-class-name="table_cell"
                    :header-cell-style="{
                        background: 'var(--table-header-bg-color)',
                        color: '#fff',
                        fontSize: '14px',
                        height: '50px',
                    }" style="width: 100%" :height="tableHeight">
                    <el-table-column prop="name" label="*名称" align="center"></el-table-column>
                    <el-table-column prop="status" label="*状态" align="center"></el-table-column>
                    <el-table-column prop="category" label="分类" align="center"></el-table-column>
                </el-table>
            </div>
            <div class="dialog_footer">
                <div class="btn_wrap btn_primary" @click="downloadList">
                    <div class="btn_l">
                        <img src="@/assets/img/export_list.png" alt />
                        <span>导出样例</span>
                    </div>
                    <i class="el-icon-arrow-right"></i>
                </div>
                <div class="btn_wrap btn_dange" @click="uploadList">
                    <div class="btn_l">
                        <img src="@/assets/img/up_to_cloud.png" alt />
                        <span>上传样例</span>
                    </div>
                    <i class="el-icon-arrow-right"></i>
                </div>
            </div>
        </el-dialog>

        <input type="file" class="up_ipt" ref="download_ipt" accept=".xls, .xlsx" @change="IptChange" />


    </div>
</template>

<script>
import echarts from 'echarts'
import * as XLSX from 'xlsx'
import FileSaver from 'file-saver'
import { get_adm_datas, dmb_sellprod_mgmt, datas_filter_cond } from '@/api/goods/goods'
import ProductPos from "./ProductPos.vue"
export default {
    components: {
        ProductPos
    },
    data() {
        return {
            loading: false,
            pageSize: 10,
            pageNumber: 1,
            total: 0,
            active_product_id: '',  // 当前选中的商品id, 编辑、查看
            pageSizes: [10, 30, 50, 100],
            chart: '',
            dialogShow: false,
            drawerShow: false,
            drawer_state: 'show',
            searchInfo: {
                blurry: '', // 模糊搜索 商品编号/名称
                soldout_status: '', // 售罄状态
                active_status: '', // 上架/下架状态
                cate_type: '', // 分类
            },
            soldouStatusOptions: [], //售罄状态
            activeStatusOptions: [], //上架/下架状态
            CateTypeOptions: [], //分类
            PubFromOptions: [], //商品售罄来源
            addDrawerinfo: {
                name: '',
                code: '',
                cate_type: '',
                active_status: '',
                thumb_img_key: ''
            },
            details_page_info: {
                pageSize: 10,
                pageNumber: 1,
                total: 0,
                pageSizes: [10, 20, 30, 40, 50],
            },
            example_data: [
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '在售', category: '奶茶' },
                { name: 'XXXXXX', status: '售罄', category: '奶茶' },
            ],
            tableData: [],
            details_drawer_list: [],     // 商品详情 已售罄门店列表
            edit_item_info: {}, // 编辑商品信息
            add_rules: {
                name: [
                    { required: true, message: '请输入商品名称', trigger: 'blur' },
                ],
                active_status: [
                    { required: true, message: '请选择商品状态', trigger: 'change' }
                ],
                cate_type: [
                    { required: true, message: '请选择商品分类', trigger: 'change' }
                ],
            },
            activeName: 'MOP',
            tableHeight: '500px',
            prodName: ''

        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        const info = sessionStorage.getItem('search_params');
        console.log(info, 'info');
        if (info) {
            this.setSearchInfo('back', JSON.parse(info));
        }
    },
    mounted() {
        this.getfilterCond();
        // this.initChart();
        this.getProductList();
    },
    methods: {
        handleClick() {

        },
        getfilterCond() {
            const params = {
                classModel: 'SellProds'
            }
            datas_filter_cond(params).then(res => {
                if (res.rst == 'ok') {
                    res.data[0].forEach(item => {
                        switch (item.filterkey) {
                            case 'soldout_status':
                                this.soldouStatusOptions = item.options;
                                break;
                            case 'active_status':
                                this.activeStatusOptions = item.options;
                                break;
                            case 'cate_type':
                                this.CateTypeOptions = item.options;
                                break;
                            case 'pub_from':
                                this.PubFromOptions = item.options;
                                break;
                        }
                    })
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        search() {
            this.pageNumber = 1;
            this.getProductList();
        },
        reset() {
            this.searchInfo.blurry = '';
            this.searchInfo.soldout_status = '';
            this.searchInfo.active_status = '';
            this.searchInfo.cate_type = '';
            this.pageNumber = 1;
            this.getProductList();
        },
        getProductList() {
            this.loading = true;
            const params = {
                classModel: 'SellProds',
                page: this.pageNumber - 1,
                size: this.pageSize,
                blurry: this.searchInfo.blurry,              // 模糊查询，商品编号或者商品名称
                soldout_status: this.searchInfo.soldout_status,      // 1 非售罄，无售罄门店，6： 售罄。表示已经下发门店售罄
                active_status: this.searchInfo.active_status,       // 是否在dmb系统上架，5：已下架， 9： 在售
                cate_type: this.searchInfo.cate_type,           // 分类
            }
            get_adm_datas(params).then(res => {
                if (res.rst == 'ok') {
                    this.tableData = res.data[0]['content'];
                    this.total = res.data[0]['totalElements'];
                    // this.total = 1000;
                } else {
                    this.$message.error(res.error_msg)
                }
                this.loading = false;
            }).catch(err => {
                this.loading = false;
                console.log('get goods list error');
            })
        },

        initChart() {
            // 获取DOM节点
            const chartDom = this.$refs.chart;
            // 初始化ECharts实例
            this.chart = echarts.init(chartDom);
            // 配置ECharts选项
            const option = {
                // title: {
                //     text: 'Referer of a Website',
                //     subtext: '123',
                //     left: 'center'
                // },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false,
                    orient: 'vertical',
                    left: 'left',
                    top: '0%',
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        // center: ['40%', '50%'],
                        startAngle: 45,
                        data: [
                            {
                                value: 0,
                                name: '售罄',
                                itemStyle: { color: '#eaeaea' },
                                emphasis: {
                                    itemStyle: {
                                        color: '#eaeaea' // 鼠标移入颜色
                                    }
                                }
                            },
                            {
                                value: 0,
                                name: '在售',
                                itemStyle: { color: '#214c3d' },
                                emphasis: {
                                    itemStyle: {
                                        color: '#214c3d' // 鼠标移入颜色
                                    }
                                }
                            },
                        ],
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: 'transparent',
                            borderWidth: 3
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                // color:'inherit'
                            }
                        },
                        label: {
                            show: false,
                            position: 'inner', // 将标签显示在饼图扇区内部
                            formatter: '{b}:{c}', // {b}是名称，{c}是数值，{d}是百分比
                            // textStyle: {color: '#ffffff'}
                        }
                    }
                ]
            };
            // 设置选项
            this.chart.setOption(option);
        },

        toGoodsManage(type, row) {
            this.setSearchInfo('go');
            this.$router.push({ path: '/goodsManage/goods_sell_out', query: { prod_id: row.prod_id, type } });
        },
        setSearchInfo(type, info) {
            if (type == 'go') {
                const search_params = {
                    ...this.searchInfo,
                    pageSize: this.pageSize,
                    pageNumber: this.pageNumber
                }
                sessionStorage.setItem('search_params', JSON.stringify(search_params));
            } else {
                for (let i in this.searchInfo) {
                    this.searchInfo[i] = info[i];
                }
                this.pageSize = info.pageSize;
                this.pageNumber = info.pageNumber;
                sessionStorage.removeItem('search_params');
            }

        },
        handleCurrentChange(val) {
            this.pageNumber = val;
            this.getProductList();
        },

        handleSizeChange(val) {
            this.pageSize = val;
            this.handleCurrentChange(1);
        },

        openDrawer(type, row) {
            this.drawerShow = true;
            this.drawer_state = type;
            if (type == 'add') {
                console.log('添加');
            } else if (type == 'edit') {
                this.edit_item_info = JSON.parse(JSON.stringify(row));
                console.log('编辑', row);
                this.prodName = row.name
            } else {
                this.active_product_id = row.prod_id;
                this.prodName = row.name
                this.getShowDrawerinfo();
            }

        },
        getShowDrawerinfo() {
            const params = {
                classModel: 'ProdSoldOutShops',
                prod_id: this.active_product_id,
                page: this.details_page_info.pageNumber - 1,
                size: this.details_page_info.pageSize
            }
            get_adm_datas(params).then(res => {
                if (res.rst == 'ok') {
                    console.log(res.data[0]['content']);
                    this.details_drawer_list = res.data[0]['content'];
                    this.details_page_info.total = res.data[0]['totalElements'];
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        detailSizeChange(val) {
            this.details_page_info.pageSize = val;
            this.details_page_info.pageNumber = 1;
            this.getShowDrawerinfo();
        },
        detailCurrentChange(val) {
            this.details_page_info.pageNumber = val;
            this.getShowDrawerinfo();
        },
        handleDel(row) {
            const title = '删除';
            const str = `确定删除商品 “${row.name}” 吗？删除后售罄信息将会丢失，请谨慎操作`;
            this.$confirm(str, title, {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: '',
                confirmButtonClass: 'el-button--danger'
            }).then(() => {
                this.del_product(row);
            }).catch(() => {
                console.log("取消删除");
            });
        },

        cancel(type) {
            this.drawerShow = false;
            this.drawer_state = 'show';
            if (type == 'add') {
                this.clearDrawer('add');
            }
        },

        infoConfirm() {
            if (this.drawer_state == 'add') {
                this.$refs.add_form.validate((valid) => {
                    if (valid) {
                        this.confirm_add_goods();
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            } else if (this.drawer_state == 'edit') {
                this.confirm_edit_goods();
            }
        },

        confirm_add_goods() {
            const params = { "act": "add", ...this.addDrawerinfo };

            // params.active_status = String(params.active_status);
            console.log(params, typeof (params.active_status));
            dmb_sellprod_mgmt(params).then(res => {
                if (res.rst == 'ok') {
                    this.$message.success('添加成功');
                    this.clearDrawer('add');
                } else {
                    this.$message.error(res.error_msg);
                }
            })
        },
        confirm_edit_goods() {
            let params = {
                "act": "edit",
                id: this.edit_item_info.prod_id,
                name: this.edit_item_info.name,
                code: this.edit_item_info.code,
                cate_type: this.edit_item_info.cate_type,
                active_status: this.edit_item_info.active_status,
                thumb_img_key: this.edit_item_info.thumb_img_key,
            };


            console.log(params);
            dmb_sellprod_mgmt(params).then(res => {
                if (res.rst == 'ok') {
                    this.$message.success('修改成功');
                    this.clearDrawer('edit');
                } else {
                    this.$message.error(res.error_msg);
                }
            })
        },
        del_product(row) {
            const params = {
                act: 'del',
                id: row.prod_id
            }
            dmb_sellprod_mgmt(params).then(res => {
                if (res.rst == 'ok') {
                    this.$message.success('删除成功');
                    this.clearDrawer('del');
                } else {
                    this.$message.error(res.error_msg);
                }
            })
        },
        clearDrawer(type) {
            this.drawerShow = false;
            if (type == 'add') {
                this.$refs.add_form.resetFields();
            } else if (type == 'edit') {
                this.edit_item_info = {};
            } else {
                this.pageNumber = 1;
            }

            this.getProductList();
        },
        dialogClose() {
            this.dialogShow = false;
        },

        downloadList() {
            //转换成excel时，使用原始的格式
            var xlsxParam = { raw: true }
            // let fix = document.querySelector(".el-table__fixed");
            let fix = document
                .querySelector('#tableId')
                .querySelector('.el-table__fixed')
            let wb
            //判断有无fixed定位，如果有的话去掉，后面再加上，不然数据会重复
            if (fix) {
                wb = XLSX.utils.table_to_book(
                    document.querySelector('#tableId').removeChild(fix),
                    xlsxParam
                )
                document.querySelector('#tableId').appendChild(fix)
            } else {
                wb = XLSX.utils.table_to_book(
                    document.querySelector('#tableId'),
                    xlsxParam
                )
            }
            var wbout = XLSX.write(wb, {
                bookType: 'xlsx',
                bookSST: true,
                type: 'array',
            })
            try {
                FileSaver.saveAs(
                    new Blob([wbout], { type: 'application/octet-stream' }),
                    '样表.xlsx'
                )
            } catch (e) {
                if (typeof console !== 'undefined') console.log(e, wbout)
            }
            return wbout
        },

        uploadList() {
            this.$refs.download_ipt.dispatchEvent(new MouseEvent('click'));
        },

        IptChange() {

        }
    },
};
</script>

<style scoped lang="scss">
.product-center {
    padding: 20px 20px 20px 30px;
    width: 100%;
    height: 100%;
    background-color: var(--light-gray);
    box-sizing: border-box;

    .dark-btn {
        background-color: var(--btn-background-dark);
        border-color: var(--btn-background-dark);

        &:active {
            filter: brightness(0.9);
        }
    }

    .center_title {
        font-size: 24px;
        font-weight: 600;
        color: #333333;
        display: flex;
        align-items: center;
    }

    .show_panel {
        display: flex;
        border: 2px solid var(--dev-border);
        width: 450px;
        border-radius: 30px;
        margin-top: 20px;
        font-weight: 600;
        margin-bottom: 20px;

        .chart_wrap {
            padding: 0 20px;
            // border: 1px solid red;
            width: 200px;
            height: 200px;

            .chart_title {
                height: 40px;
                line-height: 40px;
                font-size: 18px;
            }

            .chart {
                width: 150px;
                height: 150px;
            }
        }

        .panel_info {
            // border: 1px solid blue;
            vertical-align: bottom;
            display: grid;
            place-items: end;
            padding-bottom: 10px;
            margin-left: 10px;

            .info_item_wrap {
                .info_item {
                    margin: 15px 0;
                    font-size: 15px;

                    span:first-child {
                        display: inline-block;
                        width: 90px;
                    }
                }
            }
        }
    }

    .goods_info_wrap {
        display: flex;
        flex-direction: column;
        height: 100%;

        .goods_info_top {
            display: flex;
            padding-right: 20px;

            .info_btns {
                flex: 1;
                display: flex;
                margin-left: 30px;

                button {
                    align-self: flex-start;
                }
            }
        }

        .search_bar {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;

            .search_item {
                display: flex;
                align-items: center;
                margin: 10px 20px 10px 0;

                &>span {
                    margin-right: 10px;
                }
            }
        }

        .info_item_panel {
            margin-top: 10px;
            // height: calc(100vh - 475px);
            flex: 1;
            min-height: 450px;
            // border-top-left-radius: 20px;
            // border-top-right-radius: 20px;
            // border:2px solid ;
            // border-bottom: none;
            // border-color: var(--dev-border);
            overflow: hidden;

            ::v-deep .table_header {
                // background-color: red !important;
                height: 50px;
                font-size: 14px;

                th.el-table__cell {
                    color: #fff;
                    background-color: var(--table-header-bg-color) !important;
                }
            }

            ::v-deep .table_row {
                height: 120px;
                font-size: 14px;
            }



            ::v-deep .el-table__body-wrapper {
                &::-webkit-scrollbar {
                    width: 8px !important;
                    height: 8px !important;
                }
            }
        }

        .info_pages {
            height: 60px;
            // border-top: 1px solid var(--gray);
            background-color: #fff;
            overflow: hidden;
            // border-bottom-left-radius: 20px;
            // border-bottom-right-radius: 20px;
            // border:2px solid ;
            // border-top: none;
            // border-color: var(--dev-border);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding-right: 20px;
        }
    }

    ::v-deep .drawer {
        min-width: 550px !important;
    }

    .drawer {

        .img_box {
            width: 200px;
            height: 200px;
            margin-right: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
            background-color: var(--light-gray);
        }

        .upload_box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .upload_btn {
                font-size: 50px;
                color: var(--event-text-color);
                cursor: pointer;
                margin-bottom: 5px;

                &:hover {
                    filter: brightness(1.5);
                }

                &:active {
                    filter: brightness(1.2);
                }
            }

            .upload_title {
                color: var(--text-gray);
                font-size: 16px;
            }
        }

        .goods_img {
            position: relative;


        }

        .drawer_content {
            height: calc(100% - 60px);
            overflow-y: auto;
            box-sizing: border-box;
            padding: 20px 30px;

            .drawer_image,
            .drawer_badge {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 20px;
            }

            h2 {
                margin-bottom: 15px;
            }

            .info_item {
                font-size: 17px;
                margin: 30px 0;
            }

            ::v-deep .el-form-item label {
                font-size: 17px !important;
            }
        }

        .drawer_footer {
            height: 60px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            box-sizing: border-box;
            padding-right: 50px;
            border-top: 1px solid var(--gray);
        }


        .goods_info_drawer_content {
            height: 100%;
            padding: 25px;
            box-sizing: border-box;

            .t1 {
                margin-bottom: 35px;
            }

            .t2 {
                margin-bottom: 20px;
            }

            .info_content_wrap {
                height: calc(100% - 250px);
                margin-bottom: 10px;
                overflow-y: auto;

                .info_content {
                    background-color: var(--light-gray);
                    border: 1px solid transparent;
                    padding: 10px;

                    .info_item {
                        margin-bottom: 20px;
                        display: flex;
                        flex-wrap: wrap;
                        border-bottom: 1px solid #ccc;
                        font-size: 15px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        div {
                            margin-right: 10px;
                            margin-bottom: 12px;
                            width: 320px;
                        }
                    }
                }
            }

            .footer {
                height: 50px;

                button {
                    float: right;
                }
            }
        }
    }



    ::v-deep .el-dialog__header {
        padding: 0 !important;
    }

    .dialog_header {
        padding-left: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            position: relative;
            font-size: 18px;
            font-weight: 600;

            .line {
                position: absolute;
                width: 100%;
                bottom: -12px;
                height: 2px;
                background-image: linear-gradient(to right,
                        var(--table-header-bg-color) 0%,
                        var(--table-header-bg-color) 80%,
                        transparent 50%);
                background-size: 28px 1px;
                background-repeat: repeat-x;
            }
        }

        .close_btn {
            cursor: pointer;
            font-size: 26px;

            &:hover {
                filter: brightness(1.4);
            }
        }
    }

    .dialog_info {
        margin-top: 20px;
        margin-bottom: 40px;
        padding-left: 20px;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;

        span {
            color: red;
        }
    }

    .dialog_table {
        // padding: 0 30px 0 20px;
        margin: 0 30px 0 20px;
        border-radius: 10px 5px;
        overflow: hidden;

        ::v-deep .table_cell {
            height: 50px !important;
        }
    }

    .dialog_footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 60px;
        box-sizing: border-box;
        padding: 0 20px;
        margin-top: 35px;

        .btn_dange {
            background-color: var(--btn-danger-color);
        }

        .btn_primary {
            background-color: var(--btn-background-dark);
        }

        .btn_wrap {
            cursor: pointer;
            color: #fff;
            width: 155px;
            height: 40px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 0 15px;
            margin-right: 30px;

            &:hover {
                filter: brightness(1.2);
            }

            .btn_l {
                display: flex;
                align-items: center;

                img {
                    width: 18px;
                    margin-right: 8px;
                }
            }
        }
    }

    .up_ipt {
        position: fixed;
        width: 0px;
        top: -500px;
        left: -500px;
        z-index: -10;
    }
}

::v-deep .el-table__body-wrapper {
    height: calc(100vh - 450px) !important;
    overflow: auto !important;
}
</style>
