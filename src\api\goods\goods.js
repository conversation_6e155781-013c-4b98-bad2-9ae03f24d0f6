import { get, post } from '@/utils/request'


// 添加商品
const dmb_sellprod_mgmt = p => post('/dmb/api/json', p, 'dmb_sellprod_mgmt');

// 获取商品列表 /  获取单个商品售罄门店清单
const get_adm_datas = p => post('/dmb/api/json', p, 'get_adm_datas');

// 筛选参数定义
const datas_filter_cond = p => post('/dmb/api/json', p, 'datas_filter_cond');

// 单个商品售罄/取消
const sellprod_soldout_by_shop_by_prod = p => post('/dmb/api/json', p, 'sellprod_soldout_by_shop_by_prod');

// 获取门店标签
const get_shop_tags = p => post('/dsadm/api/json', p, 'get_shop_tags');

// pos
const get_pc_spu_list = p => post('/dmb/api/json', p, 'get_pc_spu_list')

// 详情
const get_pc_sku_details = p => post('/dmb/api/json', p, 'get_pc_sku_details')

export {
    dmb_sellprod_mgmt,
    get_adm_datas,
    datas_filter_cond,
    sellprod_soldout_by_shop_by_prod,
    get_shop_tags,
    get_pc_spu_list,
    get_pc_sku_details
}
