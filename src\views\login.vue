<template>
  <div class="login">
    <div class="left">
      <!-- :style="'background-image:url(' + Background + ');'" -->
      <div class="leftContent">
        <div class="logo" ></div>
        <h3 style="line-height: 40px;">{{ $store.state.base.projuct_Welcome }}</h3>
      </div>
    </div>
    <div class="right">
      <el-form v-show="isShow" ref="loginForm" :model="loginForm" :rules="loginRules" label-position="left"
        label-width="0px" class="login-form">
        <h3 class="title">Welcome</h3>
        <div v-show="loginError" class="error"> 用户名或密码错误 </div>
        <div v-show="loginError1" class="error"> {{ loginErrorMessage }} </div>
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="请输入常用手机号/邮箱">
            <!-- <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" /> -->
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="请输入密码">
            <!-- <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" /> -->
          </el-input>
        </el-form-item>
        <!-- <el-form-item prop="code">
        <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode">
        </div>
      </el-form-item> -->
        <div class="remember">
          <el-checkbox v-model="loginForm.rememberMe" class="checkBox" style="margin: 0 0 25px 0">
            记住账号
          </el-checkbox>
          <!-- 暂时隐藏功能 -->
          <!-- <p  @click="forgetPassword">忘记密码？</p> -->
        </div>
        <SliderUnlock status="ruleForm.status" :successFun="onMpanelSuccess" :errorFun="onMpanelError" />
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="medium" type="primary" :disabled="isDisable" style="
              width: 100%;
              background-color: rgb(245, 245, 245);
              border-color: rgb(245, 245, 245);
              margin-top: 30px;
              height: 50px;
              color: #333;
            " :class="isDisable == false ? 'isDisableBg' : ''" @click.native.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <!-- <div v-if="$store.state.settings.showFooter" id="el-login-footer">
      <span v-html="$store.state.settings.footerTxt" />
      <span> ⋅ </span>
      <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">{{ $store.state.settings.caseNumber }}</a>
    </div> -->
  </div>
</template>

<script>
import { encrypt } from "@/utils/rsaEncrypt";
import Config from "@/settings";
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import qs from "qs";
// import Background from "@/assets/images/background.jpg";
// import LoginLogo from "@/assets/images/loginLogo.png";
import LoginLogo from "@/assets/images/Starbucks1.png";
import SliderUnlock from "../components/Silder/sliderUnlock.vue";
var checkStatus = (rule, value, callback) => {
  console.log(value);
  if (!value) {
    return callback(new Error("请拖动滑块完成验证"));
  } else {
    callback();
  }
};
export default {
  name: "Login",
  data() {
    return {
      // Background: Background,
      LoginLogo: LoginLogo,
      isShow: true,
      codeUrl: "",
      cookiePass: "",
      loginForm: {
        username: "", 
        password: "", 
        rememberMe: false,
        code: "",
        uuid: "",
      },
      status: [{ validator: checkStatus, trigger: "change" }],
      isDisable: true,
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      loading: false,
      redirect: undefined,
      loginError: false,
      loginError1: false,
      loginErrorMessage: '',
      loginErrorMessage1: ''
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const data = route.query;
        if (data && data.redirect) {
          this.redirect = data.redirect;
          delete data.redirect;
          if (JSON.stringify(data) !== "{}") {
            this.redirect =
              this.redirect + "&" + qs.stringify(data, { indices: false });
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    // 获取验证码
    this.getCode();
    // 获取用户名密码等Cookie
    this.getCookie();
    // token 过期提示
    this.point();
  },
  methods: {
    getCode() {
      // getCodeImg().then((res) => {
      //   this.codeUrl = res.img;
      //   this.loginForm.uuid = res.uuid;
      // });
    },
    getCookie() {
      const username = Cookies.get("username");
      let password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      // 保存cookie里面的加密后的密码
      this.cookiePass = password === undefined ? "" : password;
      password = password === undefined ? this.loginForm.password : password;
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password,
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        code: "",
      };
    },
    // password  enter
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        const user = {
          username: this.loginForm.username,
          password: this.loginForm.password,
          rememberMe: this.loginForm.rememberMe,
          code: this.loginForm.code,
          uuid: this.loginForm.uuid,
        };
        if (user.password !== this.cookiePass) {
          user.password = encrypt(user.password);
        }
        if (valid) {
          this.loading = true;
          if (user.rememberMe) {
            Cookies.set("username", user.username, {
              expires: Config.passCookieExpires,
            });
            Cookies.set("password", user.password, {
              expires: Config.passCookieExpires,
            });
            Cookies.set("rememberMe", user.rememberMe, {
              expires: Config.passCookieExpires,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", user)
            .then((res) => {
              if (res.rst == "ok") {
                this.$message.success("登录成功")
                this.loading = false;
                this.loginError = false;
                this.loginError1 = false;

                this.$router.push({ path: this.redirect || "/" });
              } else if (res.rst == "error") {
                this.loginError = false;
                this.loginError1 = false;
                if (res.error_code == '400') {
                  this.$message.closeAll();
                  // this.$message.error("用户名或密码错误")
                  this.loading = false;
                  this.loginError = true;
                } else {
                  this.loading = false;
                  this.loginErrorMessage = res['error_msg'];
                  this.loginError1 = true;
                }
                // else if (res.error_code == '60004507') {
                //   this.loading = false;
                //   this.loginErrorMessage = res['error_msg'];
                //   this.loginError1 = true;
                // }else if(res.error_code == '60004505'){
                //   this.loginErrorMessage = res['error_msg'];
                //   this.loginError1 = true;
                // }else if(res.error_code == '60004508'){
                //   this.loginErrorMessage = res['error_msg'];
                //   this.loginError1 = true;
                // }
              }
            })
            .catch((e) => {
              this.$message.error("用户名或密码有误,请重新输入");
              console.log("e", e);
              this.$message.closeAll();
              this.$message.error("接口请求异常")
              this.loading = false;
              this.getCode();
            });
        } else if (this.loginForm.password == '' || this.loginForm.username == '') {
          this.$message.error("用户名或密码不能为空");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    point() {
      const point = Cookies.get("point") !== undefined;
      if (point) {
        this.$notify({
          title: "提示",
          message: "当前登录状态已过期，请重新登录！",
          type: "warning",
          duration: 5000,
        });
        Cookies.remove("point");
      }
    },
    forgetPassword() {
      // this.$message("这是忘记密码了？")
    },
    onMpanelSuccess() {
      this.isDisable = false;
    }
  },
  components: { SliderUnlock },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  display: flex;
  // justify-content: center;
  align-items: center;
  height: 100%;
  background-size: cover;
}

.title {
  margin-bottom: 50px;
  text-align: left;
  color: var(--text-color);
  font-size: 46px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 385px;
  height: 450px;
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  // padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  display: inline-block;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.left {
  width: 50%;
  height: 100%;
  background-size: 100% 100%;
  position: relative;
  background-color: var(--background-color);
}

.logo {
  width: 250px;
  height: 210px;
  color: rgba(80, 80, 80, 1);
  margin-top: -20px;
  font-size: 14px;
  background: url("../assets/images/Starbucks1.png");  
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
}

.leftContent {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 200px;
  text-align: center;
  width: 350px;
}

.leftContent>h3 {
  margin-top: 30px;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
}

.remember {
  display: flex;
  justify-content: space-between;
}

.remember>p {
  color: var(--check-color);
  font-size: 15px;
  cursor: pointer;
}

.isDisableBg {
  background: var(--check-color) !important;
  border-color: var(--check-color) !important;
  color: #fff !important;
}

.login-form .el-input input {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  height: 44px;
  line-height: 44px;
  font-size: 21px;
}

.right {
  position: relative;
  width: 50%;
  height: 100%;
}

.error {
  position: absolute;
  left: 0;
  top: 65px;
  color: var(--check-color);
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
  color: rgba(80, 80, 80, 1);
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background: var(--check-color) !important;
  border-color: var(--check-color);
}

// ::v-deep .el-input__inner{
//   background: yellow !important;
// }
::v-deep .el-input__inner:target {
  background: var(--check-color) !important;
  border-color: var(--check-color);
}
</style>


