<template>
    <div class="floats_resources" @keydown.esc=closeMask>
        <div class="content" :style="{ height: autoHeight.height }">
            <!-- 搜索区 -->
            <div class="content_header flex">
                <!-- 左侧筛选条件 -->
                <div class="content_search flex flex-1">
                    <el-select v-model="queryList.screenSize" clearable placeholder="屏幕尺寸" size="small"
                        style="width:151px;margin-right:7px">
                        <el-option v-for="item in screenSizeList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                    <el-select v-model="queryList.market" clearable placeholder="市场" size="small"
                        style="width:151px;margin-right:7px">
                        <el-option v-for="item in marketList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <div class="search_btn" @click="handleSearch"><span>搜索</span></div>
                </div>
                <!-- 右侧切换格子/列表 -->
                <div class="content_tabs flex">
                    <div class="tabs_btn" @click="contentShow = 0" :class="contentShow == 0 ? 'tabs_checked' : ''">
                        <span>格子</span>
                    </div>
                    <div class="tabs_btn" style="margin-left:-1px" @click="contentShow = 1"
                        :class="contentShow == 1 ? 'tabs_checked' : ''"><span>列表</span></div>
                </div>
            </div>
            <!-- 内容区 -->
            <div class="floats_content flex-1">
                <!-- 格子 -->
                <div class="lattice flex" v-show="contentShow == 0">
                    <div class="lattice_header">
                        <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox> 全选
                    </div>
                    <div class="lattice_content flex flex-1">
                        <div class="lattice_item " v-for="item in dataList" :key="item">
                            <div class="item_image">
                                <img :src="item.img_url"
                                    style="width: 100%;height:100%;object-fit:cover;background-color:#3F3F3F;" alt="">
                                <div class="edit_mask flex">
                                    <div class="mask_btn" @click="handleEdit(item)">
                                        <p><i class="el-icon-edit"></i></p>
                                        <p>编辑</p>
                                    </div>
                                    <div class="mask_btn" style="" @click="handleShow(item)">
                                        <p><i class="el-icon-view"></i></p>
                                        <p>预览</p>
                                    </div>
                                    <div class="mask_btn" @click="handleDelete(item)"
                                        v-if="checkPer([ 'res:del'])">
                                        <p><i class="el-icon-delete-solid"></i></p>
                                        <p class="del_text">删除</p>
                                    </div>
                                </div>
                            </div>
                            <div class="up_date flex" style="">
                                <div class="flex-1 txt_ellipsis">上传日期：{{ item.upload_date }}</div>
                                <el-checkbox v-model="item.checked" @change="checkOne"></el-checkbox>
                            </div>
                            <div class="item_size">
                                尺寸：{{ item.size }}
                            </div>
                            <div class="item_tags flex">
                                <div class="each_tags flex" v-for="val in         item.tags" :key="val">
                                    <img src="../../assets/img/tags.png" style="width:24px;height:24px">
                                    <span style="display: inline-block;height: 24px;line-height: 24px;">{{ val }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 列表 -->
                <div class="lists" v-show="contentShow == 1">
                    <el-table :data="dataList" height="100%" ref="table_list" @selection-change="handleSelectionChange"
                        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px' }">
                        <el-table-column width="50" align="center">
                            <template slot="header">
                                <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox>
                            </template>
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.checked" @change="checkOne"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="img_url" label="图片预览" width="110" align="center">
                            <template slot-scope="scope">
                                <img :src="scope.row.img_url"
                                    style="width: 73px;height:73px;object-fit:cover;background-color:#3F3F3F;" alt="">
                            </template>
                        </el-table-column>
                        <el-table-column prop="dimensions" label="尺寸" width="" show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="size" label="大小" width="" show-overflow-tooltip="true" align="center">
                        </el-table-column>
                        <el-table-column prop="tags" label="标签" width="" show-overflow-tooltip="true" align="center">
                            <template slot-scope="scope">
                                <div class="flex">
                                    <div v-for="(item, index) in         scope.row.tags" :key="index"
                                        class="flex align-items-center" style="margin-right:2px">
                                        <img src="../../assets/img/tags.png" style="width:24px;height:24px;" alt="">
                                        <span>{{ item }}</span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="upload_date" label="上传日期" width="" show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="account" label="账户" width="" show-overflow-tooltip="true" align="center">
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="130" align="center">
                            <template slot-scope="scope">
                                <el-button @click.native.prevent="handleShow(scope.row)" type="text"
                                    style="color:#409eff" size="small">预览</el-button>
                                <el-button @click.native.prevent="handleEdit(scope.row)" type="text"
                                    style="color:#409eff" size="small">编辑</el-button>
                                <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:red"
                                    size="small" v-if="checkPer([ 'res:del'])">删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <!-- 底部操作以及页码 -->
        <div class="footer flex">
            <div class="flex">
                <div class="btns_blue" @click="batchAddTags">
                    <span>批量标签</span>
                </div>
                <div class="btns_red" @click="batchDelete">
                    <span>批量删除</span>
                </div>
            </div>
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5' :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
            </el-pagination>
        </div>

        <!-- 批量标签 -->
        <el-dialog title="标签管理" :visible.sync="tagsDialogVisible" :close-on-click-modal='false' width="697px"
            custom-class='batch_tags_dialog' :before-close="handleClose">

            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="关联标签" name="relation">
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in         tagsList" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active">
                            <img src="../../assets/img/tags.png" alt="" style="width:24px;height:24px;margin-right:7px">
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="handleAdd">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    components: {

    },
    data() {
        return {
            tagsDialogVisible: false, //批量标签弹框
            activeName: 'relation',
            contentShow: 0, //显示的是列表还是格子
            currentPage: 1,  //页码
            totalNum: 2, //总数据数量
            pageSize: 10,
            colorList: [{ color: 'rgba(248, 231, 28, 1)', checked: false, label: '黄' },
            { color: 'rgba(154, 220, 83, 1)', checked: false, label: '绿' },
            { color: 'rgba(42, 130, 228, 1)', checked: false, label: '蓝' },
            { color: 'rgba(121, 212, 191, 1)', checked: false, label: '青' },
            { color: 'rgba(245, 112, 129, 1)', checked: false, label: '粉' },
            { color: 'rgba(222, 181, 230, 1)', checked: false, label: '紫' }],
            queryList: {
                content: '',
                screenSize: '',
                market: '',
                color: ''
            },
            screenSizeList: [
                { value: '1920*1080', label: '横屏1920*1080' },
                { value: '1080*1920', label: '竖屏1080*1920' },
                { value: 'qita', label: '其他尺寸' },
            ],
            marketList: [
                { value: 'quanguo', label: '全国市场' },
                { value: 'shanghai', label: '上海市场' },
                { value: 'beijing', label: '北京市场' },
                { value: 'chengdu', label: '成都市场' },
            ],
            isAllChecked: false,
            dataList: [],
            tagsList: [{ active: false, label: '24小时' }, { active: false, label: '清真餐厅' }, { active: false, label: '社区店' }, { active: false, label: '甜品站' }],
            checkedList: [],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getDataLsit()
    },
    mounted() {

    },
    methods: {
        // 搜索
        handleSearch() {
            // console.log(this.queryList);
        },
        // 获取列表数据
        getDataLsit() {
            // this.loading = true;
            const list = [{
                img_url: 'https://img.xiaopiu.com/userImages/img4772717fc6762d60.gif',
                dimensions: '1920*1080',
                size: '11.12MB',
                tags: ['春季上新', '新品推荐'],
                upload_date: '2022-03-15 09:32',
                account: '<EMAIL>',
                checked: false
            },
            {
                img_url: 'https://img.xiaopiu.com/userImages/img4768717fc65fc700.gif',
                dimensions: '1920*1080',
                size: '11.12MB',
                tags: ['非常清真', '快来看看'],
                upload_date: '2022-03-15 09:32',
                account: '<EMAIL>',
                checked: false
            },]

            this.dataList = list;
            // this.loading = false;
        },
        handleSelectionChange(val) {
            // console.log(val);
        },
        // 全选
        checkAll() {
            this.dataList.forEach(item => {
                item.checked = this.isAllChecked
            })
            if (this.isAllChecked) {
                this.checkedList = JSON.parse(JSON.stringify(this.dataList))
            } else {
                this.checkedList = []
            }
        },
        //单选
        checkOne() {
            this.checkedList = this.dataList.filter(item => item.checked)
            if (this.checkedList.length == this.dataList.length) {
                this.isAllChecked = true;
            } else {
                this.isAllChecked = false;
            }
        },
        // 预览
        handleShow(val) {
            this.PreviewMaskSrc = val.img_url;
            this.$emit('setImgPreview', val.img_url)
        },
        // 编辑
        handleEdit(val) {
            this.$message.success('编辑')
            // console.log('编辑', val);
        },
        // 删除
        handleDelete(val) {
            this.$confirm('是否删除该内容?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除')
                // console.log('删除', val);
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        // 批量标签
        batchAddTags() {
            if (this.checkedList.length == 0) {
                this.$message.warning('请先选择需要关联标签的内容');
                return
            }
            this.tagsDialogVisible = true;
            // console.log(this.checkedList);
        },
        // 批量标签弹框确定按钮
        handleAdd() {
            const tagsArr = this.tagsList.filter(item => item.active)
            if (tagsArr.length == 0) {
                this.$message.warning('请先选择标签')
                return
            }
            // console.log(tagsArr);
            this.handleClose();
        },
        // 批量标签弹框取消按钮关闭弹框
        handleClose() {
            this.tagsDialogVisible = false;
            this.tagsList.forEach(item => item.active = false)
        },
        // 批量删除
        batchDelete() {
            if (this.checkedList.length == 0) {
                this.$message.warning('请先选择需要删除的内容');
                return
            }
            this.$confirm('是否删除已选内容?', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.success('删除成功')
                // console.log(this.checkedList);
            }).catch(() => {
                this.$message.info('已取消删除');
            });

        },
        //页码改变
        handleCurrentChange(val) {
            // console.log(`现在是第${val}页`);
            this.currentPage = val;
        },
        handleSizeChange(val) {
            // console.log(`每页${val}条`);
            this.pageSize = val;
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 162 + 'px';
            this.autoHeight.heightNum = windowHeight - 162;
        },
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
        window.removeEventListener('keyup', this.closeScreenFull)
    },
};
</script>

<style scoped >
* {
    box-sizing: border-box;
}

.floats_resources {
    width: 100%;
    padding: 0 14px;
    background: #EEEEEE;
}

.content {
    /* border: 1px solid red; */
    display: flex;
    flex-direction: column;
}

.content_header {
    height: 67px;
}

.content_search {
    align-items: center;
}

.content_tabs {
    align-items: center;
}

.tabs_btn {
    border: 1px solid rgba(108, 178, 255, 1);
    font-size: 14px;
    padding: 6px 15px;
    cursor: pointer;
    color: rgba(108, 178, 255, 1);
    background: #fff;
    white-space: nowrap;
}

.tabs_btn:hover {
    border: 1px solid rgba(108, 178, 255, .7);
}

.tabs_checked {
    color: #fff;
    background: rgba(108, 178, 255, 1);
}

.floats_content {
    width: 100%;
    height: calc(100% - 67px);
    /* overflow-y:auto ; */
    /* border: 1px solid pink; */
}

.color_disc {
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 50%;
    cursor: pointer;
    margin-right: 10px;
}

.color_disc_active {
    /* background: black !important; */
}

/* 格子 */
.lattice {
    height: 100%;
    flex-direction: column;
}

.lattice_header {
    width: 100%;
    height: 42px;
    line-height: 42px;
    background-color: var(--text-color-light);
    padding-left: 10px;
    font-size: 14px;
    color: #fff;
}

.lattice_content {
    flex-wrap: wrap;
    overflow-y: auto;
}

.lattice_item {
    width: 240px;
    height: 350px;
    margin-right: 16px;
    margin-top: 15px;
    font-size: 13px;
    color: rgba(56, 56, 56, 1);
    background-color: #fff;
}

.item_image {
    position: relative;
    width: 100%;
    height: 240px;
    padding: 3px;
}

.item_image:hover .edit_mask {
    display: flex;
}

.edit_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 240px;
    background-color: rgba(0, 0, 0, 0.335);
    color: #fff;
    justify-content: center;
    align-items: center;
}

.mask_btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.564);
    border-radius: 50%;
    font-size: 11px;
    margin: 0 10px;
}

.mask_btn:hover {
    background-color: rgba(0, 0, 0, 0.464);
}

.del_text{
    color: var(--del-color) !important;
}

.each_tags {
    align-items: center;
    margin-right: 11px;
}

.up_date,
.item_size,
.item_tags {
    margin-top: 10px;
    align-items: center;
    padding: 0 9px;
}

.item_tags {
    margin-top: 13px;
    padding: 0 4px
}

/* 列表 */
.lists {
    width: 100%;
    height: 100%;
}

.footer {
    height: 72px;
    align-items: center;
    justify-content: space-between;
}

.txt_ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 弹框 */
.relation_tags {
    display: flex;
    flex-wrap: wrap;
    height: 279px;
    margin-bottom: 20px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    font-size: 14px;
    border-radius: 24px;
    margin-right: 24px;
    margin-bottom: 18px;
    padding: 0 13px;
    border: 1px solid rgba(209, 209, 209, 1);
    cursor: pointer;
    /* 禁止文字选中 */
    /* -moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none; */
}

.tag_active {
    color: rgba(108, 178, 255, 1);
    background-color: rgba(212, 232, 255, 1);
    border: 1px solid rgba(108, 178, 255, 1);
}
</style>
<style>
/* 把element table的复选框改为红色 */
.floats_resources .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: var(--base-color) !important;
    border-color: var(--base-color) !important;
}

.floats_resources .el-checkbox__inner {
    /* border-color:red !important; */
    width: 18px;
    height: 18px;
    border-radius: 50%;
}

.floats_resources .el-checkbox__inner::after {
    left: 6px !important;
    top: 3px !important;
}

.floats_resources .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    left: 0px !important;
    top: 7px !important;
}

.floats_resources .el-dialog__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.batch_tags_dialog {
    margin-top: 0px !important;
    border-radius: 16px !important;
}

.batch_tags_dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
    margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 1px !important;
}

.batch_tags_dialog .el-tabs__item {
    height: 50px !important;
    line-height: 50px !important;
}

.batch_tags_dialog .el-button--primary {
    background: rgba(108, 178, 255, 1);
}

.floats_resources .el-tabs__header {
    margin-bottom: 0 !important;
    width: 100% !important;
}

.floats_resources .el-tabs__nav-scroll {
    padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .floats_resources .is-active {
    color: var(--text-color) !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .floats_resources .el-tabs__nav {
    height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 0px !important;
}
</style>
