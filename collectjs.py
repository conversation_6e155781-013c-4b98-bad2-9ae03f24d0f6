import os

def get_js_files(folder_path):
    js_files = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    return js_files


def read_js_content(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()


import re

def extract_entries(content):
    # 匹配类似 [数字,"字符串","日期","URL"] 的数组项
    pattern = r'$(\d+),\s*"(.*?)",\s*"(.*?)",\s*"(.*?)"$'
    return re.findall(pattern, content)

def find_chinese_words(text):
    # 正则表达式匹配中文字符，[\u4e00-\u9fa5] 匹配所有中文字符
    pattern = re.compile(r'[\u4e00-\u9fa5]+')
    # 使用findall方法查找所有匹配的项
    chinese_words = pattern.findall(text)
    return chinese_words

def collect_js_entries(folder_path):
    entries = []
    for js_file in get_js_files(folder_path):
        if(js_file.find('node_modules') == -1):
            content = read_js_content(js_file)
            entries.extend(find_chinese_words(content))
    return entries

if __name__ == '__main__':
    # folder = 'D:\kfczip\snapzi\hh'
    folder = 'C:/Users/<USER>/Desktop/work/starbucksKA/src'
    entries = collect_js_entries(folder)
    print(entries)
    import json
    with open('entries.txt','w',encoding='utf-8') as f:
        f.write(json.dumps(entries,ensure_ascii=False))