(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-948bcfec"],{"190e":function(t,e,s){},"1d8b":function(t,e,s){"use strict";var n=s("f796"),a=s.n(n);s.d(e,"c",(function(){return a.a}));var i=s("c7e4"),o=s.n(i);s.d(e,"j",(function(){return o.a}));var r=s("4db3"),c=s.n(r);s.d(e,"d",(function(){return c.a}));var l=s("9495"),d=s.n(l);s.d(e,"g",(function(){return d.a}));var u=s("51ab0"),p=s.n(u);s.d(e,"i",(function(){return p.a}));var h=s("a283"),f=s.n(h);s.d(e,"b",(function(){return f.a}));var m=s("c6cf"),b=s.n(m);s.d(e,"e",(function(){return b.a}));var v=s("4c92"),_=s.n(v);s.d(e,"f",(function(){return _.a}));var g=s("af83"),A=s.n(g);s.d(e,"a",(function(){return A.a}));var j=s("22db"),k=s.n(j);s.d(e,"h",(function(){return k.a}))},"22db":function(t,e,s){t.exports=s.p+"img/tian.815edcd5.svg"},2400:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i})),s.d(e,"c",(function(){return o}));var n=s("b775"),a=function(t){return Object(n["c"])("dsadm/api/json",t,"get_adm_home_info")},i=function(t){return Object(n["c"])("dmb/api/json",t,"get_latest_ds_shop_stat_datas")},o=function(t){return Object(n["c"])("dmb/api/json",t,"upt_ds_shop_stat_datas")}},"2a5c":function(t,e,s){"use strict";s.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:"onlineEcharts"}})},a=[],i=s("ade3"),o=(s("ac6a"),s("673e"),s("7f7f"),s("313e")),r=s.n(o),c=s("ed08"),l={name:"onlineEcharts",components:{},props:{echartsList:{type:Array,default:function(){return[]}}},data:function(){return{detailData:[],chart:{}}},mounted:function(){var t=this;this.transLateData(),this.initChart(),this.__resizeHandler=Object(c["b"])((function(){t.chart&&t.chart.resize()}),100),window.addEventListener("resize",this.__resizeHandler)},beforeDestroy:function(){this.chart&&(window.removeEventListener("resize",this.__resizeHandler),this.chart.dispose(),this.chart=null)},computed:{},watch:{echartsList:{deep:!0,handler:function(){this.transLateData(),this.refreshChart()}}},created:function(){},methods:{switchGenarateOptions:function(t){switch(t){case"餐牌组投放":return"餐牌组投放";case"普通投放":return"普通投放";case"联屏投放":return"联屏投放";default:return t}},refreshChart:function(){this.chart&&this.chart.dispose(),this.initChart()},getTotalShops:function(){return this.detailData.reduce((function(t,e){return t+e.total.shops}),0)},transLateData:function(){this.detailData=this.echartsList.map((function(t){return{schedule:t.name,markTime:t.mark_tm,items:[{type:"餐牌组投放",count:t.note.sub.dmb_plat.btpub_cnt,success:t.note.sub.dmb_plat.succ_shop_cnt,fail:t.note.sub.dmb_plat.fail_shop_cnt},{type:"普通投放",count:t.note.sub.ds_plat.btpub_cnt,success:t.note.sub.ds_plat.succ_shop_cnt,fail:t.note.sub.ds_plat.fail_shop_cnt},{type:"联屏投放",count:t.note.sub.vs_plat.btpub_cnt,success:t.note.sub.vs_plat.succ_shop_cnt,fail:t.note.sub.vs_plat.fail_shop_cnt}],total:{shops:t.note.all_shop_cnt,success:t.note.all_succ_shop_cnt,fail:t.note.all_fail_shop_cnt,btpub:t.note.all_btpub_cnt}}}))},initChart:function(){var t=this;this.chart=r.a.init(this.$el);var e=this.detailData,s=e.length,n={dataZoom:[{type:"inside",startValue:Math.max(s-7,0),endValue:s-1},{type:"slider",top:"100%",showDetail:!1,height:0,fillerColor:"#44e4a4",startValue:Math.max(s-7,0),endValue:s-1,borderColor:"#44e4a4",minSpan:7/(s||1)*100}],tooltip:{trigger:"axis",axisPointer:{type:"shadow",shadowStyle:{color:"#e1ffdc",opacity:.3}},formatter:function(s){var n=e[s[0].dataIndex],a='\n              <div style="\n                  border-radius: 10px;\n                  font-weight: 500;\n                  color: #000;\n                  width:400px;\n                  padding: 10px;\n                  background: rgba(255, 255, 255, 0.5); /* 半透明背景 */\n                  backdrop-filter: blur(20px);\n                  overflow: hidden;\n              ">\n              <div style="margin-bottom:10px; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">'.concat(n.schedule,"</div>\n              <ul background:transparent;margin-top:10px;>");return n.items.forEach((function(e){var s=e.type;a+='\n                 <li style="margin-bottom: 8px;border-radius: 4px;list-style: none; background: rgba(255, 255, 255, 0.8); display: flex; justify-content: space-between; align-items: center;padding:8px;">\n                  <div style="color: #666; font-size: 13px;">\n                  '.concat(t.switchGenarateOptions(s),"（").concat(e.count,'个）\n                  </div>\n                  <div style="text-align: right;">\n                  <div style="color: #73d13d; font-size: 12px;">成功：').concat(e.success,' 家</div>\n                  <div style="color: #ff7875; font-size: 12px;">失败：').concat(e.fail," 家</div>\n                  </div>\n              </li>\n          ")})),a+="</ul></div>",a},backgroundColor:"none",borderColor:"#d2e0f4",borderWidth:0,textStyle:{color:"#333"},extraCssText:"box-shadow: 0 2px 12px rgba(0,0,0,0.1); border-radius: 4px;",padding:[0,0]},xAxis:{boundaryGap:!0,type:"category",data:e.map((function(t){return t.schedule})),axisLine:{lineStyle:{color:"#d2e0f4",width:1}},axisTick:{show:!0,alignWithLabel:!0,lineStyle:{color:"#d2e0f4"}},axisLabel:{color:"#7a869a",fontSize:12,margin:8,formatter:function(t){return t.length>15?t.substring(0,15)+"...":t}},axisPointer:{type:"shadow"}},yAxis:{type:"value",min:0,axisLine:{show:!1},axisTick:{show:!0,length:4,lineStyle:{color:"#d2e0f4"}},splitLine:{show:!0,lineStyle:{color:"#e6effc",type:"solid"}},axisLabel:{color:"#7a869a",fontSize:12,margin:8}},grid:{left:"0%",right:"2.1%",bottom:"2%",top:"2%",containLabel:!0,backgroundColor:"#ffffff"},series:[Object(i["a"])(Object(i["a"])(Object(i["a"])({type:"bar",data:this.detailData.map((function(e){return{value:e.total.shops,dataIndex:t.detailData.indexOf(e)}})),large:!0,itemStyle:{color:"#00c681",borderRadius:[4,4,0,0]},barWidth:"20%",emphasis:{itemStyle:{shadowBlur:6,shadowColor:"rgba(86,96,252,0.3)"}}},"large",!0),"progressive",200),"progressiveThreshold",1e3)]};this.chart.clear(),this.chart.setOption(n,!0)}}},d=l,u=(s("3ed0"),s("2877")),p=Object(u["a"])(d,n,a,!1,null,"249568d2",null);e["default"]=p.exports},"2c3f":function(t,e,s){},3129:function(t,e,s){"use strict";s("2c3f")},"31b6":function(t,e,s){t.exports=s.p+"img/little_work.92fc4a14.svg"},"3ed0":function(t,e,s){"use strict";s("190e")},4678:function(t,e,s){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d7167","./ca.js":"d7167","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"73332","./en-il.js":"73332","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function a(t){var e=i(t);return s(e)}function i(t){var e=n[t];if(!(e+1)){var s=new Error("Cannot find module '"+t+"'");throw s.code="MODULE_NOT_FOUND",s}return e}a.keys=function(){return Object.keys(n)},a.resolve=i,t.exports=a,a.id="4678"},"4c92":function(t,e,s){t.exports=s.p+"img/sources.07ab22dd.svg"},"4db3":function(t,e,s){t.exports=s.p+"img/plug.dce98502.svg"},"51ab0":function(t,e,s){t.exports=s.p+"img/timer.415093f7.svg"},9495:function(t,e,s){t.exports=s.p+"img/tan.fc4b61a6.svg"},a283:function(t,e,s){t.exports=s.p+"img/no.eedca3e8.svg"},af83:function(t,e,s){t.exports=s.p+"img/addStore.34815c5b.svg"},c1a8:function(t,e,s){},c6cf:function(t,e,s){t.exports=s.p+"img/pubShare.992183b4.svg"},c7e4:function(t,e){t.exports="data:image/png;base64,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"},dc1b:function(t,e,s){"use strict";s.r(e);s("7f7f");var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"homePage"},[e("div",{staticClass:"homebody"},[e("div",{staticClass:"homebodyLeft borderRadius",style:{width:(t.$i18n.locale,"81%")}},[e("div",{staticClass:"homebodyLeftTop"},[e("div",{staticClass:"homebodyLeftTop-left2",staticStyle:{"font-size":"0.15rem"}},[t._v("\n          欢迎回来，"+t._s(t.userName)+"\n        ")]),t._v(" "),e("div",{staticClass:"homebodyLeftTop-right2",style:{flex:"en"===t.$i18n.locale?"0 1 42%":"vi"===t.$i18n.locale?"0 1 50%":"0 1 35%"}},[e("span",[t._v("\n            数据更新时间：\n            "+t._s(t.abUpdateTime))]),t._v(" "),e("span",{staticClass:"click-effect2",on:{click:t.handleUpdate}},[t._v(t._s(this.has_running_job?"数据更新中...":"更新数据")+" ")])])]),t._v(" "),e("div",{staticClass:"homebodyLeftList"},[t._m(0),t._v(" "),e("ul",{staticClass:"stats-list"},[e("li",{on:{click:function(e){return t.openPage("AllStores")}}},[e("el-popover",{attrs:{transition:"el-fade-in-linear",placement:"right-end","visible-arrow":"false",trigger:"hover","popper-class":"popover-cla"}},[e("div",{staticStyle:{"font-size":"0.13rem","font-weight":"600"}},[t._v("\n                门店分析\n              ")]),t._v(" "),e("div",{staticClass:"popover-content-cal"},[e("div",{staticClass:"popover-content-cal-title"},[t._v("\n                  营业状态\n                ")]),t._v(" "),t.progressWidths.online||t.progressWidths.offline||t.progressWidths.pendingOperation||t.progressWidths.pause||t.progressWidths.closed?e("div",{staticClass:"popover-content-cal-Progress"},[t.progressWidths.online?e("div",{staticClass:"popover-content-cal-Progress-online",style:{width:t.progressWidths.online,borderRadius:t.borderRadii.online}}):t._e(),t._v(" "),t.progressWidths.offline?e("div",{staticClass:"popover-content-cal-Progress-offline",style:{width:t.progressWidths.offline,borderRadius:t.borderRadii.offline}}):t._e(),t._v(" "),t.progressWidths.pendingOperation?e("div",{staticClass:"popover-content-cal-Progress-pendingOperation",style:{width:t.progressWidths.pendingOperation,borderRadius:t.borderRadii.pendingOperation}}):t._e(),t._v(" "),t.progressWidths.pause?e("div",{staticClass:"popover-content-cal-Progress-stop_business",style:{width:t.progressWidths.pause,borderRadius:t.borderRadii.pause}}):t._e(),t._v(" "),t.progressWidths.closed?e("div",{staticClass:"popover-content-cal-Progress-closed",style:{width:t.progressWidths.closed,borderRadius:t.borderRadii.closed}}):t._e()]):t._e(),t._v(" "),e("div",{staticClass:"popover-content-cal-content"},[e("div",{staticClass:"status-item"},[e("div",{staticClass:"color-block online"}),t._v(" "),e("span",[t._v("营业中-在线\n                      "),e("el-tooltip",{attrs:{placement:"top",content:"门店处于正常营业时段，可提供服务。日常闭店时间（如夜间）不影响此状态，仍视为正常营业。","popper-class":"tooltip-cla"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("\n                      ：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.shop_open_online_cnt)+" 家\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item"},[e("div",{staticClass:"color-block offline"}),t._v(" "),e("span",[t._v("营业中-正常离线\n                      "),e("el-tooltip",{attrs:{placement:"top",content:"高铁站、机场店等运营方式为离线运营的门店。","popper-class":"tooltip-cla"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("\n                      ：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.shop_open_offline_normal_cnt)+" 家\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item"},[e("div",{staticClass:"color-block pending"}),t._v(" "),e("span",[t._v("未开店\n                      "),e("el-tooltip",{attrs:{placement:"top",content:"尚未正式开放，处于筹备阶段（如装修、招聘等），未来将开业的门店。","popper-class":"tooltip-cla"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("\n                      ：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.shop_wait_cnt)+" 家\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item"},[e("div",{staticClass:"color-block stop_business"}),t._v(" "),e("span",[t._v("暂停营业\n                      "),e("el-tooltip",{attrs:{placement:"top",content:"已开店，但临时暂停营业的门店。","popper-class":"tooltip-cla"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("\n                      ：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.shop_pause_cnt)+" 家\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item"},[e("div",{staticClass:"color-block closed"}),t._v(" "),e("span",[t._v("已闭店\n                      "),e("el-tooltip",{attrs:{placement:"top",content:"终止营业，永久闭店的门店。","popper-class":"tooltip-cla"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("\n                      ：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.shop_close_cnt)+" 家\n                    ")])])])]),t._v(" "),e("div",{staticClass:"stats-list-box",attrs:{slot:"reference"},slot:"reference"},[e("div",{staticClass:"circle-icon"},[e("i",{staticClass:"el-icon-s-shop"})]),t._v(" "),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-content-num"},[t._v("\n                    "+t._s(t.shop_And_ds.shop_all_cnt)+"\n                    "),e("span",[t._v("家")])]),t._v(" "),e("div",{staticClass:"stats-content-title"},[t._v("\n                    门店总数\n                  ")])])])])],1),t._v(" "),e("li",[e("div",{staticClass:"stats-list-box"},[t._m(1),t._v(" "),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-content-num"},[t._v("\n                  "+t._s(t.shop_And_ds.shop_open_offline_abnormal_cnt)+"\n                  "),e("span",[t._v(" 家")])]),t._v(" "),e("div",{staticClass:"stats-content-title"},[t._v("\n                  异常离线数\n                  "),e("el-tooltip",{attrs:{"popper-class":"tooltip-cla",placement:"top",content:"【营业中"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})])],1)])])]),t._v(" "),e("li",{on:{click:function(e){return t.openPage("totalDevices")}}},[e("el-popover",{attrs:{transition:"el-fade-in-linear",placement:"right-start","visible-arrow":"false",trigger:"hover","popper-class":"popover-cla2"}},[e("div",{staticStyle:{"font-size":"0.13rem","font-weight":"600"}},[t._v("\n                设备分析\n              ")]),t._v(" "),e("div",{staticClass:"popover-content-cal"},[e("div",{staticClass:"popover-content-cal-title"},[t._v("\n                  设备状态\n                ")]),t._v(" "),t.progressWidths2.online2||t.progressWidths2.offline2||t.progressWidths2.abnormal?e("div",{staticClass:"popover-content-cal-Progress"},[t.progressWidths2.online2?e("div",{staticClass:"popover-content-cal-Progress-online2",style:{width:t.progressWidths2.online2,borderRadius:t.borderRadii2.online2}}):t._e(),t._v(" "),t.progressWidths2.offline2?e("div",{staticClass:"popover-content-cal-Progress-offline2",style:{width:t.progressWidths2.offline2,borderRadius:t.borderRadii2.offline2}}):t._e(),t._v(" "),t.progressWidths2.abnormal?e("div",{staticClass:"popover-content-cal-Progress-abnormal",style:{width:t.progressWidths2.abnormal,borderRadius:t.borderRadii2.abnormal}}):t._e()]):t._e(),t._v(" "),e("div",{staticClass:"popover-content-cal-content2"},[e("div",{staticClass:"status-item2"},[e("div",{staticClass:"color-block online"}),t._v(" "),e("span",[t._v("在线\n                      "),e("el-tooltip",{attrs:{content:"当前设备的在线数量。","popper-class":"tooltip-cla",placement:"top"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.ds_online_cnt)+" 台\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item2"},[e("div",{staticClass:"color-block pending2"}),t._v(" "),e("span",[t._v("正常离线\n                      "),e("el-tooltip",{attrs:{content:"离线运营门店的设备及待营业","popper-class":"tooltip-cla",placement:"top"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.ds_offline_normal_cnt)+" 台\n                    ")])]),t._v(" "),e("div",{staticClass:"status-item2"},[e("div",{staticClass:"color-block abnormal"}),t._v(" "),e("span",[t._v("异常离线\n                      "),e("el-tooltip",{attrs:{content:"【营业中-在线】的门店，正常营业时间段内异常离线的设备数量。","popper-class":"tooltip-cla",placement:"top"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})]),t._v("：")],1),t._v(" "),e("div",{staticClass:"count"},[t._v("\n                      "+t._s(t.shop_And_ds.ds_offline_abnormal_cnt)+" 台\n                    ")])])])]),t._v(" "),e("div",{staticClass:"stats-list-box",attrs:{slot:"reference"},slot:"reference"},[e("div",{staticClass:"circle-icon"},[e("i",{staticClass:"el-icon-monitor"})]),t._v(" "),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-content-num"},[t._v("\n                    "+t._s(t.shop_And_ds.ds_all_cnt)+"\n                    "),e("span",[t._v("台")])]),t._v(" "),e("div",{staticClass:"stats-content-title"},[t._v("\n                    设备总数\n                  ")])])])])],1),t._v(" "),e("li",{on:{click:function(e){return t.openPage("networkExceptions")}}},[e("div",{staticClass:"stats-list-box"},[t._m(2),t._v(" "),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-content-num2"},[t._v("\n                  "+t._s(t.shop_And_ds.ds_offline_abnormal_cnt)+"\n                  "),e("span",[t._v("台")])]),t._v(" "),e("div",{staticClass:"stats-content-title"},[t._v("\n                  异常离线数\n                  "),e("el-tooltip",{attrs:{"popper-class":"tooltip-cla",content:"\n                  【营业中-在线】的门店，正常营业时间段内异常离线的设备数量。",placement:"top"}},[e("i",{staticClass:"el-icon-warning-outline tip-icon"})])],1)])])])])]),t._v(" "),e("div",{staticClass:"homebodyLeftEchartsOnline"},[e("div",{staticClass:"EchartsTitle"},[e("div",{staticClass:"homebodyLeftTop-left"},[t._v("\n            投放运营数据\n          ")]),t._v(" "),e("div",{staticClass:"homebodyLeftTop-right"},[e("span",[t._v("数据更新时间："+t._s(t.updateTime))]),t._v(" "),e("span",{staticClass:"click-effect",on:{click:t.openMarkScheduleDialog}},[t._v("标记档期")]),t._v(" "),e("span",{staticClass:"click-effect",on:{click:t.openDownloadDataDialog}},[t._v("下载运营数据")])])]),t._v(" "),e("div",{staticClass:"EchartsOnline"},[e("onlineEcharts",{attrs:{echartsList:t.echartsList}})],1)])]),t._v(" "),e("div",{staticClass:"homebodyright",style:{width:(t.$i18n.locale,"18%")}},[e("div",{staticClass:"homebodyrightTop"},[e("div",{staticClass:"homebodyrightTop-title"},[t._v("\n          快捷入口\n        ")]),t._v(" "),e("div",{staticClass:"homebodyrightTop-list"},[e("ul",t._l(t.homeTop,(function(s,n){return e("li",{key:n,on:{click:function(e){return e.stopPropagation(),t.toGo(n)}}},[e("img",{attrs:{src:t.topimgs[n]}}),t._v(" "),e("p",[t._v(t._s(s))])])})),0)])]),t._v(" "),e("div",{staticClass:"homebodyrightBottom"},[e("div",{staticClass:"homebodyrightBottom-title"},[t._v("\n          预警\n        ")]),t._v(" "),e("div",{staticClass:"homebodyrightBottom-list"},[e("ul",t._l(t.mainList,(function(n,a){return e("li",{key:a,class:t.liIndex==a?"warning-active":"",on:{click:function(e){return e.stopPropagation(),t.toScreenList(n,a)}}},[e("div",{staticClass:"warning-top"},[e("img",{attrs:{src:t.homeImg[a]}}),t._v(" "),e("p",[t._v("\n                  "+t._s(t.$t(n.tab))+"\n                  "+t._s(t.$t(n.timeType))+"\n                ")])]),t._v(" "),e("div",{staticClass:"warning-bottom"},[e("div",[e("img",{attrs:{src:s("31b6")}}),t._v(" "),e("span",[t._v(t._s(n.cnt))])])])])})),0)])])])]),t._v(" "),e("el-dialog",{attrs:{title:"标记档期",visible:t.markScheduleDialogVisible,width:"25%",center:"","custom-class":"operation-dialog","before-close":t.handleCloseMarkScheduleDialog},on:{"update:visible":function(e){t.markScheduleDialogVisible=e}}},[e("el-form",{ref:"markScheduleForm",attrs:{model:t.markScheduleForm,rules:t.markScheduleRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"档期名称：",prop:"markScheduleName","label-width":(t.$i18n.locale,"150px")}},[e("el-input",{attrs:{placeholder:"请输入档期名称",type:"text",maxlength:"15","show-word-limit":""},model:{value:t.markScheduleForm.markScheduleName,callback:function(e){t.$set(t.markScheduleForm,"markScheduleName",e)},expression:"markScheduleForm.markScheduleName"}})],1),t._v(" "),e("el-form-item",{attrs:{label:"请输入档期名称",prop:"","label-width":(t.$i18n.locale,"150px")}},[e("el-radio-group",{on:{change:t.changeResource},model:{value:t.markScheduleForm.markScheduleMode,callback:function(e){t.$set(t.markScheduleForm,"markScheduleMode",e)},expression:"markScheduleForm.markScheduleMode"}},[e("el-radio",{attrs:{label:1}},[t._v("立即手动标记档期")]),t._v(" "),e("el-radio",{attrs:{label:2}},[t._v("系统定时标记档期")])],1)],1),t._v(" "),2===t.markScheduleForm.markScheduleMode?e("el-form-item",{attrs:{label:"标记时间：",prop:"markScheduleTime","label-width":(t.$i18n.locale,"150px")}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetime",placeholder:"选择日期时间"},model:{value:t.markScheduleForm.markScheduleTime,callback:function(e){t.$set(t.markScheduleForm,"markScheduleTime",e)},expression:"markScheduleForm.markScheduleTime"}})],1):t._e()],1),t._v(" "),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.handleCloseMarkScheduleDialog}},[t._v("取消")]),t._v(" "),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.confirMarkSchedule("markScheduleForm")}}},[t._v("确认")])],1)],1),t._v(" "),e("el-dialog",{attrs:{title:"下载运营数据",visible:t.operationalDataDialogVisible,width:"25%",center:"","custom-class":"operation-dialog"},on:{"update:visible":function(e){t.operationalDataDialogVisible=e}}},[e("el-form",{ref:"operationalDataForm",attrs:{model:t.operationalDataForm,rules:t.operationalDataRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"档期名称：",prop:"scheduleName","label-width":(t.$i18n.locale,"150px")}},[e("el-select",{attrs:{clearable:"",placeholder:"请选择档期"},model:{value:t.operationalDataForm.scheduleName,callback:function(e){t.$set(t.operationalDataForm,"scheduleName",e)},expression:"operationalDataForm.scheduleName"}},t._l(t.optionEchartsList,(function(t){return e("el-option",{key:t.admin_uid,attrs:{label:t.name,value:t.name}})})),1)],1)],1),t._v(" "),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.operationalDataDialogVisible=!1}}},[t._v("取消")]),t._v(" "),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.downloadData("operationalDataForm")}}},[t._v("确认")])],1)],1)],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"homebodyLeftList-top"},[e("div",{staticClass:"homebodyLeftList-top-left"},[t._v("\n            门店数据\n          ")]),t._v(" "),e("div",{staticClass:"homebodyLeftList-top-right"},[t._v("\n            设备数据\n          ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"circle-icon"},[e("i",{staticClass:"el-icon-s-shop",staticStyle:{color:"red"}})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"circle-icon"},[e("i",{staticClass:"el-icon-monitor",staticStyle:{color:"red"}})])}],i=(s("8e6e"),s("ade3")),o=(s("4917"),s("7514"),s("6762"),s("2fdb"),s("2909")),r=(s("456d"),s("ac6a"),s("96cf"),s("1da1")),c=s("c1df"),l=s.n(c),d=s("2a5c"),u=s("2400"),p=s("e7ec"),h=s("126d"),f=s("1d8b");function m(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,n)}return s}function b(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?m(Object(s),!0).forEach((function(e){Object(i["a"])(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):m(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}var v={components:{onlineEcharts:d["default"]},watch:{"$i18n.locale":function(){this.setFormRules(),this.dataUpdataText="更新数据"},"$store.state.user.canRefresh":function(t,e){t&&(this.get_latest_ds_shop_stat_datas(),this.getInfoLev(),this.$store.state.user.canRefresh=!1)}},data:function(){return{userName:"",storeCount:9111e4,deviceCount:2e6,abnormalStoreCount:2712311,abnormalDeviceCount:48e7,homeTop:["快速发布","上传资源","新增门店","内容制作"],homeImg:[f["c"],f["j"],f["d"],f["g"],f["i"],f["b"]],topimgs:[f["e"],f["f"],f["a"],f["h"]],mainList:[],liIndex:0,bottomList:[],bottomList2:[],bottomList3:[],markScheduleDialogVisible:!1,markScheduleForm:{markScheduleName:"",markScheduleMode:"",markScheduleTime:""},markScheduleRules:{},operationalDataDialogVisible:!1,operationalDataForm:{scheduleName:""},operationalDataRules:{scheduleName:[]},echartsList:[],optionEchartsList:[],updateTime:"",isShowEcharts:!0,isUpdating:!1,clickTimer:null,dataUpdataText:"更新数据",abUpdateTime:"",has_running_job:0,shop_And_ds:{shop_all_cnt:0,shop_wait_cnt:0,shop_close_cnt:0,shop_pause_cnt:0,shop_open_cnt:0,shop_open_online_cnt:0,shop_open_offline_cnt:0,shop_open_offline_normal_cnt:0,shop_open_offline_abnormal_cnt:0,ds_all_cnt:0,ds_online_cnt:0,ds_offline_cnt:0,ds_offline_normal_cnt:0,ds_offline_abnormal_cnt:0}}},created:function(){this.userName=this.$store.state.user.user.username,this.$store.dispatch("GetInfo").then((function(t){t.data[0].curent_branch})),this.get_latest_ds_shop_stat_datas()},computed:{progressWidths:function(){return{online:this.calProgress("online"),offline:this.calProgress("offline"),pendingOperation:this.calProgress("pendingOperation"),closed:this.calProgress("closed"),pause:this.calProgress("pause")}},borderRadii:function(){return{online:this.changeBorderRadius("online"),offline:this.changeBorderRadius("offline"),pendingOperation:this.changeBorderRadius("pendingOperation"),closed:this.changeBorderRadius("closed"),pause:this.changeBorderRadius("pause")}},progressWidths2:function(){return{online2:this.calProgress2("online2"),offline2:this.calProgress2("offline2"),abnormal:this.calProgress2("abnormal")}},borderRadii2:function(){return{online2:this.changeBorderRadius2("online2"),offline2:this.changeBorderRadius2("offline2"),abnormal:this.changeBorderRadius2("abnormal")}}},mounted:function(){var t=this;this.$nextTick((function(){t.setFormRules(),t.getInfoLev(),t.get_sche_stat_job_list_fn()}))},methods:{get_latest_ds_shop_stat_datas:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,s,n=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(u["b"])({});case 3:e=t.sent,"ok"===e.rst&&(this.abUpdateTime=e.data[0].update_tm,this.has_running_job=e.data[0].has_running_job,s=e.data[0].note,Object.keys(this.shop_And_ds).forEach((function(t){s.hasOwnProperty(t)&&(n.shop_And_ds[t]=s[t])}))),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取最新数据失败:",t.t0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),handleUpdate:function(){var t=this;this.clickTimer||(this.clickTimer=setTimeout((function(){clearTimeout(t.clickTimer),t.clickTimer=null}),500),this.has_running_job>0?this.$message.warning("数据正在获取中..."):this.updateData())},updateData:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(u["c"])({});case 3:if(e=t.sent,"ok"!==e.rst){t.next=10;break}return e.data[0],t.next=8,this.get_latest_ds_shop_stat_datas();case 8:t.next=12;break;case 10:return this.$message.warning("数据正在获取中..."),t.abrupt("return");case 12:t.next=17;break;case 14:t.prev=14,t.t0=t["catch"](0),this.$message.error("数据更新失败");case 17:return t.prev=17,t.finish(17);case 19:case"end":return t.stop()}}),t,this,[[0,14,17,19]])})));function e(){return t.apply(this,arguments)}return e}(),openPage:function(t){switch(t){case"AllStores":this.$router.push({path:"/shopManage/storelist"});break;case"totalDevices":this.$router.push({path:"deviceManage/screenGroup"});break;case"networkExceptions":this.$router.push({path:"deviceManage/abnormalDetail"});break}},get_sche_stat_job_list_fn:function(){var t=this,e={};Object(p["b"])(e).then((function(e){if("ok"==e.rst){var s=e.data[0].content;t.echartsList=s.reverse(),t.optionEchartsList=Object(o["a"])(s).reverse(),t.updateTime=t.echartsList[t.echartsList.length-1].mark_tm}else t.echartsList=[],t.optionEchartsList=[],t.$message.warning(e.error_msg)}))},setFormRules:function(){this.markScheduleRules={markScheduleName:[{required:!0,message:"请输入档期名称",trigger:"blur"}],markScheduleTime:[{required:!0,message:"请选择档期时间",trigger:"blur"}]},this.operationalDataRules={scheduleName:[{required:!0,message:"请选择档期",trigger:"change"}]}},calProgress2:function(t){var e=this.shop_And_ds.ds_online_cnt+this.shop_And_ds.ds_offline_normal_cnt+this.shop_And_ds.ds_offline_abnormal_cnt,s=this.shop_And_ds.ds_online_cnt,n=this.shop_And_ds.ds_offline_normal_cnt,a=this.shop_And_ds.ds_offline_abnormal_cnt;if("online2"===t){if(!s)return 0;var i=(s/e*100).toFixed(2);return"calc(".concat(i,"% - 2px)")}if("offline2"===t){if(!n)return 0;var o=(n/e*100).toFixed(2);return"calc(".concat(o,"% - 4px)")}if("abnormal"===t){if(!a)return 0;var r=(a/e*100).toFixed(2);return"calc(".concat(r,"% - 2px)")}},changeBorderRadius:function(t){var e=this,s=["online","offline","pendingOperation","pause","closed"],n=s.filter((function(t){var s=e.calProgress(t);return 0!==s}));if(!n.includes(t))return"0";switch(t){case"online":if(1===n.length)return"0.1rem";if("online"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";break;case"offline":if(1===n.length)return"0.1rem";if(n.indexOf("offline")>0&&n.indexOf("offline")<n.length-1)return"0";if("offline"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";if("offline"===n[n.length-1]&&n.length>1)return"0 0.1rem 0.1rem 0";break;case"pendingOperation":if(1===n.length)return"0.1rem";if(n.indexOf("pendingOperation")>0&&n.indexOf("pendingOperation")<n.length-1)return"0";if("pendingOperation"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";if("pendingOperation"===n[n.length-1]&&n.length>1)return"0 0.1rem 0.1rem 0";break;case"pause":if(1===n.length)return"0.1rem";if(n.indexOf("pause")>0&&n.indexOf("pause")<n.length-1)return"0";if("pause"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";if("pause"===n[n.length-1]&&n.length>1)return"0 0.1rem 0.1rem 0";break;case"closed":if(1===n.length)return"0.1rem";if("closed"===n[n.length-1]&&n.length>1)return"0 0.1rem 0.1rem 0";break}return"0"},changeBorderRadius2:function(t){var e=this,s=["online2","offline2","abnormal"],n=s.filter((function(t){var s=e.calProgress2(t);return 0!==s}));if(!n.includes(t))return"0";switch(t){case"online2":if(1===n.length)return"0.1rem";if("online2"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";break;case"offline2":if(1===n.length)return"0.1rem";if(n.indexOf("offline2")>0&&n.indexOf("offline2")<n.length-1)return"0";if("offline2"===n[0]&&n.length>1)return"0.1rem 0 0 0.1rem";if("offline2"===n[n.length-1]&&n.length>1)return"0 0.1rem  0.1rem 0";break;case"abnormal":if(1===n.length)return"0.1rem";if("abnormal"===n[n.length-1]&&n.length>1)return"0 0.1rem 0.1rem 0";break}return"0"},calProgress:function(t){var e=this.shop_And_ds.shop_open_online_cnt+this.shop_And_ds.shop_open_offline_normal_cnt+this.shop_And_ds.shop_wait_cnt+this.shop_And_ds.shop_close_cnt+this.shop_And_ds.shop_pause_cnt,s=this.shop_And_ds.shop_open_online_cnt,n=this.shop_And_ds.shop_open_offline_normal_cnt,a=this.shop_And_ds.shop_wait_cnt,i=this.shop_And_ds.shop_close_cnt,o=this.shop_And_ds.shop_pause_cnt;if("online"===t){if(!s)return 0;var r=(s/e*100).toFixed(2);return"calc(".concat(r,"% - 2px)")}if("offline"===t){if(!n)return 0;var c=(n/e*100).toFixed(2);return"calc(".concat(c,"% - 4px)")}if("pendingOperation"===t){if(!a)return 0;var l=(a/e*100).toFixed(2);return"calc(".concat(l,"% - 4px)")}if("pause"===t){if(!o)return 0;var d=(o/e*100).toFixed(2);return"calc(".concat(d,"% - 4px)")}if("closed"===t){if(!i)return 0;var u=(i/e*100).toFixed(2);return"calc(".concat(u,"% - 2px)")}},changeResource:function(t){},openMarkScheduleDialog:function(){this.markScheduleDialogVisible=!0,this.markScheduleForm={}},handleCloseMarkScheduleDialog:function(){this.markScheduleForm={},this.$refs["markScheduleForm"]&&this.$refs["markScheduleForm"].clearValidate(),this.markScheduleDialogVisible=!1},confirMarkSchedule:function(t){var e=this;this.$refs[t].validate((function(t){if(t){var s={name:e.markScheduleForm.markScheduleName,mark_tm:""};1===e.markScheduleForm.markScheduleMode?s.mark_tm=l()().format("YYYY-MM-DD HH:mm:ss"):s.mark_tm=e.markScheduleForm.markScheduleTime,Object(p["a"])(s).then((function(t){"ok"==t.rst?(e.$message({type:"success",message:"档期创建成功"}),e.get_sche_stat_job_list_fn()):e.$message({type:"error",message:t.error_msg}),e.markScheduleDialogVisible=!1}))}}))},openDownloadDataDialog:function(){this.operationalDataForm.scheduleName="",this.operationalDataDialogVisible=!0},downloadData:function(t){var e=this;this.$refs[t].validate((function(t){if(t){var s=e.echartsList.find((function(t){return t.name===e.operationalDataForm.scheduleName}));if(!s)return void e.$message({message:"请选择有效数据",type:"warning"});var n=s.note.download_url,a=n.substring(n.lastIndexOf("/")+1),i=document.createElement("a");document.body.appendChild(i),i.style.display="none",i.href=n,i.download=a,setTimeout((function(){i.click(),URL.revokeObjectURL(i.href),document.body.removeChild(i),e.operationalDataDialogVisible=!1}),100)}}))},getBgColor:function(t){var e=["#e4edff","#eee5fe","#ffe8ea","#c7f0ed"];return e[t%4]},extractTimeUnit:function(t){var e=/(\d+)(小时|分钟|秒|M)/,s=t.match(e);if(s){var n=s[1],a=s[2];return{number:n,unit:a}}return null},translateBottomList:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],s=[];return e&&e.length?(e.forEach((function(e){for(var n in h["h"]){if(e.label&&h["h"][n]===e.label){e.changeLable="h."+n;break}if(e.tab&&e.tab.includes(h["h"][n])){switch(e.changeTab="h."+n,e.changeTab){case"h.offline":e.changeTab="h.deviceOffline";break;case"h.earlyWarning":e.changeTab="h.storageWarning";break}var a=t.extractTimeUnit(e.tab);if(a){a.number;var i=a.unit;switch(i){case"小时":e.timeType="h.Hours";break;case"分钟":e.timeType="h.Minutes";break;case"秒":e.timeType="h.Seconds";break;case"M":e.timeType="h.M"}}break}}s.push(b({},e))})),s):s},toGo:function(t){var e=["/deploy/quickDeploy","/contentCenter/srcfiles","/shopManage/storelist","/contentCenter"];2==t&&sessionStorage.setItem("needAddShop","1"),this.$router.push({path:e[t]})},clickLis:function(t){this.liIndex=t},get_adm_home_info:function(){var t=this,e={pgid:localStorage.getItem("group_id"),filter_data:"base",force:1,rst_range:"mgmt"};Object(u["a"])(e).then((function(e){if("ok"==e.rst)for(var s in console.log(e,"res"),t.bottomList3=e.data[0].home_base,t.bottomList3)""!==t.bottomList3[s]&&void 0!==t.bottomList3[s]&&0!==t.bottomList3[s]||(t.bottomList3[s]=0)}))},getInfoLev:function(){var t=this,e={pgid:localStorage.getItem("group_id"),filter_data:"alert",force:1};Object(u["a"])(e).then((function(e){"ok"==e.rst&&(console.log("res",e.data[0].home_alert),t.mainList=e["data"][0]["home_alert"])}))},toShopList:function(t,e){this.liIndex=e},toScreenList:function(t,e){this.liIndex=e;console.log(t,"item"),this.$router.push({path:"/deviceManage/screenGroup",query:{inonline:"false",fcond:JSON.stringify(t.fcond)}})}}},_=v,g=(s("f045"),s("3129"),s("2877")),A=Object(g["a"])(_,n,a,!1,null,"99a36aba",null);e["default"]=A.exports},e7ec:function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"a",(function(){return i}));var n=s("b775"),a=function(t){return Object(n["c"])("/dmb/api/json",t,"get_sche_stat_job_list")},i=function(t){return Object(n["c"])("/dmb/api/json",t,"create_sche_stat_job")}},f045:function(t,e,s){"use strict";s("c1a8")},f796:function(t,e){t.exports="data:image/png;base64,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"}}]);