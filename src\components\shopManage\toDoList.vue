<template>
    <div class="to_do_list" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"  element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <!-- 列表区 -->
        <div class="table_wrap" :style="{height:autoHeight.height}">
            <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"  :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px','text-align':'center'}" :cell-style="{'text-align':'center'}">
                <el-table-column prop="isUnified"  label="" width="50" >
                    <template slot-scope="scope">
                        <div class="angle_mark_wrap" v-if="scope.row.isUnified">
                            <div class="angle_mark mark_red"></div>
                            <div class="angle_mark_content">NEW</div>
                        </div>
                        <div class="angle_mark_wrap" v-else>
                            <div class="angle_mark mark_green"></div>
                            <div class="angle_mark_content">变更</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="No." width="70" >
                    <template slot-scope="scope">
                        {{scope.$index+1}}
                    </template>
                </el-table-column>
                <el-table-column prop="storecode"  label="餐厅编号" width=""></el-table-column>
                <el-table-column prop="name"  label="餐厅名称" width=""></el-table-column>
                <el-table-column prop="opsmarketname"  label="营运市场" width="" ></el-table-column>
                <!-- <el-table-column prop="name"  label="城市" width=""></el-table-column> -->
                <el-table-column prop="storetypename"  label="类型" width=""></el-table-column>
                <el-table-column prop="displaycnt"  label="屏幕数量" width=""></el-table-column>
                <el-table-column label="变更项" width="">
                    <template slot-scope="scope">
                        <el-popover  trigger="click" width="301" placement="bottom" :ref="`popover${scope.$index}`" >
                             <!-- scope.row.xxxx 获取该行数据 -->
                            <!-- 标题 -->
                            <div class="flex popover_header"  style="">
                                <div style="font-weight:bold">变更项</div>
                                <div @click.stop="closePopover(scope.$index)"><i class="el-icon-close" style="font-size:16px;cursor:pointer;"></i></div>
                            </div>
                            <!-- 新增项 -->
                            <div class="newly_added">
                                <div class="newly_added_header">
                                    新增项
                                </div>
                                <div class="newly_added_content flex">
                                    <div class="flex" style="height:18px;align-items:center;margin-right:20px;margin-bottom:10px">
                                        <!-- <img src="../../assets/img/checked.png" alt="" style="width:13px;height:13px"> -->
                                         <el-checkbox-group v-model="changeItem.addItem">
                                            <el-checkbox label="复选框 A"></el-checkbox>
                                            <el-checkbox label="复选框 B"></el-checkbox>
                                        </el-checkbox-group>
                                        <!-- <span style="font-size:13px;margin-left:3px">{{scope.row.num}}</span> -->
                                    </div>
                                </div>
                            </div>
                            <!-- 移除项 -->
                            <div class="remove_item">
                                <div class="remove_item_header">
                                    移除项
                                </div>
                                <div class="remove_item_content">
                                    <div class="flex" style="height:18px;align-items:center;margin-right:20px;margin-bottom:10px">
                                        <!-- <img src="../../assets/img/nochecked.png" alt="" style="width:13px;height:13px"> -->
                                        <!-- <span style="font-size:13px;margin-left:3px">{{scope.row.num}}</span> -->
                                        <el-checkbox-group v-model="changeItem.removeItem">
                                            <el-checkbox label="复选框 A"></el-checkbox>
                                            <el-checkbox label="复选框 B"></el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                            </div>
                            <!-- 触发popover的节点 -->
                            <div slot="reference" class="name-wrapper" style="cursor:pointer;">
                                <!-- {{ scope.row.name }} -->
                            </div>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column  label="系统匹配" width="" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div v-if="scope.row.data_from" class="sys_matching" style="color: rgba(39, 177, 126,1);">
                            <span style="cursor:pointer" @click="handleShow(scope.row)">{{scope.row.data_from}}</span>
                        </div>
                        <div v-else class="sys_matching" style="color:#606266;">未找到匹配门店</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作"  width="150" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="handleShow(scope.row)"   size="mini">查看</el-button>
                        <el-button @click.native.prevent="handleConfirm(scope.row)" type="primary" size="mini">确认</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="to_do_list_footer">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :pager-count='5'
                :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper"
                :total="totalNum">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {getNewShopInfo,opeingnewstoremgmt} from "../../api/shopManage/shop"
import { param } from '../../utils';
import {EventBus} from "../../utils/eventBus"

export default {
    components: {

    },
    data() {
        return {
            loading:false,
            currentPage:1, //页码
            totalNum:20, //总数据数量
            pageSize:10,
            tableData: [],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
            changeItem:{
                addItem:['复选框 A'],
                removeItem:['复选框 A'],
            }
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getInfo();
    },
    mounted() {

    },
    methods: {
              // 获取列表信息
        getInfo(){
            const params = {
                 "classModel": "OpeningNewStore",
                // "sort": "",//非必要，排序规则，storecode,createdAtS
                "page": this.currentPage - 1, //起始页码,
                "size": 10, //每页数据量,
                "status": "waiting", 
                // "blurry": "",//搜索门店编号或者名称模糊查找　
            }
            getNewShopInfo(params).then(res=>{
                console.log(res,'获取数据res代办');
                if(res.rst=='ok'){
                    this.tableData = res.data[0].content;
                    this.totalNum = res.data[0].totalElements
                }
            })
        },
        // 查看
        handleShow(row){
            console.log(row,'xxxc');
            this.$router.push({
                path:'/shopManage/shopAddEdit',
                 query:{
                    edit:true,
                    row:row
                }
            });
        },
        // 确认
        handleConfirm(row){
            this.loading = true;
            console.log(row,'000');
            const params = {
                act:"conform",
                id:row.id
            }
            console.log(params,'xxxxxxxxxx');
            opeingnewstoremgmt(params).then(res=>{
                if(res.rst=="ok"){
                    this.$message.success("成功");
                    this.getInfo()
                    this.loading = false;
                    this.$forceUpdate()
                    EventBus.$emit('todoSth')

                }else{
                    this.loading = false;
                    this.$message.info("error");
                }
            })

        },
        // 关闭Popover
        closePopover(index){
            this.$refs[`popover${index}`].doClose()
        },
        //页码改变
        handleCurrentChange(val) {
            console.log(`现在是第${val}页`);
            this.currentPage = val;
            this.getInfo()
        },
        handleSizeChange(val){
            console.log(`每页${val}条`);
            this.pageSize = val;
            this.getInfo()
        },
        // 列表区高度自适应
        getHeight(){
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 168 + 'px';
            this.autoHeight.heightNum = windowHeight - 168;
        },
    },
    destroyed () {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style scoped>
    *{
        box-sizing: border-box;
    }
    .to_do_list{
        width: 100%;
        padding: 11px;
    }/* 角标 */
    .angle_mark{
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-right: 40px solid transparent;
        z-index: 9;
    }
    .mark_red{
        border-top: 40px solid red;
    }
    .mark_green{
        border-top: 40px solid green;
    }
    .angle_mark_content{
        position: absolute;
        width: 40px;
        height: 40px;
        z-index: 10;
        text-align: center;
        line-height: 40px;
        top: -5px;
        left: -6px;
        transform: rotate(-45deg) scale(.8);
        font-size: 12px;
        color: #fff;
    }
    .popover_header{
        justify-content: space-between;
        padding:10px 0;
        border-bottom:1px solid rgba(229, 229, 229, 1);
    }
    .newly_added_header,.remove_item_header{
        font-size: 12px;
        color: rgba(80, 80, 80, 1);
        margin-bottom: 11px;
        margin-top: 11px;
    }
    .newly_added_content,.remove_item_content{
        width: 100%;
        height: 98px;
        border: 1px solid rgba(229, 229, 229, 1);
        flex-wrap: wrap;
        padding: 8px;
        overflow-y: auto;
    }
    .remove_item_content{
        /* margin-bottom: 40px; */
    }
    .sys_matching{
        width:100%;
        font-size: 13px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    /* 底部页码区 */
    .to_do_list_footer{
        box-sizing: border-box;
        width: 100%;
        height: 66px;
        font-size: 14px;
        padding-top: 17px;
        text-align: right;
        padding-right: 20px;
    }
</style>
<style>
    .el-popover{
        overflow-y: visible !important;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,.el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: var(--base-color) !important;
        border-color:var(--base-color) !important;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color: rgba(128, 128, 128, 1) !important;
    }
</style>
