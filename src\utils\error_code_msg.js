let errors_msg = {
    // For Partner Error Code Define
    usernameRequired: "用户名不能为空",
    usernameLengthError: "用户名长度不符合要求",
    usernameExists: "用户名已被占用",
    emailRequired: "邮箱不能为空",
    emailExists: "邮箱已被注册",
    freeUserDenied: "免费用户权限不足",
    // File sync service error_code definition
    invalidParameters: "无效请求参数",
    fileAlreadyExists: "文件已存在",

    // personal file service error_code definition
    methodMustBePost: "必须使用POST方法",
    unsupportedMethod: "不支持的HTTP方法",
    uploadFailed: "上传失败",
    methodNotDefined: "未定义的方法",
    // Billing error_code definition
    promotionIdRequired: "缺少必要参数",
    invalidPromotionId: "促销ID错误",
    noPurchaseInfo: "无购买信息",
    dbInsertError: "数据库写入异常",
    noActivePromotions: "当前无可用促销活动",
    invalidPromotionTime: "不在活动有效时间内",

    // Comments error_code definition
    invalidInputParams: "输入参数验证失败",
    invalidPOI: "无效的POI信息",
    commentTooLong: "评论内容过长",
    invalidLikeValue: "点赞值非法",
    duplicateComment: "同一用户不可重复提交相同评论",
    requestTooFrequent: "操作间隔过短",
    missingContent: "内容缺失",
    dataException: "数据异常",
    duplicateVote: "投票不可重复提交",

    // Pushout API Error Code
    invalidInputParams: "输入参数错误",
    taskSaveFailed: "消息任务保存失败",
    tokenExpired: "用户令牌已过期",
    mqPublishError: "消息队列发布失败",
    phpRuntimeError: "PHP运行时异常",
    deviceSessionError: "设备会话异常",
    pythonRuntimeError: "Python运行时异常",

    // Gallery API Error Code
    snsAuthFailed: "SNS账号或密码错误，请重试",
    snsSaveError: "SNS用户信息保存失败",
    snsAuthLogicError: "SNS授权逻辑异常",
    uploadLimitExceeded: "上传照片已达上限，请删除旧文件",
    uploadProcessError: "照片上传处理异常",
    shareNotReady: "分享设置未就绪，暂不可分享",
    invalidShareId: "无效的分享ID",
    unsupportedP2P: "当前平台不支持P2P共享",
    shareIdNotExist: "分享ID不存在",
    imageNotFound: "图片未找到，请确认已上传",
    guestUserRestriction: "未登录用户无写入权限，请先注册/登录",
    freeAccountLimit: "免费账户已达服务上限，请升级付费账户",
    unsupportedClientVersion: "不支持的客户端版本格式",
    unsupportedResolution: "不支持的客户端分辨率格式",
    latestVersion: "当前已是最新版本",
    snsFriendImportError: "SNS好友导入失败，需重新授权",
    noP2PContent: "无可推送的P2P内容",
    invalidP2PStatus: "不支持的P2P回调状态",
    userIdMismatch: "用户ID不匹配",


    // Client API Error Code
    clientRebindRequired: "设备已绑定其他用户，请确认解除旧绑定关系",
    accountRebindRequired: "账户已绑定其他设备，请确认更换新设备",
    invalidEmailFormat: "邮箱格式错误",
    invalidPasswordFormat: "密码格式错误",
    emailAlreadyRegistered: "该邮箱已被注册",
    accountNotFound: "未找到关联账户",
    findbackRequestLimit: "找回请求已发送，请检查邮箱或2小时后重试",
    verifyRequestLimit: "验证请求已发送，请检查邮箱或15分钟后重试",
    emailAlreadyVerified: "该邮箱已完成验证",
    wrongOldPassword: "旧密码错误",
    unauthorizedPasswordChange: "仅登录用户可修改密码",
    phoneRegistered: "手机号已被注册",
    phoneNotRegistered: "手机号未注册",
    verifyRequestTooFrequent: "验证请求过于频繁，请2分钟后再试",
    invalidUsernameFormat: "用户名格式错误",
    usernameTaken: "用户名已被占用",
    serverWriteError: "服务器数据写入失败",
    invalidHashSignature: "签名验证失败（签名不匹配）",
    expiredHashSignature: "签名验证失败（已过期或重复）",
    invalidTaskName: "任务队列命名不符合规范",
    emailExists: "邮箱已被使用，请更换其他邮箱",
    duplicateLabel: "标签名称已存在",

    // Common Excepts API Error Code
    requestParamError: "请求参数错误",
    nameExistsError: "名称已存在",
    serverMaintenance: "服务器维护中，请稍后重试",
    deviceIdInvalid: "设备ID无效或未注册",
    deviceTypeUnsupported: "设备类型不支持",
    authInfoMissing: "认证信息缺失",
    fileNotFound: "文件不存在",
    newUserSync: "新用户注册，后端数据同步中...",
    authKeyError: "认证密钥错误",
    accessDenied: "无接口访问权限",
    clientBoundConflict: "设备已绑定其他账户",
    restoreAuthError: "恢复认证参数错误",
    clientRegistered: "设备已注册，请找回密钥",
    tokenTimeout: "认证令牌过期",
    userInfoMismatch: "用户信息不匹配",
    serviceUnavailable: "服务不可用",
    emptyRequestBody: "请求体为空",
    cacheDataError: "缓存数据异常",
    requestBlocked: "请求被时段限制策略拦截",
    paramError: "参数错误",

    // 系统
    unknownError: '未知错误',
    loginExpired: "登录过期，请重新登录",
    apiRequestException: "接口请求异常，请稍后重试",
    apiRequestError: "接口请求出小差啦，请稍后重试",
    accessPermissionError: "访问权限错误",
    plan: "此计划",
    alreadyExists: "已经存在，请勿重复创建！",
    // DMB ErrorCode
    invalidDataRequest: "无效的数据请求",
    invalidStoreCode: "无效的门店编号",
    dataProcessError: "数据处理异常",
    dataPermissionError: "访问数据权限错误",
    functionPermissionError: "访问功能权限错误",
    invalidParams: "请求参数错误",
    invalidSSOCode: "无效的SSO Code",
    invalidIdentity: "身份无效",
    menuTitleDuplicate: "菜单标题重复",
    menuPermDuplicate: "菜单权限标识重复",
    mobileRegistered: "手机号已经被注册",
    emailRegistered: "邮箱已经被注册",
    usernameRegistered: "用户名已被注册",
    materialReviewed: "素材已审核过",
    invalidPendingMaterial: "待审核素材无效",
    accountNotAllowed: "账号不允许登录系统，请联系管理员",
    accountInactive: "账号未激活，请联系管理员",
    accountLocked: "密码错误次数过多，请一小时后重试",
    invalidCredentials: "用户名或密码有误",
    apiException: "接口异常",
    authError: "请求身份错误",
    paramError: "请求参数错误",
    productCodeExists: "商品编号已存在",
    productNameExists: "商品名称已存在",
    productIdNotExist: "商品ID不存在",
    productSoldOut: "商品已售罄"
}

export {
    errors_msg,
}
