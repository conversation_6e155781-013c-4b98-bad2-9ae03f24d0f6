* {
    padding: 0;
    margin: 0;
}

/*内边距上下左右*/
/****************************内边距*********************************** */
.pd-5 {
    padding: 5px;
}

.pd-10 {
    padding: 10px;
}

.pd-20 {
    padding: 20px;
}

.pd-30 {
    padding: 30px;
}

.pd-40 {
    padding: 40px;
}

.pd-50 {
    padding: 50px;
}

/* 内边距 X轴 */
.pd-x-5 {
    padding-left: 5px;
    padding-right: 5px;
}

.pd-x-10 {
    padding-left: 10px;
    padding-right: 10px;
}

.pd-x-20 {
    padding-left: 20px;
    padding-right: 20px;
}

.pd-x-30 {
    padding-left: 30px;
    padding-right: 30px;
}

.pd-x-40 {
    padding-left: 40px;
    padding-right: 40px;
}

.pd-x-50 {
    padding-left: 50px;
    padding-right: 50px;
}

/* 内边距 Y轴 */
.pd-y-5 {
    padding-top: 5px;
    padding-bottom: 5px;
}

.pd-y-10 {
    padding-top: 10px;
    padding-bottom: 10px;
}

.pd-y-20 {
    padding-top: 20px;
    padding-bottom: 20px;
}

.pd-y-30 {
    padding-top: 30px;
    padding-bottom: 30px;
}

.pd-y-40 {
    padding-top: 40px;
    padding-bottom: 40px;
}

.pd-y-50 {
    padding-top: 50px;
    padding-bottom: 50px;
}

/* 内边距 左*/
.pd-left-10 {
    padding-left: 10px;
}

.pd-left-20 {
    padding-left: 20px;
}

.pd-left-30 {
    padding-left: 30px;
}

.pd-left-40 {
    padding-left: 40px;
}

.pd-left-50 {
    padding-left: 50px;
}

/* 内边距 右*/
.pd-right-10 {
    padding-right: 10px;
}

.pd-right-20 {
    padding-right: 20px;
}

.pd-right-30 {
    padding-right: 30px;
}

.pd-right-40 {
    padding-right: 40px;
}

.pd-right-50 {
    padding-right: 50px;
}

/* 内边距 上*/
.pd-top-0 {
    padding-top: 0px;
}

.pd-top-5 {
    padding-top: 5px;
}

.pd-top-10 {
    padding-top: 10px;
}

.pd-top-20 {
    padding-top: 20px;
}

.pd-top-30 {
    padding-top: 30px;
}

.pd-top-40 {
    padding-top: 40px;
}

.pd-top-50 {
    padding-top: 50px;
}

/* 内边距下*/
.pd-bottom-5 {
    padding-bottom: 5px;
}

.pd-bottom-10 {
    padding-bottom: 10px;
}

.pd-bottom-20 {
    padding-bottom: 20px;
}

.pd-bottom-30 {
    padding-bottom: 30px;
}

.pd-bottom-40 {
    padding-bottom: 40px;
}

.pd-bottom-50 {
    padding-bottom: 50px;
}

/****************************外边距*********************************** */
.mg-5 {
    margin: 5px;
}

.mg-10 {
    margin: 10px;
}

.mg-20 {
    margin: 20px;
}

.mg-30 {
    margin: 30px;
}

.mg-40 {
    margin: 40px;
}

.mg-50 {
    margin: 50px;
}

/* 外边距 X轴 */
.mg-x-5 {
    margin-left: 5px;
    margin-right: 5px;
}

.mg-x-10 {
    margin-left: 10px;
    margin-right: 10px;
}

.mg-x-20 {
    margin-left: 20px;
    margin-right: 20px;
}

.mg-x-30 {
    margin-left: 30px;
    margin-right: 30px;
}

.mg-x-40 {
    margin-left: 40px;
    margin-right: 40px;
}

.mg-x-50 {
    margin-left: 50px;
    margin-right: 50px;
}

/* 外边距 Y轴 */
.mg-y-5 {
    margin-top: 5px;
    margin-bottom: 5px;
}

.mg-y-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}

.mg-y-20 {
    margin-top: 20px;
    margin-bottom: 20px;
}

.mg-y-30 {
    margin-top: 30px;
    margin-bottom: 30px;
}

.mg-y-40 {
    margin-top: 40px;
    margin-bottom: 40px;
}

.mg-y-50 {
    margin-top: 50px;
    margin-bottom: 50px;
}

/* 内边距 左*/
.mg-left-0 {
    margin-left: 0px;
}

.mg-left-10 {
    margin-left: 10px;
}

.mg-left-20 {
    margin-left: 20px;
}

.mg-left-30 {
    margin-left: 30px;
}

.mg-left-40 {
    margin-left: 40px;
}

.mg-left-50 {
    margin-left: 50px;
}

/* 内边距 右*/
.mg-right-10 {
    margin-right: 10px;
}

.mg-right-20 {
    margin-right: 20px;
}

.mg-right-30 {
    margin-right: 30px;
}

.mg-right-40 {
    margin-right: 40px;
}

.mg-right-50 {
    margin-right: 50px;
}

/* 内边距 上*/
.mg-top-10 {
    margin-top: 10px;
}

.mg-top-20 {
    margin-top: 20px;
}

.mg-top-30 {
    margin-top: 30px;
}

.mg-top-40 {
    margin-top: 40px;
}

.mg-top-50 {
    margin-top: 50px;
}

/* 内边距下*/
.mg-bottom-0 {
    margin-bottom: 0px;
}

.mg-bottom-5 {
    margin-bottom: 5px;
}

.mg-bottom-10 {
    margin-bottom: 10px;
}

.mg-bottom-15 {
    margin-bottom: 15px;
}

.mg-bottom-20 {
    margin-bottom: 20px;
}

.mg-bottom-30 {
    margin-bottom: 30px;
}

.mg-bottom-40 {
    margin-bottom: 40px;
}

.mg-bottom-50 {
    margin-bottom: 50px;
}

/************************************flex布局*************************** */
/*flex布局*/
.flex {
    display: flex;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-evenly {
    justify-content: space-evenly;
}

.align-items-center {
    align-items: center;
}

.align-items-start {
    align-items: flex-start;
}

.align-items-end {
    align-items: flex-end;
}

.flex-direction-column {
    flex-direction: column;
}

.flex-direction-row-reverse {
    flex-direction: row-reverse;
}

.flex-1 {
    flex: 1;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse;
}

/************************************圆角*************************** */
/*圆角*/
.rounded {
    border-radius: 8px;
}

.rounded-4 {
    border-radius: 4px;
}

/*圆*/
.rounded-circle {
    border-radius: 100%;
}

/************************************字体*************************** */
/*字体大小*/
.font-smaller {
    font-size: 10px;
}

.font-small {
    font-size: 15px;
}

.font-sm {
    font-size: 20px;
}

.font {
    font-size: 25px;
}

.font-md {
    font-size: 30px;
}

.font-lg {
    font-size: 35px;
}

.font-lger {
    font-size: 50px;
}

.font-bold {
    font-weight: bold;
}

/*字体颜色*/
.color-main {
    color: #16C2C2 !important;
}

.color-white {
    color: #FFFFFF !important;
}

.color-black {
    color: #333333 !important;
}

.color-red {
    color: red !important;
}

.color-green {
    color: #8FC31F !important;
}

.color-primary {
    color: #007AFF !important;
}

.color-light-muted {
    color: #989899
}

/* 文字相关 */
.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-white-space {
    white-space: nowrap;
}

/*文字换行溢出处理*/
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    /* lines: 1; */
}

/************************************背景色*************************** */
/*背景色*/
.bg-main {
    background-color: #21AAD6 !important;
}

.bg-disabled {
    background-color: #11a0a0;
}

.bg-light {
    background-color: #f8f9fa;
}

.bg-red {
    background-color: #e40012;
}

.bg-green {
    background-color: #8FC31F;
}

.bg-white {
    background-color: #fff;
}

.bg-black {
    background-color: #333333;
}

.bg-transparent {
    background-color: transparent;
}

/* **************************************背景位置************************************ */
.bg-center {
    background-position: center !important;
}

.bg-bottom {
    background-position: bottom !important;
}

.bg-right {
    background-position: right !important;
}

.bg-left {
    background-position: left !important;
}

.bg-no-repeat {
    background-repeat: no-repeat !important;
}

.bg-contain {
    background-size: contain !important;
}

.bg-cover {
    background-size: cover !important;
}

/************************************宽度*************************** */
/*宽度*/
.wd-100 {
    width: 100%;
}

.wd-80 {
    width: 80%;
}

.wd-50 {
    width: 50%;
}

.wd-25 {
    width: 25%;
}

.wd-30 {
    width: 30%;
}

.wd-20px {
    width: 20px
}

.wd-30px {
    width: 30px
}

.wd-40px {
    width: 40px
}

.wd-50px {
    width: 50px
}

.wd-60px {
    width: 60px
}

.wd-70px {
    width: 70px
}

.wd-80px {
    width: 80px
}

.wd-100px {
    width: 100px;
}

.wd-200px {
    width: 200px;
}

.wd-300px {
    width: 300px;
}

.wd-400px {
    width: 400px;
}

.wd-500px {
    width: 500px;
}

/************************************高度*************************** */
/*宽度*/
.hg-100 {
    height: 100%;
}

.hg-80 {
    height: 80%;
}

.hg-50 {
    height: 50%;
}

.hg-25 {
    height: 25%;
}

.hg-20px {
    height: 20px
}

.hg-30px {
    height: 30px
}

.hg-40px {
    height: 40px
}

.hg-50px {
    height: 50px
}

.hg-60px {
    height: 60px
}

.hg-70px {
    height: 70px
}

.hg-80px {
    height: 80px
}

.hg-100px {
    height: 100px;
}

.hg-200px {
    height: 200px;
}

.hg-300px {
    height: 300px;
}

.hg-400px {
    height: 400px;
}

/************************************边框*************************** */
/*边框*/
/*下边线*/
.border {
    border: 1px solid #dee2e6
}

.border-none {
    border: none;
}

.border-left {
    border-left: 1px solid #dee2e6;
}

.border-right {
    border-right: 1px solid #dee2e6;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6;
}

.border-top {
    border-top: 1px solid #dee2e6;
}

.border-radius {
    border-radius: 8px;
}

.border-blue {
    border: 1px solid rgba(142, 190, 238, 1);
}

/* ie盒模型 */
.box-sizing-box {
    box-sizing: border-box;
}

/************************************定位*************************** */
/*定位*/
.position-fixed {
    position: fixed;
}

.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;
}

/* 左距离 */
.left-0 {
    left: 0px;
}

.left-10 {
    left: 10px;
}

.left-20 {
    left: 20px;
}

/* 右距离 */
.right-0 {
    right: 0px;
}

.right-10 {
    right: 10px;
}

.right-20 {
    right: 20px;
}

/* 底部 */
.bottom-0 {
    bottom: 0px;
}

.bottom-10 {
    bottom: 10px;
}

.bottom-20 {
    bottom: 20px;
}

/* 顶部 */
.top-0 {
    top: 0px;
}

.top-10 {
    top: 10px;
}

.top-20 {
    top: 20px;
}

/* 层级 */
.z-index-0 {
    z-index: 0;
}

.z-index-1 {
    z-index: 1;
}

.z-index-999 {
    z-index: 999;
}

/* 定位上下左右居中 */
.position-center {
    left: 50%;
    top: 50%;
    transform: translate(50%, 50%);
}

/*定位---固定在底部*/
.fixed-bottom {
    position: fixed;
    /* right: 0; */
    left: 0;
    bottom: 0;
    z-index: 1;
}

/*定位---固定在顶部部*/
.fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    top: 0;
    z-index: 1;
}

.fixed-right {
    position: fixed;
    right: 0;
    /* left: 0; */
    top: 0;
    z-index: 1;
}

/* **************************************透明度************************************ */
.opacity-0 {
    opacity: 0;
}

/* ***************************************滚动条****************************************** */
/* .overflow-hidden{overflow: hidden;}
.overflow-y-scroll{overflow-y: scroll;} */
/* **************************************鼠标手势************************************ */
.cursor-pointer {
    cursor: pointer;
}

.cursor-move {
    cursor: move;
}

.el-message {
    font-size: 18px;
    z-index: 2999 !important;
}

::-webkit-scrollbar {
    width: 3px;
    /* display: none; */
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(239, 239, 239, 0.5);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb {
    background: rgba(191, 191, 191, 0.7);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(191, 191, 191, 1);
}

.el-dialog {
    font-size: 16px;
}

.cursor {
    cursor: pointer;
}

.el-time-panel__footer {
    display: flex !important;
    justify-content: flex-end;
}
.drawer_min_w{
    min-width: 550px !important;
}
.drawer_title_b .el-drawer__header{
    margin-bottom: 20px !important;
}
.drawer_form .is-required:not(.is-no-asterisk)>.el-form-item__label:before,
.drawer_form .el-form-item.is-error .el-input__inner,
.drawer_form .el-form-item__error{
    color: #e40012 !important;
    border-color: #e40012 !important;
}
