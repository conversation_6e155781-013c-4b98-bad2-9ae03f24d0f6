<template>
  <div class="genearl">
    <el-form ref="form" :model="form" label-width="130px">
      <el-form-item label="发布名称:">
        <el-input v-model="newGeneral.launchName"></el-input>
      </el-form-item>
      <el-form-item label="投放周期:">
        <el-date-picker
          v-model="newGeneral.timging"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width:300px"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="屏幕类型:">
        <el-select v-model="newGeneral.screen_type" placeholder="请选择屏幕类型" style="width:300px">
          <el-option v-for="item in screen_option" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="屏幕方向:">
        <el-select v-model="newGeneral.v_or_h" placeholder="请选择屏幕类型" style="width:300px">
          <el-option
            v-for="item in vHorder"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="屏幕标签:">
                <el-select v-model="newGeneral.v_or_h" placeholder="请选择屏幕类型" style="width:300px" multiple="true">
                    <el-option v-for="item in screenTages" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
      </el-form-item>-->

      <el-form-item label="播放类型:">
        <div>
          <el-radio
            v-for="item in playerRadio"
            :value="item[0]"
            :key="item[0]"
            v-model="newGeneral.playerState"
            :label="item[1]"
          ></el-radio>
        </div>
      </el-form-item>
      <div class="detali">
        <el-form-item label="播放详情:">
          <div v-for="item in DetaliList" :key="item" class="selectDetali">
            <el-radio v-model="newGeneral.detaliRadio" :label="item.name"></el-radio>
          </div>
        </el-form-item>
        <div v-show="newGeneral.detaliRadio == '按星期'" class="weekCheckout">
          <el-checkbox
            v-model="newGeneral.weekCheckout"
            v-for="item1 in weekList"
            :key="item1.name"
            :label="item1.value"
          >{{ item1.name }}</el-checkbox>
          <br />
          <el-time-picker
            is-range
            v-model="newGeneral.time_ranges"
            value-format="HH:mm:ss"
            style="margin-top:10px"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
          />
        </div>
        <div v-show="newGeneral.detaliRadio == '按指定时段'" class="selectCheckout">
          <!-- <el-date-picker v-model="newGeneral.genearlTimeing" valueFormat="yyyy-MM-dd hh:mm:ss"
                        type="datetimerange" style="width:300px" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" :picker-options="pickerOptions0">
          </el-date-picker>-->
          <el-time-picker
            is-range
            v-model="newGeneral.genearlTimeing"
            range-separator="至"
            format='HH:mm'
            value-format='HH:mm'
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
          ></el-time-picker>
          <!-- <el-date-picker
            v-model="newGeneral.genearlTimeing"
            type="datetimerange"
            style="width:65%"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions0"
          ></el-date-picker> -->
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { formatDate, formatting } from "@/utils/formatDate";
import { get_screen_tags } from "@/api/system/label";
export default {
  data() {
    return {
      newGeneral: {
        launchName: "",
        timging: [],
        // timging: [new Date(), new Date(new Date().getTime() + 86400000)],
        playerState: "轮播",
        screen_type: "",
        detaliRadio: "按全天",
        weekCheckout: ["1", "2", "3", "4", "5", "6", "7"],
        genearlTimeing: ['',''],
        v_or_h: ""
      },
      /**
       * 右侧DMB规格
       * order 0为横 1为竖
       */
      radio: 0,
      active: null,
      playerRadio: [],
      screen_option: [],
      vHorder: [
        {
          value: 0,
          label: "横向"
        },
        {
          value: 1,
          label: "竖向"
        }
      ],
      // detali
      DetaliList: [
        {
          name: "按全天"
        },
        {
          name: "按星期"
        },
        {
          name: "按指定时段"
        }
      ],
      weekList: [
        {
          name: "日",
          value: "1"
        },
        {
          name: "一",
          value: "2"
        },
        {
          name: "二",
          value: "3"
        },
        {
          name: "三",
          value: "4"
        },
        {
          name: "四",
          value: "5"
        },
        {
          name: "五",
          value: "6"
        },
        {
          name: "六",
          value: "7"
        }
      ],
      pickerOptions0: {
        disabledDate: time => {
          return (
            formatting(formatDate(time.getTime())) >
              formatting(formatDate(this.newGeneral.timging[1].getTime())) ||
            formatting(formatDate(time.getTime())) <
              formatting(formatDate(this.newGeneral.timging[0].getTime()))
          );
        }
      },
      screenTages: []
    };
  },

  methods: {
    eventActive(index, name) {
      this.active = index;
      this.radio = name;
      console.log(this.radio);
    },
    changeRadio(index) {
      this.active = index;
    },
    getScreenTags() {
      get_screen_tags({
        page: 0,
        size: 30
      }).then(res => {
        console.log("res", res);
        this.screenTages = res["data"][0]["tags_list"];
        console.log(this.screenTages);
      });
    }
  },
  created() {
    this.playerRadio = this.$store.state.deployDataFilters[2].options;
    this.screen_option = this.$store.state.deployDataFilters[3].options;
    this.screen_option.forEach((item, index) => {
      console.log(item[0], "item");
      if (item[0] == "dmb") {
        console.log(index, "index");
        this.screen_option.splice(index, 1);
      }
    });
    console.log(this.screen_option, "screen_options");
    this.getScreenTags();
  }
};
</script>

<style lang="scss" scoped>
.genearl {
  box-sizing: border-box;
  margin-top: 20px;
  padding-left: 25px;
  ::v-deep .el-input {
    width: 300px !important;
  }
}

.active {
  background-color: var(--text-color-light) !important;;
}

.el-form-item:nth-of-type(1) {
  margin-top: 0 !important;
}

.el-form-item {
  margin-top: 22px !important;
}

.detali {
  position: relative;

  .weekCheckout {
    position: absolute;
    left: 2.3rem;
    width: 80%;
    top: 38%;
  }

  .selectCheckout {
    position: absolute;
    left: 2.3rem;
    width: 80%;
    top: 78%;
  }
}

.selectDetali:nth-of-type(1) {
  margin-top: 0 !important;
}

.selectDetali:nth-of-type(3) {
  margin-top: 45px !important;
}

.selectDetali {
  margin-top: 25px;
}

.genearl {
  ::v-deep .data-picstime {
    width: 150px !important;

    ::v-deep .el-input {
      width: 150px !important;
    }
  }
}
</style>