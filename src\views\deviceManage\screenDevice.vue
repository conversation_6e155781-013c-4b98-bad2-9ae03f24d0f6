<template>
  <div class="box" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <!--    头部-->
    <div class="useTop">
      <!--      左边箭头-->
      <div class="pubArrow flex" style="align-items: center">
        <span class="el-icon-back" @click.stop="goBack" style="cursor: pointer"></span>
        <span style="font-size: 16px">设备列表</span>
      </div>
    </div>
    <main>
      <div class="twoPart">
        <div class="left">
          <div class="title" style="display: flex;height: 65px;justify-content: space-between;align-items: center;padding: 0px 16px;border-bottom: 1px solid #f4f4f4;
            ">
            <div class="con">
              <span v-for="item in screenInfoData.all_displayinfo" :key="item.screen_id" :class="item.display.v_or_h == 'horizontal' ? 'screen_h' : 'screen_v'
                ">{{ item.displaynum }}</span>
              {{
                screenInfoData.owner_shop
                  ? screenInfoData.owner_shop.shop_name
                  : ""
              }}
              ({{ $route.query.storecode ? $route.query.storecode : "" }})
            </div>
            <p style="color: rgba(166, 166, 166, 1)">
              设备ID：{{ screenInfoData.screen_id }}
            </p>
          </div>
          <ul>
            <li>
              <span>设备型号：</span>
              <div>
                {{
                  screenInfoData.hardware_info
                    ? screenInfoData.hardware_info.pmodel
                    : ""
                }}
              </div>
            </li>
            <li>
              <span>屏幕类型：</span>
              <div>
                {{
                  screenInfoData.usage_type_cn
                }}
              </div>
            </li>
            <li>
              <span>设备状态：</span>
              <div :style="{
                color: screenInfoData.isonline
                  ? 'rgba(0, 186, 173, 1)'
                  : 'var(--text-color)'
              }">
                {{ screenInfoData.isonline ? "在线" : "离线" }}
              </div>
            </li>
            <li>
              <span>IP地址：</span>
              <div>
                {{
                  screenInfoData.hardware_info
                    ? screenInfoData.hardware_info.lan_ip
                    : ""
                }}
              </div>
            </li>
            <li>
              <span>存储空间：</span>
              <div>
                {{
                  screenInfoData.hardware_info
                    ? screenInfoData.hardware_info.available_space
                    : ""
                }}
                MB
              </div>
            </li>
            <li>
              <span>设备版本：</span>
              <div>
                {{
                  screenInfoData.app_info ? screenInfoData.app_info.version : ""
                }}
                <!-- <i>最新</i> -->
              </div>
            </li>
            <li>
              <span>固件版本：</span>
              <div>
                {{ screenInfoData.romVersion ? screenInfoData.romVersion : "" }}
                <!-- <i>最新</i> -->
              </div>
            </li>
            <li>
              <span>server版本：</span>
              <div>
                {{ screenInfoData.serverVersion ? screenInfoData.serverVersion : "" }}
                <!-- <i>最新</i> -->
              </div>
            </li>
            <li>
              <span>绑定激活工具：</span>
              <div>
                {{ screenInfoData.launchVersion ? screenInfoData.launchVersion : "" }}
                <!-- <i>最新</i> -->
              </div>
            </li>
            <!-- v-for="items in screenInfoData.all_displayinfo" -->
            <li v-if="checkPer(['dm.scr.edit'])">
              <span style="margin-top: 6px">屏幕标签：</span>
              <div style="display: flex; width: 350px; justify-content: start" v-if="destyleList.length > 4">
                <div v-for="item in destyleList.slice(0, 3)" :key="item.name"
                  style="display: flex; align-items: center">
                  <img src="../../assets/img/home_img/little_label.svg" alt style="width: 30px; height: 30px" />
                  {{ item.name }}
                </div>

                <el-popover placement="top-start" title="屏幕标签" width="200" trigger="hover"
                  popper-class="popperOptions1">
                  <div v-for="item in destyleList" :key="item" style="display: flex; align-items: center">
                    <img src="../../assets/img/home_img/little_label.svg" alt style="width: 30px; height: 30px" />
                    <span>{{ item.name }}</span>
                  </div>
                  <div class="more cursor" slot="reference">...</div>
                </el-popover>
              </div>

              <div style="display: flex; width: 350px; justify-content: start" v-else>
                <div v-for="item in destyleList" :key="item.name" style="display: flex; align-items: center">
                  <img src="../../assets/img/home_img/little_label.svg" alt style="width: 30px; height: 30px" />
                  {{ item.name }}
                </div>
              </div>
              <p style="
                  color: var(--text-color);
                  margin-left: 20px;
                  width: 100px;
                  padding-top: 6px;
                " class="cursor" v-show="destyleList.length != 0" @click="openTags(items)">
                修改
              </p>
              <p style="
                  color: var(--text-color);
                  margin-left: 20px;
                  width: 100px;
                  padding-top: 6px;
                " class="cursor" v-show="destyleList.length == 0" @click="openTags(items)">
                新增
              </p>
            </li>
          </ul>
        </div>
        <div class="center">
          <h3>
            操作
            <span :class="arrowDirBottom == false
              ? 'el-icon-arrow-up'
              : 'el-icon-arrow-down'
              " @click.stop="arrowDir" style="cursor: pointer"></span>
          </h3>
          <ul v-show="arrowDirTopShow">
            <li>
              <span>基础操作：</span>
              <div style="display: flex">
                <div>
                  <el-button type="primary" @click="detection" v-if="!detecting">设备检测</el-button>
                  <el-button type="primary" v-else :loading="detecting">检测中</el-button>
                  当前状态:{{
                    screenInfoData.isonline == true ? "在线" : "离线"
                  }}
                </div>
                <div style="margin-left: 20px; position: relative">
                  <el-button type="primary" :disabled="isonline" @click="shotScreen"
                    :loading="screenhostState">实时截图</el-button>
                  <!-- v-show="!isonline" -->
                  <span class="lok" @click.stop="lookScreen(1)">查看截图</span>
                </div>
                <div style="margin-left: 20px; position: relative">
                  <el-button type="primary" :disabled="isonline" @click="recordingScreen"
                    :loading="screenVideoState">实时录屏</el-button>
                  <span class="lok" v-show="!isonline" @click.stop="lookScreen(2)">查看录像</span>
                </div>
              </div>
            </li>
            <li style="margin-top: 30px">
              <span>屏幕方向：</span>
              <div>
                <el-select v-model="screenDir.screen_num" placeholder="请选择" style="width: 130px" @change="changeDir">
                  <el-option v-for="item in screenInfoData.all_displayinfo" :key="item.displaynum"
                    :label="'屏幕序列' + item.displaynum" :value="item.screen_id"></el-option>
                </el-select>
                <el-select v-model="screenDir.deg" placeholder="请选择" style="width: 130px" @change="changeVal">
                  <el-option v-for="item in screenPart.options2" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
                <el-button type="primary" @click="saveScreenDir" :disabled="!checkPer(['dm.scr.edit'])">保存</el-button>
              </div>
            </li>
            <li>
              <span>设备音量：</span>
              <div class="block">
                <el-slider v-model="voiceValue" style="width: 70%"></el-slider>
                <span style="padding-left: 10px">{{ voiceValue }}</span>
                <el-button type="primary" @click="saveVolume" :disabled="!checkPer(['dm.scr.edit'])">保存</el-button>
              </div>
            </li>
            <li v-if="checkPer(['dm.scr.edit'])">
              <span style=" width:80px;white-space: nowrap;">时间机制：</span>
              <!-- v-if="screenInfoData.hardware_info.pmodel == 3399" -->
              <!-- <div class="timer" v-if="screenType == 3399"> -->
              <div class="timer">
                <!-- arrow-control -->
                <el-time-picker v-model="timing[0]" format="HH:mm" value-format="HH:mm"
                  placeholder="请选择开机时间"></el-time-picker>
                <el-time-picker style="margin-left: 30px" v-model="timing[1]" value-format="HH:mm" format="HH:mm"
                  placeholder="请选择关机时间"></el-time-picker>
                <span @click="requestTimer">确认</span>
                <span @click="cancelTimer">取消</span>
              </div>
              <!-- <div v-else style="display: flex;align-items: center;margin-top: -5px;color: var(--text-color);">
                此设备不支持定时开关机
              </div> -->
            </li>


            <li class="brightness">
              <span>设备亮度：</span>
              <div class="block" style="margin-top:-7px">
                <div v-for="(item, index) in brightnessList" :key="item.time_range" class="brightness-item">
                  <div>
                    {{ item.label }}
                  </div>
                  <div style="margin-left: 20px">
                    {{ item.time_range == "" ? '未设置时段屏幕亮度' : item.time_range }}
                  </div>
                  <div style="margin-left: 20px;" v-if=" item.brightness">
                    {{ item.brightness }}%
                  </div>
                  <div class="event" v-if="index == 0">
                    <el-button type="primary" @click="settingBrighness()">设置</el-button>
                  </div>
                </div>
              </div>
            </li>

            <li class='deepEvent'>
              <span>深度操作：</span>
              <div style="display:flex;flex-wrap: wrap;">
                <el-button type="primary" style="background-color: var(--text-color)"
                  @click.stop="clickRemote">远程升级</el-button>
                <!-- <el-button type="danger" style="background-color: var(--text-color)" @click.stop="clickRemoteDesk"
                  :disabled="isonline">远程桌面</el-button> -->
                <!-- v-if="checkPer([ 'scr:control'])" -->
                <el-button type="primary" style="background-color: var(--text-color)" @click="dialogVisible = true"
                  :disabled="isonline" v-if="checkPer(['dm.scr.control.reboot'])">设备重启</el-button>
                <el-button type="primary" style="background-color: var(--text-color)" :disabled="isonline"
                  @click="clearCache">清除缓存</el-button>
                <el-button type="primary" style="background-color: var(--text-color)" @click="unBind"
                  v-if="checkPer(['dm.scr.control'])">设备删除</el-button>
                <el-button type="primary" style="background-color: var(--text-color)" @click.stop="getScreenContent"
                  v-if="checkPer(['dm.scr.control']) && canGetContent" :disabled="disabledContent"
                  :loading="loadingDisabled">
                  内容获取
                </el-button>
              </div>
              <!-- // && isonline == true) -->
            </li>
          </ul>
        </div>
        <div class="right">
          <h3>
            外部端口配置
            <span :class="arrowDirBottom == false
              ? 'el-icon-arrow-up'
              : 'el-icon-arrow-down'
              " @click.stop="arrowDir" style="cursor: pointer"></span>
          </h3>
          <ul v-show="arrowDirTopShow">

            <li>
              <span style="width:fit-content">终端USB接入：</span>
              <el-radio-group v-model="usb_enable" style="margin-left: 38px">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开放</el-radio>
              </el-radio-group>
              <span class="active_btn" style="color:var(--text-color);"
                @click="changeTerminalSettings('usb_enable')">确认</span>
            </li>
            <li>
              <span style="width:fit-content">终端外音设备接入：</span>
              <el-radio-group v-model="sound_enable" style="margin-left: 10px">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开放</el-radio>
              </el-radio-group>
              <span class="active_btn" style="color:var(--text-color);"
                @click="changeTerminalSettings('sound_enable')">确认</span>
            </li>
            <li>
              <span style="width:fit-content">终端HDMI接入：</span>
              <el-radio-group v-model="hdmi_enable" style="margin-left: 30px">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1">开放</el-radio>
              </el-radio-group>
              <span class="active_btn" style="color:var(--text-color);"
                @click="changeTerminalSettings('hdmi_enable')">确认</span>
            </li>

          </ul>
        </div>
      </div>
      <div class="content tag_set">
        <div class="flex" style="margin-bottom: 10px">
          <h4>播放列表</h4>
          <div class="c_or_d_screen_wrap">
            <span v-for="item in screenInfoData.all_displayinfo" :key="item.screen_id" class="c_or_d_screen"
              :class="item.screen_id == activeId ? 'active' : ''" @click="changeActive(item.screen_id)">屏幕 {{
                item.displaynum }} ( id:{{ item.screen_id }} )</span>
          </div>
        </div>
        <!--          tab切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="当天内容" name="first">
            <ul class="list" v-loading="cdLoading" v-if="mainScreenList.length > 0">
              <li v-for="(item, index) in mainScreenList" :key="index">
                <div>
                  <p class="circle">{{ index + 1 }}</p>
                  <h3>{{ item.classify_label }}</h3>
                  <ul>
                    <!-- <li>
                      <span>播放周期</span>
                      <p v-if="item.play_mode == 'single_use_range'">指定时间段</p>
                      <p v-else-if="item.play_mode == 'week_range'">周时间段</p>
                      <p v-else-if="item.play_mode == 'full_day'">全天</p>
                      <p v-else-if="item.play_mode == 'time_range'">小时时段</p>
                    </li>-->
                    <li>
                      <span>专辑ID：</span>
                      <p>{{ item.sche_id }}</p>
                    </li>
                    <li v-if="item.play_mode == 'single_use_range'">
                      <span>计划属性：</span>
                      <p>{{ getPlan(item) }}</p>
                      <el-popover placement="right" width="400" trigger="click">
                        <div class="ranges_wrap">
                          <p v-for="ranges in item.time_ranges" :key="ranges">
                            {{ ranges }}
                          </p>
                        </div>

                        <div slot="reference" style="cursor: pointer;margin-left: 10px;width: 20px;height: 20px;">
                          <i class="el-icon-more" slot="reference" style="margin-top: 7px"></i>
                        </div>
                      </el-popover>
                    </li>
                    <li v-else>
                      <span>计划属性：</span>
                      <p>{{ getPlan(item) }}</p>
                    </li>
                    <li v-show="item.start_time && item.end_time">
                      <span>播放时段：</span>
                      <p>{{ item.start_time }} - {{ item.end_time }}</p>
                    </li>
                    <li>
                      <span>播放类型：</span>
                      <p>{{ item.keep_time == 1 ? "独占" : "轮播" }}</p>
                    </li>
                    <!-- <li>
                      <span>播放模式</span>
                      <p>工作日</p>
                    </li>-->

                    <li>
                      <span>发布时间：</span>
                      <p>{{ item.pub_last_time }}</p>
                    </li>
                    <li>
                      <span>发布状态：</span>
                      <p>{{ item.check_status }}</p>
                    </li>
                  </ul>
                </div>
                <div class="sideStep">
                  <div class="my_step_wrap">
                    <div class="my_step">
                      <div class="every_step_lump" v-for="(item2, idx) in pubJobTypeList" :key="item2.value">
                        <div class="step_icon success_state" v-if="item.cf_bar.process == item2.value">
                          <i class="el-icon-check" style="
                              color: #fff;
                              line-height: 35px;
                              font-size: 22px;
                            "></i>
                        </div>
                        <div class="step_icon" v-else>{{ idx + 1 }}</div>
                        <div class="step_title">{{ item2.title }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="image_wrap">
                    <img :src="item.sche_thumb" style="object-fit: cover; border: 1px solid #e9e7e7"
                      :class="item.v_or_h == 0 ? 'heng' : 'shu'" />
                    <div style="position:absolute;right:5%;top:0" class="activeState">
                      该专辑有{{ item.cs_list.length }}个内容
                    </div>
                    <div class="play" @click.stop="handlePreview(item.cs_list)">
                      <span class="el-icon-view"></span>
                      播放
                    </div>
                  </div>
                </div>
              </li>
            </ul>
            <div v-else v-loading="dayContentLoading">
              <el-empty description="暂无播放内容"></el-empty>
            </div>
          </el-tab-pane>
          <el-tab-pane label="待播内容" name="second">
            <ul class="list" v-loading="cdLoading" v-if="awaitPlayList.length != 0">
              <li v-for="(item, index) in awaitPlayList" :key="index">
                <div>
                  <p class="circle">{{ index + 1 }}</p>
                  <h3>{{ item.classify_label }}</h3>
                  <ul>
                    <!-- <li>
                      <span>播放周期</span>
                      <p v-if="item.play_mode == 'single_use_range'">指定时间段</p>
                      <p v-else-if="item.play_mode == 'week_range'">周时间段</p>
                      <p v-else-if="item.play_mode == 'full_day'">全天</p>
                      <p v-else-if="item.play_mode == 'time_range'">小时时段</p>
                    </li>-->
                    <li>
                      <span>专辑ID：</span>
                      <p>{{ item.sche_id }}</p>
                    </li>
                    <li v-if="item.play_mode == 'single_use_range'">
                      <span>计划属性：</span>
                      <p>{{ getPlan(item) }}</p>
                      <el-popover placement="right" width="400" trigger="click">
                        <div class="ranges_wrap">
                          <p v-for="ranges in item.time_ranges" :key="ranges">
                            {{ ranges }}
                          </p>
                        </div>

                        <div slot="reference" style="
                            cursor: pointer;
                            margin-left: 10px;
                            width: 20px;
                            height: 20px;
                          ">
                          <i class="el-icon-more" slot="reference" style="margin-top: 7px"></i>
                        </div>
                      </el-popover>
                    </li>
                    <li v-else>
                      <span>计划属性：</span>
                      <p>{{ getPlan(item) }}</p>
                    </li>
                    <li v-show="item.start_time && item.end_time">
                      <span>播放时段：</span>
                      <p>{{ item.start_time }} - {{ item.end_time }}</p>
                    </li>
                    <li>
                      <span>播放类型：</span>
                      <p>{{ item.keep_time == 1 ? "独占" : "轮播" }}</p>
                    </li>
                    <!-- <li>
                      <span>播放模式</span>
                      <p>工作日</p>
                    </li>-->

                    <li>
                      <span>发布时间：</span>
                      <p>{{ item.pub_last_time }}</p>
                    </li>
                    <li>
                      <span>发布状态：</span>
                      <p>{{ item.check_status }}</p>
                    </li>
                  </ul>
                </div>
                <div class="sideStep">
                  <div class="my_step_wrap">
                    <div class="my_step">
                      <div class="every_step_lump" v-for="(item2, idx) in pubJobTypeList" :key="item2.value">
                        <div class="step_icon success_state" v-if="item.cf_bar.process == item2.value">
                          <i class="el-icon-check" style="
                              color: #fff;
                              line-height: 35px;
                              font-size: 22px;
                            "></i>
                        </div>
                        <div class="step_icon" v-else>{{ idx + 1 }}</div>
                        <div class="step_title">{{ item2.title }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="image_wrap">
                    <img :src="item.sche_thumb" style="object-fit: cover; border: 1px solid #e9e7e7"
                      :class="item.v_or_h == 0 ? 'heng' : 'shu'" />
                    <div style="position:absolute;right:5%;top:0" class="activeState">
                      该专辑有{{ item.cs_list.length }}个内容
                    </div>
                    <div class="play" @click.stop="handlePreview(item.cs_list)">
                      <span class="el-icon-view"></span>
                      播放
                    </div>
                  </div>
                </div>
              </li>
            </ul>
            <div v-else>
              <el-empty description="暂无播放内容"></el-empty>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </main>
    <!-- 远程升级 -->
    <el-dialog title="远程升级" :visible.sync="remoteShow" class="remoteScreen" custom-class="remote"
      :before-close="resetDialog">
      <div>
        <ul>
          <li>
            <span>应用类型</span>
            <div>
              <el-select v-model="applyValue" placeholder="请选择" @change="getVersionList">
                <el-option v-for="item in applyOptions" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
              </el-select>
            </div>
          </li>
          <li>
            <span>版本列表</span>
            <div>
              <el-select v-model="betaValue1" placeholder="请选择">
                <el-option v-for="item in betaOptions" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </div>
          </li>
          <!-- <li>
            <span>升级时段</span>
            <div>
              <div class="block">
                <el-date-picker v-model="updataValue" type="date" placeholder="选择日期"
                  :picker-options="pickerOptions"></el-date-picker>
              </div>
            </div>
          </li> -->
          <!-- <li>
            <span>升级时间</span>
            <div>
              <el-time-picker v-model="updateTimeValue1" format="HH:mm" :picker-options="{
                selectableRange: `00:00:00 - ${updateTimeValue2 ? new Date(new Date(updateTimeValue2).getTime() - 3600000) : '23:59:59'
                  }`
              }" placeholder="请选择升级时间"></el-time-picker>
              <p style="
                  text-decoration: underline;
                  color: #c0c4cc;
                  margin: 0 8px;
                ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </p>
              <el-time-picker v-model="updateTimeValue2" format="HH:mm" :picker-options="{
                selectableRange: `${updateTimeValue1 ? new Date(new Date(updateTimeValue1).getTime() + 3600000) : '00:00:00'
                  } - 23:59:59`
              }" placeholder="请选择升级时间"></el-time-picker>
            </div>
          </li> -->
        </ul>
        <div slot="footer" class="dialog-footer" style="text-align: right">
          <el-button @click="
            remoteShow = false;
          resetDialog();
          ">取 消</el-button>
          <el-button type="primary" @click="handleUpdgrade" v-show="!isUploading">确 定</el-button>
          <el-button type="primary" style="width:59px" v-show="isUploading">
            <i class="el-icon-loading"></i>
          </el-button>
        </div>
      </div>
    </el-dialog>
    <!--      远程桌面-->
    <el-dialog title="远程桌面" :visible.sync="remoteDesk" class="remoteScreen" custom-class="remote">
      <div style="margin-top: 10px">
        <span>分辨率：</span>
        <el-select v-model="resValue" placeholder="请选择">
          <el-option v-for="item in resolution" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button @click="remoteDesk = false">取 消</el-button>
        <el-button type="primary" @click="remoteDesk = false">确 定</el-button>
      </div>
    </el-dialog>
    <!--      重启该设备-->
    <el-dialog title="重启该设备" :visible.sync="dialogVisible" width="25%" custom-class="resDevice">
      <span class="el-icon-warning"></span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="restartDev">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 实时录屏 -->
    <el-dialog title="实时录屏" :visible.sync="dialogRecord" width="35%" custom-class="resRecord"
      :close-on-click-modal="false">
      <el-form :inline="true" class="demo-form-inline" style="margin-top: 20px; padding-left: 30px">
        <el-form-item label="录制时间">
          <el-input v-model="duration" placeholder="请输入录制时间"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="
          dialogRecord = false;
        duration = '';
        ">取 消</el-button>
        <el-button type="primary" @click="restartRecord">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 清除缓存 -->
    <el-dialog title="清除缓存" :visible.sync="cacheState" class="remoteScreen" custom-class="remote" width="50%">
      <p style="margin-top: 30px">
        您好，系统复位后，设备会重新下载所有资源，设备会处于待机无内容状态界面，请确认操作！
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cacheState = false">取 消</el-button>
        <el-button type="primary" @click="requestClearCache">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 预览mask -->
    <previsualization :isPreviewMaskShow="isPreviewMaskShow" :PreviewSrc="PreviewSrc" :PreviewType="PreviewType"
      @closePreviewMask="closePreviewMask" :carouselUrl="carouselUrl"></previsualization>
    <!-- 批量标签 -->
    <Dialog :tagsDialog="tagsDialog" @handleCloseDialog="handleCloseDialog" :tagsList="tagsList"
      :delTagsList="destyleList" @saveTags="saveTags"></Dialog>

    <!-- 修改设备亮度 -->
    <el-dialog title="修改设备亮度" :visible.sync="brightnessDialog" width="40%" :before-close="brightnessDialogClose">
      <div class="devices_info">
        <div v-for="(item, index) in brightnessForm" class="devices_bright_item">
          <div>{{ item.label }}:</div>
          <div style="margin-left: 10px;" class="bright_time">
            <el-time-picker is-range v-model="item.time_range" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束时间" placeholder="选择时间范围" value-format="HH:mm:ss" format="HH:mm:ss"
              @change="changeTimeRange(item, index)">

            </el-time-picker>
          </div>
          <div style="margin-left: 10px;" class="bright_value">
            <el-input placeholder="请输入内容" type="number" min="0" max="100" v-model="item.brightness" @blur="blurInput(item, index)">
              <template slot="append">%</template>
            </el-input>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="brightnessDialog = false">取 消</el-button>
        <el-button type="primary" @click="requestSettingBrightness">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  get_screen_info,
  send_ds_player_cmd,
  get_screen_pub_content,
  screen_edit,
  screen_unbind,
  handle_set_ds_presence
} from "@/api/device/device";
import {
  screen_capture,
  screen_recording,
  screen_clear_cache,
  screen_detection,
  detection_screenshot,
  detection_record,
  screen_add_delete_tags,
  screen_depth_detection,
  check_call_player,
  notify_sbml_sync_dscontent
} from "@/api/screen/screen";
import { get_screen_tags } from "@/api/system/label";

import { set_screen_timing } from "@/api/timing/timing";
import { screen_set_volume } from "@/api/volume/volume";
import {
  datas_filter_cond,
  pull_pkg_info_by_model,
  batch_upgrade,
  create_up_plan,
  screen_batch_processing
} from "@/api/systemCenter/updateHist";

import Dialog from "./components/Dialog";
import previsualization from "@/components/communal/previsualization";

export default {
  name: "ScreenDetail",
  components: {
    Dialog,
    previsualization
  },
  data() {
    return {
      //远程升级
      remoteShow: false,
      remoteDesk: false,
      loading: false,
      dialogVisible: false,
      isPreviewMaskShow: false,
      PreviewSrc: "",
      PreviewType: "carousel",
      carouselUrl: "big_tpl_snap",
      screenInfoData: {},
      screenPart: {
        // 屏幕序列
        options1: [],
        // 屏幕方向
        options2: [
          {
            value: "0",
            label: "0度"
          },
          {
            value: "1",
            label: "90度"
          },
          {
            value: "2",
            label: "180度"
          },
          {
            value: "3",
            label: "270度"
          }
        ]
      },
      value1: "屏幕序号/2号",
      screenDir: {
        screen_num: "",
        deg: ""
      },
      //
      // options2: [
      //   {
      //     value: "选项1",
      //     label: "正常0度",
      //   },
      //   {
      //     value: "选项2",
      //     label: "旋转90度",dynamic_url
      //   },
      //   {
      //     value: "选项3",
      //     label: "旋转180度",
      //   },
      //   {
      //     value: "选项4",
      //     label: "旋转270度",
      //   },
      // ],
      value2: "正常0度",
      activeName: "first",
      //  应用类型
      applyOptions: [],
      applyValue: "",
      //  版本
      betaOptions: [],
      betaValue1: "",
      versionPkg: "",
      vesionModel: "",
      isUploading: false,
      //  升级时段
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      updataValue: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()),
      //  升级时间
      updateTimeValue1: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0),
      updateTimeValue2: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59),
      //  分辨率
      resolution: [
        {
          value: "选项1",
          label: "640*480"
        },
        {
          value: "选项2",
          label: "1280*240"
        }
      ],
      resValue: "1280*240",
      //  音量
      voiceValue: 0,
      //  操作的箭头
      arrowDirBottom: "true",
      arrowDirTopShow: true,
      // 录屏弹窗状态
      dialogRecord: false,
      // 录制时间
      duration: "",

      // 是否在线
      isonline: false,
      // 清除缓存状态
      cacheState: false,
      // 主屏播放列表
      mainScreenList: [],
      // 副屏播放列表
      secondaryScreenList: [],
      pubJobTypeList: [
        {
          title: "下发",
          value: 0.2
        },
        {
          title: "获取",
          value: 0.4
        },
        {
          title: "下载",
          value: 0.6
        },
        {
          title: "播放",
          value: 1
        }
      ],
      activeId: 0,
      cdLoading: false,
      detecting: false, //是否正在检测设备
      screenhostState: false, // 截屏
      screenVideoState: false, //  录屏
      tagsDialog: false,
      // 标签列表
      tagsList: [],
      delTagsList: [],
      destyleList: [],
      // 定时开关机
      timing: [],
      // 设备类型
      screenType: "",
      awaitPlayList: [],
      nowDate: "",
      dayContentLoading: false,
      display_info: {
        //设置之后的屏幕信息
        status: "", //（int）屏幕横竖状态：1竖屏，0横屏
        resolution: "", //（string）屏幕分辨率
        screenratio: "", //（string）屏幕大小
        reverse_rotate: "", //（int）屏幕反向旋转：1反向，0非反向（正常和正常旋转90°的状态）
        deg: "",
        screen_key: ""
      },
      screen_key: '',
      // resolution:[],
      temp_info: {
        resolution: [],
        screenKey: [],
        status: ""
      },
      screenDeg: {},
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() < new Date().getTime() - 86400000; //  - 86400000是否包括当天
        }
      },
      disabledContent: false,
      localDisabled: "",
      loadingDisabled: false,
      eventScreen: {},
      device_model: '',
      pmodel: '',
      show_sid: 0, // 终端ID显示
      show_sid_editing: false,
      usb_enable: 0, // 终端USB接入
      sound_enable: 0, // 终端外音设备接入
      hdmi_enable: 0,
      brightness: 0,
      brightnessList: [
        { label: '上午', "time_range": "", "brightness": 0 },
        { label: '中午', "time_range": "", "brightness": 0 },
        { label: '下午', "time_range": "", "brightness": 0 },
      ],
      brightnessForm: [
        { label: '上午', "time_range": "", "brightness": 0 },
        { label: '中午', "time_range": "", "brightness": 0 },
        { label: '下午', "time_range": "", "brightness": 0 },
      ],
      brightnessDialog: false
    };
  },
  created() {
    this.getScreenInfo();
    this.getTagsHave();
    this.getPlaylist(this.$route.query.sid);
    this.getTagsList();
    // this.getSelectList();
    this.nowDate = new Date().getTime();
    this.localDisabled = localStorage.getItem("branchcode");
  },
  mounted() {
  },
  watch: {
    isPreviewMaskShow(val) {
      if (val) {
        this.$nextTick(() => {
          document.addEventListener("mousewheel", this.mousewheelHandler, {
            passive: false
          });
        });
      } else {
        this.$nextTick(() => {
          document.removeEventListener("mousewheel", this.mousewheelHandler, {
            passive: false
          });
        });
      }
    }
  },
  computed: {
    canGetContent() {
      if (this.screenInfoData.usage_type == 'dmb') {
        if (this.screenInfoData.displaynum == 1) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    }
  },
  methods: {
    mousewheelHandler(e) {
      e.preventDefault();
    },
    // 获取设备详情
    getScreenInfo() {
      const screen_key = this.$route.query.sid;
      this.screen_key = screen_key;
      const params = {
        screen_key: screen_key, //屏幕id/key
        range: "full" //"full":全量数据; "simple":简易数据
      };
      get_screen_info(params).then(res => {
        if (res.rst == "ok") {
          // this.screen_key = res['data'][0]['screen_key'];
          this.screenInfoData = res.data[0];
          this.activeId = res.data[0].screen_id;
          this.voiceValue = res.data[0].deploy_info.curr_volume;
          this.screenType = this.screenInfoData.hardware_info.pmodel;
          const rom = res.data[0].app_info.other_info.find(item => {
            return item.boundle_id == "rom";
          });
          const server_info = res.data[0].app_info.other_info.find(item => {
            return item.boundle_id == "com.instwall.server";
          });
          const launch_info = res.data[0].app_info.other_info.find(item => {
            return item.boundle_id == "com.instwall.normal.launch";
          });

          this.screenInfoData.romVersion = rom ? rom.version : '';
          this.screenInfoData.serverVersion = server_info ? server_info.version : '';
          this.screenInfoData.launchVersion = launch_info ? launch_info.version : '';
          this.device_model = res['data'][0]['hardware_info']['model'];
          console.log(this.device_model, 'device_model');
          // this.timing = res.data[0].deploy_info.on_off_policy
          //   ? res.data[0].deploy_info.on_off_policy.every_day
          //   : ["", ""];

          if(res.data[0].deploy_info.on_off_policy && res.data[0].deploy_info.on_off_policy.every_day){
            this.timing = res.data[0].deploy_info.on_off_policy.every_day;
          }else{
            this.timing = ["", ""];
          }

          this.isonline = res.data[0].isonline == true ? false : true;
          this.show_sid = res.data[0].deploy_info.show_sid ? res.data[0].deploy_info.show_sid : 0;
          this.usb_enable = res.data[0].deploy_info.usb_enable ? res.data[0].deploy_info.usb_enable : 0;
          this.sound_enable = res.data[0].deploy_info.sound_enable ? res.data[0].deploy_info.sound_enable : 0;
          this.hdmi_enable = res.data[0].deploy_info.hdmi_enable ? res.data[0].deploy_info.hdmi_enable : 0;

          if (res['data'][0]['deploy_info']['brightness_policy']) {
            this.brightnessList = res['data'][0]['deploy_info']['brightness_policy']['every_day'];
            this.brightnessList.forEach((item,index)=>{
              this.$set(item,'label',)
              if(index == 0){
                item.label = "上午"
              }else if(index == 1){
                item.label = '中午'
              }else if(index == 2){
                item.label = '下午'
              }
            })

          }
          if (res['data'][0]['deploy_info']['brightness']) {
            this.brightness = res['data'][0]['deploy_info']['brightness'];
          }


          this.screenInfoData.all_displayinfo.forEach((item, index) => {
            this.temp_info = {
              ...this.temp_info,
              ...item.display
            };
            this.getSelectList();

          });

          this.screenDir.screen_num = this.screenInfoData["all_displayinfo"][0][
            "screen_id"
          ];

          this.screenDeg = this.screenInfoData["all_displayinfo"][0];
          //   // 横向

          if (this.screenInfoData.display.v_or_h == "horizontal") {
            this.display_info.status = 0;
            if (this.screenInfoData.display_info.reverse_rotate == 1) {
              // alert(1)
              this.screenDir.deg = this.screenPart.options2[2].label;
              this.display_info.reverse_rotate = 1;
              // this.display_info.status = 0;
            } else {
              // alert(2)
              this.screenDir.deg = this.screenPart.options2[0].label;
              this.display_info.reverse_rotate = 0;
              // this.display_info.status = 0;
            }
            this.temp_info.status = this.display_info.status;
          } else if (this.screenInfoData.display.v_or_h == "vertical") {
            // 竖屏
            this.display_info.status = 1;
            if (this.screenInfoData.display_info.reverse_rotate == 1) {
              this.screenDir.deg = this.screenPart.options2[3].label;
              this.display_info.reverse_rotate = 0;
              // this.display_info.status = 1;
            } else {
              this.screenDir.deg = this.screenPart.options2[1].label;
              this.display_info.reverse_rotate = 1;
              // this.display_info.status = 1;
            }
            this.temp_info.status = this.display_info.status;
          }

        } else {
          this.$message.error(res.error_msg);
        }
      });
    },
    //获取播放列表当天内容
    getPlaylist(sid) {
      this.dayContentLoading = true;
      this.mainScreenList = [];
      this.awaitPlayList = [];
      const params = {
        screen_id: parseInt(sid), // (Int) 设备id
        ky_flag: 1 // (Int) 快饮数据专用
      };
      get_screen_pub_content(params).then(res => {
        console.log(res, "001");
        if (res.rst == "ok") {
          res.data[0].cf_cs_list.forEach(item => {
            // if (
            //   this.nowDate < new Date(item.time_ranges_list[0][0]).getTime() &&
            //   item.play_mode != "time_range"
            // ) {
            //   this.awaitPlayList.push(item);
            // } else {
            //   this.mainScreenList.push(item);
            //   this.mainScreenList.push(item);
            // }
            // 当天内容 today_content==1
            console.log(item, 'item');
            if (item.today_content == 1) {
              this.mainScreenList.push(item);
            } else {
              this.awaitPlayList.push(item);
            }

            if (item.cf_bar.process == 0.6) {
              item.cf_bar.process = 1;
            }
            // if (this.nowDate < new Date(item.end_time).getTime()) {
            //   this.mainScreenList.push(item);
            // } else {
            //   this.awaitPlayList.push(item);
            // }
          });
          // console.log(this.mainScreenList, "mainScreenList");
          this.cdLoading = false;
          this.dayContentLoading = false;
        } else {
          this.$message.error("获取播放列表内容失败");
          this.cdLoading = false;
          this.dayContentLoading = false;
        }
      });
    },
    changeActive(sid) {
      if (sid == this.activeId) return;
      if (this.cdLoading) {
        this.$message.warning("获取数据中,请稍后在进行操作");
        return;
      }
      this.cdLoading = true;
      this.activeId = sid;
      this.getPlaylist(sid);
    },
    // 格式化状态
    formatAct(val) {
      let status;
      switch (val.cf_bar.pub_job_type) {
        case "02":
          status = 0;
          break;
        case "04":
          status = 1;
          break;
        case "06":
          status = 2;
          break;
        case "1":
          status = 3;
          break;
      }
      return status;
    },
    //tab切换事件
    handleClick(tab, event) {
      console.log(tab, event);
    },
    //  远程升级
    clickRemote() {
      //   let type = this.$route.query.pmodel;
      //   if (type != "LENOVO" && type != "X86" && type != 'YANHUA' && type != '3399') {
      //     return this.$message.warning("该设备暂不支持远程升级");
      //   }
      this.remoteShow = true;
    },
    // 设备检测
    detection() {
      this.detecting = true;
      // const params = {
      //   screens: [String(this.screenInfoData.screen_id)],
      //   check_real: 1,
      // };
      // screen_detection(params).then((res) => {
      //   if (res.rst == "ok") {
      //     for (let key in res.data[0]) {
      //       this.screenInfoData.isonline = res.data[0][key];
      //       setTimeout(() => {
      //         this.$message.success("设备运行监测成功");
      //         this.detecting = false;
      //       }, 1500);
      //     }
      //   }
      // });

      const params = {
        screen_id: String(this.screenInfoData.screen_id),
        cmd: "shell:id",
        range_type: "with_admin"
      };
      const paramsc = {
        pgid: localStorage.getItem("group_id"),
        // sid: '290254',
        sid: String(this.screenInfoData.screen_id),
        chkcmd: "shell:id"
        // chkcmd: "shell:id"
      };
      let state = null;
      let times = 0;
      screen_depth_detection(params).then(res => {
        if (res.rst == "ok") {
          let timer = setInterval(() => {
            times++;
            check_call_player(paramsc).then(ress => {
              if (times > 29) {
                clearInterval(timer);
                this.screenInfoData.isonline = false;
                this.detecting = false;
                this.$message.warning("设备不在线");
                this.setOnlineStatus('offline')
              } else {
                if (JSON.parse(ress.data[0].chk_msg).s.length != 0) {
                  this.screenInfoData.isonline = true;
                  this.$message.success("设备运行监测成功");
                  this.detecting = false;
                  this.setOnlineStatus('online')
                  clearInterval(timer);
                }
              }
            });
          }, 1000);

          // for (let key in res.data[0]) {
          //   this.screenInfoData.isonline = res.data[0][key];
          //   setTimeout(() => {
          //     this.$message.success("设备运行监测成功");
          //     this.detecting = false;
          //   }, 1500);
          // }
        }
      });
    },
    setOnlineStatus(type) {
      let params = {
        screen_id: this.screenInfoData.screen_id,
        ds_status: 0
      }
      if (type == 'online') {
        params.ds_status = 1;
      } else {
        params.ds_status = 0;
      }
      notify_sbml_sync_dscontent(params).then(res => {
        if (res.rst == 'ok') {
          console.log('同步成功');
        } else {
          console.log('同步失败', res.error_msg);
        }
      }).catch(err => {
        console.log('同步失败', err);
      })
    },
    //  提交
    submitRemote() {
      this.remoteShow = false;
    },
    // 事实截屏
    shotScreen() {
      this.screenhostState = true;
      const params = {
        screen_id: this.screenInfoData.screen_id // (Int) 设备id
      };
      screen_capture(params).then(res => {
        if (res.rst == "ok") {
          let timerNumber = 0;
          let timer = setInterval(() => {
            timerNumber++;
            detection_screenshot({
              screen_id: this.screenInfoData.screen_id
            }).then(res => {
              if (res.data[0].ack != 0) {
                this.$message.success("截屏成功");
                this.screenhostState = false;
                clearInterval(timer);
              } else {
                if (timerNumber == 15) {
                  this.$message.warning(
                    "截图失败,请检查设备网络和截图功能是否安装！"
                  );
                  this.screenhostState = false;
                  clearInterval(timer);
                }
              }
            });
          }, 1000);
        } else if (res.error_msg.indexOf("send cmd interval time is") != -1) {
          this.$message.warning("操作太过频繁，请5秒后再试");
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 打开录屏弹窗
    recordingScreen() {
      this.dialogRecord = true;
      this.duration = "";
    },
    // 录屏确认
    restartRecord() {
      this.screenVideoState = true;
      const params = {
        screen_id: this.screenInfoData.screen_id, // (Int) 设备id
        range_type: "with_admin", // 固定字段
        cmd: "cs_recorded", // 固定字段
        ex_info: {
          duration: this.duration
        }
      };
      screen_recording(params).then(res => {
        if (res.rst == "ok") {
          let timerNumber = 0;
          let timer = setInterval(() => {
            timerNumber++;
            detection_record({
              screen_id: this.screenInfoData.screen_id
            }).then(res => {
              if (res.data[0].ack != 0) {
                this.$message.success("录屏成功");
                this.screenVideoState = false;
                clearInterval(timer);
              } else {
                if (timerNumber == 15) {
                  this.$message.warning(
                    "录屏失败,请检查设备网络和录屏功能是否安装！"
                  );
                  this.screenVideoState = false;
                  clearInterval(timer);
                }
              }
            });
          }, 1000);
          this.dialogRecord = false;
        } else {
          this.$message.warning(res.rst);
          this.screenVideoState = false;
        }
      });
    },
    // 查看截图/录屏
    lookScreen(state) {
      console.log(this.screenInfoData, "this.screenInfoData");
      this.$router.push({
        path: "/deviceManage/shotRecord",
        query: {
          screen_id: this.screenInfoData.screen_id,
          LookState: state,
          displaynuminfo: this.screenInfoData["displaynuminfo"],
          // all_displayinfo: this.$route.query.all_displayinfo,
          all_displayinfo: JSON.stringify(this.screenInfoData.all_displayinfo),
          isLook: true
        }
      });
    },
    // 清除缓存
    clearCache() {
      this.cacheState = true;
    },
    unBind() {
      this.$confirm("此操作将拆除此设备, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const params = {
            screen_key: this.$route.query.sid
          };
          screen_unbind(params).then(res => {
            console.log(params);
            if (res.rst == "ok") {
              this.$message({
                type: "success",
                message: "设备删除成功!"
              });
              this.$router.go(-1);
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
      // console.log(this.$route.query.sid);
      // this.$confirm('此操作将拆除此屏幕, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   const params = {
      //     screen_key: this.$route.query.sid
      //   }
      //   screen_unbind(params).then(res => {
      //     if (res.rst == "ok") {
      //       this.$message({
      //         type: 'success',
      //         message: '拆除成功!'
      //       });
      //       this.$router.go(-1)
      //     } else {
      //       this.$message.warning(res.error_msg)
      //     }
      //   })
      // }).catch(() => {
      //   this.$message({
      //     type: 'info',
      //     message: '已取消删除'
      //   });
      // });
    },
    // 确认清除
    requestClearCache() {
      this.loading = true;
      const params = {
        screen_id: this.screenInfoData.screen_id,
        cmd: "clear_ds_data",
        range_type: "with_admin"
      };
      screen_clear_cache(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("清除缓存成功");
          this.loading = false;
        } else {
          this.$message.warning(res.rst);
        }
        this.cacheState = false;
      });
    },
    //  远程桌面
    clickRemoteDesk() {
      this.remoteDesk = true;
    },
    //  音量滑动
    formatTooltip(val) {
      return val / 100;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(_ => {
          done();
        })
        .catch(_ => { });
    },
    //  开启按钮
    clickStart() {
      this.$router.push({
        path: "/screenDevice/setRemoteDev"
      });
    },
    //  播放
    toPlayer(idx) {
      console.log("播放");
    },
    //  操作的箭头
    arrowDir() {
      this.arrowDirBottom = !this.arrowDirBottom;
      if (this.arrowDirBottom == false) {
        this.arrowDirTopShow = false;
      } else {
        this.arrowDirTopShow = true;
      }
    },
    goBack() {
      // this.$router.replace({ path: "/deviceManage/dslist" });
      // this.$router.go(-1);
      console.log(this.$route);
      this.$router.push({
        path: "/deviceManage/screenGroup"
      })


    },
    // 设备重启的确认按钮
    restartDev() {
      this.loading = true;
      const params = {
        screen_id: this.screenInfoData.screen_id, // (Int) 设备id
        cmd: "reboot_device", // (String) 固定参数
        range_type: "with_admin" // (String) 固定参数
      };
      send_ds_player_cmd(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("重启成功");
          this.dialogVisible = false;
          this.loading = false;
        } else {
          this.$message.fail("重启失败");
          this.dialogVisible = false;
        }
      });
    },
    // 预览
    handlePreview(src) {
      this.PreviewSrc = src;
      // this.PreviewSrc = 'http://grayds.instwall.com/store/render_native_play/?template_id=5222&section_id=694757&do_type=edit&render_type=sg';
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    },
    // 修改标签
    openTags(items) {
      this.getTagsList();
      this.tagsDialog = true;
      // console.log(items,'itemsitems');
      this.eventScreen = items;
    },
    // 获取已有标签
    getTagsHave() {
      this.destyleList = [];
      const screen_key = this.$route.query.sid;
      const params = {
        screen_key: screen_key, //屏幕id/key
        range: "full" //"full":全量数据; "simple":简易数据
      };
      get_screen_info(params).then(res => {
        if (res.data[0].tags) {
          res.data[0].tags.forEach(item => {
            this.destyleList.push({
              name: item
            });
          });
          this.delTagsList = res["data"][0].tags;
          this.destyleList.forEach((item, index) => {
            this.$set(item, "active", false);
          });
        }
      });
    },
    // 获取标签
    getTagsList() {
      this.tagsList = [];
      const params = {
        page: 0,
        size: this.TagePageSize
      };
      get_screen_tags(params).then(res => {
        console.log(res, "res");
        if (res.rst == "ok") {
          this.TagePageSize = res.data[0].totalElements;

          res.data[0].tags_list.forEach(item => {
            this.tagsList.push({
              name: item,
              active: false
            });
          });
          this.tagsList.forEach((item, index) => {
            this.destyleList.forEach(item1 => {
              if (item.name == item1.name) {
                this.tagsList.splice(index, 1);
              }
            });
          });
        }
      });
    },
    // 关闭标签弹框
    handleCloseDialog() {
      this.tagsDialog = false;
    },
    // 保存
    saveTags(tags, message) {
      if (tags.length != 0) {
        let params = {
          // screen_ids: [this.eventScreen.screen_id],
          screen_ids: [this.$route.query.sid],
          tags: tags,
          action: "del"
        };
        if (message == "deltag") {
          params.action = "del";
          screen_add_delete_tags(params).then(res => {
            if (res.rst == "ok") {
              this.$message.success("屏幕标签删除成功");
              this.getTagsHave();
              this.tagsDialog = false;
            } else {
              this.$message.error(res.error_msg);
            }
          });
        } else if (message == "relation") {
          params.action = "add";
          screen_add_delete_tags(params).then(res => {
            if (res.rst == "ok") {
              this.$message.success("屏幕标签增加成功");
              this.getTagsHave();
              this.tagsDialog = false;
            } else {
              this.$message.error(res.error_msg);
            }
          });
        }
      } else {
        this.tagsDialog = false;
      }
    },
    // 定时开关机
    requestTimer() {
      console.log(this.timing, 'timing');

      if (this.timing.length != 2) {
        return this.$message.error('请选完整开关机时间')
      }


      const params = {
        screens: [String(this.screenInfoData.screen_id)],
        check_real: 1
      };
      this.timing.forEach((item, index) => {
        if (item == null) {
          this.timing[index] = "";
        }
      });
      screen_detection(params).then(res => {
        if (res.rst == "ok") {
          for (let key in res.data[0]) {
            this.screenInfoData.isonline = res.data[0][key];
            // if (this.screenInfoData.isonline == true) {
            let param = {
              screen_key: this.$route.query.sid,
              deploy_info: {
                on_off_policy: {
                  every_day: this.timing
                }
              }
            };
            set_screen_timing(param).then(res => {
              if (res.rst == "ok") {
                this.$message.success("事件时间设置成功");
              }
            });
            //  } else {
            //   this.$message.warning("设备是否在线");
            // }
          }
        }
      });
    },
    cancelTimer() {
      this.timing = ["", ""];
      let param = {
        screen_key: this.$route.query.sid,
        deploy_info: {
          on_off_policy: {
            every_day: this.timing
          }
        }
      };
      set_screen_timing(param).then(res => {
        if (res.rst == "ok") {
          this.$message.success("取消时间机制设置成功");
        }
      });
    },
    // 设备音量
    saveVolume() {
      const params = {
        screen_key: this.screenInfoData.screen_id,
        screen_name: this.screenInfoData.screen_name,
        deploy_info: {
          curr_volume: this.voiceValue
        }
      };
      screen_set_volume(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("设备音量设置成功");
          this.getScreenInfo();
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    // 列表计划属性
    getPlan(val) {
      let intervalName = "",
        intervalDate = "",
        intervalTime = "";

      if (val.play_mode == "single_use_range") {
        return "按指定时间段";
      } else if (val.play_mode == "week_range") {
        intervalName = "按周内日期";
        val.week_list.forEach(item => {
          switch (item) {
            case 1:
              intervalDate += "周一 ";
              break;
            case 2:
              intervalDate += "周二 ";
              break;
            case 3:
              intervalDate += "周三 ";
              break;
            case 4:
              intervalDate += "周四 ";
              break;
            case 5:
              intervalDate += "周五 ";
              break;
            case 6:
              intervalDate += "周六 ";
              break;
            case 7:
              intervalDate += "周日 ";
              break;
          }
        });
        intervalTime = val.time_ranges[0];
        return `${intervalName}（${intervalDate} ${intervalTime}）`;
      } else if (val.play_mode == "full_day") {
        return "全天播放";
      } else if (val.play_mode == "time_range") {
        return `小时时段${val.time_ranges[0]} `;
      }
    },
    // changeNum(val){
    //   console.log(val,'val23');
    //   if(val==0){
    //     this.screenDir.screen_num = this.screenPart.options1[0].label
    //   }else if(val==1){
    //     this.screenDir.screen_num = this.screenPart.options1[1].label
    //   }
    //   console.log( this.screenDir.screen_num ,' this.screenDir.screen_num ');
    // },
    changeVal(val) {
      // this.temp_info.reverse_rotate = val;
      // this.screenInfoData.display.v_or_h=='horizontal'?this.temp_info.status = 0:this.temp_info.status = 1
      // 1竖屏，0横屏
      // 横屏
      // 0度
      // console.log(val,'-1');
      if (val == 0) {
        this.temp_info.reverse_rotate = 0;
        this.temp_info.status = 0;
        // 90度
      } else if (val == 1) {
        this.temp_info.reverse_rotate = 0;
        this.temp_info.status = 1;
        // 180度
      } else if (val == 2) {
        this.temp_info.reverse_rotate = 1;
        this.temp_info.status = 0;
        // 270度
      } else if (val == 3) {
        this.temp_info.reverse_rotate = 1;
        this.temp_info.status = 1;
      }
    },
    changeDir(val) {
      // console.log(this.screenInfoData.all_displayinfo[1].screen_id,'this.screenInfoData.all_displayinfo[1].screen_id');
      if (val == this.screenInfoData.all_displayinfo[1].screen_id) {
        if (
          this.screenInfoData.all_displayinfo[1].display.v_or_h == "horizontal"
        ) {
          if (
            this.screenInfoData.all_displayinfo[1].display.reverse_rotate == 1
          ) {
            this.screenDir.deg = this.screenPart.options2[2].label;
          } else {
            // alert(2)
            this.screenDir.deg = this.screenPart.options2[0].label;
            // this.display_info.reverse_rotate = 0;
            // this.display_info.status = 0;
          }
        } else if (
          this.screenInfoData.all_displayinfo[1].display.v_or_h == "vertical"
        ) {
          // 竖屏
          //  this.display_info.status = 1;
          if (
            this.screenInfoData.all_displayinfo[1].display.reverse_rotate == 1
          ) {
            this.screenDir.deg = this.screenPart.options2[3].label;
            // this.display_info.reverse_rotate = 0;
            // this.display_info.status = 1;
          } else {
            this.screenDir.deg = this.screenPart.options2[1].label;
            // this.display_info.reverse_rotate = 1;
            // this.display_info.status = 1;
          }
        }
      }
      // if (val == 0) {
      //   // console.log(this.temp_info.screenKey[0],'this.temp_info.screen_id');
      //   this.display_info.screen_key = this.temp_info.screenKey[0];
      //   this.display_info.resolution = this.temp_info.resolution[0];
      //   // console.log(this.display_info.screen_key ,'this.display_info.screen_key ');
      //   // console.log(this.temp_info,'this.temp_info.screenratio');
      //   // console.log(this.temp_info.resolution[0],'');
      //   // this.display_info.screen_id = this.temp_info.screen_id;
      // } else if (val == 1) {
      //   this.display_info.screen_key = this.temp_info.screenKey[1];
      //   this.display_info.resolution = this.temp_info.resolution[1];
      // }

      // this.screenDeg = val;
    },
    // screen_edit
    saveScreenDir() {
      // if()
      // const screen_key = this.$route.query.sid;
      // const params = {
      //   screen_key: this.screenDir.screen_num,
      //   screen_name: this.screenInfoData["screen_name"],
      //   deploy_info: {
      //     curr_volume: this.voiceValue ? this.voiceValue : 0
      //   },
      //   display_info: {
      //     status: this.temp_info.status,
      //     reverse_rotate: this.temp_info.reverse_rotate,
      //     resolution: `${this.screenDeg["display"]["pixel"][0]}*${this.screenDeg["display"]["pixel"][1]}`,
      //     screenratio: this.screenInfoData["display_info"]["screenratio"]
      //   }
      // };
      let resolution = "";
      let screenratio = "";
      // if (this.temp_info.status == 1) {
      //   let list = this.screenInfoData["display_info"]["resolution"].split("*");
      //   let rotateList = this.screenInfoData["display_info"][
      //     "screenratio"
      //   ].split(":");
      //   resolution = list[1] + "*" + list[0];
      //   screenratio = rotateList[1] + ":" + rotateList[0];
      // } else {
      //   resolution = this.screenInfoData["display_info"]["resolution"];
      //   screenratio = this.screenInfoData["display_info"]["screenratio"];
      // }


      let display_info = {
        status: '',
        resolution: '',
        reverse_rotate: '',
        screenratio: ''
      }

      switch (this.screenDir.deg) {
        case '0':
          display_info.status = 0;
          display_info.resolution = '1920*1080',
            display_info.screenratio = "16:9",
            display_info.reverse_rotate = 0
          break;

        case '1':
          display_info.status = 1;
          display_info.resolution = '1080*1920',
            display_info.screenratio = "9:16",
            display_info.reverse_rotate = 0
          break;

        case '2':
          display_info.status = 0;
          display_info.resolution = '1920*1080',
            display_info.screenratio = "16:9",
            display_info.reverse_rotate = 1
          break;

        case '3':
          display_info.status = 1;
          display_info.resolution = '1080*1920',
            display_info.screenratio = "9:16",
            display_info.reverse_rotate = 1
          break
      }


      const params = {
        screen_key: this.screenDir.screen_num,
        screen_name: this.screenInfoData["screen_name"],
        deploy_info: {
          curr_volume: this.voiceValue ? this.voiceValue : 0
        },
        display_info: display_info
      };

      screen_edit(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("修改成功");
          this.getScreenInfo();
        } else if (res.error_code == 6036) {
          this.$message.warning("在播内容不可以修改屏幕横竖");
          this.getScreenInfo();
        } else {
          this.$message.warning(res.error_msg);
        }
      });
      // const params = {
      //   "screen_key":this.display_info.screen_key,  //（int）屏幕id
      //   "screen_name":this.screenInfoData.screen_name, //（string）屏幕名称
      //   "deploy_info":{
      //       "curr_volume": this.voiceValue //（int）音量
      //   },
      //   "display_info":{ //设置之后的屏幕信息
      //       "status":this.temp_info.status, //（int）屏幕横竖状态：1竖屏，0横屏
      //       "resolution":this.display_info.resolution , //（string）屏幕分辨率
      //       "screenratio":this.temp_info.screenratio, //（string）屏幕大小
      //       "reverse_rotate":this.temp_info.reverse_rotate //（int）屏幕反向旋转：1反向，0非反向（正常和正常旋转90°的状态）
      //   }
      // }
      // console.log(this.screenDir.screen_num ,'this.screenDir.screen_num ');
      // console.log(params,'paramsxxx');
      // screen_edit(params).then(res=>{
      //   console.log(res,'resxxx');
      //   if(res.rst=='ok'){
      //     this.$message.success('修改成功');
      //     this.getScreenInfo()
      //     // this.screenPart.options1
      //     this.$set(this.screenPart,"options1",[])
      //     // this.screenDir.deg = this.display_info.deg;
      //     // 这是因为加入联屏组了，不能设置横竖
      //   }else if(res.error_msg=='Edit_screen screen sg_status joined, not enable edit screen v_or_h'){
      //     this.$message.info('该屏幕已加入联屏组了，不能设置横竖')
      //     // 这个应该是有播放内容，不能设置横竖
      //   }else if(res.error_msg=='Edit_screen screen had sub cs, not enable edit screen v_or_h'){
      //     this.$message.info('该屏幕有播放内容，不能设置横竖')
      //   }else{
      //     console.log('error');
      //   }
      // })
    },
    // 获取应用类型下拉数据
    getSelectList() {
      // let type = this.$route.query.pmodel;
      // let type = this.$route.query.pmodel;
      // let type = 

      // if (this.device_model == "LENOVO" || this.device_model == "X86" || this.device_model == "YANHUA") {
      const params = {
        classModel: "BatchUpgrade"
      };
      datas_filter_cond(params).then(res => {
        if (res.rst == "ok") {
          this.applyOptions = res.data[0][2]["options"];
        } else {
          this.$message.error(res.error_msg);
        }
      });
      // }
    },
    // 用 升级设备，应用类型查版本列表
    getVersionList() {
      this.betaOptions = [];
      this.betaValue1 = "";
      if (this.applyValue != "") {
        const user_id = localStorage.getItem("user_id");
        console.log(this.device_model, 'this.device_model');
        let pmodel = this.device_model
        // if (this.device_model.indexOf('Lenovo_3399') != -1 ) {
        //   pmodel = '3399_Lenovo'
        // } else if (this.device_model.indexOf('x86') != -1) {
        //   pmodel = 'X86'
        // } else if (this.device_model.indexOf('yh3399') != -1) {
        //   pmodel = '3399_yh'
        // }

        // console.log(pmodel, 'pmodel');
        this.pmodel = pmodel

        const params = {
          ruid: user_id, //（string）登录ID
          pkg: this.applyValue, //（string）应用类型
          pmodel: pmodel //（string）设备类型
        };
        pull_pkg_info_by_model(params).then(res => {
          this.betaOptions = res.data[0]["version"];
          this.versionPkg = res.data[0].pkg;
          this.vesionModel = res.data[0].model;
        });
      }
    },
    // 设备升级
    handleUpdgrade() {
      let status = this.valueCheck();
      if (status != "") {
        this.$message.warning(status);
        return;
      }
      let sceenList = this.screenInfoData.all_displayinfo.map(item => {
        return item.screen_id;
      });
      this.isUploading = true;
      let day = dayjs(this.updataValue).format("YYYY-MM-DD");
      let stime = dayjs(this.updateTimeValue1).format("HH:mm");
      let etime = dayjs(this.updateTimeValue2).format("HH:mm");
      const params = {
        ruid: localStorage.getItem("user_id"),
        up_type: "0",
        // plan_time: `${day}|${stime}~${etime}`,
        pkg: this.versionPkg,
        // pmodel: this.$route.query.pmodel,
        pmodel: this.pmodel,
        model: this.vesionModel,
        version: this.betaValue1,
        upgrade_type: "single",
        screen_id: sceenList
      };
      console.log(params, 'params');
      // ret
      // console.log(this.applyValue,'applyValue');
      batch_upgrade(params).then(res => {
        if (res.rst == "ok") {
          this.deviceUpgrade(res.data[0]);
        } else {
          this.isUploading = false;
        }
      });
    },
    // 创建升级计划
    // ceeateUpPlan(params){
    //     create_up_plan(params).then(res=>{
    //         if(res.rst == 'ok'){
    //             this.deviceUpgrade(params)
    //         }else{
    //             if(res.error_msg.results !== 1002){
    //               this.$message.error(res.error_msg);
    //             }
    //             this.isUploading = false;
    //         }
    //     })
    // },
    // 设备升级
    deviceUpgrade(val) {
      let arr = [];
      val.screencheck.split("-").forEach(item => {
        if (item != "") {
          arr.push(item);
        }
      });
      let batch_job = ''
      if (this.applyValue == 'rom') {
        batch_job = 'do_rom_upgrade'
      } else {
        batch_job = 'do_upgrade'
      }


      let params = {
        screen_ids: arr,
        // batch_job: "do_upgrade",
        // batch_info: "do_upgrade"
        batch_job: batch_job,
        batch_info: val.batch_info
      };
      screen_batch_processing(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("创建升级计划成功,详情请前往升级记录查看");
          this.isUploading = false;
          this.resetDialog();
        } else {
          this.$message.error(res.error_msg);
          this.remoteShow = false;
          this.isUploading = false;
        }
      });
    },
    resetDialog() {
      this.betaOptions = [];
      this.remoteShow = false;
      this.applyValue = "";
      this.betaValue1 = "";
      this.updataValue = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
      this.updateTimeValue1 = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0);
      this.updateTimeValue2 = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 23, 59);
    },
    // 校验
    valueCheck() {
      if (this.applyValue == "") {
        return "请先选择应用类型";
      }
      if (this.betaValue1 == "") {
        return "请先选择升级版本";
      }
      if (this.updataValue == "") {
        return "请先选择升级时段";
      }
      if (this.updateTimeValue1 == "") {
        return "请先选择开始时间";
      }
      if (this.updateTimeValue2 == "") {
        return "请先选择结束时间";
      }
      return "";
    },
    getScreenContent() {
      this.disabledContent = true;
      this.loadingDisabled = true;
      let params = {
        screen_id: this.screenInfoData.screen_id
      };

      notify_sbml_sync_dscontent(params).then(res => {
        if (res.rst == "ok") {
          if (res["data"][0]["do_status"] == 1) {
            this.$message.success("获取内容触发成功,内容推送中");
            this.loadingDisabled = false;
          } else {
            this.$message.warning("内容获取失败");
            this.loadingDisabled = false;
          }
          setTimeout(() => {
            this.disabledContent = false;
          }, 15000);
        } else {
          this.$message.error(res.error_msg);
          this.loadingDisabled = false;
          this.disabledContent = true;
          setTimeout(() => {
            this.disabledContent = false;
          }, 15000);
        }
      });
    },
    changeTerminalSettings(type) {
      console.log(this.screenDir, 'this.screenDir');

      let params = {
        screen_key: this.screen_key,
        screen_name: this.screenInfoData["screen_name"],
        deploy_info: {}
      };
      switch (type) {
        case 'show_sid':
          params.deploy_info.show_sid = this.show_sid;
          break;
        case 'usb_enable':
          params.deploy_info.usb_enable = this.usb_enable;
          break;
        case 'sound_enable':
          params.deploy_info.sound_enable = this.sound_enable;
          break;
        case 'hdmi_enable':
          params.deploy_info.hdmi_enable = this.hdmi_enable;
      }
      screen_edit(params).then(res => {
        console.log(res);
        if (res.rst == 'ok') {
          this.$message.success('修改成功');
        } else {
          this.$message.error(res.error_msg);
        }
      })
    },
    brightnessDialogClose() {
      this.brightnessDialog = false;
    },
    settingBrighness() {
      this.brightnessDialog = true;
      console.log(this.brightnessList,'brightnessList');
      
      if(!this.brightnessList[0]['time_range'] || !this.brightnessList[1]['time_range'] || !this.brightnessList[2]['time_range']) {
        return
      }
      this.brightnessForm = JSON.parse(JSON.stringify(this.brightnessList)) 
      this.brightnessForm.forEach(item=>{
        let listStr = [item.time_range.split("~")[0],item.time_range.split("~")[1]]
        item.time_range = JSON.parse(JSON.stringify(listStr))
      })
    },
    changeTimeRange(item, index) {
      console.log(index, 'index')
      console.log(item, 'index')
      switch (index) {
        case 0:
          this.brightnessForm[1]['time_range'] = []
          this.brightnessForm[1]['time_range'][0] = item.time_range[1]
          this.brightnessForm[1]['time_range'][1] = this.addOneHour(item.time_range[1])
          break;
        case 1:
          console.log(item.time_range, 'item.time_range')
          this.brightnessForm[2]['time_range'] = []
          this.brightnessForm[2]['time_range'][0] = item.time_range[1]
          this.brightnessForm[2]['time_range'][1] = this.addOneHour(item.time_range[1])
          break;
      
        default:
          break;
      }
    },
    addOneHour(timeString) {
      // 正则表达式解析时间字符串
      const regex = /^(\d{2}):(\d{2}):(\d{2})$/;
      const match = timeString.match(regex);

      if (!match) {
        throw new Error('Invalid time format');
      }

      // 提取小时、分钟和秒
      const hours = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const seconds = parseInt(match[3], 10);

      // 增加一个小时
      let newHours = (hours + 1) % 24; // 考虑超过24小时的情况

      // 格式化输出
      const newMinutes = String(minutes).padStart(2, '0');
      const newSeconds = String(seconds).padStart(2, '0');
      const newTimeString = `${String(newHours).padStart(2, '0')}:${newMinutes}:${newSeconds}`;

      return newTimeString;
    },
    blurInput(item,index){
      console.log(item,'index')
      if (item.brightness < 0 || item.brightness > 100) {
        // 如果值不在0到100之间，你可以重置它或者给出提示
        this.$message.error('亮度值必须在0到100之间');
        item.brightness = Math.max(0, Math.min(100, item.brightness)); // 可选：自动修正值
      }

    },
    requestSettingBrightness() {
      console.log(this.brightnessForm, 'brightnessForm');
      let bright_time_range = []
      this.brightnessForm.forEach(item => {
        bright_time_range.push(
          { "time_range": `${item.time_range[0]}~${item.time_range[1]}`, brightness: item.brightness }
        )
      })
      const params = {
        screen_key: this.screenDir.screen_num,
        screen_name: this.screenInfoData["screen_name"],
        deploy_info: {
          brightness_policy: {
            every_day: bright_time_range
          }
        },
      };

      console.log(params,'params');
      

      screen_edit(params).then(res=>{
        if (res.rst == "ok") {
          this.$message.success("修改成功");
          this.brightnessDialog = false;
          this.getScreenInfo();
        } else {
          this.$message.warning(res.error_msg);
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  min-height: calc(100vh - 50px);
  padding: 0 25px 80px;
  background-color: #fff;
  position: relative;
}

.box>.useTop {
  display: flex;
  padding: 0.15rem 0 0.15rem 0.12rem;
  justify-content: space-between;
  border-bottom: 1px solid #dcdcdc;
  width: 100%;
}

.el-icon-back {
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}

.useShortCuts>.useTop>.pubArrow {
  width: 1.5rem;
  height: 0.4rem;
  line-height: 0.4rem;
  font-size: 0.14rem;
}

main {
  overflow-y: scroll;
  height: 100%;
}

.twoPart {
  display: flex;
  padding: 27px 0 0;
  justify-content: space-around;

  .left {
    // width: 565px;
    flex: 1;
    height: 470px;
    color: rgba(80, 80, 80, 1);
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    font-size: 14px;
    border: rgba(229, 229, 229, 1) solid 1px;
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    font-weight: bold;

    ul>li {
      display: flex;
      color: rgba(80, 80, 80, 1);
      font-size: 14px;
      text-align: left;
      font-weight: bold;
      margin: 15px 0 0 20px;
    }

    ul>li:nth-last-child(1)>div {
      justify-content: space-between;
    }

    ul>li>span {
      width: 100px;
      height: 21px;
    }

    ul>li>div {
      display: flex;
    }

    .left>ul>li>div>i {
      width: 40px;
      height: 20px;
      color: rgba(80, 80, 80, 1);
      background-color: rgba(219, 176, 37, 1);
      border-radius: 11px;
      line-height: 19px;
      font-size: 10px;
      text-align: center;
      color: #fff;
      margin-left: 10px;
    }

    ul>li>div>img {
      width: 20px;
      height: 20px;
    }
  }

}


.con .screen_h {
  width: 28px;
  height: 18px;
  line-height: 18px;
  display: inline-block;
  color: rgba(39, 177, 126, 1);
  background-color: rgba(37, 195, 101, 0.3642857142857143);
  font-size: 14px;
  border: rgba(39, 177, 126, 1) solid 1px;
  text-align: center;
}

.con .screen_v {
  width: 18px;
  height: 28px;
  line-height: 28px;
  display: inline-block;
  color: rgba(39, 177, 126, 1);
  background-color: rgba(37, 195, 101, 0.3642857142857143);
  font-size: 14px;
  border: rgba(39, 177, 126, 1) solid 1px;
  text-align: center;
}

.twoPart>.center {
  flex: 1;
  height: 470px;
  color: rgba(80, 80, 80, 1);
  border-radius: 10px;
  font-size: 14px;
  margin: 0 10px;
  border: rgba(229, 229, 229, 1) solid 1px;
}

.twoPart>.center>h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  color: rgba(52, 52, 52, 0.9642857142857143);
  border-bottom: 1px solid #e3e3e3;
  font-size: 14px;
}

.twoPart>.center>h3>img {
  width: 24px;
  height: 24px;
}

.twoPart>.center>ul>li {
  display: flex;
  align-items: center;
  padding-left: 20px;
  margin: 20px 0;
}

.twoPart>.center>ul>li>.block {
  width: 380px;
  display: flex;
}

.block {
  align-items: center;

  button {
    margin-left: 10px;
    width: 70px;
    height: 30px;
  }
}

.twoPart>.center>ul>li>div>.el-select {
  margin-right: 10px;
}

.twoPart>.center>ul>li>div>.el-button--primary {
  // background-color: rgba(108, 178, 255, 1);
  background-color: var(--btn-background-color);
}

.twoPart>.center>ul>li>span {
  width: 70px;
  height: 21px;
  color: rgba(56, 56, 56, 1);
  font-size: 14px;
}

.twoPart>.right {
  flex: 0.8;
  height: 470px;
  color: rgba(80, 80, 80, 1);
  border-radius: 10px;
  font-size: 14px;
  border: rgba(229, 229, 229, 1) solid 1px;
}

.twoPart>.right>h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  color: rgba(52, 52, 52, 0.9642857142857143);
  border-bottom: 1px solid #e3e3e3;
  font-size: 14px;
}

.twoPart>.right>h3>img {
  width: 24px;
  height: 24px;
}

.twoPart>.right>ul>li {
  display: flex;
  align-items: center;
  padding-left: 20px;
  margin: 20px 0;
}

.twoPart>.right>ul>li>.block {
  width: 380px;
  display: flex;
}

.block {
  align-items: center;

  button {
    margin-left: 10px;
    width: 70px;
    height: 30px;
  }
}

.twoPart>.right>ul>li>div>.el-select {
  margin-right: 10px;
}

.twoPart>.right>ul>li>div>.el-button--primary {
  // background-color: rgba(108, 178, 255, 1);
  background-color: var(--btn-background-color);
}

.twoPart>.right>ul>li>span {
  width: 70px;
  height: 21px;
  color: rgba(56, 56, 56, 1);
  font-size: 14px;
}

.content {
  margin-top: 10px;
}

.content>h5 {
  margin: 10px 20px;
}

.content ::v-deep .el-tabs__nav {
  width: 100%;
  height: 40px;
  border-top: 1px solid #e5e5e5;
}

.content ::v-deep .el-tabs__header {
  margin: 0;
}

.content .list {
  padding-left: 30px;
}

.content .list>li {
  border-left: 3px solid #dfdfdf;
  padding: 30px;
  position: relative;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  display: flex;
}

.content .list>li>div>.circle {
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--background-color);
  font-size: 20px;
  text-align: center;
  color: #fff;
  height: 40px;
  left: -22px;
  top: 20px;
  position: absolute;
}

.content .list>li>div>h3 {
  color: rgba(16, 16, 16, 1);
  font-size: 20px;
  font-weight: bold;
}

.content .list>li>div>ul>li {
  margin: 20px;
  display: flex;
  align-items: center;
}

.content .list>li>div>ul>li:last-of-type>p {
  color: rgba(0, 186, 173, 1);
  line-height: 21px;
}

.content .list>li>div>ul>li>span {
  width: 70px;
  height: 21px;
  line-height: 21px;
}

.content .list>li>div>ul>li>p {
  height: 21px;
  line-height: 21px;
}

/*步骤条*/
.sideStep {
  min-width: 500px;
  padding: 30px 20px;
}

.my_step_wrap {
  display: flex;
  width: 100%;
  align-items: center;
}

.my_step {
  display: flex;
  height: 76px;
  width: 100%;
}

.every_step_lump {
  position: relative;
  height: 100%;
  margin-left: 100px;
}

.every_step_lump:first-of-type {
  margin-left: 0;
}

.every_step_lump::before {
  position: absolute;
  top: 17px;
  left: -100px;
  content: "";
  height: 2px;
  width: 100px;
  background: rgba(153, 153, 153, 1);
}

.every_step_lump:first-of-type::before {
  width: 0;
}

.step_icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-content: center;
  background: rgba(210, 210, 210, 1);
  color: rgba(128, 128, 128, 1);
  line-height: 35px;
  font-size: 16px;
  cursor: default;
  user-select: none;
}

.success_state {
  background: var(--screen-color);
}

.step_title {
  text-align: center;
  margin-top: 5px;
  color: rgba(80, 80, 80, 1);
}

.sideStep>div {
  /* width: 550px; */
  height: 124px;
  position: relative;
}

.sideStep>.image_wrap>img {
  object-fit: contain;
  height: 100%;
  display: block;
}

.image_wrap .shu {
  width: 31%;
}

.image_wrap {
  width: 50%;
  position: relative;
}

.image_wrap .heng {
  width: 100%;
}

.sideStep>.image_wrap>.play {
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 30px;
  font-size: 14px;
  color: #fff;
  border: rgba(255, 255, 255, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  margin: auto;
  right: 0;
  bottom: 0;
  display: none;
  cursor: pointer;
}

.sideStep>div>.play>span {
  font-size: 24px;
  display: block;
  margin-top: 10px;
  color: rgba(255, 255, 255, 1);
}

.sideStep>div:hover .play {
  display: block;
}

.timer {
  display: flex;
  align-items: center;
}

.timer .el-input {
  width: 30% !important;
}

.timer span {
  font-size: 14px;
  font-weight: bold;
  color: var(--text-color);
  margin-left: 10px;
  cursor: pointer;
}

/*弹出框*/
.remoteScreen ul {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  margin-top: 20px;
  border: rgba(229, 229, 229, 1) solid 1px;
}

.remoteScreen ul>li {
  height: 40px;
  margin: 20px 0 20px 20px;
  display: flex;
  align-items: center;
}

.remoteScreen ul>li>span {
  width: 70px;
  height: 21px;
  color: rgba(56, 56, 56, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: left;
}

.remoteScreen ul>li>div {
  display: flex;
  align-items: center;
}

.remoteScreen ul>li>div .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 173px;
}

.remoteScreen ul>li .block .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 199px;
}

.remoteDesktop {
  height: 260px;
}

.remoteDesktop>div {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

.remoteDesktop>div>span {
  width: 80px;
  display: inline-block;
}

.remoteDesktop>button {
  float: right;
  margin-top: 100px;
}

/*重启设备*/
.restartDev {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  width: 433px;
  height: 148px;
  bottom: 0;
  top: 0;
  background-color: #fff;
  border: 1px solid #c0c4cc;
}

/*重启设备弹出框*/
::v-deep .el-dialog__title {
  margin-left: 23px;
}

/* .remote  .el-dialog__title {
  margin-left: 0 !important;
} */
::v-deep .el-dialog__header {
  padding-bottom: 0;
  padding-left: 0 !important;
}

::v-deep .el-dialog__body {
  padding: 0 20px 30px;
}

::v-deep .dialog-footer {
  margin-top: 10px;
}

.spe {
  display: flex;
  align-items: center;
}

.spe>img {
  width: 275px !important;
}

.c_or_d_screen_wrap {
  margin-left: 15px;
}

.c_or_d_screen {
  margin-right: 10px;
  cursor: pointer;
  user-select: none;
}

.c_or_d_screen:hover {
  color: rgba(108, 178, 255, 1);
}

.active {
  // color: rgba(108, 178, 255, 1);
  color: var(--btn-success-color)
}

/* 预览mask */
.preview_mask {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  z-index: 9999;
  overflow-y: auto;
}

.preview_content {
  position: absolute;
  left: 50%;
  top: 50%;
  /* background: #fff; */
  width: 1920px;
  height: 1080px;
  transform: translate(-50%, -50%) scale(0.5);
}

.close_preview_mask {
  position: absolute;
  right: 40px;
  top: 40px;
  height: 40px;
  width: 40px;
  font-size: 18px;
  text-align: center;
  border-radius: 50%;
  line-height: 40px;
  cursor: pointer;
  color: #fff;
  background: #606266;
}

.close_preview_mask:hover {
  /* color: rgba(0, 0, 0, 0.6); */
  background: #6d6d6e;
}

.brightness {
  display: flex;
  align-items: flex-start !important;

  .block {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .brightness-item {
      width: 100%;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      font-size: 16px;
    }
  }
}

.devices_info {
  display: flex;
  flex-direction: column;
  justify-content: space-around;

  .devices_bright_item {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .bright_time {
      ::v-deep .el-input__inner {
        width: 300px !important;
      }
    }

    .bright_value {
      ::v-deep .el-input__inner {
        width: 80px !important;
      }
    }
  }
}

.deepEvent {
  span {
    white-space: nowrap;
  }

  button {
    margin-left: 0 !important;
    margin-right: 10px;
    margin-bottom: 5px;
  }
}
</style>
<style lang="scss">
/* .remote .el-dialog__title {
  margin-left: 0 !important;
} */
::v-deep .resDevice .el-dialog__title {
  margin-left: 40px !important;
}

.resDevice {
  padding: 0 19px;
}

.resDevice .el-dialog__body {
  margin-top: -26px;
  font-size: 25px;
  color: #ffab04;
  margin-left: -27px;
  border-radius: 4px;
}

/* tabs选中的样式 */
.tag_set .is-active {
  color: var(--text-color) !important;
  background-color: var(--active-color) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-first {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-second {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
  /* display: none; */
  background-color: var(--text-color) !important;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
  color: var(--text-color) !important;
}

.lok {
  position: absolute;
  bottom: -20px;
  left: 17%;
  color: var(--text-color-light);
  cursor: pointer;
  width: 100px;
}

.more {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.ranges_wrap {
  padding-left: 15px;
}

.ranges_wrap p {
  margin: 5px 0;
}

.activeState {
  position: absolute;
  padding: 5px 10px;
  background: rgba($color: #000000, $alpha: 0.4) !important;
  color: #fff !important;
}

.active_btn {
  font-weight: bold;
  margin-left: 100px;
  cursor: pointer;
  user-select: none;
}

.active_btn:active {
  filter: brightness(1.8);
}
</style>
