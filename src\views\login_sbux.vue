<template>
    <div class="login">
        <div class="left">
            <div class="leftContent">
                <div class="logo"></div>
                <h3 style="line-height: 40px;">{{ $store.state.base.projuct_Welcome }}</h3>
            </div>
        </div>
        <div class="right">
            <div class="sign_in">
                <h3 class="title">Welcome</h3>
                <el-button :loading='loading' type="primary" class="singInBtn"
                    style="background-color: var(--btn-background-color);width: 100%;height: 40px" @click="SignIn">{{
                        loading ? '登录中...' : '登 录' }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import Cookies from "js-cookie";
import qs from "qs";
import LoginLogo from "@/assets/images/Starbucks1.png";
import { sso_login_sbux } from "@/api/login"
export default {
    name: "Login",
    data() {
        return {
            LoginLogo: LoginLogo,
            loading: false,
            redirect: undefined,
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                const data = route.query;
                if (data && data.redirect) {
                    this.redirect = data.redirect;
                    delete data.redirect;
                    if (JSON.stringify(data) !== "{}") {
                        this.redirect = this.redirect + "&" + qs.stringify(data, { indices: false });
                    }
                }
            },
            immediate: true,
        },
    },
    created() {
        // token 过期提示
        this.point();
    },
    methods: {
        SignIn() {
            this.loading = true;
            sso_login_sbux().then(res => {
                console.log(res.to_url, '成功');
                window.location.href = res.to_url;
                return
                const base = window.location.origin;   // 线上
                // const base = 'https://sbuxgray.showtop.com';   // 本地环境测试
                window.location.href = base + '/dmb/saml/sso';
                // this.loading = false;
            }).catch(err => {
                console.log(err, '失败');
                this.$message.error('请求出错');
                this.loading = false;
            })
        },

        point() {
            const point = Cookies.get("point") !== undefined;
            if (point) {
                this.$notify({
                    title: "提示",
                    message: "当前登录状态已过期，请重新登录！",
                    type: "warning",
                    duration: 5000,
                });
                Cookies.remove("point");
            }
        },

    },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
    display: flex;
    // justify-content: center;
    align-items: center;
    height: 100%;
    background-size: cover;
}

.title {
    margin-bottom: 50px;
    text-align: left;
    color: var(--text-color);
    font-size: 46px;
}

.login-form {
    border-radius: 6px;
    background: #ffffff;
    width: 385px;
    height: 450px;
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;

    // padding: 25px 25px 5px 25px;
    .el-input {
        height: 38px;

        input {
            height: 38px;
        }
    }

    .input-icon {
        height: 39px;
        width: 14px;
        margin-left: 2px;
    }
}

.login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
}

.login-code {
    width: 33%;
    display: inline-block;
    height: 38px;
    float: right;

    img {
        cursor: pointer;
        vertical-align: middle;
    }
}

.left {
    width: 50%;
    height: 100%;
    background-size: 100% 100%;
    position: relative;
    background-color: var(--background-color);
}

.logo {
    width: 250px;
    height: 210px;
    color: rgba(80, 80, 80, 1);
    margin-top: -20px;
    font-size: 14px;
    background: url("../assets/images/Starbucks1.png");
    background-size: 100% 100%;
    text-align: center;
    margin: 0 auto;
}

.leftContent {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 200px;
    text-align: center;
    width: 350px;
}

.leftContent>h3 {
    margin-top: 30px;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 1);
    font-size: 25px;
}

.remember {
    display: flex;
    justify-content: space-between;
}

.remember>p {
    color: var(--check-color);
    font-size: 15px;
    cursor: pointer;
}



.login-form .el-input input {
    border: none;
    border-bottom: 1px solid #dcdfe6;
    height: 44px;
    line-height: 44px;
    font-size: 21px;
}

.right {
    position: relative;
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .sign_in {
        width: 385px;

        .singInBtn {
            &:hover {
                filter: brightness(1.2);
            }

            &:active {
                filter: brightness(0.9);
            }
        }
    }
}

.error {
    position: absolute;
    left: 0;
    top: 65px;
    color: var(--check-color);
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(80, 80, 80, 1);
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background: var(--check-color) !important;
    border-color: var(--check-color);
}

// ::v-deep .el-input__inner{
//   background: yellow !important;
// }
::v-deep .el-input__inner:target {
    background: var(--check-color) !important;
    border-color: var(--check-color);
}
</style>