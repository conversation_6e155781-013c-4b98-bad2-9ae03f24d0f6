<template>
    <div class="goods_sell_out">
        <div class="top_back">
            <i class="el-icon-back" @click="back2managent"></i>
        </div>
        <div class="sell_out_content">
            <div class="left_overview">
                <div class="l_header">售罄管理</div>
                <div class="l_content">
                    <div class="goods_img">
                        <div class="img_url">
                            <img src="https://img0.baidu.com/it/u=3048272039,3225979290&fm=253&fmt=auto&app=138&f=JPEG?w=533&h=500" alt="">
                        </div>
                        
                    </div>
                    <div class="goods_info">
                        <div class="info_item"><span>ID：</span><b>123456</b></div>
                        <div class="info_item"><span>商品名称：</span><b>万花筒系列气温仍然</b></div>
                        <div class="info_item"><span>商品分类：</span><b>早餐类</b></div>
                        <div class="info_item"><span>状态：</span><b>售罄</b></div>
                    </div>
                </div>
            </div>
            <div class="right_shop_edit">
                <div class="r_header">编辑</div>
                <div class="shop_content">
                    <div class="search_bar">
                        <div class="ipt_wrap">
                            <span>门店类型 </span>
                            <el-input size="small" style="width:180px"></el-input>
                        </div>
                        <div class="ipt_wrap">
                            <span>门店标签 </span>
                            <el-input size="small" style="width:180px"></el-input>
                        </div>
                        <div class="ipt_wrap">
                            <span>门店编码/名称 </span>
                            <el-input size="small" style="width:180px"></el-input>
                        </div>
                        <div class="ipt_wrap">
                            <el-button size="small" type="primary">搜索</el-button>
                        </div>
                    </div>
                    <div class="shops">
                        <div class="shop_item" v-for="item in shop_list" :key="item.shop_name">
                            <div class="item_check_box">
                                <el-checkbox></el-checkbox>
                            </div>
                            <div class="shop_icon">
                                <!-- <img src="@/assets/img/shop.png" alt=""> -->
                            </div>
                            <div class="item_info">
                                <p>*********</p>
                                <p title="24实验门撒大苏打伟大伟大阿松大安王店">24实验门撒大苏打伟大伟大阿松大安王店</p>
                            </div>
                        </div>
                    </div>
                    <div class="page">
                        <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page.sync="currentPage1"
                        :page-size="100"
                        background
                        layout="total, prev, pager, next"
                        :total="1000">
                        </el-pagination>
                    </div>
                    <div class="footer">
                        <div class=""></div>
                        <div class="right_btn">
                            <el-button size="medium" type="primary">取消</el-button>
                            <el-button size="medium" type="primary">确认</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {

    },
    data() {
        return {
            shop_list:[
                {id:123,shop_name:'门店1'},
                {id:123,shop_name:'门店2'},
                {id:123,shop_name:'门店3'},
                {id:123,shop_name:'门店4'},
                {id:123,shop_name:'门店5'},
                {id:123,shop_name:'门店6'},
                {id:123,shop_name:'门店7'},
                {id:123,shop_name:'门店8'},
                {id:123,shop_name:'门店9'},
                {id:123,shop_name:'门店10'},
                {id:123,shop_name:'门店11'},
                {id:123,shop_name:'门店12'},
                {id:123,shop_name:'门店13'},
                {id:123,shop_name:'门店14'},
                {id:123,shop_name:'门店15'},
                {id:123,shop_name:'门店1'},
                {id:123,shop_name:'门店2'},
                {id:123,shop_name:'门店3'},
                {id:123,shop_name:'门店4'},
                {id:123,shop_name:'门店5'},
                {id:123,shop_name:'门店6'},
                {id:123,shop_name:'门店7'},
                {id:123,shop_name:'门店8'},
                {id:123,shop_name:'门店9'},
                {id:123,shop_name:'门店10'},
                {id:123,shop_name:'门店11'},
                {id:123,shop_name:'门店12'},
                {id:123,shop_name:'门店13'},
                {id:123,shop_name:'门店14'},
                {id:123,shop_name:'门店15'},
                {id:123,shop_name:'门店1'},
                {id:123,shop_name:'门店2'},
                {id:123,shop_name:'门店3'},
                {id:123,shop_name:'门店4'},
                {id:123,shop_name:'门店5'},
                {id:123,shop_name:'门店6'},
                {id:123,shop_name:'门店7'},
                {id:123,shop_name:'门店8'},
                {id:123,shop_name:'门店9'},
                {id:123,shop_name:'门店10'},
                {id:123,shop_name:'门店11'},
                {id:123,shop_name:'门店12'},
                {id:123,shop_name:'门店13'},
                {id:123,shop_name:'门店14'},
                {id:123,shop_name:'门店15'},
            ]
        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {
        back2managent(){
            this.$router.replace('/goodsManage/product_center')
        }
    },
};
</script>

<style scoped lang="scss">
    .goods_sell_out{
        width: 100%;
        height: 100%;
        padding: 20px;
        padding-right: 40px;
        box-sizing: border-box;
        font-size: 14px;
        background-color: var(--light-gray);
        .top_back{
            width: 100%;
            height: 30px;
            font-size: 26px;
            color: var(--btn-background-dark);
            margin-bottom: 20px;
            i{
                cursor: pointer;
            }
        }

        .sell_out_content{
            display: flex;
            height: calc(100vh - 150px);
            .left_overview{
                margin-right: 50px;
                height: 100%;
                .l_header{
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .l_content{
                    width: 400px;
                    min-height: 240px;
                    background-color: #fff;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 10px 0;
                    .goods_img{
                        width: 180px;
                        min-height: 200px;
                        margin-right: 20px;
                        background-color: var(--light-gray);
                        border-radius: 10px;
                        .img_url{
                            width: 100%;
                            height: 200px;
                            img{
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                        .img_title{
                            width: 100%;
                            padding-left: 10px;
                            p:first-child{
                                font-size: 16px;
                                font-weight: bold;
                                margin-bottom: 5px;
                            }
                            p:last-child{
                                color: var(--text-gray);
                            }
                        }
                    }
                    .goods_info{
                        width: 170px;
                        min-height: 200px;
                        max-height: 100%;
                        display: grid;
                        place-content: end start;
                        .info_item{
                            margin-top: 15px;
                            color: var(--text-gray);
                            display: flex;
                            b{
                                color: #000;
                                display: inline-block;
                                width: 100px;
                            }
                        }
                    }
                }
            }
            
            .right_shop_edit{
                flex: 1;
                min-height: 550px;
                min-width: 300px;
                display: flex;
                flex-direction: column;
                .r_header{
                    font-size: 18px;
                    font-weight: bold;
                    // color: var(--text-color);
                    margin-bottom: 20px;
                    padding-left: 20px;
                    opacity: 0;
                }
                .shop_content{
                    flex: 1;
                    background-color: #fff;
                    min-height: calc(100vh - 200px);
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    .search_bar{
                        width: 100%;
                        min-height: 60px;
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 0 20px 0;
                        .ipt_wrap{
                            margin: 5px 20px 5px 0;
                        }
                        .ipt_wrap > span{
                            font-size: medium;
                        }
                    }
                    .shops{
                        flex: 1;
                        min-height: 400px;
                        box-sizing: border-box;
                        padding: 20px;
                        display: flex;
                        flex-wrap: wrap;
                        align-content: flex-start;
                        overflow-y: auto;
                        .shop_item{
                            width: 220px;
                            height: 60px;
                            margin: 0 15px 15px 0;
                            display: flex;
                            align-items: center;
                            padding: 0 10px 0;
                            background-color: var(--light-gray);
                            border-radius: 10px;
                            overflow: hidden;
                            .item_check_box{
                                margin-right: 10px;
                                ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner{
                                    background-color: var(--check-color);
                                    border-color: var(--check-color);
                                }
                                ::v-deep .el-checkbox__inner{
                                    width: 20px;
                                    height: 20px;
                                    border-radius: 50%;
                                    &::after{
                                        height: 10px;
                                        width: 4px;
                                        left: 6px;
                                        top: 2px;
                                    }
                                }
                            }
                            .shop_icon{
                                width: 30px;
                                height: 30px;
                                border-radius: 50%;
                                overflow: hidden;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 10px;
                                img{
                                    width: 60%;
                                    height: 60%;
                                    object-fit: cover;
                                }
                                background-color: var(--background-color);
                            }
                            .item_info{
                                width: 130px;
                                height: 100%;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                                p:first-child{
                                    margin-bottom: 5px;
                                }
                                p{
                                    width: 100%;
                                    font-size: 15px;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    // 文字省略号
                                    text-overflow: ellipsis;
                                }
                            }
                        }
                    }
                    .page{
                        height: 50px;
                        display: flex;
                        justify-content: flex-end;
                        align-items: center;
                        padding-right: 30px;
                    }
                    .footer{
                        min-height: 50px;
                        display: flex;
                        flex-wrap: wrap;
                        align-items: center;
                        justify-content: space-between;
                        padding: 0 30px;
                    }
                }
            }
        }
    }
</style>
