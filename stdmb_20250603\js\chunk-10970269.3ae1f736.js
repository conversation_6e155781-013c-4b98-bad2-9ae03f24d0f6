(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-10970269"],{3706:function(t,e,n){},"3f55":function(t,e,n){"use strict";n("3706")},"80d5":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this;t._self._c;return t._m(0)},c=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"jump"},[e("div",{staticClass:"loader"}),t._v(" "),e("div",{staticClass:"loadtext"},[t._v("LOADING...")]),t._v(" "),e("div",{staticClass:"text"},[t._v("正在跳转页面，请稍后")])])}],r=(n("a481"),n("7ded")),s={components:{},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){var t=this,e=this.$route.query.code;e?Object(r["d"])({code:e}).then((function(e){"ok"==e.rst?t.$store.dispatch("Jump",e).then((function(e){t.$router.replace({path:"/"})})).catch((function(t){console.log(t,"rej")})):(t.$message.error(e.error_msg),t.$router.replace({path:"/login"}))})):this.$router.replace({path:"/login"})},methods:{}},a=s,i=(n("3f55"),n("2877")),u=Object(i["a"])(a,o,c,!1,null,"214a6d04",null);e["default"]=u.exports}}]);