<template>
  <div class="homeRight">
    <!--  顶部-->
    <ul>
      <li v-for="(item, index) in homeTop" :key="index" @click.stop="toGo(index)">
        <!-- <img src='../../assets/img/home_img/addStore.svg'>  -->
        <img :src="topimgs[index]" />
        <p>{{ item }}</p>
      </li>
    </ul>
    <!--  中间数据-->
    <div class="homeMain">
      <p>预警</p>
      <ul>
        <!--          :class="item.num==0?'red_bg':''"-->
        <li
          v-for="(item, index) in mainList"
          :key="index"
          :class="liIndex == index ? 'red_bg' : ''"
          @click.stop="clickLis(index)"
        >
          <div class="main_top">
            <img :src="homeImg[index]" />
            <p>{{ item.tab }}</p>
          </div>
          <div class="main_bottom">
            <div @click.stop="toShopList(item, index)">
              <!-- <img src='../../assets/img/home_img/little_store.svg'> -->
            </div>
            <div @click.stop="toScreenList(item, index)">
              <img src="../../assets/img/home_img/little_work.svg" />
              <span>{{ item.cnt }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <!--      底部-->
    <div class="homeBottom">
      <p>数据中心</p>
      <!--        门店数据-->
      <div class="storeData">
        <p>门店数据</p>
        <div v-for="(item, index) in bottomList" :key="index">
          <span>{{ item.label }}</span>
          <p>{{ item.cnt }}</p>
        </div>
      </div>
      <!--        设备数据-->
      <div class="storeData">
        <p>设备数据</p>
        <div v-for="(item, index) in bottomList2" :key="index" v-if="item.label != '已过期'">
          <span>{{ item.label }}</span>
          <p>{{ item.cnt }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get_adm_home_info } from "@/api/index/index";
import {
  player,
  up,
  plug,
  tan,
  timer,
  no,
  pubShare,
  sources,
  addStore,
  tian
} from "../../utils/publicInfo";
export default {
  watch: {
    "$store.state.user.canRefresh"(newVal, oldVal) {
      if (newVal) {
        this.get_adm_home_info();
        this.getInfoLev();
        this.$store.state.user.canRefresh = false;
      }
    }
  },
  data() {
    return {
      homeTop: ["快速发布", "上传资源", "新增门店", "内容制作"],
      homeImg: [player, up, plug, tan, timer, no],
      topimgs: [pubShare, sources, addStore, tian],
      // homeTitle:["player离线≥72h","U盘异常接入≥1m","HDMI线异常≥1h","MAC地址冲突","CPU≥80% 5min预警","无内容≥48h"],
      mainList: [],
      liIndex: 0,
      //门店数据
      bottomList: [],
      //设备数据
      bottomList2: []
    };
  },
  methods: {
    toGo(idx) {
      // let jump_url = ["/deploy/quickpub", "/contentCenter/srclist", "/shopManage/openNewShop", "/contentCenter/csproducer"]
      let jump_url = [
        "/deploy/quickDeploy",
        "/contentCenter/srcfiles",
        "/shopManage/storelist",
        "/contentCenter"
      ];
      if(idx == 2){
        sessionStorage.setItem('needAddShop','1')
      }
      this.$router.push({
        path: jump_url[idx]
      });
    },
    clickLis(idx) {
      this.liIndex = idx;
    },
    // 门店数据和设备数据
    get_adm_home_info() {
      const params = {
        pgid: localStorage.getItem("group_id"), // (String)账号节点id
        filter_data: "base", // (String)获取店铺和设备数据固定用"base"
        force: 1 // (Int)固定传 1
      };
      get_adm_home_info(params).then(res => {
        if (res.rst == "ok") {
          this.bottomList = res.data[0].home_base[1].sub;
          this.bottomList2 = res.data[0].home_base[0].sub;
          console.log(this.bottomList2,'bottomList2');
          
        }
      });
    },
    getInfoLev() {
      const params = {
        pgid: localStorage.getItem("group_id"), // (String) 账号节点id
        filter_data: "alert", // (String) 获取内容统计数据标识
        force: 1 // (String) 固定参数
      };
      get_adm_home_info(params).then(res => {
        if (res.rst == "ok") {
          this.mainList = res.data[0].home_alert;
        }
      });
    },
    toShopList(item, index) {
      this.liIndex = index;
      console.log(item);
      console.log("?");
    },
    toScreenList(item, index) {
      this.liIndex = index;
      let queryString = "";
      let quertType = null;
      console.log(item,'item');
      // switch (index) {
      //   case 0:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 1:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 2:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 3:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;

      //   default:
      //     break;
      // }

      this.$router.push({
        path: "/deviceManage/screenGroup",
        query: {
          // queryString: item.threshold,
          // quertType: quertType,
          inonline: 'false',
          fcond:JSON.stringify(item.fcond)
        }
      });
    }
  },
  created() {
    this.$store.dispatch("GetInfo").then(res => {
      console.log(res);
      if (res.data[0].curent_branch) {
        console.log("%cyou", "color:red");
      } else {
      }
    });
    this.get_adm_home_info();
    this.getInfoLev();
  }
};
</script>
<style scoped lang='css'>
ul {
  list-style: none;
}

.homeRight {
  width: 100%;
  height: 100%;
  padding: 0.14rem 0 0 0;
  background-color: #fff;
}

.homeRight > ul {
  height: 1.18rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.2rem 0 0.28rem;
}

.homeRight > ul > li {
  display: flex;
  align-items: center;
  width: 22%;
  height: 0.99rem;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 232, 234, 1);
  border-radius: 0.1rem;
  font-size: 0.14rem;
  text-align: center;
  cursor: pointer;
}

.homeRight > ul > li > img {
  width: 0.36rem;
  height: 0.36rem;
  margin: 0 0.22rem 0 0.29rem;
}

.homeRight > ul > li > p {
  width: 1rem;
  height: 0.27rem;
  left: 8.94rem;
  top: 1.12rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.18rem;
  line-height: 150%;
  text-align: left;
  font-weight: bold;
  padding-left: 0.28rem;
}

.homeRight > ul > li:nth-child(1) {
  background-color: #e4edff;
}

.homeRight > ul > li:nth-child(2) {
  background-color: #eee5fe;
}

.homeRight > ul > li:nth-child(3) {
  background-color: #ffe8ea;
}

.homeRight > ul > li:nth-child(4) {
  background-color: #c7f0ed;
}

/*中间部分*/
.homeMain {
  width: 100%;
  padding-left: 0.28rem;
}

.homeMain > p {
  height: 0.48rem;
  line-height: 0.48rem;
  left: 1.85rem;
  top: 1.94rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  width: 100%;
  text-align: left;
  border-bottom: 0.01rem solid #e5e5e5;
}

.homeMain > ul {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding: 0.24rem 0 0 0;
}

.homeMain > ul > li {
  width: 23%;
  height: 1.21rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  border: 0.01rem solid rgba(166, 166, 166, 1);
  cursor: pointer;
  margin: 0 0.23rem 0.22rem 0;
}

.homeMain > ul > li > .main_top {
  display: flex;
  height: 0.4rem;
  margin: 0.19rem 0 0 0.19rem;
  justify-content: flex-start;
}

.homeMain > ul > .red_bg {
  background-color: rgba(211, 57, 57, 0.06428571428571431);
  border-color: var(--text-color);
}

.homeMain > ul > li > .main_top > img {
  width: 0.22rem;
  height: 0.22rem;
  margin-right: 0.09rem;
}

.homeMain > ul > li > .main_top > p {
  height: 0.21rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  line-height: 150%;
  text-align: left;
  width: 1.5rem;
}

/* .homeMain>ul>li:nth-child(1)>.main_top>p{
  width: 1.5rem;
} */
.homeMain > ul > li > .main_bottom {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.homeMain > ul > li > .main_bottom > div {
  margin-top: 10px;
}

.homeMain > ul > li > .main_bottom > div > img {
  width: 0.24rem;
  height: 0.24rem;
  font-size: 0.24rem;
  color: rgba(166, 166, 166, 0.5);
}

.homeMain > ul > li > .main_bottom > div > span {
  font-size: 0.32rem;
  text-align: left;
  font-weight: bold;
  margin-right: 0.17rem;
  color: rgba(166, 166, 166, 1);
}

.homeMain > ul > .red_bg > .main_bottom > div > span {
  color: var(--text-color);
}

/*底部*/
.homeBottom > p {
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  width: 100%;
  text-align: left;
  padding: 0.1rem 0 0.15rem 0;
  margin-left: 0.28rem;
  border-bottom: 0.01rem solid #e5e5e5;
}

.homeBottom > .storeData {
  display: flex;
  align-items: baseline;
  height: 1.1rem;
  margin: 0.26rem 0 0.35rem 0;
}

.homeBottom > .storeData > p {
  width: 0.64rem;
  height: 0.24rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
  padding: 0 0.35rem 0 0.21rem;
}

.homeBottom > .storeData > div {
  width: 2.3rem;
  height: 1.1rem;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(247, 247, 247, 1);
  border-radius: 0.1rem;
  font-size: 0.14rem;
  text-align: center;
  margin-right: 0.16rem;
}

.homeBottom > .storeData > div > span {
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
  display: block;
  text-align: center;
  margin: 0.15rem 0 0.12rem 0;
  height: 0.24rem;
}

.homeBottom > .storeData > div > p {
  color: rgba(80, 80, 80, 1);
  font-size: 0.32rem;
  font-weight: bold;
  height: 0.48rem;
  text-align: center;
  margin-bottom: 0.11rem;
}
</style>
