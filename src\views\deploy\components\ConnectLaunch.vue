<template>
  <div class="connect">
    <el-form ref="form" :model="form" label-width="130px">
      <el-form-item label="发布名称:">
        <el-input v-model="newConnect.launchName"></el-input>
      </el-form-item>
      <el-form-item label="投放周期:">
        <el-date-picker v-model="newConnect.timging" type="datetimerange" :default-time="['00:00:00', '23:59:59']"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 300px"></el-date-picker>
      </el-form-item>
      <el-form-item label="联屏规格:">
        <div class="Specification">
          <div class="gp">
            <div v-for="(item, index) in speci" class="speci" :key="item">
              <div class="specic" @click="eventActive(index, item)">
                <div v-for="item1 in item.num" :class="item.order == 0 ? 'across' : 'vertical'" :key="item1.order">
                </div>
              </div>
              <div class="alternative">
                <el-radio v-model="radio" :label="item.name" @change="changeRadio(index, item)"></el-radio>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="播放类型:" class="leixing">
        <div>
          <el-radio v-for="item in playerRadio" :value="item[0]" :key="item[0]" v-model="newConnect.playerState"
            :label="item[1]"></el-radio>
        </div>
      </el-form-item>
      <el-form-item label="场景类型:">
        <div>
          <el-radio v-for="item in sceneTypeList" :value="item.label" :key="item.value" v-model="newConnect.scene_type_state"
            :label="item.label"></el-radio>
        </div>
      </el-form-item>
      <div class="detali">
        <el-form-item label="播放详情:">
          <div v-for="item in DetaliList" :key="item" class="selectDetali">
            <el-radio v-model="newConnect.detaliRadio" :label="item.name"></el-radio>
          </div>
        </el-form-item>
        <div v-show="newConnect.detaliRadio == '按星期'" class="weekCheckout">
          <el-checkbox v-model="newConnect.weekCheckout" v-for="item1 in weekList" :key="item1" :label="item1.name">{{
            item1.name }}</el-checkbox>
          <br />

          <el-time-picker is-range v-model="newConnect.time_ranges" value-format="HH:mm:ss" style="margin-top:10px"
            range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围" />
        </div>
        <div v-show="newConnect.detaliRadio == '按指定时段'" class="selectCheckout">

          <el-time-picker is-range v-model="newConnect.connectTimeing" range-separator="至" format="HH:mm"
            value-format="HH:mm" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围"></el-time-picker>
        </div>
      </div>
      <el-form-item label="播放间隔:" class="selectTimer">
        <!-- <el-select v-model="newConnect.waitting_time" filterable placeholder="请选择" @blur="selectBlur">
                    <el-option v-for="item in Timeroptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
        </el-select> S-->

        <el-input v-model="newConnect.waitting_time" style="width:150px !important" filterable
          placeholder="请选择"></el-input>S
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      newConnect: {
        launchName: "",
        timging: [],
        // timging: [new Date(), new Date(new Date().getTime() + 86400000)],
        playerState: "轮播",
        screen_type: "",
        detaliRadio: "按全天",
        weekCheckout: ["日", "一", "二", "三", "四", "五", "六"],
        connectTimeing: ["00:00", "23:59"],
        speciState: null,
        time: "",
        timeFrame: "",
        vs_spec: "",
        waitting_time: "0",
        scene_type: "0",
        scene_type_state: "默认场景"
        // time_ranges:[]
      },

      radio: "",
      active: 0,
      /**
       * 右侧餐牌组规格
       * order 0为横 1为竖
       */
      speci: [],
      playerRadio: [],
      screen_option: [],
      // detali
      DetaliList: [
        {
          name: "按全天"
        },
        {
          name: "按星期"
        },
        {
          name: "按指定时段"
        }
      ],
      weekList: [
        {
          name: "日"
        },
        {
          name: "一"
        },
        {
          name: "二"
        },
        {
          name: "三"
        },
        {
          name: "四"
        },
        {
          name: "五"
        },
        {
          name: "六"
        }
      ],
      timeOptions: [
        {
          label: "1",
          value: "1"
        },
        {
          label: "5",
          value: "5"
        },
        {
          label: "10",
          value: "10"
        },
        {
          label: "15",
          value: "15"
        },
        {
          label: "30",
          value: "30"
        },
        {
          label: "60",
          value: "60"
        }
      ],
      timeFrame: [
        {
          label: "秒",
          value: "秒"
        },
        {
          label: "分",
          value: "分"
        },
        {
          label: "时",
          value: "时"
        }
      ],
      pickerOptions0: {
        disabledDate: time => {
          return (
            time.getTime() > this.newConnect.timging[1].getTime() ||
            time.getTime() < this.newConnect.timging[0].getTime()
          );
        }
      },
      Timeroptions: [
        {
          value: 15,
          label: 15
        },
        {
          value: 30,
          label: 30
        },
        {
          value: 45,
          label: 45
        },
        {
          value: 60,
          label: 60
        }
      ],
      sceneTypeList: [
        {
          label: '默认场景',
          value: 0
        },
        {
          label: '插播场景',
          value: 1
        },
      ]
    };
  },
  methods: {
    eventActive(index, item) {
      this.active = index;
      this.radio = item.name;
      this.newConnect.vs_spec = item.value;
    },
    changeRadio(index, item) {
      this.active = index;
      this.radio = item.name;
      this.newConnect.vs_spec = item.value;
    },
    selectBlur(e) {
      console.log(e);
      this.newConnect.waitting_time = e.target.value;
      console.log(this.newConnect.waitting_time);
    }
  },
  created() {
    this.playerRadio = this.$store.state.deployDataFilters[2].options;
    this.screen_option = this.$store.state.deployDataFilters[3].options;
    this.speciData = this.$store.state.deployDataFilters[4].options;
    console.log(this.newConnect, "newConnectnewConnect");
  },
  mounted() {
    this.speci = [];
    this.speciData.forEach(item => {
      this.speci.push({
        name: item[1],
        value: item[0],
        num: item[0][4] != undefined ? Number(item[0][4]) : 1,
        order: item[1][0] == "横" ? 0 : 1
      });
    });
  }
};
</script>

<style lang="scss" scoped>
.connect {
  box-sizing: border-box;
  margin-top: 20px;
  padding-left: 25px;

  ::v-deep .el-input {
    width: 300px !important;
  }

  .Specification {
    width: 100%;
    // flex-wrap: wrap;
    margin-top: -30px;

    .speci {
      width: 138px !important;
      height: 77px;
      margin-left: 10px;
      margin-top: 40px;
      cursor: pointer;

      .specic {
        height: 100%;
        border: rgba(215, 215, 215, 1) solid 1px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;
      }

      .alternative {
        height: 30px;
        display: flex;
        align-items: center;
        margin-top: 5px;
        justify-content: center;
      }
    }
  }
}

.active {
  background-color: var(--text-color-light) !important;
  ;
}

.el-form-item:nth-of-type(1) {
  margin-top: 0 !important;
}

.el-form-item {
  margin-top: 22px !important;
}

.detali {
  position: relative;

  .weekCheckout {
    position: absolute;
    left: 35%;
    width: 80%;
    top: 43%;
  }

  .selectCheckout {
    position: absolute;
    left: 2.3rem;
    width: 80%;
    top: 78%;
  }
}

.selectDetali:nth-of-type(1) {
  margin-top: 0 !important;
}

.selectDetali {
  margin-top: 25px;
}

.selectTimer {
  .el-select {
    width: 150px !important;

    ::v-deep .el-input {
      width: 100% !important;
    }
  }
}

.gp {
  // width: 870px;
  width: 100%;
  display: flex;
  display: inline-black;
  flex-wrap: wrap;
  margin-left: -10px;
}

.across {
  width: 26px;
  height: 16px;
  background-color: rgba(229, 229, 229, 1);
  border: rgba(166, 166, 166, 1) solid 1px;
}

.vertical {
  width: 16px;
  height: 26px;
  background-color: rgba(229, 229, 229, 1);
  border: rgba(166, 166, 166, 1) solid 1px;
}

::v-deep .leixing {
  margin-top: 30px !important;
}
</style>