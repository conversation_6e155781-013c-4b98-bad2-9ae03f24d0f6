<template>
    <div class="update_hist"  v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"  element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <!-- title -->
        <div class="update_title flex">
            
            <div class="flex" style="align-items:center"> 
                <i class="el-icon-back back_btn" style="
                font-size: 24px;
                color: #25acd1;
                cursor: pointer;
                font-weight: bold;" 
                @click="back"></i>
                <span style="margin-left:12px">升级区域</span></div>
        </div>
        
        <!-- 表格区 -->
        <div class="table_wrap">
            <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"  :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px','text-align':'center'}" :cell-style="{'text-align':'center'}">
                <!-- <el-table-column type="selection" width="50"></el-table-column> -->
                <el-table-column prop="plan_name_dis" label="计划名称" width=""></el-table-column>
                <el-table-column prop="ct_time" label="创建时间" width=""></el-table-column>
                <el-table-column prop="screen_count" label="升级数量" width=""></el-table-column>
                <el-table-column prop="pkg" label="升级apk" width=""></el-table-column>
                <el-table-column prop="up_time" label="升级时间" width=""></el-table-column>
                <el-table-column prop="install_success" label="升级成功" width=""></el-table-column>
                <el-table-column prop="installing" label="正在升级" width=""></el-table-column>
                <el-table-column prop="download_success" label="下载完成" width=""></el-table-column>
                <el-table-column prop="downloading" label="正在下载" width=""></el-table-column>
                <el-table-column prop="waiting" label="等待响应" width=""></el-table-column>
                <el-table-column prop="error" label="升级出错" width=""></el-table-column>
                <el-table-column prop="date" label="升级详情" width="">
                    <template slot-scope="scope">
                        <el-button @click="handleSee(scope.row)" type="text" size="small">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 底部和页码 -->
        <div class="alert_footer">
            <div class="right_page_wrap flex">
                <div style="min-width:240px">
                </div>
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="pageSize"
                    :pager-count='5'
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total,sizes,prev,pager, next, jumper"
                    :total="totalNum">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
// import { up_plan_list } from '@/api/updateApp/update'
import {up_plan_list_v2} from '@/api/systemCenter/updateHist'
import { resolve } from 'path';
import { log } from 'util';
export default {
    components: {

    },
    data() {
        return {
            loading:false,
            currentPage:1,  //页码
            totalNum:0, //总数据数量
            pageSize:10,
            tableData: [],  //列表数据
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
            
        };
    },
    computed: {

    },
    watch: {
        
    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getDataLsit();
    },
    mounted() {
        
    },
    methods: {
        // 获取列表数据
        getDataLsit(){
            this.loading = true;
            const user_id = localStorage.getItem("user_id")
            const params = {
                 "ruid": user_id,	//（string）登录ID
                 "iDisplayStart":(this.currentPage-1)*this.pageSize,	//（int）显示的起始位置
                 "iDisplayLength":this.pageSize,	//（int）显示的最大数量
                 "list_type":'group',
                 "name_time":this.$route.query.name_time
            }
            
            up_plan_list_v2(params).then(res => {
                if(res.rst == 'ok'){
                    this.tableData = res.data[0].up_last_his;
                    this.totalNum = res.data[0].iTotalRecords;
                    this.loading = false;
                }else{
                    this.$message.error(res.error_msg)
                    this.loading = false;
                }
            })
            
        },
        getUpgradeColor(row){
            if(row.error){
                return 'color:var(--text-color)'
            }
            if(row.install_success){
                return 'color:rgba(31, 152, 80, 1)'
            }
            return 'color:rgba(42, 130, 228, 1)'
        },
        // 格式化升级状态
        getUpgradeStatus(row){
            if(row.error){
                return '升级失败'
            }
            if(row.downloading){
                return '下载中'
            }
            if(row.download_success){
                return '下载完成'
            }
            if(row.installing){
                return '升级中'
            }
            if(row.install_success){
                return '升级成功'
            }
            return '等待升级'
        },
        back(){
            this.$router.go(-1)
        },
        // 查看升级详情
        handleSee(val){
            this.$router.push({
                path: "/systemCenter/upgradeDetails",
                query:{
                    plan_name:val.plan_name
                }
            });
        },
        // 列表区高度自适应
        getHeight(){
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 168 + 'px';
            this.autoHeight.heightNum = windowHeight - 218;
        },
        //页码改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getDataLsit();
        },
        handleSizeChange(val){
            this.pageSize = val;
            this.getDataLsit();
        },
    },
    destroyed () {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style lang='scss' scoped>
    .update_hist{
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 0 20px;
    }   
    .update_title{
        width: 100%;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        color: rgba(42, 130, 228, 1);
        font-size: 14px;
        line-height: 40px;
        text-align: left;
        font-weight: bold;
        margin-bottom: 17px;
        border-bottom: 1px solid rgba(145, 195, 252, 1);
    }
    .upload_wrap{
        position: relative;
        height: 32px;
        width: 118px;
    }
    .upload_ipt{
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        width: 0px;
        height: 0px;
        overflow: hidden;
        opacity: 0;
    }
    .uptext_btn{
        cursor: pointer;
        font-weight: normal;
        margin-right: 15px;
    }
    .upload_button{
        // position: absolute;
        // left: 0;
        // top: 0;
        // margin-left: 10px;
        width: 118px;
        height: 32px;
        line-height: 32px;
        background: var(--text-color);
        border-radius: 6px;
        font-size: 13px;
        text-align: center;
        color: #fff;
        cursor: pointer;
    }
    .upload_button:hover{
         background: rgba(211, 57, 57, .85);
    }
    /* 表格区 */
    .table_wrap{
        border-bottom: 1px solid rgba(145, 195, 252, 1);
    }
    /* 底部 */
    .alert_footer{
        box-sizing: border-box;
        width: 100%;
        height: 55px;
        font-size: 14px;
        padding-top: 17px;
    }
    .right_page_wrap{
        align-items: center;
        justify-content: space-between;
    }
    /* 弹框 */
    .dialog_content{
        box-sizing: border-box;
        height:350px;
        width: 100%;
        overflow-y: scroll;
        padding: 11px 0;
        padding: 16px 40px;
        border:1px solid rgba(153, 153, 153, 0.29)
    }
    .dialog_content::-webkit-scrollbar{ 
        width: 0 !important 
    }
    .dialog_content{
        overflow: -moz-scrollbars-none; 
        -ms-overflow-style: none;
    }
    .dialog_row{
        margin-bottom: 17px;
    }
    .upload_list{
        display: flex;
    }
    .upload_list .content_name{
        width: 100px;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: rgba(80, 80, 80, 1);
        font-weight: bold;
    }
    .upload_list .content_option{
        height: 30px;
        line-height: 30px;
    }
    .upload_dragger{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 5px;
        height: 200px;
        width: 350px;
        border: 1px dashed rgb(217, 217, 217);
        cursor: pointer;
        border-radius: 6px;
    }
    .upload_dragger:hover{
        border-color: #409eff;
    }
    .upload_dragger em{
        color: #409eff;
        font-style: normal
    }
    .icon_wrap{
        margin-top: -20px;
    }
    .icon_wrap i{
        font-size:67px;
        color:#c0c4cc;
    }
    .upload_file_info{
        padding-left: 100px;
        padding-top: 10px;
    }
    .progress_bar{
        --wrap-width:450px;
        --wrap-height:125px;
        /* border: 1px solid red; */
        border: 1px solid #ebeef5;
        position: absolute;
        width: var(--wrap-width);
        height: var(--wrap-height);
        top: calc(50% - (var(--wrap-height)/2) - 50px);
        left: calc(50% - (var(--wrap-width)/2));
        background: #fff;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        padding: 15px;
        z-index: 2;
    }
    .progress_title{
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 18px;
        padding-bottom: 0px;
        i{
            cursor: pointer;
        }
    }
    .show_progress{
        // width: 95px;
        color: #409eff;
        margin-right: 8px;
        cursor: pointer;
    }
    ::v-deep .el-progress__text{
        font-size: 16px !important;
    }
    .err_tips{
        color: #f56c6c;
        font-size: 14px;
    }
</style>
<style >
    .update_hist .el-dialog{
        border-radius: 16px;
    }
    .update_hist .el-dialog .el-dialog__header{
        padding-top:15px !important;
        padding-bottom:10px !important;
        /* border-bottom:1px solid rgba(153, 153, 153, 0.29) */
    }
    .update_hist .el-dialog .el-dialog__header .el-dialog__title{
        font-size: 16px !important;
    }
    .update_hist .el-dialog .el-dialog__body{
        padding-top:10px !important;
        padding-bottom:0 !important;
        /* padding-right:0px !important; */
    }
</style>
