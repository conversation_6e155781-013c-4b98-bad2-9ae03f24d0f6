<template>
  <div
    class="shop_detials"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading"
  >
    <!-- title -->
    <div class="shop_detials_title">
      <i
        class="el-icon-back back_btn"
        style="
                      font-size: 24px;
                      color: #25acd1;
                      cursor: pointer;
                      font-weight: bold;
                    "
        @click="back"
      ></i>
      <span
        style="font-size: 16px"
      >{{ restaurantDetails.storename }}({{ restaurantDetails.storecode }})</span>
    </div>
    <!-- 内容 -->
    <div class="detials_content">
      <!-- 餐厅详情 -->
      <div class="restaurant_detials flex">
        <p class="detials_title">餐厅详情</p>
        <div class="restaurant_content set_bcg_color">
          <p>
            <span class="content_name">运营市场：</span>
            {{ restaurantDetails.marketname }}
          </p>
          
          <p>
            <span class="content_name">开业时间：</span>
            {{ restaurantDetails.open_date }}
          </p>
        </div>
        <div class="restaurant_content set_bcg_color">
          <p>
            <span class="content_name">餐厅类型：</span>
            {{ restaurantDetails.storetypename }}
          </p>
          
        </div>
        <div class="restaurant_content set_bcg_color">
          <p>
            <span class="content_name">商圈类型：</span>
            {{ restaurantDetails.jdetradezonename }}
          </p>
          <p>
            <span class="content_name">屏幕动线：</span>
            {{ restaurantDetails.storeplaylayout_display }}
          </p>
        </div>
        <div class="flex flex-1 shop_tags" style>
          <div class style="display: block; width: 70px;padding-top:3px">门店标签：</div>
          <div
            class="flex flex-1 tags_wrap"
            style
            v-if="restaurantDetails.shop_tags &&
            restaurantDetails.shop_tags.length > 0
            "
          >
            <div
              v-for="(item, index) in (new Set(restaurantDetails.shop_tags))"
              :key="index"
              class="flex tags_list"
              style="padding-top:3px"
            >
              <img
                src="../../assets/img/checked.png"
                alt
                style="width: 16px; height: 16px; margin-right: 10px"
              />
              <span>{{ item }}</span>
            </div>
          </div>
          <div style="padding-top:3px" v-else>暂无标签</div>
        </div>
      </div>
      
      <!-- 其他信息设置：屏幕动线，屏幕数量 -->
      <div style="display: flex;">
        <div class="other_info_setting">
            <p class="detials_title">预安装信息设置</p>
            <div class="other_info_content">
                <div style="margin-bottom:10px">预安装屏幕数量：</div>
                <div class="screen_nums">
                    <div class="screen_nums_block" v-for="item in screen_settings.screen_displaycnt_list" :key="item.type">
                        <span class="screen_nums_type">{{ item.label }}：</span>
                        <el-input style="width:200px" type="number" v-model="item.value" :min="0"></el-input>
                    </div>
                </div>
                <div class="screen_layout">
                    <span>播放动线：</span>
                    <el-select v-model="screen_settings.storeplaylayout" placeholder="请选择">
                        <el-option v-for="option in playlayoutOptions" :label="option.label" :value="option.value" :key="option.value"></el-option>
                    </el-select>
                </div>
                <div class="screen_setting_btn">
                    <el-button type="primary" size="medium" @click="settingSave" :disabled="isSettingSava">
                        {{ isSettingSava ? '保存中...' : '预安装信息保存' }}
                    </el-button>
                </div>
            </div>
        </div>
        <div class="install_desc">
            <p class="detials_title">安装信息备注</p>
            <div style="padding: 15px;box-sizing: border-box;">
                <el-input
                    resize="none"
                    type="textarea"
                    show-word-limit
                    maxlength="1000"
                    placeholder="请输入备注信息"
                    class="textarea_ipt"
                    size="medium"
                    v-model="install_desc">
                </el-input>
            </div>
            <div class="screen_setting_btn" style="padding-top: 0;">
                <el-button type="primary" size="medium" @click="settingInstallDesc" :disabled="desc_save">
                    {{ desc_save ? '保存中...' : '保 存' }}
                </el-button>
            </div>
        </div>
      </div>

      <!-- 屏幕详情和经营时段 -->
      <div class="bottom_detials">
        <!-- 屏幕详情 -->
        <div class="screen_detials flex">
          <p class="detials_title">屏幕详情</p>
          <div class="screen_info flex" v-if="haveDma != false">
            <p class="flex" style="margin-right: 35px; align-items: center">联屏组</p>
            <div class="flex" style="justify-content: space-between; padding-right: 40px">
              <div class="multiple_num_title" style="margin-right: 15px">
                <span
                  class="multiple_num_h"
                  v-for="(item, index) in multipleNum"
                  :key="index"
                  style="font-weight: normal"
                >{{ item.screen_index }}</span>
              </div>
              <div class="see_text_btn" @click="multipleSee">查看</div>
            </div>
          </div>
          <p class="detials_table_title">Display详情</p>
          <div class="detials_table flex flex-1">
            <el-table
              :data="infoTableData"
              v-loadmore="loadMorePerson"
              v-loading="tableLoading"
              element-loading-background="rgba(0, 0, 0, 0)"
              :row-class-name="tableRowClassName"
              :max-height="maxtableHeight"
              :header-cell-style="{
                background: 'rgba(116,169,236,0.07)',
                height: '48px',
                color: '#666',
                'font-size': '13px',
              }"
            >
              <!-- <el-table-column type="selection" width="50"></el-table-column> -->
              <el-table-column prop="screen" label="屏幕设备" width align="center">
                <template slot-scope="scope">
                  <div>
                    <span
                      :class="item.display.v_or_h == 'horizontal'
                      ? 'multiple_num_h'
                      : 'multiple_num_v'
                      "
                      v-for="item in scope.row.all_displayinfo"
                      :key="item"
                    >{{ item.displaynum }}</span>
                  </div>
                  <!-- <span>{{scope.row.screen}}</span> -->
                </template>
              </el-table-column>
              <el-table-column prop="screen_id" label="屏幕ID" width align="center"></el-table-column>
              <el-table-column prop="usage_type_cn" label="屏幕类型" align="center"></el-table-column>
              <el-table-column label="屏幕标签" width align="center">
                <!-- <div v-for="item in scope.row.tags" :key="item.name">
                    {{ item.name }}
                </div>-->
                <template slot-scope="scope">
                  <div class="flex" style="flex-wrap: wrap;align-items:center">
                    <div
                      class="flex"
                      style="flex-direction:column"
                      v-if="scope.row.tags.length > 2"
                    >
                      <div style="display:flex;align-items:center">
                        <img
                          src="../../assets/img/home_img/little_label.svg"
                          style="width: 24px; height: 24px"
                        />
                        {{ scope.row.tags[0].name }}
                      </div>
                      <div style="display:flex;align-items:center">
                        <img
                          src="../../assets/img/home_img/little_label.svg"
                          style="width: 24px; height: 24px"
                        />
                        {{ scope.row.tags[1].name }}
                      </div>
                      <el-popover
                        placement="top-start"
                        title="设备标签"
                        popper-class="popperOptions"
                        width="200"
                        trigger="hover"
                      >
                        <div
                          v-for="item in scope.row.tags"
                          :key="item"
                          style="display:flex;align-items:center"
                        >
                          <img
                            src="../../assets/img/home_img/little_label.svg"
                            style="width: 24px; height: 24px"
                          />
                          <span>
                            {{
                            item.name
                            }}
                          </span>
                        </div>
                        <span class="cursor" slot="reference">...</span>
                      </el-popover>
                    </div>
                    <div
                      class="flex"
                      style="flex-direction:column"
                      v-else-if="scope.row.tags.length > 0 && scope.row.tags.length <= 2"
                    >
                      <div
                        style="display:flex;align-items:center"
                        v-for="item in  scope.row.tags"
                        :key="item"
                      >
                        <img
                          src="../../assets/img/home_img/little_label.svg"
                          style="width: 24px; height: 24px"
                        />
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="pmodel" label="设备类型" width align="center"></el-table-column>
              <el-table-column prop="createdtime" label="安装时间" width="130" align="center"></el-table-column>
              <el-table-column label="操作" width="90" align="center">
                <template slot-scope="scope">
                  <el-button
                    @click.native.prevent="handleShow(scope.row)"
                    type="text"
                    size="small"
                  >查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!-- 营业属性 -->
        <div class="daypart_wrap">
          <p class="detials_title flex" style="justify-content: space-between">
            <span>
              营业属性 当前经营时段: {{ daypartgroupName }}
              <el-button
                style="margin-left:10px;background-color: var(--background-color);color:var(--btn-color);"
                @click="downCenterBody"
                v-if="restaurantDetails.dpfrom == '2'"
              >下内容中台</el-button>
            </span>
            <span class="edit_text_btn"  v-if="checkPer(['dgm.shop_group.dpedit'])" @click="daypartEdit">编辑</span>
          </p>
          <div class="daypart_table" style="over-flow: hidden">
            <el-table
              :data="daypartDataList"
              :row-class-name="tableRowClassName"
              height="452px"
              :header-cell-style="{
              background: 'rgba(116,169,236,0.07)',
              height: '48px',
              color: '#666',
              'font-size': '13px',
              'text-align': 'center',
            }"
              :cell-style="{ 'text-align': 'center' }"
            >
              <el-table-column type="index" label="序号" width="100"></el-table-column>
              <el-table-column prop="daypartname" label="经营时段" width></el-table-column>
              <el-table-column prop="stime" label="时段" width>
                <template slot-scope="scope">
                  <div>{{ scope.row.stime }} - {{ scope.row.etime }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="" label="数据来源" width>
                <template slot-scope="scope">
                  <div
                    :style="{ color: scope.row.from_type == 2 ? 'rgb(255,87,51)' : 'rgb(80,80,80)' }"
                  >
                    {{
                        scope.row.from_type_display
                    }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- daypart设置 -->
    <el-drawer
      title="营业属性"
      :visible.sync="isDrawerShow"
      :before-close="handleDrawerClose"
      :with-header="false"
      :wrapperClosable="false"
      size="570px"
    >
      <div class="hg-100 flex flex-direction-column">
        <div
          class="flexBoxTwoTitle flex justify-between pd-x-20"
          style="align-items: center; border-bottom: 1px solid #ededed"
          v-show="!isAddShow"
        >
          营业属性
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="handleDrawerClose"
            alt
          />
        </div>
        <div
          class="flexBoxTwoTitle flex justify-between pd-x-20"
          style="align-items: center; border-bottom: 1px solid #ededed"
          v-show="isAddShow"
        >
          <div style="padding-top: 5px;">
            <i
              class="el-icon-close"
              style="cursor:pointer;color:rgba(166, 166, 166, 1);font-size:20px"
              @click="cancelAdding"
            ></i>
          </div>

          <div class="flex-1" style="margin:0 5px">
            <el-input
              v-model="periodName"
              placeholder="请输入内容"
              style="border:none;"
              class="period_ipt"
            ></el-input>
          </div>

          <div style="padding-top: 5px;">
            <i
              class="el-icon-check"
              style="cursor:pointer;color:rgba(166, 166, 166, 1);font-size:20px"
              @click="confirmAdd"
            ></i>
          </div>
        </div>
        <div class="bucket_type pd-x-30" style>
          时段类型：
          <el-cascader
            :options="timeIntervalList"
            v-model="bucketType"
            filterable
            clearable
            style="width:38%"
            popper-class="daypartpopper"
            :props="{
              checkStrictly: true, expandTrigger: 'click',
            }"
            @change="changeTimeInterval"
            :filter-method="query"
          ></el-cascader>
          <!-- :filter-method="query" 精确 -->

          <!-- <el-select v-model="bucketType" clearable placeholder="时段类型" size="small"
            style="width: 186px; margin-right: 12px" @change="changeTimeInterval" filterable :props="{
              checkStrictly: true, expandTrigger: 'click',
            }" :filter-method="query">
            <el-option v-for="item in timeIntervalList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>-->
        </div>
        <div style="padding: 0 28px; margin-bottom: 23px" v-show="isTimeRepeat">
          <el-alert title="该时段已重复设置" type="warning" :closable="false" show-icon></el-alert>
        </div>
        <div class="flex-1 pd-x-30" style="overflow-y: auto;padding-bottom:40px">
          <div
            class="flex"
            style="font-size: 14px; align-items: center; margin-bottom: 28px"
            v-for="(item, index) in daypartDataListCopy"
            :key="index"
          >
            <!-- <div class="checkbox_wrap">
              <el-checkbox v-model="item.ischecked"></el-checkbox>
            </div>-->
            <div style="margin-right: 8px; width: 100px">{{ item.daypartname }}：</div>
            <div style="font-size: 12px">
              <el-time-picker
                v-model="item.stime"
                size="small"
                style="width: 136px"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="任意时间点"
                :editable="false"
                @change="timeChange(item.stime, index)"
              ></el-time-picker>至
              <el-time-picker
                v-model="item.etime"
                size="small"
                style="width: 136px"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="任意时间点"
                :editable="false"
                @change="timeChange(item.etime, index)"
              ></el-time-picker>
            </div>
            <!-- <div class="add_or_del">
              <div class="del_time">
                <i class="el-icon-remove-outline" @click="addOrEditTime('del',item,index)"></i>
              </div>
              <div class="add_time">
                <i class="el-icon-remove-outline" @click="delDaypart(item, index)"></i>
              </div>
            </div>-->
          </div>
          <!-- 暂时隐藏新增时段，后台暂不支持 -->
          <!-- <div style="text-align: center">
            <el-button type="danger" style="background:rgb(212,48,48);border-color:rgb(212,48,48);width:370px" @click="addTimes">
              新增时段
            </el-button>
          </div>-->
        </div>
        <div class="saveBtnParent wd-100 flex pd-x-30" style="align-items: center">
          <div style="margin-left: auto">
            <el-button type="default" size="small" style="width: 80px" @click="handleDrawerClose">取消</el-button>

            <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="editPart"
              v-if="!isEditing"
            >保存</el-button>

            <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              :loading="true"
              disabled
              v-else
            >保存中</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  get_storeinfo,
  get_adm_datas,
  shop_daypartmgmt,
  edit_group_shop_info
} from "@/api/shopManage/shop";
import { get_shop_data } from "@/api/screen_group/screen_group";
import { down_center_shop } from "@/api/down_shop/down_shop";
import { datas_filter_cond } from '@/api/commonInterface'
export default {
  components: {},
  // 监听表格滚动，添加列表触底事件
  directives: {
    loadmore: {
      bind(el, binding) {
        const selectWrap = el.querySelector(".el-table__body-wrapper");
        selectWrap.addEventListener("scroll", function() {
          const scrollDistance =
            this.scrollHeight - this.scrollTop - this.clientHeight;
          if (scrollDistance <= 0.5) {
            binding.value(); //执行在使用时绑定的函数，在这里即loadMorePerson方法
          }
        });
      }
    }
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      tablePageNum: 1,
      eqTableTotal: 0,
      shopId: "",
      isDrawerShow: false,
      detialsData: {}, //本地存储获取的数据
      restaurantDetails: [],
      multipleNum: [], //联屏组是几联屏
      infoTableData: [], //屏幕详情列表数据
      daypartDataList: [], //daypart列表数据
      daypartDataListCopy: [],
      timeIntervalList: [], //时段信息
      bucketType: "",
      bucketTypeList: [],
      isTimeRepeat: false, //设置的时段是否重复
      isEditing: false,
      haveDma: false,
      DmaData: null,
      periodName: "",
      isAddShow: false,
      maxtableHeight: "406px",
      daypartgroupName: "",
      isSettingSava:false,
      screen_settings:{
        screen_displaycnt_list:[
            {label:'菜单屏',type:'dmb',value:0},
            {label:'叫号屏',type:'call',value:0},
            {label:'引流屏',type:'attracting',value:0},
            {label:'其他',type:'others',value:0},
        ],
        storeplaylayout:''
      },
      install_desc:'',
      desc_save:false,
      playlayoutOptions:[],
    };
  },
  computed: {},
  watch: {
    loading(val) {
      if (val) {
        document.body.parentNode.style.overflow = "hidden";
      } else {
        document.body.parentNode.style.overflow = "";
      }
    }
  },
  created() {
    //把从列表点击的数据从存储中拿出
    // const data = sessionStorage.getItem('shop_detials')
    // this.detialsData = JSON.parse(data)
    // console.log(this.$route.query.shopid);
    this.shopId = this.$route.query.shopid;
    this.getDataList();
    this.GetShopDAta();
    this.getPubInfo();
  },
  mounted() {},
  methods: {
    getPubInfo(){
        const parmas = {
            classModel:"ContentPub"
        }
        datas_filter_cond(parmas).then(res=>{
            if(res.rst == 'ok'){
                let layout_data = res.data[0].find(item=> item.filterkey == 'storeplaylayout');
                
                let list = layout_data.options.map(item=>{
                    return{
                        label:item[1],
                        value:item[0]
                    }
                })
                if(!this.screen_settings.storeplaylayout){
                    this.screen_settings.storeplaylayout = list[0].value;
                }
                
                this.playlayoutOptions = list;
            }
        })
    },
    back() {
      this.$router.replace({ path: "/shopManage/storelist" });
      sessionStorage.removeItem("shop_detials");
      // sessionStorage.setItem('activepath','/ShopManage');
    },
    downCenterBody() {
      this.$confirm("此操作将清空内容中台推送的内容, 是否继续执行?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          down_center_shop({
            shop_id: this.$route.query.shopid
          }).then(res => {
            if (res["rst"] == "ok") {
              if (res["data"][0]["done_status"] == 9) {
                this.$message.success("下内容中台成功");
                this.getDataList();
                this.GetShopDAta();
              }
            } else {
              this.$message.warning(res["rst"]);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    query(value, old) {
      
      if (value.text == old) {
        return true;
      } else {
        return false;
      }
    },
    getDataList() {
      this.loading = true;
      Promise.all([
        this.getDetialsData(),
        this.getEquipmentData(),
        this.getDaypartList()
      ])
        .then(res => {
          this.eqTableTotal = res[1].totalElements;
          this.loading = false;
        })
        .catch(rej => {
          this.loading = false;
          console.log(rej, "rej");
          console.log("有个失败了");
        });
    },
    // 获取详情数据
    getDetialsData() {
      return new Promise((resolve, reject) => {
        const params = {
          shop_id: this.shopId,
          filtertags: 1 //非必要，是否返回门店标签信息
        };
        get_storeinfo(params).then(res => {
            if (res.rst == "ok") {
                this.restaurantDetails = res.data[0];
                this.screen_settings.storeplaylayout = res.data[0]["storeplaylayout"];
                if(res.data[0]['more']){
                    if(res.data[0]['more']['displaycnt']){
                        const num_info = res.data[0]['more']['displaycnt']
                        for(let i in num_info){
                            this.screen_settings.screen_displaycnt_list.forEach(item=>{
                                if(item.type == i){
                                    item.value = num_info[i]
                                }
                            })
                        }
                    }
                    if(res.data[0]['more']['install_desc']){
                        this.install_desc = res.data[0]['more']['install_desc']
                    }
                }
                resolve();
            } else {
                reject();
            }
          })
          .catch(rej => {
            reject();
          });
      });
    },
    // 获取设备列表数据
    getEquipmentData() {
      return new Promise((resolve, reject) => {
        const params = {
          classModel: "ScreenMgmt",
          sort: "", //非必要，排序规则，storecode,createdAtS
          page: this.tablePageNum - 1, //起始页码,
          size: 10, //每页数据量,
          isonline: -1,
          shop_id: this.shopId
        };

        get_adm_datas(params)
          .then(res => {
            if (res.rst == "ok") {
              this.infoTableData = [
                ...this.infoTableData,
                ...res.data[0].content
              ];
              resolve(res.data[0]);
            } else {
              reject();
            }
          })
          .catch(rej => {
            reject();
          });
      });
    },
    // 获取daypart数据
    getDaypartList() {
      return new Promise((resolve, reject) => {
        let params = {
          act: "get",
          shop_id: this.shopId
        };
        shop_daypartmgmt(params)
          .then(res => {
            if (res.rst == "ok") {
              this.timeIntervalList = [];
              this.daypartDataList = res.data[0].content;
              this.daypartgroupName =
                res["data"][0]["sys_daypart_info"]["current_sys_groupname"];
              res.data[0].sys_daypart_info.group_list.forEach((item, index) => {
                item.attr_list.forEach(items => {
                  items.id = "";
                });
                this.timeIntervalList.push({
                  value: item.group_name,
                  label: item.group_name,
                  list: item.attr_list
                });
              });
              resolve(res.data[0]);
            } else {
              reject();
            }
          })
          .catch(rej => {
            reject();
            console.log("失败了");
          });
      });
    },
    changeTimeInterval(val) {
      if (val == "") {
        this.daypartDataListCopy = JSON.parse(
          JSON.stringify(this.daypartDataList)
        );
        return;
      }
      for (let i = 0; i < this.timeIntervalList.length; i++) {
        if (this.timeIntervalList[i].value == val) {
          this.daypartDataListCopy = this.timeIntervalList[i]["list"];
          break;
        }
      }
    },
    // 屏幕详情触底加载
    loadMorePerson() {
      if (this.infoTableData.length == this.eqTableTotal) {
        return;
      }
      this.tableLoading = true;
      this.tablePageNum += 1;
      this.getEquipmentData().then(res => {
        this.tableLoading = false;
      });
    },
    multipleSee() {
      //   this.$message.warning("虽然是一个查看,但我并不知道是什么查看");
      this.$router.push({
        path: "/deviceManage/connectDetail",
        query: {
          dmakey: this.DmaData.screen_group_list[0].sg_key,
          storecode: this.infoTableData[0].storecode,
          shop_id: this.DmaData.shop_id,
          specification: this.DmaData.screen_group_list[0].specification
        }
      });
    },
    //详情列表查看点击
    handleShow(row) {
      console.log(row);
      this.$router.push({
        path: "/deviceManage/screenDetail",
        query: {
          sid: row.screen_id,
          storecode: row.storecode,
          pmodel: row.pmodel
        }
      });
    },
    // Daypart编辑
    daypartEdit() {
      console.log("%cwhat", "color:red");
      this.daypartDataListCopy = JSON.parse(
        JSON.stringify(this.daypartDataList)
      );
      if (this.daypartgroupName != "") {
        this.bucketType = this.daypartgroupName;
      }
      this.isDrawerShow = true;
    },
    // 时段时间改变触发方法
    timeChange(time, idx) {
      //如果清除了时间，时间为空时不进行操作
      if (!time) return;
      this.timeCompare(idx);
      // debugger
      // let sChange = this.daypartDataListCopy[idx].stime;
      // let eChange = this.daypartDataListCopy[idx].etime;
      // for(let i=0;i<this.daypartDataListCopy.length;i++){
      //     if(i!=idx){
      //         let sContrast = this.daypartDataListCopy[i].stime;
      //         let eContrast = this.daypartDataListCopy[i].etime;
      //         let status = this.isOverlap(sChange,eChange,sContrast,eContrast)
      //         // console.log(this.daypartDataListCopy[i]);
      //         console.log(status);
      //         if(status){
      //             this.isTimeRepeat = true
      //             this.isTimeRepeatList[i] = true
      //             console.log(this.isTimeRepeatList);
      //             return
      //         }else{
      //             this.isTimeRepeat = false
      //         }
      //     }
      // }
    },
    // 循环比较时段是否有重复
    timeCompare(idx) {
      // 如果是24小时，不进行判断
      if (this.daypartDataListCopy[idx]["daypartname"] == "24小时") return;
      //如果开始时间等于结束时间，证明选择的时间是一整天，这时候如果时段信息大于一个,那么必然重复
      //如果只有一个时段，那么不用考虑重复
      if (
        this.daypartDataListCopy[idx].stime ==
          this.daypartDataListCopy[idx].etime &&
        this.daypartDataListCopy.length > 1
      ) {
        this.isTimeRepeat = true;
        return;
      } else if (this.daypartDataListCopy.length == 1) {
        this.isTimeRepeat = false;
        return;
      }
      //不满足上述条件，进行循环比较时间段是否重复设置
      for (let i = 0; i < this.daypartDataListCopy.length - 1; i++) {
        for (let j = i + 1; j < this.daypartDataListCopy.length; j++) {
          //不与24小时时段进行对比
          if (
            this.daypartDataListCopy[i]["daypartname"] != "24小时" &&
            this.daypartDataListCopy[j]["daypartname"] != "24小时"
          ) {
            //拿到4个时间点
            let sChange = this.daypartDataListCopy[i].stime;
            let eChange = this.daypartDataListCopy[i].etime;
            let sContrast = this.daypartDataListCopy[j].stime;
            let eContrast = this.daypartDataListCopy[j].etime;
            //调用检测方法，返回值为true时，时间段重复
            let status = this.isOverlap(sChange, eChange, sContrast, eContrast);
            if (status) {
              this.isTimeRepeat = true;
              return;
            } else {
              this.isTimeRepeat = false;
            }
          }
        }
      }
    },
    // 选择时间段是否与检测时间段重叠
    isOverlap(sChange, eChange, sContrast, eContrast) {
      // 将选择的时间段处理为Int格式判断,sChange定义为选择的时间段开始时间，eChange定义为选择的时间段结束时间
      // 此处的sChange = "09:00", eChange = "18:00"（举例）
      let startTime = parseInt(sChange.split(":")[0] + sChange.split(":")[1]); //900
      let endTime = parseInt(eChange.split(":")[0] + eChange.split(":")[1]); //1800
      // 将检测的时间段处理为Int格式判断，sContrast定义为检测的时间段开始时间，eContrast定义为检测的时间段结束时间,同上
      let isStartTime = parseInt(
        sContrast.split(":")[0] + sContrast.split(":")[1]
      );
      let isEndTime = parseInt(
        eContrast.split(":")[0] + eContrast.split(":")[1]
      );

      // 五种对比
      // 1、选择的开始时间 == 检测的开始时间，选择的结束时间 == 检测的结束时间，必定重复
      // 2、选择的时间 开始<结束，检测的时间 开始<结束，两个都没有跨天
      // 3、选择的时间 开始<结束，检测的时间 开始>结束，选择的时间没有跨天，检测的时间跨天
      // 4、选择的时间 开始>结束，检测的时间 开始>结束，两个都跨天,必定重复
      // 5、选择的时间 开始>结束，检测的时间 开始<结束，选择的时间跨天，检测的时间没有跨天
      if (startTime == isStartTime && endTime == isEndTime) {
        return true;
      } else if (startTime < endTime && isStartTime < isEndTime) {
        if (
          (startTime > isStartTime && startTime < isEndTime) ||
          (endTime > isStartTime && endTime < isEndTime) ||
          (isStartTime > startTime && isStartTime < endTime) ||
          (isEndTime > startTime && isEndTime < endTime)
        ) {
          return true;
        }
      } else if (startTime < endTime && isStartTime > isEndTime) {
        if (
          startTime > isStartTime ||
          endTime > isStartTime ||
          startTime < isEndTime ||
          endTime < isEndTime
        ) {
          return true;
        }
      } else if (startTime > endTime && isStartTime > isEndTime) {
        return true;
      } else if (startTime > endTime && isStartTime < isEndTime) {
        if (
          isStartTime > startTime ||
          isEndTime > startTime ||
          isStartTime < endTime ||
          isEndTime < endTime
        ) {
          return true;
        }
      }
      return false;
    },
    //抽屉关闭
    handleDrawerClose() {
      this.isDrawerShow = false;
      this.isTimeRepeat = false;
    },
    // 是否有联屏
    GetShopDAta() {
      const params = {
        g_group_id: localStorage.getItem("group_id"),
        shop_id: this.shopId
      };
      get_shop_data(params).then(res => {
        if (
          res.data[0].screen_group_list.length != 0 &&
          res.data[0].screen_group_list[0].screens.length != 0
        ) {
          this.maxtableHeight = "361px";
          this.haveDma = true;
          this.DmaData = res.data[0];
          this.multipleNum = res.data[0].screen_group_list[0].screens;
        } else {
          this.maxtableHeight = "406px";
          this.haveDma = false;
        }
      });
    },
    //daypart增加或者删除时段
    addOrEditTime(type, val, idx) {
      let operationName = this.daypartDataListCopy[idx];
      let lastIndex = 0;
      let startIndex;
      let startNum = 0;
      let hasFirst = false;
      if (type == "add") {
        //这里判断点击项的最后一位是不是数字
        let lastWord = operationName.daypartname.charAt(
          operationName.daypartname.length - 1
        );
        let judge;
        if (!isNaN(lastWord)) {
          judge = operationName.daypartname.substring(
            0,
            operationName.daypartname.length - 1
          );
        } else {
          judge = operationName.daypartname;
          hasFirst = true;
          // newarr.forEach(item=>{
          //   if(item.daypartname == item.daypartname.substring(0,item.daypartname.length-1)){
          //     hasFirst = true
          //   }
          // })
        }
        let newarr = this.daypartDataListCopy.filter((item, index) => {
          let last = item.daypartname.charAt(item.daypartname.length - 1);
          return item.daypartname.indexOf(judge) != -1;
        });
        if (isNaN(lastWord)) {
          newarr.forEach(item => {
            if (
              item.daypartname ==
              item.daypartname.substring(0, item.daypartname.length - 1)
            ) {
              hasFirst = true;
            }
          });
        }
        console.log(hasFirst, "有没有初始的");
        return;
        this.daypartDataListCopy.forEach((item, index) => {
          let last = item.daypartname.charAt(item.daypartname.length - 1);
          if (item.daypartname == judge) {
            startIndex = index;
          }
          // if((item.daypartname.indexOf(judge) != -1 && !isNaN(last)) || item.daypartname == judge){
          if (
            (item.daypartname.substring(0, item.daypartname.length - 1) ==
              judge &&
              !isNaN(last)) ||
            item.daypartname == judge
          ) {
            lastIndex = index + 1;
            startNum += 1;
          }
        });
        let obj = JSON.parse(JSON.stringify(operationName));
        obj.id = "";
        obj.stime = "";
        obj.etime = "";
        let lastVal = newarr[newarr.length - 1].daypartname;
        let lastnum;
        if (isNaN(lastVal.charAt(lastVal.length - 1))) {
          console.log("走了这里");
          lastnum = 1;
        } else {
          lastnum = parseInt(lastVal.charAt(lastVal.length - 1)) + 1;
        }
        // let lastnum = parseInt(lastVal.charAt(lastVal.length-1)) + 1
        obj.daypartname = newarr[0].daypartname + lastnum;

        this.daypartDataListCopy.splice(lastIndex, 0, obj);
        // let newarr = this.daypartDataListCopy.filter((item,index)=>{
        //   let last = item.daypartname.charAt(item.daypartname.length-1)
        //   console.log(isNaN(last),'这是每一项');
        //   console.log(judge,'judge111');
        //   return item.daypartname.indexOf(operationName.daypartname) != -1
        // })
        // console.log(operationName,newarr[newarr.length-1]);
        return;
        console.log(val, idx, this.daypartDataListCopy);
        this.daypartDataListCopy.splice(idx + 1, 0, sss);
      } else if (type == "del") {
        console.log("删除");
        this.daypartDataListCopy.splice(idx, 1);
      }
    },
    // 删除时段
    delDaypart(val, index) {
      this.daypartDataListCopy.splice(index, 1);
    },
    // 添加时段
    addTimes() {
      this.isAddShow = true;
    },
    // 取消添加
    cancelAdding() {
      this.isAddShow = false;
      this.periodName = "";
    },
    // 确认添加
    confirmAdd() {
      console.log(this.periodName);
      let obj = {
        stimemeal: "",
        daypartseqid: "",
        from_type: 2,
        etime: "",
        shop_id: this.shopId,
        stime: "",
        etimemeal: "",
        daypartname: this.periodName,
        id: ""
      };
      this.daypartDataListCopy.push(obj);
      this.isAddShow = false;
      this.periodName = "";
    },
    // 保存daypart设置
    editPart() {
      if (this.isTimeRepeat) {
        this.$message.warning("有时段信息重复设置，请检查确认");
        return;
      }

      // 前边有选择框时的判断
      // let changeDaypartList = this.daypartDataListCopy.filter((item) => {
      //   return item.ischecked;
      // });

      // if (changeDaypartList.length == 0) {
      //   this.$message.warning("请先选择要编辑的时段");
      //   return;
      // }
      // for (let i = 0; i < changeDaypartList.length; i++) {
      //   if (
      //     changeDaypartList[i].stime == null ||
      //     changeDaypartList[i].etime == null
      //   ) {
      //     this.$message.warning("时间点不能设置为空");
      //     return;
      //   }
      // }
      // let changeStatus = false;
      // this.isEditing = true;

      // this.daypartDataListCopy.forEach((item, index) => {
      //   if (item.ischecked) {
      //     if (
      //       this.daypartDataListCopy[index].stime !==
      //         this.daypartDataList[index].stime ||
      //       this.daypartDataListCopy[index].etime !==
      //         this.daypartDataList[index].etime
      //     ) {
      //       changeStatus = true;
      //     }
      //   }
      // });
      // if (!changeStatus) {
      //   this.isDrawerShow = false;
      //   this.isEditing = false;
      //   this.$message.success("时段编辑成功");
      //   return;
      // }
      // let changeInfoList = [];
      // changeDaypartList.forEach((item) => {
      //   changeInfoList.push({
      //     id: item.id,
      //     daypartname: item.daypartname,
      //     stime: item.stime,
      //     etime: item.etime,
      //   });
      // });
      // let params = {
      //   shop_id: this.shopId,
      //   act: "edit",
      //   dayparts: changeInfoList,
      // };
      let changeInfoList = [];
      this.daypartDataListCopy.forEach(item => {
        changeInfoList.push({
          id: item.id,
          daypartname: item.daypartname,
          stime: item.stime,
          etime: item.etime
        });
      });
      console.log(this.bucketType, "this.bucketType");
      console.log(typeof this.bucketType, "this.bucketType");
      let bucketType = "";
      if (typeof this.bucketType == "string") {
        bucketType = this.bucketType;
      } else {
        bucketType = this.bucketType[0];
      }
      let params = {
        shop_id: this.shopId,
        sys_groupname:bucketType,
        act: "edit",
        dayparts: changeInfoList
      };
      console.log(params, "params");
      shop_daypartmgmt(params)
        .then(res => {
          if (res.rst == "ok") {
            this.$message.success("时段编辑成功");
            this.getDaypartList();
            this.isDrawerShow = false;
            this.isEditing = false;
            this.bucketType = "";
          } else {
            this.$message.error("编辑时段信息失败");
            this.isEditing = false;
            this.bucketType = "";
          }
        })
        .catch(rej => {
          this.$message.error("编辑时段信息失败");
          this.isEditing = false;
          this.bucketType = "";
        });
    },
    //表格奇偶行背景色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return "row_bcg";
      } else {
        return "";
      }
    },
    settingSave(){
        this.isSettingSava = true;
        let displaycnt = {};
        this.screen_settings.screen_displaycnt_list.forEach((item,index)=>{
            displaycnt[item.type] = parseInt(item.value) < 0 ? 0 : parseInt(item.value);
        })
        const params = {
            group_id:localStorage.getItem("group_id"),
            shop_id:this.shopId,
            edit_info:{
                storeplaylayout:this.screen_settings.storeplaylayout,
                more:{
                    displaycnt
                }
            }
        }

        edit_group_shop_info(params).then(res=>{
            if(res.rst == 'ok'){
                this.$message.success('保存成功');
                console.log(res,'成功了');
            }else{
                this.$message.error(res.error_msg);
            }
            this.isSettingSava = false;
        }).catch(err=>{
            this.isSettingSava = false;
        })
    },
    settingInstallDesc(){
        this.desc_save = true;
        const params = {
            group_id:localStorage.getItem("group_id"),
            shop_id:this.shopId,
            edit_info:{
                storeplaylayout:this.screen_settings.storeplaylayout,
                more:{
                    install_desc:this.install_desc
                }
            }
        }
        edit_group_shop_info(params).then(res=>{
            if(res.rst == 'ok'){
                this.$message.success('保存成功');
            }else{
                this.$message.error(res.error_msg);
            }
            this.desc_save = false;
        }).catch(err=>{
            this.desc_save = false;
        })
    }
  }
};
</script>

<style scoped>
* {
  box-sizing: border-box;
}

.shop_detials {
  width: 100%;
  /* padding:0 14px; */
}

/* 标题 */
.shop_detials_title {
  position: relative;
  width: 100%;
  height: 58px;
  line-height: 58px;
  text-align: center;
}

.back_btn {
  position: absolute;
  left: 18px;
  top: 15px;
}

.detials_title {
  box-sizing: border-box;
  width: 100%;
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  padding-left: 14px;
  font-size: 14px;
  color: rgba(80, 80, 80, 1);
  font-weight: bold;
}

/* 内容区 */
.detials_content {
  box-sizing: border-box;
  height: calc(100% - 65px);
  overflow-y: auto;
  padding: 0 14px;
}

/*修改滚动条样式*/
.detials_content::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

.detials_content::-webkit-scrollbar-track {
  background: rgba(239, 239, 239, 0.5);
  border-radius: 2px;
}

.detials_content::-webkit-scrollbar-thumb {
  background: rgba(191, 191, 191, 0.7);
  border-radius: 2px;
}

.detials_content::-webkit-scrollbar-thumb:hover {
  background: rgba(191, 191, 191, 1);
}

.set_table_color p:nth-of-type(2n) {
  background-color: rgba(116, 169, 236, 0.07);
}

/* 餐厅详情 */
.restaurant_detials {
  box-sizing: border-box;
  flex-direction: column;
  width: 100%;
  min-width: 860px;
  height: 231px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 4px;
  font-size: 14px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin-bottom: 33px;
}

.restaurant_content {
  display: flex;
  box-sizing: border-box;
  height: 48px;
  width: 100%;
  padding-left: 52px;
  align-items: center;
}

.restaurant_content p {
  width: 337px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.restaurant_detials .set_bcg_color:nth-of-type(2n) {
  background: rgba(116, 169, 236, 0.1);
}

.content_name {
  display: inline-block;
  width: 70px;
  text-align: right;
}

.shop_tags {
  box-sizing: border-box;
  width: 100%;
  padding-left: 52px;
  padding-top: 20px;
}

.tags_wrap {
  max-height: 63px;
  overflow-y: auto;
  flex-wrap: wrap;
  padding-left: 19px;
  flex-direction: row;
}

.tags_list {
  align-items: center;
  height: 21px;
  margin: 0px 70px 12px 0;
}

/* 屏幕详情和经营时段 */
.bottom_detials {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  height: 501px;
  margin-bottom: 20px;
  /* border: 1px solid green; */
}

/* 屏幕详情 */
.screen_detials {
  box-sizing: border-box;
  height: 100%;
  width: 51.3%;
  min-width: 453px;
  border-radius: 4px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin-right: 18px;
  font-size: 14px;
  flex-direction: column;
}

.screen_info {
  padding-left: 14px;
  margin-top: 23px;
  height: 21px;
  font-size: 14px;
  font-weight: bold;
  color: rgba(80, 80, 80, 1);
  /* border: 1px solid red; */
}

.see_text_btn {
  display: flex;
  font-weight: normal;
  color: rgba(39, 177, 126,1);
  cursor: pointer;
  align-items: center;
}

.see_text_btn:hover {
  color: rgba(116, 169, 236, 0.8);
}

.multiple_num_h {
  display: inline-block;
  width: 32px;
  height: 21px;
  box-sizing: border-box;
  text-align: center;
  line-height: 21px;
  font-size: 12px;
  border: 1px solid var(--screen-color);
  margin-right: -1px;
  color:var(--btn-color);
  background-color: var(--screen-color);
}

.multiple_num_v {
  display: inline-block;
  width: 21px;
  height: 32px;
  box-sizing: border-box;
  text-align: center;
  line-height: 32px;
  font-size: 12px;
  border: 1px solid rgba(108, 178, 255, 1);
  margin-right: -1px;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.36);
}

.detials_table_title {
  padding-left: 14px;
  font-size: 14px;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
  color: rgba(80, 80, 80, 1);
}

.detials_table {
  width: 100%;
}

/* 营业属性 */
.daypart_wrap {
  box-sizing: border-box;
  height: 100%;
  width: 47.9%;
  min-width: 409px;
  border-radius: 4px;
  overflow: hidden;
  border: rgba(229, 229, 229, 1) solid 1px;
}

.edit_text_btn {
  font-weight: normal;
  color: var(--text-color);
  cursor: pointer;
  margin-right: 14px;
}

.edit_text_btn:hover {
  color: rgba(211, 57, 57, 0.8);
}

.daypart_table {
  width: 100%;
  /* padding-bottom: 20px; */
}

/* 抽屉样式 */
.saveBtnParent {
  height: 50px;
  text-align: right;
  background: #fff;
}

.flexBoxTwoTitle {
  user-select: none;
  font-size: 14px;
  color: #666;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
  width: 100%;
  background: #fff;
}

.closeCss {
  width: 15px;
  height: 15px;
}

.shop_detials ::v-deep .el-loading-mask {
  height: calc(100vh - 50px);
}

.shop_detials .el-table ::v-deep .el-loading-mask {
  height: 100%;
}

.bucket_type {
  display: flex;
  align-items: center;
  /* height: 63px; */
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgb(237, 237, 237);
  margin-bottom: 10px;
}

.checkbox_wrap {
  margin-right: 5px;
  margin-top: 2px;
}

.add_or_del {
  display: flex;
  height: 30px;
  align-items: center;
  margin-left: 13px;
}

.del_time,
.add_time {
  /* width: 22px;
  height: 22px; */
  font-size: 22px;
}

.del_time i,
.add_time i {
  cursor: pointer;
}

.del_time {
  color: rgb(193, 191, 191);
}

.add_time {
  margin-left: 10px;
  color: var(--background-color);
}

.period_ipt ::v-deep input {
  outline: none;
  border: none;
  /* border-left: 1px solid #c0c4cc; */
  /* border-radius: 0; */
  /* height: 100%; */
  /* width: 100%; */
}
.other_info_setting{
    width: 50%;
    border: 1px solid rgba(229, 229, 229, 1);
    margin-bottom: 33px;
    margin-right: 30px;
}
.other_info_content{
    box-sizing: border-box;
    padding:14px;
}
.screen_nums{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    /* border-bottom: 1px solid rgba(229, 229, 229, 1); */
    padding-bottom: 15px;
}
.screen_nums_block{
    margin-right: 30px;
    margin-bottom: 10px;
}
.screen_nums_type{
    margin-bottom: 10px;
}
.screen_layout{
    /* border-bottom: 1px solid rgba(229, 229, 229, 1); */
    padding-bottom: 15px;
}
.screen_setting_btn{
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top:15px;
}
.install_desc{
    width:50%;
    /* margin-left: 20px; */
    margin-bottom: 33px;
    border: 1px solid rgba(229, 229, 229, 1);
}
::v-deep .textarea_ipt textarea{
    height:190px !important;
}
</style>
<style>
.shop_detials .el-table .row_bcg {
  background: rgba(116, 169, 236, 0.07) !important;
}

.shop_detials .el-table__body-wrapper::-webkit-scrollbar {
  width: 4px !important;
}

.shop_detials .el-table__body-wrapper::-webkit-scrollbar-track {
  background: rgba(239, 239, 239, 0.5);
  border-radius: 2px;
}

.shop_detials .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: rgba(191, 191, 191, 0.7);
  border-radius: 2px;
}

.shop_detials .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(191, 191, 191, 1);
}

.shop_detials .el-table .el-table__cell.gutter {
  background: rgba(116, 169, 236, 0.07) !important;
}

/* .el-icon-loading{
        color: rgb(211, 57, 57) !important;
    }
    .el-loading-text{
        color: rgb(211, 57, 57) !important;
    } */
.shop_detials .checkbox_wrap .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.shop_detials .checkbox_wrap .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.shop_detials .checkbox_wrap .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.shop_detials
  .checkbox_wrap
  .el-checkbox__input.is-indeterminate
  .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}
</style>
