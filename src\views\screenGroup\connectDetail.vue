<template>
  <div class="box" v-loading="loading">
    <!--    头部-->
    <div class="useTop">
      <!--      左边箭头-->
      <div class="pubArrow">
        <span class="el-icon-back cursor" @click.stop="goBack"></span>
        <span>联屏管理</span>
      </div>
    </div>

    <div class="center">
      <div class="center_table">
        <div class="center_header">
          <div class="header_left">
            <div v-for="(item, index) in tableData" :class="item.v_or_h == 'h' ? '' : 'devShu'" :key="index">{{
              item.screen_index }}</div>
          </div>
          <div class="header_text">
            <!-- {{ storecode }} -->
            <!-- {{ DetailData.group_name ? DetailData.group_name : "" }} -->
            <!-- {{DetailData}} -->
            {{ DetailData.shop_info ? DetailData.shop_info.storename : '' }} ({{ storecode }})
          </div>
          <div class="header_id">联屏组ID: {{ DetailData.sg_id ? DetailData.sg_id : "" }}</div>
        </div>
        <div class="table_data">
          <div class="table_header">设备清单</div>
          <el-table :data="tableData" style="width: 100%" height="170" :header-cell-style="{ background: '#f0f0f0' }">
            <el-table-column prop="screen_index" label="屏幕序号" align="center">
              <template slot-scope="scope">
                <div :class="scope.row.v_or_h == 'h' ? 'screen_index' : 'screen_shu'">{{ scope.row.screen_index }}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="screen_id" label="屏幕ID"></el-table-column>
            <el-table-column align="center" prop="screen_model" label="屏幕标签" width="150">
              <template slot-scope="scope">
                <div class="flex" style="flex-wrap: wrap;align-items:center;">
                  <div class="flex" style="flex-direction:column;width:85px;align-items:start"
                    v-if="scope.row.screen_tags.length > 2">
                    <div style="display:flex;align-items:center">
                      <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                      {{ scope.row.screen_tags[0] }}
                    </div>
                    <div style="display:flex;align-items:center">
                      <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                      {{ scope.row.screen_tags[1] }}
                    </div>
                    <el-popover placement="top-start" title="设备标签" popper-class="popperOptions" width="200"
                      trigger="hover">
                      <div v-for="item in scope.row.screen_tags" :key="item" style="display:flex;align-items:center">
                        <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                        <span>
                          {{
                            item
                          }}
                        </span>
                      </div>
                      <span class="cursor" slot="reference">...</span>
                    </el-popover>
                  </div>
                  <div class="flex" style="flex-direction:column;width:85px;align-items:start" v-else>
                    <div style="display:flex;align-items:center" v-for="item in scope.row.screen_tags.slice(0, 2)"
                      :key="item">
                      <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                      {{ item }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="是否在线">
              <template slot-scope="scope">{{ scope.row.isonline == 0 ? '离线' : '在线' }}</template>
            </el-table-column>
            <el-table-column align="center" prop="screen_pmodel" label="设备类型"></el-table-column>
            <el-table-column align="center" prop="address" label="操作">
              <template slot-scope="scope">
                <span @click="lookDetali(scope.row)" class="lok1">查看</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="center_tags">
          <div class="tags">
            <span class="label">联屏组标签</span>
            <!-- <div style="display:flex;">
              <div v-for="item in DetailData.sg_tags" :key="item" style="display:flex;align-items:center">
                <img src="@/assets/img/tags.png" alt="" />
                <span class="ts"> {{ item }} </span>
              </div>
            </div>-->
            <div style="display:flex;width:350px;justify-content:start"
              v-if="DetailData.sg_tags ? DetailData.sg_tags.length > 3 : ''">
              <div v-for="item in DetailData.sg_tags.slice(0, 3)" :key="item.name"
                style="display:flex;align-items:center">
                <img src="../../assets/img/home_img/little_label.svg" alt style="width:30px;height:30px" />
                {{ item }}
              </div>

              <el-popover placement="top-start" title="屏幕标签" width="200" trigger="hover" popper-class="popperOptions1">
                <div v-for="item in DetailData.sg_tags" :key="item" style="display:flex;align-items:center;">
                  <img src="../../assets/img/home_img/little_label.svg" alt style="width:30px;height:30px" />
                  <span>{{ item }}</span>
                </div>
                <div class="more cursor" slot="reference">...</div>
              </el-popover>
            </div>
            <div style="display:flex;width:350px;justify-content:start" v-else>
              <div v-for="item in DetailData.sg_tags" :key="item.name" style="display:flex;align-items:center">
                <img src="../../assets/img/home_img/little_label.svg" alt style="width:30px;height:30px" />
                {{ item }}
              </div>
            </div>

            <span class="change cursor" @click="changeTags">修改</span>
          </div>
        </div>
      </div>
      <div style="flex: 6; margin-right: 17px">
        <ConnectOperation ref="ConnectOperation" :DetailData="DetailData" @Detection="Detection" @realTime="realTime"
          @reStart="reStart" @SavePosition="SavePosition" @Dismantle="Dismantle" />
      </div>
    </div>

    <div class="content tag_set">
      <h5>播放管理</h5>
      <!--          tab切换-->
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="当天内容" name="first">
          <!-- {{mainScreenList}} -->
          <ul class="list" v-loading="cdLoading" v-if="mainScreenList.length > 0">
            <li v-for="(item, index) in mainScreenList" :key="index">
              <div>
                <p class="circle">{{ index + 1 }}</p>
                <h3>{{ item.classify_label }}</h3>
                <ul>
                  <!-- <li>
                      <span>播放周期</span>
                      <p v-if="item.play_mode == 'single_use_range'">指定时间段</p>
                      <p v-else-if="item.play_mode == 'week_range'">周时间段</p>
                      <p v-else-if="item.play_mode == 'full_day'">全天</p>
                      <p v-else-if="item.play_mode == 'time_range'">小时时段</p>
                  </li>-->
                  <li>
                    <span>专辑ID：</span>
                    <p>{{ item.cf_id }}</p>
                  </li>
                  <li v-if="item.play_mode == 'single_use_range'">
                    <span>计划属性：</span>
                    <p>{{ getPlan(item.classify_info) }}</p>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="ranges_wrap">
                        <p v-for="ranges in item.time_ranges" :key="ranges">{{ ranges }}</p>
                      </div>

                      <div slot="reference" style="
                                  cursor: pointer;
                                  margin-left: 10px;
                                  width: 20px;
                                  height: 20px;
                                ">
                        <i class="el-icon-more" slot="reference" style="margin-top: 7px"></i>
                      </div>
                    </el-popover>
                  </li>
                  <li v-else>
                    <span>计划属性：</span>
                    <p>{{ getPlan(item.classify_info) }}</p>
                  </li>
                  <li v-show="item.start_time && item.end_time">
                    <span>播放时段：</span>
                    <p>{{ item.start_time }} - {{ item.end_time }}</p>
                  </li>
                  <li>
                    <span>播放类型：</span>
                    <p>{{ item.keep_time == 1 ? "独占" : "轮播" }}</p>
                  </li>
                  <!-- <li>
                      <span>播放模式</span>
                      <p>工作日</p>
                  </li>-->

                  <li>
                    <span>发布时间：</span>
                    <p>{{ item.create_time }}</p>
                  </li>
                  <li>
                    <span>发布状态：</span>
                    <p>{{ item.status == 99 ? "发布成功" : (item.status == 1 ? "待发布" : "发布异常") }}</p>
                  </li>
                </ul>
              </div>
              <div class="sideStep">
                <div class="my_step_wrap">
                  <div class="my_step" v-if="item.pub_bar">
                    <div class="every_step_lump" v-for="(item2, idx) in pubJobTypeList" :key="item2.value">
                      <div class="step_icon success_state" v-if="item.pub_bar.process == item2.value">
                        <i class="el-icon-check" style="
                                    color: #fff;
                                    line-height: 35px;
                                    font-size: 22px;
                                  "></i>
                      </div>
                      <div class="step_icon" v-else>{{ idx + 1 }}</div>
                      <div class="step_title">{{ item2.title }}</div>
                    </div>
                  </div>
                </div>
                <div class="image_wrap">
                  <img :src="item.cf_thumb" style="object-fit: cover;" :class="item.v_or_h == 0 ? 'heng' : 'shu'" />
                  <div style="position:absolute;right:4%;top:0" class="activeState">该专辑有{{ item.cs_list.length }}个内容
                  </div>
                  <div class="play" @click.stop="handlePreview(item.cs_list)">
                    <span class="el-icon-view"></span>
                    播放
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <div v-else v-loading="dayContentLoading">
            <el-empty description="暂无播放内容"></el-empty>
          </div>
        </el-tab-pane>
        <el-tab-pane label="待播内容" name="second">
          <ul class="list" v-loading="cdLoading" v-if="awaitPlayList.length != 0">
            <li v-for="(item, index) in awaitPlayList" :key="index">
              <div>
                <p class="circle">{{ index + 1 }}</p>
                <h3>{{ item.classify_label }}</h3>
                <ul>
                  <!-- <li>
                      <span>播放周期</span>
                      <p v-if="item.play_mode == 'single_use_range'">指定时间段</p>
                      <p v-else-if="item.play_mode == 'week_range'">周时间段</p>
                      <p v-else-if="item.play_mode == 'full_day'">全天</p>
                      <p v-else-if="item.play_mode == 'time_range'">小时时段</p>
                  </li>-->
                  <li>
                    <span>专辑ID：</span>
                    <p>{{ item.cf_id }}</p>
                  </li>
                  <li v-if="item.play_mode == 'single_use_range'">
                    <span>计划属性：</span>
                    <p>{{ getPlan(item.classify_info) }}</p>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="ranges_wrap">
                        <p v-for="ranges in item.time_ranges" :key="ranges">{{ ranges }}</p>
                      </div>

                      <div slot="reference" style="
                                  cursor: pointer;
                                  margin-left: 10px;
                                  width: 20px;
                                  height: 20px;
                                ">
                        <i class="el-icon-more" slot="reference" style="margin-top: 7px"></i>
                      </div>
                    </el-popover>
                  </li>
                  <li v-else>
                    <span>计划属性：</span>
                    <p>{{ getPlan(item.classify_info) }}</p>
                  </li>
                  <li v-show="item.start_time && item.end_time">
                    <span>播放时段：</span>
                    <p>{{ item.start_time }} - {{ item.end_time }}</p>
                  </li>
                  <li>
                    <span>播放类型：</span>
                    <p>{{ item.keep_time == 1 ? "独占" : "轮播" }}</p>
                  </li>
                  <!-- <li>
                      <span>播放模式</span>
                      <p>工作日</p>
                  </li>-->

                  <li>
                    <span>发布时间：</span>
                    <p>{{ item.create_time }}</p>
                  </li>
                  <li>
                    <span>发布状态：</span>
                    <p>{{ item.status == 99 ? "发布成功" : (item.status == 1 ? "待发布" : "发布异常") }}</p>
                  </li>
                </ul>
              </div>
              <div class="sideStep">
                <div class="my_step_wrap">
                  <div class="my_step" v-if="item.pub_bar">
                    <div class="every_step_lump" v-for="(item2, idx) in pubJobTypeList" :key="item2.value">
                      <div class="step_icon success_state" v-if="item.pub_bar.process == item2.value">
                        <i class="el-icon-check" style="
                                    color: #fff;
                                    line-height: 35px;
                                    font-size: 22px;
                                  "></i>
                      </div>
                      <div class="step_icon" v-else>{{ idx + 1 }}</div>
                      <div class="step_title">{{ item2.title }}</div>
                    </div>
                  </div>
                </div>
                <div class="image_wrap">
                  <img :src="item.cf_thumb" style="object-fit: cover;" :class="item.v_or_h == 0 ? 'heng' : 'shu'" />
                  <div style="position:absolute;right:4%;top:0" class="activeState">该专辑有{{ item.cs_list.length }}个内容
                  </div>
                  <div class="play" @click.stop="handlePreview(item.cs_list)">
                    <span class="el-icon-view"></span>
                    播放
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <div v-else>
            <el-empty description="暂无播放内容"></el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- 暂时隐藏功能 -->
      <!-- <div class="content_tabs flex">
        <div
          class="tabs_btn"
          @click="contentShow = 0"
          :class="contentShow == 0 ? 'tabs_checked' : ''"
        >
          <span>格子</span>
        </div>
        <div
          class="tabs_btn"
          style="margin-left: -1px"
          @click="contentShow = 1"
          :class="contentShow == 1 ? 'tabs_checked' : ''"
        >
          <span>列表</span>
        </div>
      </div>-->
      <!-- 暂时隐藏功能 -->
      <!-- <table class="view" v-show="contentShow == 1">
        <tr>
          <th>内容/时间轴</th>
          <th>10:00</th>
          <th v-for="(item, index) in 21" :key="index">0{{ index + 1 }}</th>
        </tr>
        <tr v-for="(item, index) in 2" :key="index">
          <td>
            <img src="../../assets/img/home_img/audio.png" />
          </td>
          <td></td>
          <td v-for="(item, index) in 21" :key="index"></td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td v-for="(item, index) in 21" :key="index"></td>
        </tr>
      </table>-->
    </div>
    <!-- 预览mask -->
    <previsualization :isPreviewMaskShow="isPreviewMaskShow" :PreviewSrc="PreviewSrc" :PreviewType="PreviewType"
      @closePreviewMask="closePreviewMask" :carouselUrl="carouselUrl"></previsualization>

    <Dialog :tagsDialog="tagsDialog" :ShopTagList="ShopTagList" :delTagsList="delTagsList" @saveTags="saveTags"
      @handleCloseDialog="handleCloseDialog"></Dialog>
  </div>
</template>

<script>
import {
  get_dma_detail,
  dma_detection,
  dma_screenshot,
  dma_restart,
  dma_dismantle,
  // dma_position,
  dma_detection_screenshot,
  get_dma_content,
  add_dma_or_delete
} from "@/api/screen_group/screen_group";
import { dma_position } from "@/api/device/device";
import ConnectOperation from "./component/connectOperation.vue";
import Dialog from "./component/Dialog.vue";
import { get_tags_list } from "@/api/system/label";
import previsualization from "@/components/communal/previsualization";
// import { disposeTime } from "@/utils/formatDate"
export default {
  name: "connectDetail",
  components: {
    ConnectOperation,
    Dialog,
    previsualization
  },
  data() {
    return {
      tableData: [],
      //  tab默认第一个
      activeName: "first",
      activeNameList: "first",
      //  代播
      daiboShow: true,
      daiboShow2: false,

      // lh
      dmakey: "",
      // 详情
      DetailData: {},
      storecode: "",
      // loading效果
      loading: false,
      // 店铺id
      shop_id: "",
      // 联屏id
      sg_id: "",
      // 弹框状态
      tagsDialog: false,
      // 联屏组标签
      ShopTagList: [],
      delTagsList: [],
      // 条数
      PageSize: 100,
      // 主屏播放列表
      mainScreenList: [],
      awaitPlayList: [],
      dayContentLoading: false,
      isPreviewMaskShow: false,
      PreviewSrc: "",
      PreviewType: "carousel",
      carouselUrl: "thumb_url",
      temp: {
        week_range: [],
        speTime: ""
      },
      pubJobTypeList: [
        {
          title: "下发",
          value: 0.2
        },
        {
          title: "获取",
          value: 0.4
        },
        {
          title: "下载",
          value: 0.6
        },
        {
          title: "播放",
          value: 1
        }
      ],
      weeks: [
        {
          label: "星期一",
          value: 2
        },
        {
          label: "星期二",
          value: 3
        },
        {
          label: "星期三",
          value: 4
        },
        {
          label: "星期四",
          value: 5
        },
        {
          label: "星期五",
          value: 6
        },
        {
          label: "星期六",
          value: 7
        },
        {
          label: "星期日",
          value: 1
        }
      ]
    };
  },
  methods: {
    //返回
    goBack() {
      this.$router.go(-1);
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return "warning-row";
      } else if (rowIndex === 3) {
        return "success-row";
      }
      return "";
    },
    // 表格头第一行上色
    rowClass({ row, rowIndex }) {
      return "background:#f0f0f0";
    },
    //  待播内容切换
    handleClick(tab, event) {
      if (tab.$options.propsData.label == "当天内容") {
      } else if (tab.$options.propsData.label == "待播内容") {
        this.daiboShow2 = true;
      }
    },
    //  视图tab切换
    viewTabClick(tab, event) {
      if (tab.$options.propsData.label == "视图") {
        this.daiboShow = false;
        this.daiboShow2 = true;
      } else {
        this.activeNameList = "first";
        this.daiboShow = true;
      }
    },
    // 获取详情数据
    getDetail() {
      const params = {
        sg_key: this.dmakey
      };
      this.delTagsList = [];
      get_dma_detail(params).then(res => {
        if (res.rst == "ok") {
          this.DetailData = res.data[0];
          console.log(this.DetailData, 'DetailData');
          res.data[0].sg_tags.forEach(item => {
            this.delTagsList.push({
              name: item,
              active: false
            });
          });
          this.tableData = this.DetailData.screen_info;
          res["data"][0]["screens"].forEach(item => {
            this.tableData.forEach(items => {
              // console.log(items,'item');
              items.v_or_h = item.v_or_h;
            });
          });
          this.DetailData.screen_info.forEach((item, index) => {
            this.$set(
              item,
              "label",
              `序号${item.screen_index} ${item.screen_id}`
            );
            this.$set(item, "x", Number(index));
            this.$set(item, "y", 0);
            this.$set(item, "style", `0`);
          });
          this.sg_id = res.data[0].sg_id;
          this.getDmaConent();
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 查看
    lookDetali(row) {
      this.$router.push({
        path: "/deviceManage/screenDetail",
        query: {
          sid: row.screen_id,
          storecode: row.storecode,
          shop_id: row.shop_id
        }
      });
      // this.$router.push({
      //   // path: "/deviceManage/dslist",
      //   path: "/deviceManage/screenGroup",
      //   query: {
      //     sid: row.screen_id,
      //   },
      // });
    },
    // 设备检测
    Detection(params) {
      this.$refs.ConnectOperation.detectionLoading = true;
      dma_detection(params).then(res => {
        if (res.rst == "ok") {
          if (res.data[0].failed != 0) {
            this.$refs.ConnectOperation.onlineState = true;
            this.$refs.ConnectOperation.detectionLoading = false;
          } else {
            this.$refs.ConnectOperation.onlineState = false;
            this.$refs.ConnectOperation.detectionLoading = false;
          }
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 实时截图
    realTime(params, selectScreen) {
      this.$refs.ConnectOperation.ScreenshotLoading = true;
      dma_screenshot(params).then(res => {
        if (res.rst == "ok") {
          let startnum = 0;
          let timer = setInterval(() => {
            let screenids = [];
            startnum++;
            selectScreen.forEach(item => {
              dma_detection_screenshot({
                screen_id: item.screen_id
              }).then(resuccess => {
                // screenids[index].ack = resuccess.data[0].ack
                screenids.push(resuccess.data[0].ack);
              });
            });
            setTimeout(() => {
              if (screenids.indexOf(0) == -1) {
                clearInterval(timer);
                this.$message.success("截图成功");
                this.$refs.ConnectOperation.ScreenshotLoading = false;
              } else {
                if (startnum == 8) {
                  this.$message.warning(
                    "截图失败,请检查设备网络和截图功能是否安装！"
                  );
                  this.$refs.ConnectOperation.ScreenshotLoading = false;
                  clearInterval(timer);
                }
              }
            }, 1000);
          }, 2000);
          // this.$message.success("截图成功");
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 设备重启
    reStart(params) {
      this.$confirm("此操作会将设备重启, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.loading = true;
          dma_restart(params).then(res => {
            if (res.rst == "ok") {
              this.loading = false;
              this.$message.success("重启成功");
            } else {
              this.$message.warning(res.rst);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消重启设备"
          });
        });
    },
    // 保存位置修改
    SavePosition(param) {
      const params = {
        sg_key: this.dmakey,
        screens: param
      };
      this.loading = true;
      dma_position(params)
        .then(res => {
          if (res.rst == "ok") {
            this.$message.success("位置调整成功");
            this.$refs.ConnectOperation.PositionState = false;
            this.loading = false;
            this.getDetail();
            this.$refs.ConnectOperation.setSelect();
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 联屏拆除
    Dismantle(params) {
      this.$confirm("是否拆除屏幕?", "联屏拆除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.loading = true;

          dma_dismantle(params).then(res => {
            if (res.rst == "ok") {
              this.$message.success("联屏拆除成功");
              this.loading = false;
              this.$router.push("/deviceManage/vslist");
              // this.getDetail();
            } else {
              this.$message.warning(res.rst);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消拆除"
          });
        });
    },
    // 获取联屏播放内容
    getDmaConent() {
      this.dayContentLoading = true;
      const params = {
        sg_id: this.sg_id
      };
      get_dma_content(params).then(res => {
        console.log("res当天内容", res);
        if (res.rst == "ok") {
          // this.mainScreenList = res['data'][0]['cf_info_list'];
          // console.log(this.mainScreenList, "mainScreenList");
          res.data[0].cf_info_list.forEach(item => {
            // if (item.single_use_range)
            //   if (item.classify_info.play_mode == "single_use_range") {

            //   }
            if (item.pub_bar.process == 0.6) {
              item.pub_bar.process = 1;
            }
            this.mainScreenList.push(item);
            // disposeTime(item.classify_info.single_use_range)
            //  if (item.today_content==1) {
            //   } else {
            //     this.awaitPlayList.push(item);
            //   }
            // if(item.classify_info.week_range){
            //      item.classify_info.week_range.forEach(list=>{
            //     this.temp.speTime = list.split("|")[1]
            //     console.log(list,'list');
            //   })
            // }
            //   if(item.classify_info.single_use_range){
            //     //  item.classify_info.week_range.forEach(list=>{
            //     this.temp.speTime = item.classify_info.single_use_range
            //     console.log(list,'list');
            // })
            // }
          });
          this.cdLoading = false;
          this.dayContentLoading = false;
        } else {
          this.$message.error("获取播放列表内容失败");
          this.cdLoading = false;
          this.dayContentLoading = false;
        }
      });
    },
    // 预览
    handlePreview(src) {
      this.PreviewSrc = src;
      // this.PreviewSrc = 'http://grayds.instwall.com/store/render_native_play/?template_id=5222&section_id=694757&do_type=edit&render_type=sg';
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    },
    // 获取联屏组标签
    getTagsData() {
      this.ShopTagList = [];
      const params = {
        tag_type: "sngroup",
        page: 0,
        size: this.PageSize,
        dmbSource: true
      };
      get_tags_list(params).then(res => {
        res.data[0].tags_list.forEach(item => {
          this.ShopTagList.push({
            name: item,
            active: false
          });
        });
      });
    },
    // 修改标签弹框
    changeTags() {
      this.tagsDialog = true;
      this.getTagsData();
    },
    // 保存修改联屏
    saveTags(val, message) {
      const params = {
        sg_ids: [this.DetailData.sg_id],
        tags: val,
        action: "add"
      };
      if (message == "relation") {
        params.action = "add";
        add_dma_or_delete(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("新增标签成功");
            this.tagsDialog = false;
            this.getDetail();
          }
        });
      } else if (message == "deltag") {
        params.action = "del";
        add_dma_or_delete(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("删除标签成功");
            this.tagsDialog = false;
            this.getDetail();
          }
        });
      }
    },
    // 取消
    handleCloseDialog() {
      this.tagsDialog = false;
    },
    // 列表计划属性
    getPlan(val) {
      let intervalName = "",
        intervalDate = [],
        intervalTime = "";

      if (val.play_mode == "single_use_range") {
        return "按指定时间段";
      } else if (val.play_mode == "week_range") {
        intervalName = "按周内日期";
        if (val.week_range) {
          val.week_range.forEach(item => {
            // console.log(item,'myitem');
            this.weeks.forEach(lists => {
              if (item.split("|")[0] == lists.value) {
                intervalDate.push(lists.label);
              }
            });
            intervalTime = item.split("|")[1];
          });
        }
        // intervalTime = val.time_ranges[0];
        return `${intervalName}（${intervalDate} ${intervalTime}）`;
      } else if (val.play_mode == "full_day") {
        return "全天播放";
      } else if (val.play_mode == "time_range") {
        return "小时时段";
      }
    }
  },
  created() {
    this.dmakey = this.$route.query.dmakey;
    this.storecode = this.$route.query.storecode;
    this.shop_id = this.$route.query.shop_id;
    this.specification = this.$route.query.specification;
    this.getTagsData();
  },
  mounted() {
    this.getDetail();
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .warning-row {
  background: #f0f0f0;
}

::v-deep .el-table .el-table__cell {
  padding: 5px 0;
}

::v-deep .el-table .success-row {
  background: #f0f0f0;
}

.devShu {
  width: 20px !important;
  height: 25px !important;
  line-height: 25px !important;
}

.box {
  width: 100%;
  background-color: #fff;
  font-size: 14px;
  position: relative;
  box-sizing: border-box;
  padding: 0 25px;
}

.useTop {
  display: flex;
  width: 100%;
  height: 60px;
  align-content: center;
  border-bottom: 2px solid #ededed;
}

.el-icon-back {
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}

.useTop>.pubArrow {
  font-size: 15px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.center {
  display: flex;
  height: 335px;
  margin-top: 24px;

  .center_table {
    // width: 30%;
    flex: 2;
    height: 330px;
    border-radius: 10px;
    border: rgba(229, 229, 229, 1) solid 1px;

    .center_header {
      width: 100%;
      height: 60px;
      border-bottom: 1px solid #ededed;
      display: flex;
      align-items: center;
      padding: 0 5px 0 12px;

      .header_left {
        display: flex;
        flex: 1;
        justify-content: left;

        div {
          width: 28px;
          height: 18px;
          background-color: var(--screen-color);
          color: #fff;
          border: #fff solid 1px;
          font-size: 14px;
          text-align: center;
          line-height: 18px;
        }
      }

      .header_text {
        height: 18px;
        color: rgba(80, 80, 80, 1);
        font-size: 14px;
        font-weight: bold;
        padding-left: 8px;
        margin-right: 10px;
        line-height: 18px;
        padding-top: 1px;
        flex: 6;
      }

      .header_id {
        color: rgba(166, 166, 166, 1);
        font-size: 14px;
        flex: 3;
        padding-right: 10px;
        display: flex;
        justify-content: right;
        font-weight: bold;
      }
    }

    .table_data {
      margin-top: 10px;
      padding-left: 12px;
      padding-right: 12px;

      .table_header {
        color: rgba(52, 52, 52, 0.9642857142857143);
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .screen_index {
        width: 27px;
        height: 17px;
        line-height: 17px;
        text-align: center;
        color: #fff;
        background-color: var(--screen-color);
        font-size: 14px;
      }

      .screen_shu {
        width: 20px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        color: #fff;
        background-color: var(--screen-color);
        font-size: 14px;
      }
    }

    .center_tags {
      display: flex;
      height: 60px;
      align-items: center;
      padding-left: 12px;

      .tags {
        display: flex;
        align-items: center;

        .label {
          color: rgba(80, 80, 80, 1);
          font-size: 14px;
          font-weight: bold;
        }

        img {
          width: 32px;
          height: 22px;
          padding-left: 8px;
        }

        .ts {
          color: rgba(91, 91, 91, 1);
          font-size: 13px;
          font-weight: bold;
        }

        .change {
          color: var(--text-color);
          font-size: 13px;
          font-weight: bold;
          padding-left: 22px;
        }
      }
    }
  }
}

.content {
  margin-top: 10px;
  position: relative;
}

.content>h5 {
  margin: 10px 0;
  color: rgba(52, 52, 52, 0.9642857142857143);
  font-size: 14px;
  font-weight: bold;
}

.content ::v-deep .el-tabs__nav {
  width: 100%;
  height: 40px;
  border-top: 1px solid #e5e5e5;
}

.content ::v-deep .el-tabs__header {
  margin: 0;
}

.content .list {
  padding-left: 30px;
}

.content .list>li {
  border-left: 3px solid #dfdfdf;
  padding: 30px;
  position: relative;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  display: flex;
}

.content .list>li>div>.circle {
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--background-color);
  font-size: 20px;
  text-align: center;
  color: #fff;
  height: 40px;
  left: -22px;
  top: 20px;
  position: absolute;
}

// .content .list>li>div:nth-child(1){
//   width: 25%;
// }
// .content .list>li>div:nth-child(1) ul li span{
//   width: 100px;
// }
.content .list>li>div>h3 {
  color: rgba(16, 16, 16, 1);
  font-size: 20px;
  font-weight: bold;
}

.content .list>li>div>ul>li {
  margin: 20px;
  display: flex;
  align-items: center;
}

.content .list>li>div>ul>li:last-of-type>p {
  color: rgba(0, 186, 173, 1);
  line-height: 21px;
}

.content .list>li>div>ul>li>span {
  width: 70px;
  height: 21px;
  line-height: 21px;
}

.content .list>li>div>ul>li>p {
  height: 21px;
  line-height: 21px;
}

.sideStep>div {
  position: relative;
  height: 124px;
}

.content .el-tabs__nav {
  padding: 0 5px;
}

::v-deep .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  height: 100%;
}

.tabBg {
  background-color: var(--btn-background-color);
}

/*步骤条*/
::v-deep .el-step__title.is-finish {
  color: rgba(80, 80, 80, 1);
}

.sideStep>div>img:hover+.play {
  display: block;
}

.sideStep>div>.play {
  width: 60px;
  height: 60px;
  background-color: #474747;
  opacity: 0.7;
  color: #fff;
  border: 1px solid #fff;
  border-radius: 50%;
  position: absolute;
  margin: auto;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  line-height: 85px;
  display: none;
}

.sideStep>div>.play>img {
  display: block;
  width: 26px;
  height: 26px;
  left: 50%;
  top: 6px;
  margin-left: -13px;
  position: absolute;
}

.content .list>li>div>.circle {
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--background-color);
  font-size: 20px;
  text-align: center;
  color: #fff;
  height: 40px;
  left: -20px;
  top: 20px;
  position: absolute;
}

.content .list>li>div>h3 {
  color: rgba(16, 16, 16, 1);
  font-size: 20px;
  font-weight: bold;
}

.content .list>li>div>ul>li {
  margin: 20px;
  display: flex;
  align-items: center;
}

.content .list>li>div>ul>li>span {
  width: 70px;
  height: 21px;
}

/*列表视图tab切换*/
.tabView {
  /*width: 137px;*/
  /*height: 28px;*/
  line-height: 28px;
  position: absolute;
  top: 29px;
  right: 155px;
  border-color: #cccccc;
}

::v-deep .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  line-height: 50px;
}

/*视图*/
.view {
  width: 1109px;
  margin-top: 20px;
  z-index: 1002;
  color: rgba(128, 128, 128, 1);
  border: rgba(198, 198, 198, 1) solid 1px;
  height: 212px;
  background-color: rgba(255, 255, 255, 1);
  border-collapse: collapse;
}

.view ::v-deep el-tabs__item {
  position: absolute;
  right: 50px;
}

.view .el-tabs__nav {
  text-align: right;
}

.view ::v-deep #tab-second {
  position: absolute;
  right: 0;
}

.tabView> ::v-deep .el-tabs__header {
  text-align: right;
}

.view>tr>td>img {
  width: 150px;
  height: 50px;
  padding: 2px;
  display: block;
}

.view>tr {
  width: 50px;
  height: 50px;
}

.view>tr>td {
  border: 1px solid #cccccc;
  width: 50px;
  height: 50px;
}

.view>tr>th:first-child {
  width: 100px;
}

.view>tr>th {
  height: 30px;
  width: 30px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
}

::v-deep table {
  margin-top: 0;
}

::v-deep .el-table td.el-table__cell div {
  display: flex;
  justify-content: center;
}

.content_tabs {
  position: absolute;
  right: 0;
  top: 36px;
}

.tabs_btn {
  border: 1px solid rgba(108, 178, 255, 1);
  font-size: 14px;
  padding: 6px 15px;
  cursor: pointer;
  color: rgba(108, 178, 255, 1);
  background: #fff;
  white-space: nowrap;
}

.tabs_btn:hover {
  border: 1px solid rgba(108, 178, 255, 0.7);
}

.tabs_checked {
  color: #fff;
  background: rgba(108, 178, 255, 1);
}

.video_content {
  width: 100%;
  height: calc(100% - 67px);
  /* overflow-y:auto ; */
  /* border: 1px solid pink; */
}

.color_disc {
  width: 26px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  border-radius: 50%;
  cursor: pointer;
  margin-right: 10px;
}

.color_disc_active {
  /* background: black !important; */
}

/* 格子 */
.lattice {
  height: 100%;
  flex-direction: column;
}

.lattice_header {
  width: 100%;
  height: 42px;
  line-height: 42px;
  background-color: var(--text-color-light);
  padding-left: 10px;
  font-size: 14px;
  color: #fff
}

.lattice_content {
  flex-wrap: wrap;
  overflow-y: auto;
}

.lattice_item {
  width: 300px;
  height: 340px;
  margin-right: 16px;
  margin-top: 15px;
  font-size: 13px;
  color: rgba(56, 56, 56, 1);
  background-color: #fff;
}

.item_image {
  position: relative;
  width: 100%;
  height: 176px;
  padding: 3px;
}

.item_image:hover .edit_mask {
  display: flex;
}

.video_icon {
  position: absolute;
  width: 34px;
  height: 34px;
  top: 13px;
  right: 13px;
  object-fit: cover;
}

.edit_mask {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 176px;
  background-color: rgba(0, 0, 0, 0.335);
  color: #fff;
  justify-content: center;
  align-items: center;
}

.mask_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 48px;
  height: 48px;
  background-color: rgba(0, 0, 0, 0.564);
  border-radius: 50%;
  font-size: 11px;
  margin: 0 10px;
}

.mask_btn:hover {
  background-color: rgba(0, 0, 0, 0.464);
}

.each_tags {
  align-items: center;
  margin-right: 11px;
}

.up_date,
.item_size,
.item_tags {
  margin-top: 10px;
  align-items: center;
  padding: 0 9px;
}

.item_tags {
  margin-top: 13px;
  padding: 0 4px;
}

/* 列表 */
.lists {
  width: 100%;
  height: 100%;
}

.footer {
  height: 72px;
  align-items: center;
  justify-content: space-between;
}

.txt_ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 弹框 */
.relation_tags {
  display: flex;
  flex-wrap: wrap;
  height: 279px;
  margin-bottom: 20px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;
  font-size: 14px;
  border-radius: 24px;
  margin-right: 24px;
  margin-bottom: 18px;
  padding: 0 13px;
  border: 1px solid rgba(209, 209, 209, 1);
  cursor: pointer;
  /* 禁止文字选中 */
}

.tag_active {
  color: rgba(108, 178, 255, 1);
  background-color: rgba(212, 232, 255, 1);
  border: 1px solid rgba(108, 178, 255, 1);
}

.lok1 {
  cursor: pointer;
}

.sideStep {
  min-width: 500px;
  padding: 30px 20px;
}

.my_step_wrap {
  display: flex;
  width: 100%;
  align-items: center;
}

.my_step {
  display: flex;
  height: 76px;
  width: 100%;
}

.every_step_lump {
  position: relative;
  height: 100%;
  margin-left: 100px;
}

.every_step_lump:first-of-type {
  margin-left: 0;
}

.every_step_lump::before {
  position: absolute;
  top: 17px;
  left: -100px;
  content: "";
  height: 2px;
  width: 100px;
  background: rgba(153, 153, 153, 1);
}

.every_step_lump:first-of-type::before {
  width: 0;
}

.step_icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-content: center;
  background: rgba(210, 210, 210, 1);
  color: rgba(128, 128, 128, 1);
  line-height: 35px;
  font-size: 16px;
  cursor: default;
  user-select: none;
}

.success_state {
  background: var(--screen-color);
}

.step_title {
  text-align: center;
  margin-top: 5px;
  color: rgba(80, 80, 80, 1);
}

.sideStep>div {
  /* width: 550px; */
  height: 124px;
  position: relative;
}

.sideStep>.image_wrap>img {
  /* width: 100%; */
  object-fit: contain;
  height: 100%;
  display: block;
}

.image_wrap {
  width: 50%;
  position: relative;
}

.image_wrap .shu {
  width: 31%;
}

.image_wrap .heng {
  width: 100%;
}

.sideStep>.image_wrap>.play {
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.9642857142857143);
  border-radius: 30px;
  font-size: 14px;
  color: #fff;
  border: rgba(255, 255, 255, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  margin: auto;
  right: 0;
  bottom: 0;
  display: none;
  cursor: pointer;
}

.sideStep>div>.play>span {
  position: absolute;
  left: 17px;
  font-size: 24px;
  display: block;
  margin-top: 10px;
  color: rgba(255, 255, 255, 1);
}

.sideStep>div:hover .play {
  display: block;
}
</style>
<style lang="scss">
/* tabs选中的样式 */
.tag_set .is-active {
  color: var(--text-color) !important;
  background-color: var(--active-color) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-first {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-second {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
  /* display: none; */
  background-color: var(--text-color) !important;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
  color: var(--text-color) !important;
}

.activeState {
  position: absolute;
  padding: 5px 10px;
  background: rgba($color: #000000, $alpha: 0.4) !important;
  color: #fff !important;
}
</style>