<template>
  <div>
    <table>
      <tr class='timeSpace'>
        <th>时段</th>
        <td colspan='2'>
          <div class='two'>
            <div>
              <p>1</p>
              <p>2</p>
            </div>
            <p>ID：2345612</p>
          </div>
        </td>
        <td colspan='2'>
          <div class='two'>
            <div>
              <p>3</p>
              <p>4</p>
            </div>
            <p>ID：2345612</p>
          </div>
        </td>
      </tr>
      <tr>
        <th>工作日早餐</th>
        <td>
          <!--          :class="showBg?'alertBgImg':[]"-->
          <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          <el-dialog custom-class="newAddDialog" width="45%" :visible.sync="showBg">
            <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          </el-dialog>
          <!--          <div class='blackClose' v-show='showBg' @click.stop='showBg=false' style="cursor: pointer">×</div>-->
          <div class='imgBtns'>
            <div class='fourBtn' @click.stop='fourPreview'>
              <i class='el-icon-view'></i>
              预览
            </div>
            <div class='fourBtn' @click.stop='fourClear'>
              <i class='el-icon-delete'></i>
              清空
            </div>
            <div class='fourBtn' @click.stop='fourEdit'>
              <i class='el-icon-edit'></i>
              编辑
            </div>
            <div class='fourBtn' @click.stop='fourAdd'>
              <i class='el-icon-circle-plus-outline'></i>
              新增
            </div>

          </div>
        </td>
        <td>
          <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          <div class='imgBtns'>
            <div class='fourBtn' @click.stop='fourPreview'>
              <i class='el-icon-view'></i>
              预览
            </div>
            <div class='fourBtn' @click.stop='fourClear'>
              <i class='el-icon-delete'></i>
              清空
            </div>
            <div class='fourBtn' @click.stop='fourEdit'>
              <i class='el-icon-edit'></i>
              编辑
            </div>
            <div class='fourBtn' @click.stop='fourAdd'>
              <i class='el-icon-circle-plus-outline'></i>
              新增
            </div>

          </div>
        </td>
        <td>
          <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          <div class='imgBtns'>
            <div class='fourBtn' @click.stop='fourPreview'>
              <i class='el-icon-view'></i>
              预览
            </div>
            <div class='fourBtn' @click.stop='fourClear'>
              <i class='el-icon-delete'></i>
              清空
            </div>
            <div class='fourBtn' @click.stop='fourEdit'>
              <i class='el-icon-edit'></i>
              编辑
            </div>
            <div class='fourBtn' @click.stop='fourAdd'>
              <i class='el-icon-circle-plus-outline'></i>
              新增
            </div>

          </div>
        </td>
        <td>
          <!-- <img src='../../assets/img/home_img/breakfast.png'> -->
          <div class='imgBtns'>
            <div class='fourBtn' @click.stop='fourPreview'>
              <i class='el-icon-view'></i>
              预览
            </div>
            <div class='fourBtn' @click.stop='fourClear'>
              <i class='el-icon-delete'></i>
              清空
            </div>
            <div class='fourBtn' @click.stop='fourEdit'>
              <i class='el-icon-edit'></i>
              编辑
            </div>
            <div class='fourBtn' @click.stop='fourAdd'>
              <i class='el-icon-circle-plus-outline'></i>
              新增
            </div>

          </div>
        </td>
      </tr>
      <tr>
        <th>早午餐过渡1</th>
        <td v-for="(item,index) in 4" :key="index">
          <div class='add' @click.stop='add'>
            <img src='../../assets/img/home_img/jia.svg' class='addImg' />
            新增
          </div>
        </td>
      </tr>
      <tr>
        <th>早午餐过渡2</th>
        <td v-for="(item,index) in 4" :key="index" @click.stop='add'>
          <div class='add'>
            <img src='../../assets/img/home_img/jia.svg' class='addImg' />
            新增
          </div>
        </td>
      </tr>
      <tr>
        <th>午餐</th>
        <td v-for="(item,index) in 4" :key="index" @click.stop='add'>
          <div class='add'>
            <img src='../../assets/img/home_img/jia.svg' class='addImg' />
            新增
          </div>
        </td>
      </tr>
      <tr>
        <th>下午茶</th>
        <td v-for="(item,index) in 4" :key="index" @click.stop='add'>
          <div class='add'>
            <img src='../../assets/img/home_img/jia.svg' class='addImg' />
            新增
          </div>
        </td>
      </tr>
      <tr class='timeSpace'>
        <th></th>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
    </table>
    <!--    点击后出现-->
    <el-dialog title="新增内容" :visible.sync="showAlertBox" width="61%" custom-class="tabsDatat"
      :before-close="showAlertBox">
      <!--      <div class='top'>-->
      <!--        <h3>新增内容</h3>-->
      <!--        <p @click.stop='showAlertBox=false'>×</p>-->
      <!--      </div>-->
      <div class='threeParts tag_set'>
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="图片" name="first">
            <div class='content'>
              <div class='chunk' v-for="(item,index) in 6" :key="index">
                <!-- <img src='../../assets/img/home_img/breakfast.png' /> -->
                <div :class="checkShow?'el-icon-circle-check':''" @click.stop="checked(index)"></div>
                <p>1920*1080</p>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="视频" name="second">
            <div class='content'>视频管理</div>
          </el-tab-pane>
          <el-tab-pane label="H5" name="third">
            <div class='content'>角色管理</div>
          </el-tab-pane>
        </el-tabs>
        <div class='selectAll'>
          <el-select v-model="optionValue1" placeholder="请选择">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="optionValue2" placeholder="请选择">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class='view' style="cursor:pointer;">查看</div>
      </div>
      <div class='btns'>
        <div class='close' @click.stop='showAlertBox=false'>取消</div>
        <div @click.stop='alertOk'>确定</div>
      </div>
    </el-dialog>
    <!--    <div class='alertBox' v-show='showAlertBox'>-->
    <!--     -->
    <!--    </div>-->
  </div>

</template>

<script>
export default {
  name: 'TimeFrame',
  data() {
    return {
      //选择框
      checkIndex: -1,
      checkShow: false,
      showAlertBox: false,
      //  图片、视频、H5
      activeName: 'first',
      //  大图
      showBg: false,
      //  横屏
      options1: [{
        value: '选项1',
        label: '横屏1920*1080'
      }, {
        value: '选项2',
        label: '竖屏1080*1920'
      }, {
        value: '选项3',
        label: '其他尺寸'
      }],
      optionValue1: '横屏1920*1080',
      //  全国市场
      //  横屏
      options2: [{
        value: '选项1',
        label: '全国市场'
      }, {
        value: '选项2',
        label: '上海市场'
      }, {
        value: '选项3',
        label: '北京市场'
      }, {
        value: '选项4',
        label: '成都市场'
      }],
      optionValue2: '全国市场'
    }
  },
  methods: {
    //新增
    add() {
      this.showAlertBox = true;
    },
    //  弹出框取消按钮
    alertOk() {
      this.showAlertBox = false;
    },
    //  tab切换
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 预览
    fourPreview() {
      this.showBg = true;
    },
    // 清空
    fourClear() {

    },
    // 编辑  跳转页面
    fourEdit() {
      this.$router.push({
        path: "/deploy/useShortEdit"
      })
    },
    // 新增
    fourAdd() {

    },
    //选择框
    checked(idx) {
      this.checkShow = !this.checkShow;
      this.checkIndex = idx;
    }
  }
}
</script>

<style scoped>
table {
  font-size: 0.14rem;
  width: 9.5rem;
  height: 7.72rem;
  background-color: rgba(255, 255, 255, 1);
  border-spacing: 0;
}

table>tr {
  width: 0.95rem;
  height: 0.85rem;
  text-align: center;
}

th {
  border: 1px solid rgba(224, 224, 224, 0.99);
  width: 1rem;
  font-weight: 500;
}

td {
  border: 0.01rem solid rgba(224, 224, 224, 0.99);
}

table>.timeSpace {
  height: 0.3rem;
}

td>.two {
  display: flex;
  justify-content: center;
  align-items: center;
}

td>.two>div>p {
  width: 0.28rem;
  height: 0.18rem;
  display: inline-block;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 0.14rem;
  border: rgba(39, 177, 126,1) solid 0.01rem;
  text-align: center;
}

td>.two>p {
  color: rgba(166, 166, 166, 1);
  font-size: 0.12rem;
  font-weight: bold;
  margin-left: 0.2rem;
}

td>img {
  width: 2.06rem;
  height: 1.2rem;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  right: 0;
}

td:hover .imgBtns {
  display: block;
}

.fourBtn>i {
  display: block;
  margin: 5px 0 2px;
  font-size: 10px;
}

td>.imgBtns {
  z-index: 5;
  position: absolute;
  margin: auto;
  height: 41px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
}

/*编辑的大图*/
td>.alertBgImg {
  width: 938px;
  height: 524px;
  position: absolute;
  left: -257px;
  top: 400px;
  z-index: 10;
  object-fit: contain;
}

.blackClose {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(17, 17, 17, 1);
  border-radius: 20px;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top: -6px;
  right: -480px;
  z-index: 10;
}

/*编辑、新增*/
td>.imgBtns>.fourBtn {
  width: 41px;
  height: 41px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border: rgba(255, 255, 255, 1) solid 1px;
  border-radius: 21px;
  color: #fff;
  font-size: 10px;
  float: left;
  margin: 0 4px;
  cursor: pointer;
}

td>.imgBtns>div>.iconfont {
  width: 17px;
  height: 17px;
  display: block;
  margin: 3px 0 0 12px;
  color: #fff;
  font-size: 17px;
  color: rgba(255, 255, 255, 1);
}

td {
  width: 210px;
  position: relative;
}

.add {
  width: 0.51rem;
  height: 0.51rem;
  color: rgba(80, 80, 80, 1);
  border-radius: 0.26rem;
  font-size: 0.12rem;
  border: rgba(229, 229, 229, 1) solid 0.01rem;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  line-height: 0.7rem;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.12rem;
  color: rgba(229, 229, 229, 1);
}

.add>.addImg {
  width: 0.2rem;
  height: 0.2rem;
  position: absolute;
  margin: auto;
  top: 0.05rem;
  left: 0;
  right: 0;
}

/*点击后弹出*/
.alertBox {
  width: 10rem;
  height: 6.75rem;
  position: absolute;
  margin: auto;
  left: 0px;
  right: 0;
  top: -1rem;
  bottom: 0;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.16rem;
  font-size: 0.14rem;
  line-height: 150%;
  text-align: center;
  padding: 0 0.15rem 0.21rem 0.3rem;
}

.alertBox>.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.5rem;
  border-bottom: 0.01rem solid #e5e5e5;
}

.alertBox>.top>h3 {
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
}

.alertBox>.top>p {
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.3rem;
  color: #999999;
}

.threeParts {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.threeParts>.el-tabs {
  width: 100%;
}

::v-deep .el-tabs__header {
  margin: 0;
}

.selectAll {
  position: absolute;
  right: 1.23rem;
  top: 0.08rem;
  height: 0.4rem;
}

.el-select {
  margin-left: 0.1rem;
}

/*!*下拉框内容*!*/
::v-deep .el-tabs__nav-scroll {
  height: 0.42rem;
}

.view {
  width: 0.88rem;
  height: 0.32rem;
  color: rgba(80, 80, 80, 1);
  background-color: var(--background-color);
  border-radius: 0.06rem;
  font-size: 0.14rem;
  text-align: center;
  right: 0.24rem;
  top: 0.04rem;
  color: #fff;
  line-height: 0.32rem;
  position: absolute;
}

.content {
  height: 4.3rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  padding: 0.23rem 0 0 11px;
}

.content>.chunk {
  width: 1.85rem;
  height: 1.85rem;
  position: relative;
  margin-right: 0.1rem;
  margin-bottom: 10px;
  box-shadow: 0rem 0.03rem 0.03rem 0rem rgba(0, 0, 0, 0.12857142857142861);
}

.content>.chunk:hover>p {
  display: block;
  cursor: pointer;
}

.content>.chunk>img {
  width: 100%;
  height: 100%;
}

.content>.chunk>div {
  width: 0.23rem;
  height: 0.23rem;
  background-color: #75726a;
  opacity: .9;
  position: absolute;
  border-radius: 50%;
  top: 0.13rem;
  left: 0.1rem;
  cursor: pointer;
}

.content>.chunk>p {
  height: 0.26rem;
  width: 100%;
  line-height: 0.26rem;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(0, 0, 0, 0.35);
  text-align: center;
  position: absolute;
  opacity: 0.7;
  bottom: 0;
  background-color: #a29d86;
  font-size: 0.11rem;
  display: none;
}

/*取消*/
.btns {
  display: flex;
  margin-top: -12px;
  padding-bottom: 10px;
  justify-content: flex-end;
}

.btns>div {
  width: 0.81rem;
  height: 0.36rem;
  cursor: pointer;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  border: rgba(166, 166, 166, 1) solid 1px;
  text-align: center;
  line-height: 0.36rem;
  color: #fff;
  font-weight: bold;
  background-color: rgba(108, 178, 255, 1);
}

.btns>.close {
  margin-right: 0.2rem;
  color: rgba(128, 128, 128, 1);
  background-color: #fff;
}

/*单选框*/
.el-icon-circle-check::before {
  font-size: 23px;
  color: #fff;
  background: var(--text-color);
  border-radius: 50%;
  margin-left: -1px;
  cursor: pointer;
}
</style>
<style>
.newAddDialog ::v-deep .el-dialog__body,
.el-dialog__header {
  padding: 0;
}

.tabsDatat .el-dialog__body {
  padding: 0 20px !important;
}

/* tabs选中的样式 */
.tag_set .is-active {
  color: var(--text-color) !important;
  background-color: var(--active-color) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-first {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-third {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
  /* display: none; */
  background-color: var(--text-color) !important;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
  color: var(--text-color) !important;
}
</style>
