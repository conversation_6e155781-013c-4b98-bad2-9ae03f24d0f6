:root {
    --background-color: #285f42;
    --text-color: #285f42;
    --check-color: #285f42;
    --base-color: #285f42;
    --active-color: #285f424d;
    --btn-background-color: #276042;
    --text-color-light: #24b17d;
    --dev-border: #24b17d;
    --screen-color: #24b17d;
    --btn-color: #fff;
    --del-color: #fe0000;
    --event-text-color: #276042;
    --btn-success-color:#27b17e;
    --table-header-bg-color:#24b17d;
    --text-gray:#606266;
    --gray:#dfdfdf;
    --light-gray:#f5f5f5;
    --radio-color:#52977e;

    /* ------------------------------------------------------------- */
    --btn-background-dark:#286042;
    --light-green:#26b17d;

    --btn-danger-color:#ed4d4d ;
    --btn-danger-light-color:#f78989;
}

.el-button--text {
    color: var(--event-text-color) !important;
}

.del_text{
    color: var(--del-color) !important;
}

.el-button--primary{
    background-color: var(--btn-background-dark) !important;
    border-color: var(--btn-background-dark) !important;
    
}
.el-button--primary:hover{
    background-color: var(--light-green) !important;
    border-color: var(--light-green) !important;
}

.el-button--danger{
    background-color: var(--btn-danger-color) !important;
    border-color: var(--btn-danger-color) !important;
    
}
.el-button--danger:hover{
    background-color: var(--btn-danger-light-color) !important;
    border-color: var(--btn-danger-light-color) !important;
}