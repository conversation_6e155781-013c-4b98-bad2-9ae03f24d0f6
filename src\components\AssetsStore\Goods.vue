<template>
    <div class="flex flex-wrap" style="align-content: flex-start;">
        <div v-for="item in list" class="list">
            <img :src="item" alt="" class="">
            <span>K记饭桶单人餐</span>
        </div>
    </div>
</template>
<script>
    export default {
        props: {
            list: {
                type: Array,
                default: [],
                required: true,
            }
        },
        data () {
            return {

            }
        }
    }
</script>
<style scoped>
    .list {
        width: 150px;
        height: 180px;
        /* border: 1px solid rgba(204, 204, 204, 1); */
        margin: 0 20px 0px 0;
    }

    .list span {
        font-size: 14px;
        font-weight: bold;
        color: rgba(128, 128, 128, 1);
    }

    .list img {
        width: 150px;
        height: 150px;
        border: 1px solid rgba(204, 204, 204, 1);
    }
</style>