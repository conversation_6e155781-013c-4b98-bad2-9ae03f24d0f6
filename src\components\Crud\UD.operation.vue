<template>
  <div>
    <el-button v-permission="permission.edit" :loading="crud.status.cu === 2" :disabled="disabledEdit" size="mini"
      type="primary" icon="el-icon-edit" @click="crud.toEdit(data)"
      v-if="checkPer(['perm.admin.user.edit']) || checkPer(['perm.admin.rose.edit'])" />
    <el-popover v-model="pop" v-permission="permission.del" placement="top" width="180" trigger="manual"
      @show="onPopoverShow" @hide="onPopoverHide">
      <p>{{ msg }}</p>
      <div style="text-align: right; margin: 0">
        <el-button size="mini" type="text" @click="doCancel">取消</el-button>
        <el-button :loading="crud.dataStatus[crud.getDataId(data)].delete === 2" type="primary" size="mini"
          @click="crud.doDelete(data)">确定</el-button>
      </div>
      <el-button slot="reference" :disabled="disabledDle" type="danger" icon="el-icon-delete" class="delete" size="mini"
        v-if="checkPer(['perm.admin.user.del']) || checkPer(['perm.admin.rose.del'])" @click="toDelete" />
    </el-popover>
  </div>
</template>
<script>
import CRUD, { crud } from "@crud/crud";
export default {
  mixins: [crud()],
  props: {
    data: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledDle: {
      type: Boolean,
      default: false
    },
    msg: {
      type: String,
      default: "确定删除本条数据吗？"
    }
  },
  data() {
    return {
      pop: false
    };
  },
  methods: {
    doCancel() {
      this.pop = false;
      this.crud.cancelDelete(this.data);
    },
    toDelete() {
      // this.pop = true
      this.$confirm("此操作将永久删除该用户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.crud.doDelete(this.data);
          // this.$message({
          //   type: 'success',
          //   message: '删除成功!'
          // });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    [CRUD.HOOK.afterDelete](crud, data) {
      if (data === this.data) {
        this.pop = false;
      }
    },
    onPopoverShow() {
      setTimeout(() => {
        document.addEventListener("click", this.handleDocumentClick);
      }, 0);
    },
    onPopoverHide() {
      document.removeEventListener("click", this.handleDocumentClick);
    },
    handleDocumentClick(event) {
      this.pop = false;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .delete {
  background-color: var(--btn-background-color) !important;
}
</style>