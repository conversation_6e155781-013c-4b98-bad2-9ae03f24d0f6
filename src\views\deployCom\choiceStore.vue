<template>
  <div class='choiceScreen'>
    <!--    左部-->
    <div class='pubLeft'>
      <p>组织机构筛选</p>
      <el-tree :data='leftdata' :props='defaultProps' @node-click='handleNodeClick'></el-tree>
    </div>
    <div class='pubRight'>
      <div class='topBtns'>
          <el-input
            style="width: 200px"
            placeholder="请输入内容"
            prefix-icon="el-icon-search"
            v-model="input2">
          </el-input>
        <div>
          <el-select v-model="choiceScreenValue1" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen1"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div>
          <el-select v-model="choiceScreenValue2" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen2"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div>
          <el-select v-model="choiceScreenValue3" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen3"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class='ScreensSearch' style="cursor: pointer">搜索</div>
      </div>
      <div class='bottomList'>
        <h4 style='font-size: 17px;font-weight: 800;color: rgba(83, 82, 82, 1)'>标签设置</h4>
        <ul class='contents'>
          <li v-for="(item,index) in 20" :key="index">
            是否小镇餐厅
            <p></p>
            <img src='../../assets/img/home_img/checked.svg'>
          </li>
        </ul>
        <div class='lists'>
          <div>已选择20个门店 <span style="cursor: pointer">重置</span></div>
          <ul class='storeData'>
            <li v-for='(item,index) in 8' :key='index'>
              <img src='../../assets/img/home_img/shop.png'>
              <p>新曹格餐厅</p>
              <span class='el-icon-close' style="cursor: pointer"></span>
            </li>
          </ul>
        </div>
      </div>
      <div class='screenBtn' @click.stop='pubShow'>投放</div>
    </div>
    <div class='alertSavePub' v-show='showPub'>
      <div class='duiLogo'>
        <img src='../../assets/img/home_img/dui.svg'>
      </div>
      <h3>投放已下发</h3>
      <p @click='toHist'>请前往历史投放中查看结果</p>
      <div class='text'>
        <img src='../../assets/img/home_img/blue_selectDui.svg'>
        保存该投放策略
      </div>
      <div class='blackClose' @click.stop='showPub=false'>×</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChoiceStore',
  data(){
    return{
      //  第三部分  左侧数控数据
      //左侧的数控结构
      leftdata: [{
        label: '星巴克总部',
        children: [
          {
            label: '上海市场',
            children: [
              {
                label: '标准门店'
              },
              {
                label: '特殊门店',
                children: [{
                  label: '虹桥机场店SKA273'
                }]
              }
            ]
          },
          {
            label: '浙江市场',
            children: [{
              label: '三级选项1',
              children: [
                {
                  label: '四级选项1'
                },
                {
                  label: '四级选项2'
                }
              ]
            }]
          },
          {
            label: '北京市场',
            children: [
              {
                label: '西北区'
              },
              {
                label: '华北区'
              },
              {
                label: '北京'
              },
              {
                label: '西安'
              },
              {
                label: '上海'
              }
            ]
          }
        ]
      }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      //  第三部分 的三个select选择器
      choiceScreen1: [{
        value: '选项1',
        label: '上海市场'
      }, {
        value: '选项2',
        label: '浙江市场'
      }, {
        value: '选项3',
        label: '北京市场'
      }, {
        value: '选项4',
        label: '深圳市场'
      }],
      choiceScreenValue1: '上海市场',
      //  第二个select
      choiceScreen2: [{
        value: '选项1',
        label: '机场店'
      }, {
        value: '选项2',
        label: '常规店'
      }, {
        value: '选项3',
        label: '高铁店'
      }, {
        value: '选项4',
        label: '特殊门店'
      }],
      choiceScreenValue2: '机场店',
      //  第三个select
      choiceScreen3: [{
        value: '选项1',
        label: '餐牌组'
      }, {
        value: '选项2',
        label: '大厅电视'
      }, {
        value: '选项3',
        label: '儿童屏'
      }, {
        value: '选项4',
        label: '联组屏'
      }],
      choiceScreenValue3: '餐牌组',
    //  点击投放
      showPub:false,
    }
  },
  methods:{
    //  投放按钮
    pubShow(){
      this.showPub = true;
    },
    //前往历史投放
    toHist(){
      this.$router.push({
        path:"/deploy/deployHist"
      })
    },
  }
}
</script>

<style scoped>
ul{
  list-style: none;
}
/*选择屏幕*/
.choiceScreen{
  background-color: #fff;
  height: 100%;
  display: flex;
}
.pubLeft {
  width: 277px;
  height: 100%;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 0.14rem;
  text-align: center;
  border-right: 0.02rem solid #f8f7f7;
}

.choiceScreen > .pubLeft > p {
  color: rgba(56, 56, 56, 1);
  font-size: 0.16rem;
  text-align: left;
  border-bottom: 0.01rem solid rgba(236, 235, 235, 1);
  padding: 0.26rem 0.84rem 0.08rem 0.2rem
}

::v-deep .el-tree {
  padding: 0.2rem 0.04rem 0 0.06rem;
}

.el-tree-node__content {
  background-color: #fff;
}

::v-deep .el-tree-node__label {
  color: rgba(79, 77, 77, 1);
}

/*符号*/
::v-deep .el-tree-node__expand-icon {
  color: #595959;
}
/*右边*/
.pubRight{
  height: 100%;
  width: calc(100% - 295px);
}
.pubRight>.topBtns{
  padding: 10px 10px;
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(236, 235, 235, 1);
}
.pubRight>.topBtns>div{
  margin: 0 10px;
}
.pubRight>.topBtns .el-select{
  width: 160px;
  height: 32px;
}
.pubRight>.topBtns>.ScreensSearch{
  width: 88px;
  height: 32px;
  line-height: 32px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}
.pubRight>.screenBtn{
  width: 181px;
  height: 48px;
  line-height: 48px;
  margin: auto;
  color: #fff;
  background-color: rgba(108, 178, 255, 1);
  text-align: center;
}
.pubRight>.bottomList{
  width: 100%;
  /*height: 100%;*/
  overflow: auto;
}
.pubRight>.bottomList>h4{
  padding: 14px 14px 0;
}
.pubRight>.bottomList>.contents{
  color: rgba(77, 147, 226, 1);
  font-size: 16px;
  display: flex;
  flex-wrap: wrap;
  padding: 0 14px;
}
.pubRight>.bottomList>.contents>li{
  width:80px;
  height: 40px;
  margin:10px 118px 0 0;
}
.pubRight>.bottomList>.contents>li>p{
  width: 17px;
  height: 17px;
  display: inline-block;
  color: rgba(80, 80, 80, 1);
  border-radius: 3px;
  font-size: 14px;
  line-height: 150%;
  border: rgba(128, 128, 128, 1) solid 1px;
  text-align: center;
  margin-left: 20px;
}
.pubRight>.bottomList>.contents>li>img{
  width: 17px;
  height: 17px;
}
.pubRight>.bottomList>.lists{
  margin-top: 5px;
  border-top: 1px solid #ebebeb;
  padding: 18px 21px;

}
.pubRight>.bottomList>.lists>div{
  color: rgba(166, 166, 166, 1);
  font-size: 14px;
  text-align: left;
}
.pubRight>.bottomList>.lists>div>span{
  width: 28px;
  height: 21px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: left;
}
.pubRight>.bottomList>.lists>ul{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.pubRight>.bottomList>.lists>.storeData>li{
  width: 150px;
  height: 47px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  margin: 18px 25px 0 0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
}
.pubRight>.bottomList>.lists>.storeData>li>img{
  width: 24px;
  height: 24px;
}
.pubRight>.bottomList>.lists>.storeData>li>.el-icon-close{
  font-size: 25px;
  color: rgba(108, 178, 255, 1);
}
.alertSavePub{
  width: 400px;
  height: 244px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 10px;
  font-size: 14px;
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 3;
  text-align: center;
  border: 1px solid #ddd;
}
.alertSavePub>.duiLogo{
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border:1px solid #afafaf;
  font-size: 28px;
  color: rgba(132, 132, 132, 1);
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 40px;
}
.alertSavePub>.duiLogo>img{
  width: 28px;
  height: 28px;
  font-size: 28px;
  color: rgba(132, 132, 132, 1);
  margin: 5px;
}
.alertSavePub>h3{
  margin-top: 90px;
}
.alertSavePub>p{
  color: rgba(166, 166, 166, 1);
  font-size: 14px;
  margin: 12px 0 30px 0;
}
.alertSavePub>.text{
  color: rgba(166, 166, 166, 1);
  font-size: 14px;
}
.alertSavePub>.text>img{
  width: 24px;
  height: 24px;
  font-size: 24px;
  color: rgba(108, 178, 255, 1);
  vertical-align: middle;
}
.blackClose{
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(17, 17, 17, 1);
  border-radius: 20px;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top:-10px;
  cursor: pointer;
  right: -10px;
}
/*保存按钮*/
.alertSave{
  width: 399px;
  height: 170px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 10px;
  font-size: 14px;
  text-align: center;
  position: absolute;
  margin: auto;
  left: 100px;
  right: 0;
  top: -200px;
  bottom: 0;
  z-index: 3;
  border: 1px solid rgba(221, 221, 221, 0.96);
}
.alertSave>img{
  width: 43px;
  height: 43px;
  margin-top: 43px;
}
.alertSave>h3{
  color: rgba(80, 80, 80, 1);
  font-size: 16px;
  text-align: left;
  font-weight: bold;
  text-align: center;
}
.alertSave>.blackClose{
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(17, 17, 17, 1);
  border-radius: 20px;
  font-size: 22px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top:-10px;
  right: -10px;
}
</style>
