// 门店管理-门店列表
import { get, post, uploadFile } from '@/utils/request'

// 获取升级区域、升级设备、应用类型
export function datas_filter_cond(data) {
    return post('/dmb/api/json', data, 'datas_filter_cond')
}

// 拉取设备的升级包信息
export function pull_pkg_info_by_model(data) {
    return post('/dsadm/api/json', data, 'pull_pkg_info_by_model')
}

// 添加升级包(新添加)
export function add_new_dsapk(data) {
    return post('/dsadm/api/json', data, 'add_new_dsapk')
}

// 升级清单
export function up_plan_list_v2(data) {
    return post('/dsadm/api/json', data, 'up_plan_list_v2')
}

//批量升级
export function batch_upgrade(data) {
    return post('/dsadm/api/json', data, 'batch_upgrade')
}

// 创建升级计划
export function create_up_plan(data) {
    return post('/dsadm/api/json', data, 'create_up_plan')
}

// 设备升级
export function screen_batch_processing(data) {
    return post('/geo/api_ds/json', data, 'screen_batch_processing')
}

