<template>
    <div class="Parameters">
        <el-dialog title="参数设置" :visible.sync="parameterShow" width="40%" custom-class="parameters_dialog"
            :close-on-click-modal='false' :before-close="handleClose">
            <div class="dialog_content">
                <div class="timer">
                    <!-- arrow-control -->
                    时间机制：
                    <el-time-picker v-model="timing[0]" format='HH:mm' value-format="HH:mm" placeholder="请选择开机时间">
                    </el-time-picker>
                    <el-time-picker style="margin-left:10px" v-model="timing[1]" value-format="HH:mm" format='HH:mm'
                        placeholder="请选择关机时间">
                    </el-time-picker>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="determine">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    components: {

    },
    props: {
        parameterShow() {
            type: Boolean
        },
    },
    data() {
        return {
            isAllDay: false,
            dateList: '',
            timing: ['','']
        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {
        // 确定
        determine() {
            this.timing.forEach((item, index) => {
                if (item == null) {
                    this.timing[index] = ''
                }
            })
            console.log(this.timing);
             this.$emit('confirmEditing', this.timing)
        },
        // 取消
        handleClose() {
            this.$emit('cancelEditing', false)
        }

    },
};
</script>

<style scoped lang="scss">
.dialog_content {
    height: 170px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid rgba(229, 229, 229, 1);
    border-bottom: 1px solid rgba(229, 229, 229, 1);

    .is_all_day {
        margin-left: 15px;
    }
}

::v-deep .parameters_dialog {
    // margin-top: 0px !important;
    border-radius: 16px !important;
}

::v-deep .parameters_dialog .el-dialog__body {
    // padding-top: 0 !important;
    // padding-bottom: 0 !important;
    padding: 0;
}

::v-deep .parameters_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
}

::v-deep .parameters_dialog .el-button--primary {
    background: rgba(108, 178, 255, 1);
}
</style>
