<template>
  <div class="wd-100 pd-x-20">
    <div
      class="flexBoxTwoTitle flex justify-between"
      style="border-bottom: 1px solid #ededed"
    >
      <div class="flex">
        <div class="borderBottomCss">组织架构</div>
        <!-- <div
          class="mg-left-10 cursor-pointer"
          style="font-size: 12px; color: #326ffa"
        >
          使用教程
        </div> -->
      </div>
    </div>
    <!-- start -->
    <div
      class="wd-100 boxScroll overflow-y-scroll"
      :style="{ height: autoHeight.height }"
    >
      <div class="position-relative hg-100">
        <Tree
          @shopDetailBolFun="shopDetailBolFun"
          @notSetAllProvince="notSetAllProvince"
          @setAllProvince="setAllProvince"
          @brandDetailBolFun="brandDetailBolFun"
          @addShop="addShop"
          @creatRegion="creatRegion"
          @clickZongBu="clickZongBu"
          @addBrandOne="addBrandOne"
          @addBrandTwo="addBrandTwo"
          @addBrandThree="addBrandThree"
          @showDrawer="showDrawer"
          @currentClickValue="currentClickValue"
          @getTreeData="getTreeData"
          ref="tree"
          style="transform: scale(0.7); transform-origin: top left"
        />
        <!-- mask box -->
        <div
          class="maskBoxParent wd-100 hg-100 position-absolute"
          v-show="showMaskParent"
        >
          <div class="maskBox position-absolute">
            <div
              class="flexBoxTwoTitle flex justify-between mg-bottom-10"
              style="border-bottom: 1px solid #ededed"
            >
              <div class="pd-x-20">添加品牌</div>
              <div class="pd-x-20">
                <img
                  src="../../assets/img/closeIcon.png"
                  class="closeCss cursor-pointer"
                  @click="showMaskParent = false"
                />
              </div>
            </div>
            <div class="pd-x-20">
              <div style="height: 180px">
                <div class="flex align-items-center mg-top-20">
                  <div class="font-13 color-block" style="width: 80px">
                    品牌名称:
                  </div>
                  <el-input
                    class="mg-right-10"
                    size="small"
                    v-model="brandName"
                    style="width: 200px"
                    placeholder="请输入品牌名称"
                  ></el-input>
                </div>
              </div>

              <div class="lineCss"></div>
              <div
                class="flex flexParent align-items-center"
                style="margin-top: 15px"
              >
                <div class="flexLeft text-right"></div>
                <div class="flexRight flex-1 text-right" style="">
                  <el-button
                    type="default"
                    size="small"
                    @click="showMaskParent = false"
                    >取消</el-button
                  >
                  <el-button
                    type="primary"
                    size="small"
                    @click="addBrandParentFun"
                    >确认</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- mask box -->
      </div>
    </div>
    <!-- end -->
    <!-- 品牌详情 start -->
    <el-drawer
      title="我是标题"
      :visible.sync="brandDetailBol"
      :with-header="false"
      :wrapperClosable="false"
    >
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div
          class="flexBoxTwoTitle flex justify-between"
          style="align-items: center; border-bottom: 1px solid #ededed"
        >
          品牌详情
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="brandDetailBol = false"
            alt=""
          />
        </div>
        <div class="flex-1 overflowBoxCss" style="">
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            品牌LOGO:
          </div>
          <div class="autoImgCssBox position-relative flex-center">
            <img :src="partObj.logo" class="imgAutoCss" style="width: 100%;height: 100%;" alt="" />
            <input
              type="file"
              id="file"
              @change="uploadImgFun"
              class="inputCssFour wd-100 hg-100"
            />
          </div>
          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            品牌名称:
          </div>
          <el-input
            class=""
            size="small"
            v-model="partObj.name"
            style="width: 80%"
            placeholder="品牌名称"
          ></el-input>

          <!-- end -->
          <!-- start -->
          <!-- <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select
            v-model="createRegion.treeOneValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeOne"
            ref="selectChOne"
          >
            <el-option
              v-for="item in treeObj.treeOne"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="createRegion.treeTwoValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeTwo"
            ref="selectChTwo"
          >
            <el-option
              v-for="item in treeObj.treeTwo"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="createRegion.treeThreeValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            ref="selectChThree"
            @change="changeTreeThree"
          >
            <el-option
              v-for="item in treeObj.treeThree"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select> -->
          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            品牌地址详情:
          </div>
          <el-input
            class=""
            size="small"
            v-model="partObj.addr"
            style="width: 80%"
            placeholder="请输入品牌地址"
          ></el-input>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            品牌描述详情:
          </div>
          <el-input
            class=""
            size="small"
            type="textarea"
            v-model="partObj.description"
            style="width: 80%"
            placeholder="请输入品牌描述"
          ></el-input>

          <div class="mg-top-20 text-right" style="width: 80%">
            <el-button
              v-show="group_type == 1"
              type="danger"
              size="small"
              :disabled="delBlandBol"
              @click="deleteBland"
            >
              {{ delBlandBol ? '移除中' : '移除品牌' }}</el-button
            >
          </div>
          <!-- end -->
        </div>
        <!-- <div> <el-button type="danger" size="small" style="margin-top: 10px"
            >移除品牌</el-button> </div> -->

        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto">
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="brandDetailBol = false"
              >取消</el-button
            >

            <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="editPart"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 品牌详情 end -->
    <!-- 门店详情 start -->
    <el-drawer
      title="我是标题"
      :visible.sync="shopDetailBol"
      :with-header="false"
      :wrapperClosable="false"
    >
      <div
        class="addRoleContent flex flex-direction-column pd-x-20 hg-100"
        v-loading="loadingShopDetail"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
      >
        <div
          class="flexBoxTwoTitle flex justify-between"
          style="align-items: center; border-bottom: 1px solid #ededed"
        >
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="shopDetailBol = false"
            alt=""
          />
        </div>
        <div class="flex-1 overflowBoxCss" style="">
          <!-- start -->
          <div
            class="flex justify-between"
            style="height: 40px; align-items: center"
          >
            <div class="font-small">{{ shopDetailObj.shop_name }}</div>
            <!-- <div class="font-smaller" style="color: #326ffa">查看</div> -->
          </div>
          <!-- end -->
          <!-- start -->
          <div class="flex" style="height: 30px; align-items: center">
            <div class="font-smaller">
              屏幕数：{{ shopDetailObj.all_count }}
            </div>
            <div class="font-smaller"></div>
          </div>
          <!-- end -->
          <!-- start -->
          <div class="flex" style="height: 30px; align-items: center">
            <div class="font-smaller">
              分 区：{{ shopDetailObj.parent_label }}
            </div>
            <div class="font-smaller"></div>
          </div>
          <!-- end -->
          <div class="lineCss mg-top-20"></div>
          <!-- start -->
          <div class="mg-top-20" style="">
            <div class="font-small">标 签：</div>
            <div class="font-smaller flex">
              <div
                class="labelBox mg-top-10 mg-right-10"
                v-for="item in shopDetailObj.shop_tags"
              :key="item">
                {{ item }}
              </div>
            </div>
          </div>
          <!-- end -->
          <div class="lineCss mg-top-20"></div>
          <!-- start -->
          <div class="flex" style="height: 40px; align-items: center">
            <div class="font-smaller">
              门店账号： {{ shopDetailObj._contact }}
            </div>
            <div class="font-smaller"></div>
          </div>
          <!-- end -->
          <!-- start -->
          <!-- <div class="mg-top-10 flex" style="align-items: center">
            <div class="font-small" style="width: 90px">店铺状态：</div>
            <div class="">
              <el-select
                v-model="addShopObj.shop_status"
                size="small"
                style="160"
                placeholder="门店状态"
              >
                <el-option
                  v-for="item in shopStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
          </div> -->
          <!-- end -->
          <div style="width: 80%" class="text-right mg-top-30">
            <el-button
              v-show="group_type == 1"
              type="danger"
              size="small"
              :disabled="delShopBol"
              style=""
              @click="del_admin_group_funOne"
              >{{ delShopBol ? '移除中' : '移除店铺' }}</el-button
            >
          </div>
        </div>

        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto">
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="shopDetailBol = false"
              >取消</el-button
            >

            <!-- <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="simple_group_part_create_fun"
              >保存</el-button
            > -->
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 门店详情 end -->
    <!-- 创建品牌 start -->
    <el-drawer
      title="我是标题"
      :visible.sync="creatRegionDrawer"
      :with-header="false"
      :wrapperClosable="false"
    >
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div
          class="flexBoxTwoTitle flex justify-between"
          style="align-items: center; border-bottom: 1px solid #ededed"
        >
          创建品牌
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="creatRegionDrawer = false"
            alt=""
          />
        </div>
        <div class="flex-1 overflowBoxCss">
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <!-- <span class="spanMust">*</span> -->
            上传LOGO:
          </div>
          <div class="autoImgCssBox position-relative flex-center">
            <img :src="createRegion.logo" class="imgAutoCss" alt="" />
            <input
              type="file"
              id="file"
              @change="uploadImgFun"
              class="inputCssFour wd-100 hg-100"
            />
          </div>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            品牌名称:
          </div>
          <el-input
            class=""
            size="small"
            v-model="createRegion.region_name"
            style="width: 80%"
            placeholder="请输入品牌名称"
          >
          </el-input>

          <!-- end -->
          <!-- start -->
          <!-- <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select
            v-model="createRegion.treeOneValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeOne"
            ref="selectChOne"
          >
            <el-option
              v-for="item in treeObj.treeOne"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="createRegion.treeTwoValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeTwo"
            ref="selectChTwo"
          >
            <el-option
              v-for="item in treeObj.treeTwo"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="createRegion.treeThreeValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            ref="selectChThree"
            @change="changeTreeThree"
          >
            <el-option
              v-for="item in treeObj.treeThree"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select> -->
          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10 pd-left-5">
            <!-- <span class="spanMust">*</span> -->
            品牌地址:
          </div>
          <el-input
            class=""
            size="small"
            v-model="createRegion.address"
            style="width: 80%"
            placeholder="请输入品牌地址"
          >
          </el-input>

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10 pd-left-5">
            <!-- <span class="spanMust">*</span> -->
            品牌描述:
          </div>
          <el-input
            class=""
            size="small"
            type="textarea"
            v-model="createRegion.desc"
            style="width: 80%"
            placeholder="请输入品牌描述"
          ></el-input>

          <!-- end -->
        </div>
        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto; margin-right: 10px">
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="creatRegionDrawer = false"
              >取消</el-button
            >
            <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="simple_group_part_create_fun"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 创建品牌 end -->
    <!-- 机构详情 start -->
    <el-drawer
      title="我是标题"
      :visible.sync="drawerAddRole"
      :with-header="false"
      :wrapperClosable="false"
    >
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div
          class="flexBoxTwoTitle flex justify-between"
          style="align-items: center; border-bottom: 1px solid #ededed"
        >
          机构详情
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="drawerAddRole = false"
            alt=""
          />
        </div>
        <div class="flex-1 overflowBoxCss">
          <!-- start -->
          <div class="color-666 font-small mg-top-10 font-bold">
            西安可立可立公司
          </div>
          <div class="color-666 font-smaller mg-y-10">门店数：10</div>
          <div class="color-666 font-smaller mg-y-10">设备数：100</div>
          <div
            class="editBtn flex-center cursor-pointer"
            @click="addShopDrawer = true"
            style="width: 85px"
            v-show="clickStr == ''"
          >
            添加门店
          </div>
          <div
            class="editBtn flex-center cursor-pointer"
            @click="creatRegionDrawer = true"
            style="width: 85px"
            v-show="clickStr == '点击总部'"
          >
            新增分区
          </div>
          <div class="lineCss mg-y-10"></div>
          <div class="flex justify-between mg-bottom-10 align-items-center">
            <div class="font-small color-block">关联账号：</div>
            <div>
              <div
                class="editBtn flex-center cursor-pointer"
                style="width: 85px; border: none"
              >
                添加账号
              </div>
            </div>
          </div>
          <el-table
            :header-cell-style="{
              background: '#EAF4FF',
              color: '#666',
              'font-size': '13px',
            }"
            :data="tableData"
            height="500px"
            style="width: 100%"
          >
            <el-table-column prop="date" align="center" label="账号">
            </el-table-column>
            <el-table-column prop="name" align="center" label="用户名">
            </el-table-column>
            <el-table-column prop="address" align="center" label="角色">
            </el-table-column>

            <el-table-column prop="name" align="center" label="操作">
              <template>
                <div scope="scope" class="flex-center">
                  <div class="opsBtn">查看</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- end -->
        </div>
        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto; margin-right: 10px">
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="drawerAddRole = false"
              >取消</el-button
            >
            <el-button
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="drawerAddRole = true"
              >保存
            </el-button>
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 机构详情 end -->
    <!-- 添加门店 start -->
    <el-drawer
      title="我是标题"
      :visible.sync="addShopDrawer"
      :wrapperClosable="false"
      :with-header="false"
    >
      <div class="addRoleContent flex flex-direction-column pd-x-20 hg-100">
        <div
          class="flexBoxTwoTitle flex justify-between"
          style="align-items: center; border-bottom: 1px solid #ededed"
        >
          添加门店
          <img
            src="../../assets/img/closeIcon.png"
            class="closeCss cursor-pointer"
            @click="addShopDrawer = false"
            alt=""
          />
        </div>
        <div class="flex-1 overflowBoxCss">
          <!-- start -->
          <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select
            :disabled="disabledBol"
            v-model="treeValue.treeOneValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeOne"
            ref="selectChOne"
          >
            <el-option
              v-for="item in treeObj.treeOne"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="treeValue.treeTwoValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="changeTreeTwo"
            ref="selectChTwo"
          >
            <el-option
              v-for="item in treeObj.treeTwo"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="treeValue.treeThreeValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            ref="selectChThree"
            @change="changeTreeThree"
          >
            <el-option
              v-for="item in treeObj.treeThree"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <!-- end -->
          <!-- start -->
          <!-- <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            地区:
          </div>
          <el-select
            v-model="regionOneValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="change_regionOne"
          >
            <el-option
              v-for="item in regionOne"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="regionTwoValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="change_regionTwo"
          >
            <el-option
              v-for="item in regionTwo"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="regionThreeValue"
            size="small"
            style="width: 100px"
            placeholder="请选择"
            @change="change_regionThree"
          >
            <el-option
              v-for="item in regionThree"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select> -->
          <!-- end -->
          <!-- start -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            店铺名称:
          </div>
          <el-input
            class=""
            size="small"
            v-model="addShopObj.shop_name"
            style="width: 80%"
            placeholder="请输入店铺名称"
          >
          </el-input>

          <!-- end -->
          <!-- <div class="drawerTitle mg-y-10">
            <span class="spanMust">*</span>
            门店状态:
          </div>
          <el-select
            disabled
            v-model="addShopObj.shop_status || '已开店'"
            size="small"
            style="width: 80%"
            placeholder="门店状态"
          >
            <el-option
              v-for="item in shopStatus"
              :key="item.value"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select> -->

          <!-- end -->

          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            门店角色权限:
          </div>
          <div class="pd-left-10">
            <div class="font-smaller color-666">自定义：</div>
            <div>
              <el-checkbox-group
                v-model="addShopObj.roleList_diy_check"
              >
                <el-checkbox
                  v-for="item in roleList_diy"
                  :label="item.id"
                  :key="item.id + '1'"
                  >{{ item.text }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="font-smaller color-666 mg-top-5">系统默认：</div>
            <div>
              <el-checkbox-group v-model="addShopObj.roleList_default_check">
                <el-checkbox
                  disabled
                  v-for="item in roleList_default"
                  :label="item.id"
                  :key="item.id + '2'"
                  >{{ item.text }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <!-- end -->
          <!-- start -->
          <!-- <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            登录账号:
          </div>
          <div class="">
            <el-input
              class=""
              size="small"
              v-model="addShopObj.contact_phone_number"
              style="width: 150px"
              placeholder="请输入手机号"
            ></el-input>
            <el-input
              class=""
              size="small"
              v-model="addShopObj.contact_email"
              style="width: 150px"
              placeholder="请输入邮箱"
            ></el-input>
          </div>
          <div class="mg-top-5 font-smaller color-999 pd-left-5">
            手机号、邮箱至少填写一个
          </div> -->
          <!-- end -->

          <!-- start -->
          <!-- <div class="drawerTitle mg-top-10">
            <span class="spanMust">*</span>
            确认密码:
          </div>
          <el-input
            class=""
            size="small"
            v-model="addShopObj.signin_passwd_sure"
            style="width: 80%"
            placeholder="请输入密码"
          ></el-input> -->

          <!-- end -->
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <!-- <span class="spanMust">*</span> -->
            登录密码:
          </div>
          <el-input
           @change="checkPassword2"
            class=""
            size="small"
            type="password"
            v-model="addShopObj.signin_passwd"
            style="width: 80%"
            placeholder="请输入密码"
          ></el-input>
 <div class="flex">
            <!-- <div class="font-smaller" style="width: 60px; line-height: 32px"></div> -->
            <div class="spanMust font-smaller">{{ checkPassWordMsg }}</div>
          </div>
          <!-- end -->
          <div class="lineCss mg-y-10"></div>
          <!-- start -->
          <div class="drawerTitle mg-top-10">
            <!-- <span class="spanMust">*</span> -->
            登录账号:
          </div>
          <el-input
            class=""
            size="small"
            v-model="addShopObj.contact_phone_number"
            style="width: 150px"
            placeholder="请输入电话号码"
          ></el-input>
          <el-input
            class=""
            size="small"
            v-model="addShopObj.contact_email"
            style="width: 150px"
            placeholder="请输入邮箱"
          >
          </el-input>
          <div class="font-smaller color-999">手机号、邮箱至少填写一个</div>
          <!-- end -->
          <div class="lineCss mg-y-10"></div>
          <!-- start -->
          <div class="drawerTitle mg-top-10">联系地址:</div>
          <el-input
            class=""
            size="small"
            v-model="addShopObj.address"
            style="width: 80%"
            placeholder="请输入"
          ></el-input>

          <!-- end -->
        </div>
        <div class="saveBtnParent wd-100 flex" style="align-items: center">
          <div style="margin-left: auto; margin-right: 10px">
            <el-button
              type="default"
              size="small"
              style="width: 80px"
              @click="addShopDrawer = false"
              >取消</el-button
            >
            <el-button
              :disabled="!isCanClickBol"
              type="primary"
              size="small"
              style="width: 80px; height: 32px"
              @click="addShopSave"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </el-drawer>
    <!-- 添加门店 end -->
  </div>
</template>
<script>
import {simple_get_shop_struct,del_admin_group,shop_user_list,edit_group_part,simple_get_tpl_address,simple_group_part_create,
get_g_role_user_list,get_g_role_list,simple_create_or_edit_shop,create_shop} from '@/api/systemCenter/structure'
import Tree from '@/components/Tree.vue'
import axios from 'axios';
export default {
  components: {
    Tree,
  },
  data() {
    return {
      isCanClickBol: true,
      delBlandBol: false,
      loadingShopDetail: false,
      delShopBol: false,
      partObj: {
        logo: '',
        description: '',
        addr: '',
        name: '',
      },
      shopDetailObj: {},
      shopDetailBol: false,
      brandDetailBol: false,
      disabledBol: true,
      searchRoleName: '',
      tempFile: '',
      createRegion: {
        region_name: '',
        treeOneValue: '',
        treeTwoValue: '',
        treeThreeValue: '',
        treeOneText: '',
        treeTwoText: '',
        treeThreeText: '',
        region_detail_address: '',
        logo: '',
        desc: '',
        address: '',
      },
      creatRegionDrawer: false,
      roleList_diy: [],
      roleList_default: [],
      shopStatus: [
        {
          label: '营业中',
          id: 9,
        },
        {
          label: '待开业',
          id: 7,
        },
        {
          label: '已闭店',
          id: 4,
        },
      ],
      regionOne: [],
      regionTwo: [],
      regionThree: [],
      regionOneValue: '',
      regionTwoValue: '',
      regionThreeValue: '',
      addShopObj: {
        shop_status: '', //店铺状态,
        shop_name: '',
        contact_email: '',
        contact_phone_number: '',
        signin_passwd: '',
        category: '',
        roleValue: [],
        roleList_diy_check: [],
        roleList_default_check: [],
      },
      clickStr: '',

      treeValue: {
        treeOneValue: '',
        treeOneText: '',
        treeTwoValue: '',
        treeTwoText: '',
        treeThreeValue: '',
        treeThreeText: '',
      },
      treeObj: {
        treeOne: [],
        treeTwo: [],
        treeThree: [],
      },
      brandName: '',
      showMaskParent: false,
      addShopDrawer: false,
      tableData: [
        {
          date: '2016-05-02',
          name: '王小虎',
          address: '金沙江',
        },
      ],
      drawerAddRole: false,
      autoHeight: {
        height: '',
      },
      authInfo: {},
      regionObjAll: {
        province: [],
        requsetByAllProvince: false,
      },
      click_g_id: '',
      group_type: '',
      checkPassWordMsg: '',
    }
  },
  mounted() {
    this.authInfo = JSON.parse(localStorage.getItem('device_info'))
    // this.simple_get_shop_struct_fun()

    this.simple_get_tpl_address_fun('province')
    this.group_type = parseInt(localStorage.getItem('group_type'))
    let group_type = localStorage.getItem('group_type')
    if (group_type == '2') {
      this.get_g_role_user_list_fun()
    } else {
      this.get_g_role_list_fun()
    }
  },
  methods: {
    checkPassword2() {
      let reg = /^(?=.*[0-9])(?=.*[a-zA-Z0-9])(.{8,30})$/
      if (this.addShopObj.signin_passwd.length > 0) {
        if (!reg.test(this.addShopObj.signin_passwd)) {
          this.checkPassWordMsg = '至少为8位由字母和数字组成的字符'
        } else {
          let resg2 = new RegExp(
            "[`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？ ]"
          )
          if (resg2.test(this.addShopObj.signin_passwd)) {
            this.checkPassWordMsg = '密码不能包含特殊字符'
          } else {
            this.checkPassWordMsg = ''
          }
        }
      } else {
        this.checkPassWordMsg = ''
      }
    },
    deleteBland() {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.del_admin_group_funTwo()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    del_admin_group_funTwo() {
      let params = {
        d_g: this.click_g_id,
      }
      this.delBlandBol = true
      del_admin_group(params).then((res) => {
          console.log('del_admin_group',red);
        this.delBlandBol = false
        if (res.rst == 'ok') {
          this.$message.success('移除成功')
          this.brandDetailBol = false
          this.$refs.tree.simple_get_shop_struct_fun()
        } else {
          if ((res.error_code = 7008)) {
            this.$message.error('该品牌包含子门店，移除失败')
          }
          setTimeout(() => {
            this.$message.error('移除失败')
          }, 1000)
        }
      })
    },
    del_admin_group_funOne() {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.del_admin_group_fun()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    getNumGroundId(val) {
      let temp_ground_id = val
      let last_ground_id = ''
      if (temp_ground_id.charAt(0) == 'g') {
        last_ground_id = parseInt(temp_ground_id.split('g')[1])
      }
      return last_ground_id
    },
   
   del_admin_group_fun() {
      // console.log('移除店铺123')
      let params = {
        d_g: this.getNumGroundId(this.shopDetailObj.shop_id),
        actl: 99,
      }
      // console.log('移除店铺456')
      this.delShopBol = true
      del_admin_group(params).then((res) => {
        // console.log('移除店铺789')
        this.delShopBol = false
        if (res.rst == 'ok') {
          this.$message.success('移除成功')
          this.shopDetailBol = false
          this.$refs.tree.simple_get_shop_struct_fun()
        }else if(res.rst=='error'){
          this.$message.error('账号下有设备,不能移除')
        }
         else {
          this.$message.error(res.error_msg)
        }
      })
    },
    getShopDetail() {
      let params = {
        pgid: this.getNumGroundId(this.shopDetailObj.shop_id),
        rt_screen_count: 1,
        gt: 3,
      }
      this.loadingShopDetail = true
      shop_user_list(params).then((res) => {
          console.log('shop_user_list',res);
        this.loadingShopDetail = false
        if (res.rst == 'ok') {
          res.data[0].user_list[0].shop_id = this.shopDetailObj.shop_id
          this.shopDetailObj = res.data[0].user_list[0]
        }
      })
    },
    editPart() {
      let params = {
        type: 'edit',
        g_id: this.click_g_id,
        p_group_logo: this.partObj.logo,
        p_group_desc: this.partObj.description,
        p_group_addr: this.partObj.addr,
        p_group_name: this.partObj.name,
      }
      edit_group_part(params).then((res) => {
        if (res.rst == 'ok') {
          this.$message.success('保存成功')
          this.brandDetailBol = false
        }
      })
    },
    edit_group_part_fun() {
      let params = {
        type: 'get',
        g_id: this.click_g_id,
      }
      edit_group_part(params).then((res) => {
        if (res.rst == 'ok') {
          let return_data = res.data[0].return_data
          this.partObj = {
            logo: return_data.logo,
            description: return_data.description,
            addr: return_data.addr,
            name: return_data.name,
          }
        }
      })
    },
    shopDetailBolFun(val) {
      this.shopDetailBol = true
      console.log('点击店铺', val)
      this.shopDetailObj.shop_name = val.text
      this.shopDetailObj.shop_id = val.id
      this.getShopDetail()
    },
    notSetAllProvince() {
      this.regionObjAll.requsetByAllProvince = false
    },
    setAllProvince() {
      this.treeObj.treeTwo = []
      this.treeObj.treeTwo = this.regionObjAll.province
      this.regionObjAll.requsetByAllProvince = true
    },
    simple_get_tpl_address_fun(typeStr, id) {
      let params = {
        Type: typeStr,
        id: id ? id : 0,
        real: 1,
      }
      simple_get_tpl_address(params).then((res) => {
        if (res.rst == 'ok') {
          let group_type = localStorage.getItem('group_type')

          let data = res.data[0].data
          let dataObj = []
          data.forEach((item) => {
            let tempObj = {
              type: 'province',
              text: item[0],
              id: item[1],
            }
            dataObj.push(tempObj)
          })
          if (typeStr == 'province') {
            this.regionObjAll.province = dataObj
            console.log('province', this.treeObj.treeTwo)
          } else {
            this.treeObj.treeThree = dataObj
            console.log('city', res.data)
          }
        }
      })
    },
    brandDetailBolFun(valStr, val) {
      // console.log(111222, valStr, val.id)
      this.click_g_id = this.getNumGroundId(val.id)
      this.brandDetailBol = true
      this.edit_group_part_fun()
    },
    addShop(val) {
      // console.log('val', val)
      this.addShopDrawer = true
      this.treeValue = {
        treeOneValue: '',
        treeOneText: '',
        treeTwoValue: '',
        treeTwoText: '',
        treeThreeValue: '',
        treeThreeText: '',
      }
      // this.treeObj = {
      //   treeOne: [],
      //   treeTwo: [],
      //   treeThree: [],
      // }
      // this.addShopObj = {
      //   shop_status: '', //店铺状态,
      //   shop_name: '',
      //   contact_email: '',
      //   contact_phone_number: '',
      //   signin_passwd: '',
      //   category: '',
      //   roleValue: [],
      //   roleList_diy_check: [],
      //   roleList_default_check: [],
      // }

      this.addShopObj.shop_status = ''
      this.addShopObj.shop_name = ''
      this.addShopObj.contact_email = ''
      this.addShopObj.contact_phone_number = ''
      this.addShopObj.signin_passwd = ''
      this.addShopObj.category = ''
      this.addShopObj.roleValue = []
      this.addShopObj.roleList_diy_check = []

      if (val == '点击总部') {
        this.disabledBol = false
        this.treeValue.treeOneValue = ''
        this.treeValue.treeTwoValue = ''
        this.treeValue.treeThreeValue = ''
        this.setAllProvince()
      } else if (val == '点击品牌') {
        this.disabledBol = true
      }
    },
    creatRegion() {
      this.creatRegionDrawer = true
    },
    uploadImg(val) {
      var _this = this
      let time = new Date()
      let id = time.getTime()
      let formdata = new FormData()
      let params = {
        userfile: val,
        auth_token:
        _this.authInfo['auth_token'] || _this.authInfo['device_token'],
        device_type: _this.authInfo['device_type'],
        auth_id: _this.authInfo['auth_id'] || _this.authInfo['device_id'],
        source_type: 2,
        identify_str: 'ds-admin-edit' + id,
        caption: 'ds-admin-edit-content',
        tag_list: JSON.stringify([
          this.setLabelValue ? this.setLabelValue : '内容编辑',
        ]),

        client_info: JSON.stringify({
          language: 'zh-CN',
          useragent:
            'Mozilla/5.0(Windows NT 10.0; Win64; x64)AppleWebKit/537.36(KHTML,like Gecko)Chrome/77.0.3865.120 Safari/537.36',
          boundleid: 'com.liankexinxi.PicBox',
        }),
      }
      for (let i in params) {
        formdata.append(i, params[i])
      }
      // console.log('formdata22', formdata)
      // let urlTemp = window.location.origin + "/file/upload_photo"
      let urlStr =
        window.location.origin.indexOf('localhost') == -1
          ? window.location.origin
          : 'http://smp.instwall.com'
      let urlTemp = window.location.origin + '/file/upload_photo'
      _this.loadingBol = true
      axios({
          method: 'post',
          url: urlTemp,
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          data: formdata,
        })
        .then((res) => {
          _this.loadingBol = false
          _this.showMaskParent = false
          if (res.data.rst == 'ok') {
            _this.$message.success('上传成功')

            _this.createRegion.logo = res.data.data[0].photo_url
            _this.partObj.logo = res.data.data[0].photo_url
          } else if (res.data.rst == 'error') {
            if (res.data.error_code == 460) {
              _this.$message.error('图片宽高必须大于300*300')
            } else if (res.data.error_code == 461) {
              _this.$message.error('图片宽高必须小于12288*12288')
            } else if (res.data.error_code == 400) {
              if (
                res.data.error_msg.indexOf('File size is not allowed') !== -1
              ) {
                _this.$message.error('图片大小必须小于30M')
              }
            } else if (res.data.error_code == 415) {
              // 登出
              _this.$message.error({
                message: '请重新登录',
                center: true,
                duration: 5000,
              })
              setTimeout(() => {
                _this.$router.push('/')
              }, 5000)
            } else {
              _this.$message.error('上传出错')
            }
          } else {
            _this.$message.error('网络错误,请稍后重试!')
          }
        })
    },
    uploadImgFun(e) {
      console.log('77777777777777', e.target.files[0])
      var tempFile = e.target.files[0]
      this.tempFile = tempFile
      this.uploadImg(this.tempFile)
    },
    getRegionText() {
      this.treeObj.treeOne.forEach((item) => {
        if (item.id == this.createRegion.treeOneValue) {
          this.createRegion.treeOneText = item.text
        }
      })
      this.treeObj.treeTwo.forEach((item) => {
        if (item.id == this.createRegion.treeTwoValue) {
          this.createRegion.treeTwoText = item.text
        }
      })
      this.treeObj.treeThree.forEach((item) => {
        if (item.id == this.createRegion.treeThreeValue) {
          this.createRegion.treeThreeText = item.text
        }
      })
      // console.log(this.createRegion, 'createRegion')
    },

    simple_group_part_create_fun() {
      if (!this.createRegion.region_name) {
        this.$message.error('请输入品牌名称')
        return
      }
      this.getRegionText()
      let params = {
        parent_group_id:
          this.$store.state.group_id || localStorage.getItem('group_id'), //省的id 父级节点id(eg:若创建市级分区,则传他上一级的省级节点id)
        action: 'register', //创建分区
        req_g_type: 2,
        GroupPLogo: this.createRegion.logo, //图片url
        p4_region: '1', //----->固定传1
        GroupHeadShopName: '', //分区名称
        p4_province: this.createRegion.treeTwoText, //-->省级名称
        region_str: this.createRegion.region_name, //>第二层级名称
        area_id: '', // ?
        GroupV: '2', //--->固定传2
        signinPhone: '',
        city: '', //---->市级名称
        area: '',
        adminEmail: '',
        province: this.createRegion.treeTwoText, //--->省级名称
        province_id: '8', //--->省id
        confirmPass: '',

        p4_city: this.createRegion.treeThreeText, //---->市级名称
        city_id: '', // --->城市id

        GroupPLevel: 'pl_1', //----> 默认选到市 分区级别,第二级'pl_1',第三级'pl_2',第三级'pl_4',
        GroupBrandName: '',
        GroupPDesc: this.createRegion.desc, //---->描述信息
        region: '1', //----->固定传1
        adminUname: '',
        p4_region_str: '',
        GroupPAddr: this.createRegion.address, //--->地址信息(自动生成)
        signinPass: '',
        agree: '', //----->固定传on
      }
      simple_group_part_create(params).then((res) => {
        if (res.rst == 'ok') {
          this.$message.success('创建成功')
          this.createRegion.desc = ''
          this.createRegion.treeThreeText = ''
          this.createRegion.address = ''
          this.createRegion.treeTwoText = ''
          this.createRegion.logo = ''
          this.createRegion.region_name = ''
          this.creatRegionDrawer = false
          this.$refs.tree.simple_get_shop_struct_fun()
        } else {
          this.$message.error('创建失败')
        }
      })
    },
    formTreeData(tree) {
      let treeTempData = []
      for (let i = 0; i < tree.length; i++) {
        tree[i].children = []
        // if (tree[i].type !== 'h' && tree[i].type !== 'L0') {
        //   tree[i].children = []
        // }
        const id = tree[i].id
        for (let j = 0; j < tree.length; j++) {
          if (id == tree[j].parent) {
            if (tree[i].children) {
              tree[i].children.push(tree[j])
            }
          }
        }
      }
      tree.forEach((item) => {
        if (item.type == 'L0' && item.type !== 'h' && item.type !== 'v') {
          treeTempData.push(item)
        } else if (
          item.type == 'L1' &&
          item.type !== 'h' &&
          item.type !== 'v'
        ) {
          if (treeTempData.length == 0) treeTempData.push(item)
        } else if (
          item.type == 'L2' &&
          item.type !== 'h' &&
          item.type !== 'v'
        ) {
          if (treeTempData.length == 0) treeTempData.push(item)
        } else if (
          item.type == 'L3' &&
          item.type !== 'h' &&
          item.type !== 'v'
        ) {
          if (treeTempData.length == 0) treeTempData.push(item)
        } else if (
          item.type == 'L4' &&
          item.type !== 'h' &&
          item.type !== 'v'
        ) {
          if (treeTempData.length == 0) treeTempData.push(item)
        }
      })
      console.log('tree[1]', treeTempData)
      return treeTempData
    },
    simple_get_shop_struct_fun() {
      let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''

      let params = {
        purpose: 'load_pub_tree',
        group_id: this.$store.state.group_id || localStorage.getItem('group_id'),
        need_screen: 1,
      }
      this.loadingTree = true
      simple_get_shop_struct(params).then((res) => {
        this.loadingTree = false
        if (res.rst == 'ok') {
            console.log('simple_get_shop_struct','1579',res);
          const tree = res.data[0].tree_data.tree
          let dataTree = this.formTreeData(tree)
          console.log('dataTree', dataTree)

          this.regionOne = dataTree[0].children
        }
      })
    },
    change_regionThree(val) {
      console.log('市级id', val)
    },
    change_regionTwo(val) {
      this.regionTwo.forEach((item) => {
        if (val == item.id) {
          this.regionThree = item.children
        }
      })
    },
    change_regionOne(val) {
      console.log(4444444, val)
      this.regionOne.forEach((item) => {
        if (val == item.id) {
          this.regionTwo = item.children
          this.addShopObj.category = item.text
        }
      })
    },
    get_g_role_user_list_fun() {
      let params = {
        one_part_user: JSON.parse(localStorage.getItem('device_info')).uid + '',
      }
      get_g_role_user_list(params).then((res) => {
        if (res.rst == 'ok') {
          let data = res.data[0]
          data.forEach((item) => {
            if (item[4] == 1) {
              // 自定义
              let tempObj = {
                id: item[0],
                type: item[1],
                text: item[2],
                tiem: item[3],
              }
              this.roleList_diy.push(tempObj)
            } else {
              this.addShopObj.roleList_default_check.push(item[0])
              // 系统默认
              let tempObj = {
                id: item[0],
                type: item[1],
                text: item[2],
                tiem: item[3],
              }
              this.roleList_default.push(tempObj)
            }
          })
          console.log(8888, this.roleList_diy, this.roleList_default)
        }
      })
    },
    get_g_role_list_fun() {
      let params = {
        pgid: this.$store.state.group_id || localStorage.getItem('group_id'),
        role_name: this.searchRoleName ? this.searchRoleName : '',
      }
      get_g_role_list(params).then((res) => {
        if (res.rst == 'ok') {
          let data = res.data[0]
          data.forEach((item) => {
            if (item[4] == 1) {
              // 自定义
              let tempObj = {
                id: item[0],
                type: item[1],
                text: item[2],
                tiem: item[3],
              }
              this.roleList_diy.push(tempObj)
            } else {
              // 系统默认
              this.addShopObj.roleList_default_check.push(item[0])
              let tempObj = {
                id: item[0],
                type: item[1],
                text: item[2],
                tiem: item[3],
              }
              this.roleList_default.push(tempObj)
            }
          })
          console.log(8888, this.roleList_diy, this.roleList_default)
        }
      })
    },
    addShopSave() {
      this.addShopObj.roleValue = this.addShopObj.roleList_default_check.concat(
        this.addShopObj.roleList_diy_check
      )
      this.simple_create_or_edit_shop_fun('add')
    },
    clickZongBu(val) {
      console.log('44444', val)
      this.clickStr = '点击总部'

      this.drawerAddRole = true
    },
    getNumGroundId(val) {
      console.log(val, 789789)
      let temp_ground_id = val
      let last_ground_id = ''
      if (typeof val !== 'number') {
        if (temp_ground_id.charAt(0) == 'g') {
          last_ground_id = parseInt(temp_ground_id.split('g')[1])
        }
        return last_ground_id
      } else {
        return val
      }
    },
    changeTreeOne(val) {
      // this.treeValue.treeTwoValue = this.treeObj.treeTwo[0].text
      this.treeValue.treeThreeValue = ''
      // this.simple_get_shop_struct_child(this.getNumGroundId(val), 1)
    },
    changeTreeTwo(val) {
      console.log('改变了', val)
      this.treeValue.treeThreeValue = ''
      if (!this.regionObjAll.requsetByAllProvince) {
        this.simple_get_shop_struct_child(this.getNumGroundId(val), 2)
        this.treeValue.treeTwoText = this.treeObj.treeTwo[val]
      } else {
        this.simple_get_tpl_address_fun('city', val)
        this.treeObj.treeTwo.forEach((item) => {
          if (item.id == val) {
            console.log('fffffffff', item.text)
            this.treeValue.treeTwoText = item.text
          }
        })
      }

      console.log('1234567', this.treeValue, this.treeObj)
    },
    changeTreeThree(val) {
      this.treeObj.treeThree.forEach((item) => {
        if (item.id == val) {
          this.treeValue.treeThreeText = item.text
        }
      })

      console.log('1234567', this.treeValue)
    },
    simple_create_or_edit_shop_fun(strType) {
      this.treeObj.treeOne.forEach((item) => {
        if (this.treeValue.treeOneValue == item.id) {
          this.addShopObj.category = item.text
        }
      })
      let params = {
        g_group_id:
          this.$store.state.group_id || localStorage.getItem('group_id'),
        permFrom: 'roles',
      }
      if (strType == 'add') {
        if (!this.treeValue.treeTwoValue) {
          this.$message.error('请选择省')
          return
        }
        if (!this.treeValue.treeThreeValue) {
          this.$message.error('请选择市')
          return
        }
        // if (!this.addShopObj.shop_status) {
        //   this.$message.error('请选择门店状态')
        //   return
        // }
        if (!this.addShopObj.shop_name) {
          this.$message.error('请填写店铺名称')
          return
        }
        if (this.addShopObj.roleValue.length == 0) {
          this.$message.error('请选择角色')
          return
        }
        if (this.checkPassWordMsg) {
          this.$message.error(this.checkPassWordMsg)
          return
        }
        // if (
        //   this.addShopObj.contact_phone_number == '' &&
        //   this.addShopObj.contact_email == ''
        // ) {
        //   return this.$message.error('请输入手机号或邮箱')
        // }
        // if (!this.addShopObj.contact_phone_number) {
        //   this.$message.error('请输入手机号')
        //   return
        // }

        params.contact_email = this.addShopObj.contact_email //邮箱,
        params.contact_phone_number = this.addShopObj.contact_phone_number //手机号,
        params.signin_passwd = this.addShopObj.signin_passwd //登录密码,
        params.category = this.addShopObj.category //品牌名称(第二级分类名称),
        params.perm_roles_list = this.addShopObj.roleValue //--->权限列表,
        params.shop_name = this.addShopObj.shop_name //店铺名称,
        params.shop_status = this.addShopObj.shop_status //店铺状态,
        // params.p_gid = this.regionThreeValue //市级管理员节点id,
        // params.p_gid = this.treeValue.
        
      }
      if (this.regionObjAll.requsetByAllProvince) {
        params.province = this.treeValue.treeTwoText //辽宁省,
        params.province_id = this.treeValue.treeTwoValue //省id,
        params.city = this.treeValue.treeThreeText //:沈阳市,
        params.city_id = this.treeValue.treeThreeValue //市id,
        params.parent_id = this.treeValue.treeThreeValue
      }
      this.isCanClickBol = false
      simple_create_or_edit_shop(params).then((res) => {
        this.isCanClickBol = true
        if (res.rst == 'ok') {
          this.$message.success('添加成功')
          this.addShopDrawer = false
          this.$refs.tree.simple_get_shop_struct_fun()
        } else {
          this.$message.error('添加失败')
          this.$message.error(res.error_msg.error_msg)
        }
      })
    },

    simple_get_shop_struct_child(ground_id, num) {
      let user_id = JSON.parse(localStorage.getItem('device_info')).uid + ''
      let params = {
        user_id: this.$store.state.user_id || user_id,
        group_id: ground_id,
        direction: 0,
        sel_unit: 'shop',
      }
      simple_get_shop_struct(params).then((res) => {
        if (res.rst == 'ok') {
            console.log('simple_get_shop_struct','1822',res);
          let structure = res.data[0].structure
          structure.forEach((item) => {
            item.children = []
            item.show = false
          })
          if (num == 1) {
            this.treeObj.treeTwo = structure
          } else if (num == 2) {
            this.treeObj.treeThree = structure
          }
        }
      })
    },
    currentClickValue(val) {
      // console.log('接收到的值', val)
      // this.treeValue.treeOneValue = val.text
      // this.treeObj.treeTwo = val.children
    },
    getTreeData(val) {
      console.log('接收到的值', val)
      this.treeObj = val
    },
    create_shop_fun() {
      this.treeValue.treeOneText = this.$refs.selectChOne.selected.label
      this.treeValue.treeTwoText = this.$refs.selectChTwo.selected.label
      this.treeValue.treeThreeText = this.$refs.selectChThree.selected.label
      let tempAddress =
        this.treeValue.treeOneText +
        '-' +
        this.treeValue.treeTwoText +
        '-' +
        this.treeValue.treeThreeText
      console.log('this.treeValue', this.treeValue)
      // let params = {
      //   name: this.addShopObj.shop_name, // required, type:string, shop name
      //   category: '', // required, type:string, shop refer industry, options: 'CN_FOOD', 'WEST_FOOD', 'JAPAN_CUISINE', 'COFFEE'
      //   opening_hours: '', // required, type:string, you can set it "", shop opening hours
      //   description: '', // optional, type:string, shop description
      //   logo: '', // optional, type:string, shop logo url
      //   geo: '', // required, type:string, you can set it "",  shop geo point, "lat,lon"
      //   address: tempAddress,
      //   traffic_route: '', // optional, type:string, you can set it "", describe how to go to shop by bus.
      //   shop_phone_number: '', // optional, type:string, shop phone number
      //   contact_username: '', // required, type:string, you can set it "", shop contact user name
      //   contact_phone_number: '', // required, type:string, you can set it"",  shop contact phone number
      //   contact_email: '', // required, type:string,  you can set it "", shop contact email
      //   web_site: '', // optional, type:string, web address.
      //   from_type: '', //default is normal or not this filed, "wx_applet"
      // }
      let params = {
        name: this.addShopObj.shop_name,
        address: tempAddress,
      }
      create_shop(params).then((res) => {
        if (res.rst == 'ok') {
        }
      })
    },
    showDrawer() {
      this.addShopDrawer = true
    },
    addBrandThree(val) {
      this.treeValue.treeThreeValue = this.treeObj.treeThree[val].id
    },
    addBrandTwo(val) {
      this.treeValue.treeTwoValue = this.treeObj.treeTwo[val].id
      this.treeValue.treeThreeValue = ''
    },

    addBrandOne(val) {
      console.log('444', val)
      // this.showMaskParent = true

      this.treeValue.treeOneValue = this.treeObj.treeOne[val].id
      this.treeValue.treeTwoValue = ''
      this.treeValue.treeThreeValue = ''
    },

    getHeight() {
      let windowHeight = parseInt(window.innerHeight)
      this.autoHeight.height = windowHeight - 100 + 'px'
    },
    addBrandParentFun() {
      console.log('点击了', this.$refs.tree.treeList)
      this.$refs.tree.treeList[0].children.push({
        label: this.brandName,
        show: true,
      })
      this.showMaskParent = false
    },
  },
  created() {
    window.addEventListener('resize', this.getHeight)
    this.getHeight()
  },
  destroyed() {
    window.removeEventListener('resize', this.getHeight)
  },
}
</script>
<style scoped>
.flexBoxTwoTitle {
  font-size: 14px;
  color: #666;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
  width: 100%;
  background: #fff;
}

.borderBottomCss {
  border-bottom: 3px solid #326ffa;
}

.boxScroll::-webkit-scrollbar {
  width: 0;
}

/* drawer start */
.closeCss {
  width: 15px;
  height: 15px;
}

.drawerTitle {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.spanMust {
  color: #f73146;
}

.saveBtnParent {
  height: 40px;
  text-align: right;

  border-top: 1px solid #ececec;
  background: #fff;
}

.overflowBoxCss {
  overflow-y: scroll;
}

.overflowBoxCss::-webkit-scrollbar {
  width: 0;
}

/* drawer end */

.editBtn {
  width: 50px;
  height: 30px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #326ffa;
  font-size: 13px;
  color: #326ffa;
}

.lineCss {
  height: 1px;
  background: #ededed;
}

.opsBtn {
  font-size: 13px;
  color: #326ffa;
}

/* mask box */
.maskBoxParent {
  background: rgba(0, 0, 0, 0.3);
  z-index: 20;
  top: 0;
}

.maskBox {
  width: 500px;
  height: 300px;
  background: #fff;
  top: calc(50% - 150px);
  left: calc(50% - 250px);
}

.spanMust {
  color: #f73146;
}

.flexLeft {
  font-size: 13px;
  font-weight: 500;
}

.closeCss {
  width: 15px;
  height: 15px;
}

/* mask box */
.autoImgCssBox {
  width: 100px;
  height: 100px;
  border: 1px solid #ececec;
}

.inputCssFour {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
}

.labelBox {
  background: #dee9ff;
  border-radius: 10px;
  color: #326ffa;
  padding: 2px 6px;
  font-size: 12px;
}
</style>