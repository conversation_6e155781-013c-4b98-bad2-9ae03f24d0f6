<template>
  <div class="datafield">
    <div class="header">
      <p>数据范围</p>
    </div>
    <div class="center">
      <div class="new">
        <el-button class="new" @click="newDataFiled">+新建数据</el-button>
      </div>
      <!-- 列表 -->
      <div class="table_wrap">
        <el-table
          :data="tableData"
          :height="autoHeight.height"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{
            background: '#24b17d',
            color: '#fff',
            'font-size': '13px',
            'text-align': 'center',
          }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column label="编号" align="center" width="100px">
            <template slot-scope="scop">
              {{ scop.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="数据范围名称"
            width=""
          ></el-table-column>
          <el-table-column
            prop="datas_count"
            label="店铺个数"
            width=""
          ></el-table-column>
          <el-table-column
            prop="createdAt"
            label="创建时间"
            width=""
          ></el-table-column>
          <el-table-column label="操作" width="130px">
            <template slot-scope="scope">
              <div class="event">
                <span class="edit" @click="toEdit(scope.row)">编辑</span>
                <span class="delete" @click="Delete(scope.row)">删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="bottom">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage4"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { get_data_filed, add_upt_del_data } from "@/api/datafiled/datafiled";
export default {
  data() {
    return {
      tableData: [],
      //   高度
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      //   搜索条件
      SearchForm: {
        classModel: "DataRangeTpl",
        page: 0,
        size: 10,
        blurry: 0,
      },
      // 总数
      total: null,
      // loading
      loading: true,
    };
  },
  methods: {
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 260 + "px";
      this.autoHeight.heightNum = windowHeight - 230;
    },
    // 获取列表
    getTableData() {
      get_data_filed(this.SearchForm).then((res) => {
        console.log(res);
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;
          this.total = res.data[0].totalElements;
          this.loading = false;
        }
      });
    },
    // 跳转新增
    newDataFiled() {
      this.$router.push({
        path: "/systemCenter/newdatafiled",
        query: {
          type: "new",
        },
      });
    },
    toEdit(row) {
      this.$router.push({
        path: "/systemCenter/newdatafiled",
        query: {
          type: "edit",
          id: row.id,
          name: row.name,
        },
      });
    },
    // 分页
    handleSizeChange(val) {
      this.loading = true;
      this.SearchForm.size = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.loading = true;
      this.SearchForm.page = val - 1;
      this.getTableData();
    },
    // 删除
    Delete(row) {
      console.log(row);
      this.$confirm("此操作将删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          const params = {
            act: "del",
            name: row.name,
            id: row.id,
            dataAct: "del",
          };
          add_upt_del_data(params).then((res) => {
            console.log(res);
            if (res.rst == "ok") {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getTableData();
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getTableData();
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style lang="scss" scoped>
.datafield {
  width: 100%;
  padding: 0 23px 0 23px;
  .header {
    height: 52px;
    line-height: 52px;
    border-bottom: 1px solid rgba(229, 229, 229, 1);
    p {
      color: rgba(108, 178, 255, 1);
      font-size: 14px;
      font-weight: bold;
    }
  }
  .center {
    margin-top: 15px;
    box-sizing: border-box;
    padding: 0 10px;
    .new {
      width: 106px;
      //   height: 32px;
      float: right;
      background-color: var(--text-color);
      color: #fff;
      border-radius: 6px;
      font-size: 14px;
      text-align: center;
    }
    .table_wrap {
      padding-top: 50px;
      .event {
        display: flex;
        span {
          flex: 1;
          cursor: pointer;
        }
        .edit {
          color: rgba(42, 130, 228, 1);
          font-size: 14px;
        }
        .delete {
          color: #ff4949;
          font-size: 14px;
        }
      }
    }
  }
  .bottom {
    float: right;
    margin-top: 30px;
  }
}
</style>