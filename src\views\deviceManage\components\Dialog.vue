<template>
    <div class="screen_dialog">
        <el-dialog title="标签管理" :visible.sync="tagsDialog" width="40%" :before-close="handleClose">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="关联标签" name="relation">
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in tagsList" :key="item.name"
                            :class="item.active ? 'tag_active' : ''" @click="addTags(item)">
                            <img src="@/assets/img/tags.png" alt="" style="width:24px;height:24px;margin-right:7px">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="删除标签" name="deltag">
                    <div class="relation_tags1">
                        <div class="every_tag" v-for="item in delTagsList" :key="item.name"
                            :class="item.active ? 'tag_active' : ''" @click="addTags(item)">
                            <img src="@/assets/img/tags.png" @click="changeActive(item)" alt=""
                                style="width:24px;height:24px;margin-right:7px">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" style="height:35px" @click="saveTags">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<script>
export default {
    data() {
        return {
            activeName: "relation",
            deletetags: [],
            tags: []
        }
    },
    props: {
        tagsDialog() {
            type: Boolean
        },
        tagsList() {
            type: Array
        },
        delTagsList() {
            type: Array
        },
    },
    watch: {
        tagsList: {
            handler(newValue, oldValue) {
            },
        },
        delTagsList: {
            handler(newValue, oldValue) {
            },
        }
    },
    methods: {
        handleClose() {
            this.$emit('handleCloseDialog')
        },
        addTags(item) {
            this.$nextTick(() => {
                item.active = !item.active;
            })
            let index = this.tags.indexOf(item.name);
            if (this.tags.indexOf(item.name) != -1) {
                this.tags.splice(index, 1)
            } else {
                this.tags.push(item.name)
            }
        },
        saveTags() {
            this.$emit('saveTags', this.tags, this.activeName)
            this.tags = []
        },
        changeActive(item) {
            item.active = !item.active
        },
        handleClick(e) {

        }
    },
    created() {
    }
}
</script>

<style lang="scss" scoped>
.screen_dialog {
    ::v-deep .el-dialog {
        border-radius: 15px !important;

        .el-dialog__header {
            font-weight: bold;
        }

    }

    .el-tabs__header {
        margin: 0 !important;
    }

    .el-tabs__nav-wrap::after {
        height: 1px !important;
    }

    .el-tabs__item {
        height: 50px !important;
        line-height: 50px !important;
    }

    .el-button--primary {
        background: rgba(108, 178, 255, 1);
    }

    .el-tabs__header {
        margin-bottom: 0 !important;
        width: 100% !important;
    }

    .el-tabs__nav-scroll {
        padding-left: 50px;
    }

    /* tabs选中的样式 */
    .is-active {
        color: var(--text-color) !important;
        background-color: rgba(255, 255, 255, 0.3) !important;
        /* border-bottom: 2px solid var(--text-color) !important; */
    }

    .el-tabs__nav {
        height: 45px;
    }

    .batch_tags_dialog .el-tabs__nav-wrap::after {
        height: 0px !important;
    }
}

.relation_tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    overflow-y: auto;
    height: 320px;
    border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    font-size: 14px;
    border-radius: 24px;
    margin-right: 24px;
    margin-bottom: 18px;
    padding: 0 13px;
    border: 1px solid rgba(209, 209, 209, 1);
    cursor: pointer;
    /* 禁止文字选中 */
    /* -moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none; */
}

.tag_active {
    color: rgba(108, 178, 255, 1);
    background-color: rgba(212, 232, 255, 1);
    border: 1px solid rgba(108, 178, 255, 1);
}

.show_more_tags {
    box-sizing: border-box;
    height: 24px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.show_more_tags:hover {
    color: #83B3EE;
}

.stow_tags {
    margin-top: 10px;
    padding: 0 9px;
    position: relative;
}

.more_tags_wrap {
    position: absolute;
    width: 255px;
    height: 140px;
    background: #fff;
    padding: 15px 5px 10px;
    top: -142px;
    left: 3px;
    /* max-height: 140px; */
    z-index: 200;
    border-radius: 5px;
    border: 1px solid #e5dfdf;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.135);
}

/* 第一个三角形颜色换成边框颜色 */
.more_tags_wrap::before {
    content: '';
    display: block;
    position: absolute;
    bottom: -7px;
    left: 8px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    // border-bottom: 20px solid #d9d9d9;
    border-top: 7px solid #fff;
    z-index: 50;
}

/* 第二个三角形颜色换成背景色 */
.more_tags_wrap::after {
    content: '';
    display: block;
    position: absolute;
    bottom: -8px;
    left: 7px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 8px solid rgba(0, 0, 0, 0.135);
    // border-top: 7px solid red;
}

.more_tags {
    width: 100%;
    max-height: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    overflow-y: auto;
    flex-shrink: 0;
}
.relation_tags1 {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    height: 100%;
    max-height: 320px;
    
    overflow-y: auto;
    border: 1px solid rgba(229, 229, 229, 1);
}
</style>