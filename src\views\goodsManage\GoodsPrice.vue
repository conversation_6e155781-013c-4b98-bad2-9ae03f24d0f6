<template>
    <div class="goods_sell_out">
        <div class="top_back">
            <i class="el-icon-back" @click="back2managent"></i>
        </div>
        <div class="sell_out_content">
            <div class="left_overview">
                <div class="l_content">
                    <div class="goods_info">
                        <div class="info_item"><span>商品ID：</span><b>{{ }}</b></div>
                        <div class="info_item"><span>商品编码：</span><b>{{ }}</b>
                        </div>
                        <div class="info_item"><span>商品名称：</span><b>{{ }}</b></div>
                        <div class="info_item"><span>商品分类：</span><b>{{ }}</b></div>
                        <div class="info_item"><span>商品状态：</span><b>{{ }}</b></div>
                    </div>
                </div>
            </div>
            <div class="right_shop_edit">
                <div class="shop_content">

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { get_pc_sku_details } from '@/api/goods/goods'
export default {
    components: {

    },
    data() {
        return {
            loading: false,
            searchForm: {
                sku_code: '',
                store_id: '',
                page_num: 0,
                page_size: 10
            }
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.searchForm.sku_code = this.$route.query.sku_code
    },
    mounted() {
        this.getGoodsDetail()
    },
    methods: {
        getGoodsDetail() {
            get_pc_sku_details(this.searchForm).then(res => {
                console.log(res,'res')
            })
        }
    },
};
</script>

<style scoped lang="scss">
.goods_sell_out {
    width: 100%;
    height: 100%;
    min-height: 700px;
    padding: 20px;
    padding-right: 40px;
    box-sizing: border-box;
    font-size: 14px;
    background-color: var(--light-gray);

    .top_back {
        width: 100%;
        height: 30px;
        font-size: 26px;
        color: var(--btn-background-dark);
        margin-bottom: 20px;

        i {
            cursor: pointer;
        }
    }

    .sell_out_content {
        display: flex;
        height: calc(100vh - 150px);
        min-height: 600px;

        .left_overview {
            margin-right: 35px;
            height: 100%;

            .l_header {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
            }

            .l_content {
                width: 230px;
                min-height: 240px;
                background-color: #fff;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 10px 0;

                .goods_img {
                    width: 200px;
                    min-height: 200px;
                    background-color: var(--light-gray);
                    border-radius: 10px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .no_img {
                        color: #8b8b8b;
                    }

                    .img_url {
                        width: 100%;
                        height: 200px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .img_title {
                        width: 100%;
                        padding-left: 10px;

                        p:first-child {
                            font-size: 16px;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }

                        p:last-child {
                            color: var(--text-gray);
                        }
                    }
                }

                .goods_info {
                    width: 200px;
                    min-height: 150px;
                    max-height: 100%;
                    margin-top: 10px;

                    // display: grid;
                    // place-content: end start;
                    .info_item {
                        margin-top: 15px;
                        color: var(--text-gray);
                        display: flex;

                        b {
                            color: #000;
                            display: inline-block;
                            width: 100px;
                        }
                    }
                }
            }
        }

        .right_shop_edit {
            flex: 1;
            min-height: 550px;
            min-width: 300px;
            display: flex;
            padding-top: 40px;
            box-sizing: border-box;
            flex-direction: column;

            .r_header {
                font-size: 18px;
                font-weight: bold;
                // color: var(--text-color);
                margin-bottom: 20px;
                padding-left: 20px;
                opacity: 0;
            }

            .shop_content {
                min-height: calc(100vh - 200px);
                border-radius: 10px;
                display: flex;
                flex-direction: column;

                .content_center_wrap {
                    flex: 1;
                    height: calc(100% - 50px);
                    display: flex;
                    justify-content: space-between;

                    .sswr {

                        // min-width: 450px;
                        width: 50%;
                        // margin: 0 20px;
                        box-sizing: border-box;
                        padding: 20px 10px 10px 10px;
                        border: 2px solid var(--dev-border);
                        font-size: 14px;
                        border-radius: 20px;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        box-sizing: border-box;

                        .title {
                            font-size: 16px;
                            font-weight: bold;
                        }

                        .table_content {
                            flex: 1;
                            overflow-y: hidden;

                            .table_header {
                                height: 50px;
                                box-sizing: border-box;
                                padding-right: 5px;

                                .header_row {
                                    width: 100%;
                                    height: 50px;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .table_body {
                                height: calc(100% - 50px);
                                overflow-y: auto;

                                .body_row {
                                    width: 100%;
                                    height: 50px;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .header_row,
                            .body_row {
                                border-bottom: 1px solid var(--gray);

                                .cell {
                                    text-align: center;
                                    flex: 1;

                                    &:first-child {
                                        flex: 0 0 45px;
                                    }
                                }
                            }

                            ::v-deep .el-checkbox__inner {
                                width: 18px;
                                height: 18px;

                                &::after {
                                    height: 10px;
                                    width: 4px;
                                    left: 6px;
                                }
                            }
                        }

                        .footer {
                            height: 50px;
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;
                            box-sizing: border-box;
                            margin-top: 10px;
                        }
                    }

                    .shop_unselected {
                        margin-right: 30px;

                        .filter_content {
                            margin-top: 20px;
                            margin-bottom: 10px;
                            display: flex;
                            flex-wrap: wrap;

                            &>div {
                                margin-right: 20px;
                                margin-bottom: 10px;
                            }
                        }
                    }

                    .shop_selected {
                        .title {
                            margin-bottom: 20px;
                        }
                    }
                }


                .btns {
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    padding-right: 30px;
                }
            }

        }
    }
}
</style>
