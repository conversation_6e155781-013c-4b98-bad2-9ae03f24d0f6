<template>
    <div class="goods_sell_out">
        <div class="top_back">
            <i class="el-icon-back" @click="back2managent"></i>
        </div>
        <div class="sell_out_content">
            <div class="left_overview">
                <div class="l_content">
                    <div class="goods_info">
                        <div class="info_item"><span>SPU code :</span><b>{{ row_info['spu_code'] }}</b></div>
                        <div class="info_item"><span>商品名称 :</span><b>{{ row_info['spu_name'] }}</b></div>
                        <div class="info_item"><span>商品状态 :</span><b>{{ row_info['active_status'] == 9 ? '在售' : '已下架'
                        }}</b></div>
                        <div class="info_item"><span>SKU code :</span><b>{{ row_info['sku_list'][0]['sku_code'] }}</b>
                        </div>
                        <div class="info_item"><span>SKU :</span>
                            <div class="sku_list">
                                <div v-for="sku in row_info['sku_list']" :key="sku['sku_code']" class="sku_item">
                                    {{ sku['unit_label'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right_shop_edit">
            <div class="shop_content">

            </div>
        </div>
    </div>
</template>

<script>
import { get_pc_sku_details } from '@/api/goods/goods'
export default {
    components: {

    },
    data() {
        return {
            loading: false,
            row_info: {},
            searchForm: {
                sku_code: '',
                store_id: '',
                page_num: 0,
                page_size: 10
            }
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.searchForm.sku_code = this.$route.query.sku_code;
        this.row_info = JSON.parse(this.$route.query.row_info);
        console.log(this.row_info)
    },
    mounted() {
        this.getGoodsDetail()
    },
    methods: {
        back2managent() {
            this.$router.push({
                path: '/goodsManage/product_center'
            })
        },
        getGoodsDetail() {
            get_pc_sku_details(this.searchForm).then(res => {
                console.log(res, 'res')
            })
        }
    },
};
</script>

<style scoped lang="scss">
.goods_sell_out {
    width: 100%;
    height: 100%;
    min-height: 700px;
    padding: 20px;
    padding-right: 40px;
    box-sizing: border-box;
    font-size: 14px;
    background-color: var(--light-gray);

    .top_back {
        width: 100%;
        height: 30px;
        font-size: 26px;
        color: var(--btn-background-dark);
        margin-bottom: 20px;

        i {
            cursor: pointer;
        }
    }

    .sell_out_content {
        display: flex;
        height: calc(100vh - 150px);
        min-height: 600px;

        .left_overview {
            margin-right: 35px;
            height: 100%;

            .l_header {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
            }

            .l_content {
                width: 280px;
                background-color: #fff;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                padding: 10px 20px;

                .goods_info {
                    .info_item {
                        margin-top: 15px;
                        color: var(--text-gray);
                        display: flex;
                        font-size: 18px;
                        font-weight: bold;

                        span {
                            white-space: nowrap;
                        }

                        b {
                            font-size: 17px;
                            color: #000;
                            display: inline-block;
                            padding-left: 5px;
                        }
                    }
                }
            }
        }

        .right_shop_edit {
            flex: 1;
            min-height: 550px;
            min-width: 300px;
            display: flex;
            padding-top: 40px;
            box-sizing: border-box;
            flex-direction: column;

            .r_header {
                font-size: 18px;
                font-weight: bold;
                // color: var(--text-color);
                margin-bottom: 20px;
                padding-left: 20px;
                opacity: 0;
            }

            .shop_content {
                min-height: calc(100vh - 200px);
                border-radius: 10px;
                display: flex;
                flex-direction: column;

                .content_center_wrap {
                    flex: 1;
                    height: calc(100% - 50px);
                    display: flex;
                    justify-content: space-between;

                    .sswr {

                        // min-width: 450px;
                        width: 50%;
                        // margin: 0 20px;
                        box-sizing: border-box;
                        padding: 20px 10px 10px 10px;
                        border: 2px solid var(--dev-border);
                        font-size: 14px;
                        border-radius: 20px;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        box-sizing: border-box;

                        .title {
                            font-size: 16px;
                            font-weight: bold;
                        }

                        .table_content {
                            flex: 1;
                            overflow-y: hidden;

                            .table_header {
                                height: 50px;
                                box-sizing: border-box;
                                padding-right: 5px;

                                .header_row {
                                    width: 100%;
                                    height: 50px;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .table_body {
                                height: calc(100% - 50px);
                                overflow-y: auto;

                                .body_row {
                                    width: 100%;
                                    height: 50px;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .header_row,
                            .body_row {
                                border-bottom: 1px solid var(--gray);

                                .cell {
                                    text-align: center;
                                    flex: 1;

                                    &:first-child {
                                        flex: 0 0 45px;
                                    }
                                }
                            }

                            ::v-deep .el-checkbox__inner {
                                width: 18px;
                                height: 18px;

                                &::after {
                                    height: 10px;
                                    width: 4px;
                                    left: 6px;
                                }
                            }
                        }

                        .footer {
                            height: 50px;
                            display: flex;
                            align-items: center;
                            justify-content: flex-end;
                            box-sizing: border-box;
                            margin-top: 10px;
                        }
                    }

                    .shop_unselected {
                        margin-right: 30px;

                        .filter_content {
                            margin-top: 20px;
                            margin-bottom: 10px;
                            display: flex;
                            flex-wrap: wrap;

                            &>div {
                                margin-right: 20px;
                                margin-bottom: 10px;
                            }
                        }
                    }

                    .shop_selected {
                        .title {
                            margin-bottom: 20px;
                        }
                    }
                }


                .btns {
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    padding-right: 30px;
                }
            }

        }
    }
}

.sku_list {
    width: 280px;
    display: flex;
    flex-wrap: wrap;

    .sku_item {
        width: 45%;
        border: 1px solid #ccc;
    }
}
</style>
