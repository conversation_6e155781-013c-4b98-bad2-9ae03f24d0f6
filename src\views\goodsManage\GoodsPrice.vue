<template>
    <div class="goods_sell_out">
        <div class="top_back">
            <i class="el-icon-back" @click="back2managent"></i>
        </div>
        <div class="sell_out_content">
            <div class="left_overview">
                <div class="l_content">
                    <div class="goods_info">
                        <div class="info_item"><span>SPU code :</span><b>{{ row_info['spu_code'] }}</b></div>
                        <div class="info_item"><span>商品名称 :</span><b>{{ row_info['spu_name'] }}</b></div>
                        <div class="info_item"><span>商品状态 :</span><b>{{ row_info['active_status'] == 9 ? '在售' : '已下架'
                        }}</b></div>
                        <div class="info_item"><span>SKU code :</span><b>{{ row_info['sku_list'][0]['sku_code'] }}</b>
                        </div>
                        <div class="info_item"><span>SKU :</span>
                            <div class="sku_list">
                                <div v-for="sku in row_info['sku_list']" :key="sku['sku_code']" class="sku_item"
                                    @click="change_sku(sku['sku_code'])"
                                    :class="current_sku == sku['sku_code'] ? 'active' : ''">
                                    {{ sku['unit_label'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right_shop_edit">
                <div class="search_bar">
                    <div class="search_item">
                        <el-input v-model="searchForm.store_id" style="width:220px" placeholder="请输入门店编号"
                            clearable></el-input>
                    </div>
                    <div class="search_item">
                        <el-input v-model="searchForm.sku_code" style="width:220px" placeholder="请输入sku_code"
                            clearable></el-input>
                    </div>


                    <div class="search_item">
                        <el-button type="primary" @click="search">搜索</el-button>
                        <el-button @click="reset">重置</el-button>
                    </div>
                </div>
                <div class="shop_content">
                    <el-table :data="tableData" border v-loading="loading" header-row-class-name="table_header"
                        :height="tableHeight" row-class-name="table_row" style="width: 100%;">
                        <el-table-column prop="store_id" label="门店编码" align="center" width="100"></el-table-column>
                        <el-table-column prop="name" label="名称" align="center" min-width="150"></el-table-column>
                        <el-table-column prop="code" label="Poskey" align="center" min-width="150">
                            <template slot-scope="scope">
                                <span>{{ scope.row.price_list[0]['pos_key'] }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="售价" align="center">
                            <template slot-scope="scope">
                                <span>¥{{ scope.row.price_list[0]['unit_price'] }}({{
                                    scope.row.price_list[0]['valid_from'] }} - {{ scope.row.price_list[0]['valid_until']
                                    }} )</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="info_pages">
                        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="pageNumber" :page-sizes="pageSizes" :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="total">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { get_pc_sku_details } from '@/api/goods/goods'
export default {
    components: {

    },
    data() {
        return {
            loading: false,
            row_info: {},
            searchForm: {
                sku_code: '',
                store_id: '',
                page_num: 0,
                page_size: 10
            },
            current_sku: '',
            tableData: [],
            tableHeight: '500px',
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.searchForm.sku_code = this.$route.query.sku_code;
        this.current_sku = this.$route.query.sku_code;
        this.row_info = JSON.parse(this.$route.query.row_info);
        console.log(this.row_info)
    },
    mounted() {
        this.getGoodsDetail()
    },
    methods: {
        back2managent() {
            this.$router.push({
                path: '/goodsManage/product_center'
            })
        },
        getGoodsDetail() {
            this.loading = true;
            get_pc_sku_details(this.searchForm).then(res => {
                console.log(res, 'res')
                this.tableData = res['data'][0]['content']
                this.total = res['data'][0]['total_elements']
                this.loading = false;
            })
        },
        change_sku(sku_code) {
            this.current_sku = sku_code;
            this.searchForm.sku_code = sku_code;
            this.getGoodsDetail(1);
        },
        handleCurrentChange(val) {
            this.searchForm.page_num = val - 1;
            this.getGoodsDetail();
        },

        handleSizeChange(val) {
            this.searchForm.page_size = val;
            this.getGoodsDetail(1);
        },
        search() {
            this.getGoodsDetail(1);
        },
        reset() {
            this.searchForm = {
                sku_code: '',
                store_id: '',
                page_num: 0,
                page_size: 10
            }

        }

    },
};
</script>

<style scoped lang="scss">
.goods_sell_out {
    width: 100%;
    height: 100%;
    min-height: 700px;
    padding: 20px;
    padding-right: 40px;
    box-sizing: border-box;
    font-size: 14px;
    background-color: var(--light-gray);

    .top_back {
        width: 100%;
        height: 30px;
        font-size: 26px;
        color: var(--btn-background-dark);
        margin-bottom: 20px;

        i {
            cursor: pointer;
        }
    }

    .sell_out_content {
        display: flex;
        height: calc(100vh - 150px);
        min-height: 600px;

        .left_overview {
            margin-right: 35px;
            height: 100%;

            .l_header {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
            }

            .l_content {
                width: 280px;
                background-color: #fff;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                padding: 10px 20px;

                .goods_info {
                    .info_item {
                        margin-top: 15px;
                        color: var(--text-gray);
                        display: flex;
                        font-size: 18px;
                        font-weight: bold;

                        span {
                            white-space: nowrap;
                        }

                        b {
                            font-size: 17px;
                            color: #000;
                            display: inline-block;
                            padding-left: 5px;
                        }
                    }
                }
            }
        }

        .right_shop_edit {
            flex: 1;
            min-height: 550px;
            min-width: 300px;
            display: flex;
            box-sizing: border-box;
            flex-direction: column;

            .r_header {
                font-size: 18px;
                font-weight: bold;
                // color: var(--text-color);
                margin-bottom: 20px;
                padding-left: 20px;
                opacity: 0;
            }

            .shop_content {
                border-radius: 10px;
                display: flex;
                flex-direction: column;

                .btns {
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    padding-right: 30px;
                }
            }

        }
    }
}

.sku_list {
    width: 285px;
    display: flex;
    flex-wrap: wrap;
    margin-left: 10px;

    .sku_item {
        width: calc(50% - 5px);
        /* 每个项目占50%宽度，减去间距 */
        margin-right: 10px;
        /* 右边距 */
        margin-bottom: 10px;
        /* 下边距 */
        box-sizing: border-box;
        /* 包含边框和内边距在宽度内 */
        border: 1px solid #ccc;
        padding: 2px 4px;
        border-radius: 5px;
        text-align: center;
        cursor: pointer;

        /* 每行第二个元素不需要右边距 */
        &:nth-child(2n) {
            margin-right: 0;
        }
    }
}

.active {
    background-color: var(--btn-background-dark);
    color: #fff;
}

::v-deep .table_header {
    // background-color: red !important;
    height: 50px;
    font-size: 14px;

    th.el-table__cell {
        color: #fff;
        background-color: var(--btn-background-dark) !important;
    }
}

::v-deep .el-table__body-wrapper {
    height: calc(100vh - 500px) !important;
    overflow: auto !important;
}

.info_pages {
    height: 60px;
    // border-top: 1px solid var(--gray);
    background-color: #fff;
    overflow: hidden;
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;
    // border:2px solid ;
    // border-top: none;
    // border-color: var(--dev-border);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
}

.search_bar {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;

    .search_item {
        display: flex;
        align-items: center;
        margin: 10px 20px 10px 0;

        &>span {
            margin-right: 10px;
        }
    }
}
</style>
