<template>
    <div class="pictur_audit" v-loading="loading">
        <div class="search_header">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item>
                    <el-select v-model="searchForm.auditType" placeholder="审核类型">
                        <el-option v-for="item in auditTypeList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="searchForm.marketname" placeholder="营运市场" clearable>
                        <el-option :label="item[1]" :value="item[0]" v-for="item in MarketList" :key="item"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" class="search_button">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 列表 -->
        <div class="table_wrap flex-1">
            <el-table :data="tableData" :height="autoHeight.height" row-key="shop_id" style="width: 100%"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <el-table-column width="50" align="center">
                    <template slot="header">
                        <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox>
                    </template>
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.checked" @change="checkOne(scope.row)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="photo_url" label="图片预览" width="110" align="center">
                    <template slot-scope="scope">
                        <img :src="scope.row.photo_url" style="width: 73px;height:73px;object-fit:cover" alt="">
                    </template>
                </el-table-column>
                <el-table-column prop="photo_info" label="尺寸" width="" show-overflow-tooltip="true" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.source.src_width }} * {{ scope.row.source.src_height }}
                    </template>
                </el-table-column>
                <el-table-column prop="src_file_length" label="大小" width="" show-overflow-tooltip="true"
                    align="center"></el-table-column>
                <el-table-column prop="take_time_str" label="上传日期" width="" show-overflow-tooltip="true"
                    align="center"></el-table-column>
                <el-table-column prop="account" label="账户" width="" show-overflow-tooltip="true" align="center">
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="130" align="center">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="handleShow(scope.row)" type="text" style="color:#409eff"
                            size="small">预览</el-button>
                        <el-button @click.native.prevent="handleShow(scope.row)" type="text" style="color:#409eff"
                            size="small">同意</el-button>
                        <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:var(--text-color)"
                            size="small">驳回
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="pictur_footer flex flex-1">
            <div class="left_button_wrap flex-1">
                <!-- <el-button type="primary" size="small" @click="setDaypart">批量经营时段设置</el-button> -->
            </div>
            <div class="right_page_wrap">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5'
                    :page-sizes="[10, 20, 50, 100]" layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
                </el-pagination>
            </div>
        </div>
    </div>
</template>


<script>
import { datas_filter_cond } from '@/api/commonInterface'
import { get_adm_datas, hand_newstore_to_brand } from '@/api/shopManage/shop'
import { self_photo_list, delete_self_photo_statusonly, add_or_edit_photo_tags } from '@/api/files/pictureResources'
export default {
    data() {
        return {
            searchForm: {
                auditType: '',
                marketname: ''
            },
            auditTypeList: [
                {
                    value: '0',
                    label: '历史审核'
                },
                {
                    value: '1',
                    label: '待审核'
                }
            ],
            MarketList: [],
            tableData: [],//列表数据
            loading: true,
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
            currentPage: 1, //页码
            totalNum: 0, //总数据数量
            pageSize: 10,
            totalNum: 0, //总数据数量
        }
    },
    methods: {
        // 获取搜索条件的营运市场
        getMarketSearchData() {
            datas_filter_cond({
                'classModel': 'GroupShop'
            }).then(res => {
                console.log(res, 'res');
                this.MarketList = res['data'][0][1]['options']
            })
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 230 + 'px';
            this.autoHeight.heightNum = windowHeight - 230;
        },
        // 获取门店列表数据
        getTableData() {
            this.loading = true;
            const params = {
                unit: "all", // (String) options: “all”， "in_year", "in_month", "in_day"  // optional
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                query_status: [1, 2, 9, 10], // (List)  
                order_by: "-last_mtime",
                root_flag: 0  // (Int) 是否继承总部图片
            }
            self_photo_list(params).then(res => {
                if (res.rst == 'ok') {
                    res.data[0].photos.forEach(item => {
                        item.checked = false;
                        item.isShowTage = false;
                    })
                    this.tableData = res.data[0].photos;
                    this.totalNum = res.data[0].unit_info.total;
                    this.loading = false;
                } else {
                    this.$message.error(res.error_msg);
                    this.loading = false;
                }
            })
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getTableData();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getTableData()
        },
        // 预览
        handleShow(val) {
            this.$emit('setImgPreview', val.original_url)
        },
    },
    created() {
        this.getMarketSearchData()
        window.addEventListener('resize', this.getHeight);
        this.getHeight()
        this.getTableData()
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
    },
}
</script>

<style lang="scss" scoped>
.pictur_audit {
    background-color: #ffffff;
}

.search_header {
    height: 60px;
    display: flex;
    padding: 0 10px;
    align-items: center;

    .search_button {
        background-color: var(--text-color);
        color: #fff;
        border: 0;
    }
}

::v-deep .search_header .el-form-item {
    margin-bottom: 0 !important;
}

.pictur_footer {
    height: 89px;
    align-items: center;
    padding-left: 24px;
    padding-right: 22px;
}
</style>