<template>
    <div class="shop_add_edit">
        <!-- title -->
        <div class="shop_add_edit_title">
            <i class="el-icon-back back_btn" style="font-size:24px;color:#25acd1;cursor:pointer;font-weight:bold;"
                @click="back"></i>
            <span style="font-size:16px">新开店</span>
        </div>
        <!-- 内容区 -->
        <div class="content_wrap flex">
            <div class="introl_content flex-1">
                <!-- 基本设置 -->
                <div class="basic_settings">
                    <div class="content_title">基本设置</div>
                    <div class="basic_settings_content content_bg">
                        <div class="flex" style="justify-content: space-between;margin-bottom:56px">
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">市场：</span>
                                    <el-select v-model="lists.opsmarketname" placeholder="请选择"
                                        :disabled="disabled && lists.opsmarketname" size="small flex-1"
                                        @change="changeMarket">
                                        <el-option v-for="item in lists.itMarket" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <!--   -->
                            <!--  -->
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">餐厅名称：</span>
                                    <el-input size="small flex-1" :disabled="disabled && lists.name"
                                        v-model="lists.name"></el-input>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">餐厅编号：</span>
                                    <el-input size="small flex-1" :disabled="disabled && lists.storecode"
                                        v-model="lists.storecode">
                                    </el-input>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">商&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;圈：</span>
                                    <el-select :disabled="disabled && lists.jdetradezonecode != ''"
                                        v-model="lists.jdetradezonecode" placeholder="请选择" size="small flex-1">
                                        <el-option v-for="item in lists.businessArr" :key="item.value" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                    <!-- <el-input size="small flex-1"  v-model="lists.bussiness"></el-input> -->
                                </div>
                            </div>
                        </div>
                        <div class="flex" style="justify-content:space-between;margin-bottom:20px">

                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">餐厅类型：</span>
                                    <!-- :disabled="disabled" -->
                                    <el-select :disabled="disabled" v-model="lists.type" placeholder="请选择"
                                        size="small flex-1" @change="changeType">
                                        <el-option v-for="(item, index) in lists.storesType" :key="index"
                                            :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content double">
                                    <!-- 系统自建 -->
                                    <!-- <div style="display:flex;align-items:center;justify-content: space-between;"> -->
                                    <span class="option_name" style="margin-left:0px">屏幕数量：</span>
                                    横：<el-input-number size="mini" v-model="lists.screenCount1" @change="handleChange"
                                        :min="0" :max="20" label="描述文字"></el-input-number>
                                    竖：<el-input-number size="mini" v-model="lists.screenCount2" @change="handleChange"
                                        :min="0" :max="20" label="描述文字"></el-input-number>
                                    <!-- <el-input placeholder="请输入屏幕数量" v-model="lists.screenCount"  style="display:inline-block" @input="changeNum"></el-input> -->
                                    <!-- </div> -->
                                    <!-- <div v-else-if=" !disabled" class="doubleS">
                                          <span class="option_name" >屏幕数量：</span>
                                          横
                                            <el-select v-model="lists.screenCount" placeholder="请选择" size="small flex-1" @change="changeScreen" >
                                                <el-option v-for="item in lists.screenNum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                            竖
                                             <el-select v-model="lists.screenCount" placeholder="请选择" size="small flex-1" @change="changeScreen" >
                                                <el-option v-for="item in lists.screenNum" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                            </el-select>
                                    </div>
                                     <div v-else  style="display:flex;align-items:center">
                                          <span class="option_name" >屏幕数量：</span>
                                    <el-input placeholder="请输入屏幕数量" v-model="lists.screenCount"  style="display:inline-block" @input="changeNum"></el-input>
                                  </div> -->
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">安装日期：</span>
                                    <el-date-picker v-model="lists.installdate" size="small flex-1" type="date"
                                        placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                                    </el-date-picker>
                                </div>
                                <div class="selection_prompt line_feed">
                                    开业日期如在未来三天之内，请立刻联系广告公司，即使制作餐牌
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <!-- <span class="option_name">IE店：</span>
                                    <div class="flex-1" style=""><el-checkbox v-model="lists.isie" ></el-checkbox></div> -->
                                </div>
                            </div>
                        </div>
                        <div class="flex" style="justify-content:space-between;">
                            <!-- <div class="choose_wrap" >
                                <div class="choose_content" >
                                    <span class="option_name">甜品店：</span>
                                    <div class="flex-1" style="min-width:45px"><el-checkbox v-model="lists.isdessert" ></el-checkbox></div>
                                </div>
                            </div> -->

                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">是否有RSC维护：</span>
                                    <div class="flex-1">
                                        <el-checkbox v-model="lists.isrsc" @change="changeISsrc"></el-checkbox>
                                    </div>
                                </div>
                            </div>
                            <!-- 这两个单纯用来占位 -->
                            <div class="choose_wrap"></div>
                            <div class="choose_wrap"></div>
                        </div>

                    </div>
                </div>
                <!-- 营业属性 -->
                <div class="business_attribute">
                    <div class="content_title">营业属性</div>
                    <div class="business_attribute_content content_bg">
                        <div class="flex" style="justify-content:space-between;margin-bottom:20px">
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">开业日期：</span>
                                    <!--  :disabled='!canEdit'-->
                                    <el-date-picker v-model="lists.opendate" size="small flex-1" type="date"
                                        placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                                    </el-date-picker>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name" style="display:line-block;width:96px;">营业开始时间：</span>
                                    <el-time-picker v-model="lists.openstime" size="small flex-1" format="HH:mm"
                                        value-format="HH:mm"></el-time-picker>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name" style="display:line-block;width:96px;">营业结束时间：</span>
                                    <el-time-picker v-model="lists.openetime" size="small flex-1" format="HH:mm"
                                        value-format="HH:mm"></el-time-picker>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">是否24小时：</span>
                                    <div class="flex-1" style="">
                                        <el-checkbox v-model="lists.is24hour"></el-checkbox>
                                    </div>
                                </div>
                                <div class="selection_prompt line_feed">
                                    勾选此项，表示“晚餐结束”到“早餐开始”之间的时段放置夜宵菜单，否则放置“晚餐菜单”
                                </div>
                            </div>
                        </div>
                        <div class="flex" style="justify-content:space-between;">
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name">早餐开始：</span>
                                    <el-time-picker v-model="lists.breakfasttime" size="small flex-1" format="HH:mm"
                                        value-format="HH:mm"
                                        :picker-options="{ selectableRange: '00:00:00 - 23:59:59', }"></el-time-picker>
                                </div>
                                <div class="selection_prompt flex">
                                    <span style="width:82px;"></span>
                                    <span class="flex-1 line_feed">如果没有早餐菜单，此次无需修改</span>
                                </div>
                            </div>
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name"
                                        style="display:line-block;width:96px;text-align:right">午餐开始：</span>
                                    <el-time-picker v-model="lists.lunchtime" size="small flex-1" format="HH:mm"
                                        value-format="HH:mm"
                                        :picker-options="{ selectableRange: '00:00:00 - 23:59:59', }"></el-time-picker>
                                </div>
                                <div class="selection_prompt flex">
                                    <span style="width:110px;"></span>
                                    <span class="flex-1 line_feed">即早餐结束时间(售卖早餐餐厅)或开始营业时间(不售卖早餐餐厅)</span>
                                </div>
                            </div>

                            <!-- 晚餐 -->
                            <div class="choose_wrap">
                                <div class="choose_content">
                                    <span class="option_name"
                                        style="display:line-block;width:96px;text-align:right">晚餐开始：</span>
                                    <el-time-picker v-model="lists.dinnertime" size="small flex-1" format="HH:mm"
                                        value-format="HH:mm"
                                        :picker-options="{ selectableRange: '00:00:00 - 23:59:59', }"></el-time-picker>
                                </div>
                                <!-- <div class="selection_prompt flex">
                                    <span style="width:110px;"></span>
                                    <span class="flex-1 line_feed">如果没有夜宵菜单，此次无需修改</span>
                                </div> -->
                            </div>
                            <!-- <div class="choose_wrap" v-if="lists.is24hour">
                                <div class="choose_content">
                                    <span class="option_name" style="display:line-block;width:96px;text-align:right">夜宵开始：</span>
                                    <el-time-picker v-model="lists.yeSatrt" size="small flex-1" format="HH:mm"  value-format="HH:mm" :picker-options="{ selectableRange: '00:00:00 - 23:59:59',}"></el-time-picker>
                                </div>
                                <div class="selection_prompt flex">
                                    <span style="width:110px;"></span>
                                    <span class="flex-1 line_feed">如果没有夜宵菜单，此次无需修改</span>
                                </div>
                            </div> -->
                            <!-- 这个单纯用来占位 -->
                            <div class="choose_wrap"></div>
                        </div>
                    </div>
                </div>
                <!-- attrtags -->
                <div class="tags_settings" :style="{ 'margin-bottom': canEdit ? '0px' : '58px' }">
                    <div class="content_title">标签设置</div>
                    <!-- <div class="tags_settings_content content_bg"> -->
                    <!-- <div style="width:25%;margin-bottom:58px" > -->
                    <!-- <span>{{item}}</span>
                            <span><el-checkbox v-model="lists.checked" ></el-checkbox></span> -->
                    <!-- <el-checkbox-group v-model="lists.attrtags">
                                    <el-checkbox v-for="item,index in lists.checkBox" :label="item.label" :key="index" :value="item.value">{{item}}</el-checkbox>
                                </el-checkbox-group> -->
                    <!-- {{ lists.checkList }} -->
                    <el-checkbox-group v-model="lists.checkList" class="tags_settings_content content_bg">
                        <el-checkbox v-for="(item, index) in lists.setTags" :key="item.value" :label="item.value"
                            :value="item.value" style="margin-bottom:58px">{{ item.name }}</el-checkbox>
                        <!-- {{ item }} -->
                    </el-checkbox-group>

                    <!-- {{ lists.setTags }} -->
                    <!-- </div> -->
                    <!-- </div> -->
                </div>
            </div>
            <!-- 底部按钮 -->
            <!-- v-if="canEdit" -->
            <div class="btn_wrap flex">
                <el-button size="small" @click="cancel">返 回</el-button>
                <el-button size="small" type="primary" @click="handleConfirm">提 交</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { opeingnewstoremgmt } from "../../api/shopManage/shop"
import { datas_filter_cond } from "../../api/commonInterface"
import { removeDuplicateObj } from "@/utils/setArray"

export default {
    components: {

    },
    data() {
        return {
            lists: {
                opsmarketname: "",
                // 餐厅名称
                name: "",
                // 餐厅编号
                storecode: "",
                // 商圈
                jdetradezonecode: "",
                businessArr: [],
                // 市场
                itMarket: [],
                // 餐厅类型
                storesType: [],
                type: "",
                // 是否rsc
                isrsc: false,
                // 屏幕数量
                screenNum: [],
                // 甜品店
                isdessert: false,
                // IE店
                isie: false,
                // 安装日期
                installdate: "",
                // 开业日期
                opendate: '',
                // 营业开始时间
                openstime: '',//开业开始时间
                // 营业结束时间
                openetime: "",
                // 是否24小时
                is24hour: false,
                // 早餐开始
                breakfasttime: "",
                // 午餐开始 
                lunchtime: "",
                // attrtags
                setTags: [],
                checkList: [],
                screenCount1: 0,
                screenCount2: 0
                // 是否选中

            },
            tempEditArr: {
            },
            tempNumBefore: [],
            tempNumAfter: [],
            tempTags: [],
            infos: {
                act: "",
                message: ""
            },
            screenArr: "",
            disabled: false,
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        // 系统自建
        // this.$route.query.row.type_display
        console.log(this.$route, 'xxxxxxx');
        this.datas_filter_cond();


    },
    mounted() {

    },
    methods: {
        // 新增 市场
        datas_filter_cond() {
            const params = {
                "classModel": "OpeningNewStore",//
            }
            datas_filter_cond(params).then(res => {
                if (res.rst == 'ok') {
                    console.log(res, 'resxxx');
                    //  市场
                    res.data[0][0].options.forEach(item => {
                        // console.log(item,'1111111111');
                        this.lists.itMarket.push({
                            value: item[0],
                            label: item[1],
                        })
                        // console.log(this.lists.itMarket,'market');
                    })
                    this.tempNumBefore = res.data[0][2].options;
                    this.tempArr = res.data[0][3].options;
                    // if(this.lists.isrsc){
                    // 门店类型
                    // this.$set(this.lists,"this.lists.storesType",[])
                    res.data[0][2].options.forEach(item => {
                        // console.log(item[0],'ew');
                        this.lists.storesType.push({
                            label: item[1],
                            value: item[0]
                        })
                    })
                    // }else{
                    // this.lists.storesType = 
                    // rsc 门店
                    // this.$set(this.lists,"this.lists.storesType",[])
                    //  this.tempNumBefore.forEach(item=>{
                    // // console.log(item,'屏幕数量');
                    //     // 屏幕数量
                    //     this.lists.storesType.push({
                    //         label:item[1],
                    //         value:item[0],
                    //         })
                    //     })
                    // }
                    // attrtags
                    res.data[0][4].options.forEach(item => {
                        // console.log(item[0],'ll');
                        console.log(item, 'sss');
                        this.lists.setTags.push({
                            label: item[1],
                            value: item[1],
                            name: item[0],
                            id: item[1]
                        })
                    })
                    // 商圈
                    // storesType  storetypename
                    res.data[0][1].options.forEach(item => {
                        this.lists.businessArr.push({
                            label: item[1],
                            value: item[0]
                        })
                    })
                    //是否是编辑
                    // alert("编辑")
                    // if(this.lists.displaycnt!=''){

                    // }

                    console.log(this.lists.screenCount2, 'this.lists.screenCount2');
                    if (this.$route.query.edit) {
                        console.log(this.lists, '[000]');
                        this.infos.act = 'edit'
                        this.infos.message = '编辑成功'
                        this.lists = {
                            ...this.lists,
                            ...this.$route.query.row,
                        };
                        console.log(this.lists, '000');
                        if (this.lists.displaycnt) {
                            if (this.lists.displaycnt.split("+")) {
                                this.lists.screenCount1 = this.lists.displaycnt ? this.lists.displaycnt.split("+")[0] : 0
                                this.lists.screenCount2 = this.lists.displaycnt.split("+")[1] ? this.lists.displaycnt.split("+")[1] : 0
                            }
                        }
                        this.lists.screenCount2 == "undefined" ? 0 : 0
                        console.log(this.lists.screenCount2, 'this.lists.screenCount2 ');

                        console.log(this.$route.query.row, 'this.$route.query.row');
                        if (this.$route.query.row.type_display == "主档中台") {
                            this.disabled = true
                            this.lists.isrsc = false
                        } else {
                            // this.lists.isrsc = true
                            this.disabled = false
                            if (this.lists.isrsc) {
                                // this.$set(this.lists,"type",)
                            }
                        }
                        this.$set(this.lists, "type", this.lists.storetypename);
                        // 编辑 标签设置选中
                        if (this.lists.attrtags) {
                            this.lists.attrtags.forEach(item => {
                                // console.log(item,'item---');
                                this.lists.setTags.forEach(list => {
                                    // console.log(list,'list---');
                                    // setTags
                                    if (item.value == list.value) {
                                        this.lists.checkList.push(list.value)
                                    }
                                })
                            })
                        }
                        console.log(this.lists.checkList, 'this.lists.checkList');

                        // opsmarketname
                        if (this.lists.itMarket) {
                            this.lists.itMarket.forEach(item => {
                                if (this.lists.opsmarketname == item.label) {
                                    console.log("-----");
                                    this.lists.opsmarketname = item.value
                                }
                            })
                        }
                        // 餐厅类型
                        // this.tempNumAfter.forEach((item,index)=>{
                        //     // console.log(item,'itemitem12');
                        //     // console.log(item[0],'item[1]');
                        //     if(this.lists.type==item[1]){
                        //           item[2].forEach(num=>{
                        //             this.lists.screenNum.push({
                        //             label:num,
                        //             value:num
                        //             })
                        //         })
                        //     }
                        //     // 屏幕数量
                        //     this.$set(this.lists,"screenCount",this.lists.displaycnt)
                        // })
                        // 是否Rsc
                        if (!this.lists.isrsc) {
                            // storetypecode
                            // console.log(this.lists.storesType,'pp');
                            this.lists.storesType.forEach(item => {
                                // console.log(item,'01');
                                if (item.label == this.lists.storetypename) {
                                    this.lists.type = item.value;
                                }
                            })
                        }
                    } else {
                        this.infos.act = 'add'
                        this.infos.message = '新增成功'
                    }
                }
            })

        },
        // 市场
        changeMarket(val) {

        },
        // changeType餐厅类型
        changeType(val) {
            // this
            this.$set(this.lists, 'type', val)
            this.$set(this.lists, "screenCount", '')
            //    if(this.$route.query.edit){
            this.$set(this.lists, "screenNum", [])
            // }
            // this.lists.displaycnt = []
            this.tempNumAfter.forEach(item => {
                if (val == item[0]) {
                    item[2].forEach(list => {
                        this.lists.screenNum.push({
                            label: list,
                            value: list
                        })
                    })
                }
            })

            this.$forceUpdate()
            // this.$set(this.lists,"screenCount",screenCount)
        },
        // 是否rsc
        changeISsrc(val) {
            // false
            // if(!val){
            //      if(this.disabled){

            //      }else{
            //           // 门店类型
            //      this.$set(this.lists,"type","")
            //      this.$set(this.lists,"storesType",[]) 
            //         this.tempNumBefore.forEach(item=>{
            //         this.lists.storesType.push({
            //             label:item[1],
            //             value:item[0]
            //         })
            //     })
            //      }
            // console.log(val,'val21');
            //  // 门店类型
            //  this.$set(this.lists,"type","")
            //  this.$set(this.lists,"storesType",[]) 
            //     this.tempNumBefore.forEach(item=>{
            //     this.lists.storesType.push({
            //         label:item[1],
            //         value:item[0]
            //     })
            // })
            // }else{
            //     if(this.disabled){

            //     }else{
            //             this.$set(this.lists,"storesType",[])
            //      this.tempNumAfter.forEach(item=>{
            //     // console.log(item,'屏幕数量');
            //         // 屏幕数量
            //         this.lists.storesType.push({
            //             label:item[1],
            //             value:item[0],
            //         })
            //     })
            //     }
            //  this.$set(this.lists,"storesType",[])
            //  this.tempNumAfter.forEach(item=>{
            // // console.log(item,'屏幕数量');
            //     // 屏幕数量
            //     this.lists.storesType.push({
            //         label:item[1],
            //         value:item[0],
            //     })
            // })
            //  this.$set(this.lists,"type","")
            // }
            //  this.$set(this.lists,"screenCount","")
        },
        // 屏幕数量
        changeScreen(val) {
            this.$set(this.lists, "screenCount", val)
        },
        changeNum(val) {
            // console.log(val,'numVal');
            this.$set(this.lists, "screenCount", val)
        },
        // 取消
        cancel() {
            // this.$message.success('已取消');
            this.$router.go(-1);
            // this.lists = []
        },
        // 确认
        handleConfirm() {
            let attrTags = []
            let myTags = []
            console.log(this.lists.setTags, 'this.lists.setTags');
            this.lists.setTags.forEach((item, index) => {
                attrTags.push({
                    name: item.name,
                    id: index + 1,
                    value: item.value,
                })
            })
            // console.log(attrTags,'attrTagsattrTags');
            attrTags.forEach((item, index) => {
                this.lists.checkList.forEach(num => {
                    // console.log(num,'3123123');
                    console.log(num, item.name, 'item.value');
                    if (num == item.name) {
                        myTags.push({
                            name: item.name,
                            id: index + 1,
                            value: item.value,
                        })
                    }
                })
            })
            console.log(myTags, 'myTags');
            // this.$message.success('确认')
            let num = ""
            // if(this.lists.screenCount2==" " ||this.lists.screenCount2==0||this.lists.screenCount1==0 ||this.lists.screenCount1==" "){
            //     alert(1)
            //     num = this.lists.screenCount2
            // }else{
            console.log(this.lists.screenCount1, 'this.lists.screenCount1');
            if (this.lists.screenCount1 == 'undefined') {
                this.lists.screenCount1 = 0
                num = this.lists.screenCount1 + this.lists.screenCount2
            } else if (this.lists.screenCount2 == 'undefined') {
                this.lists.screenCount2 = 0
                num = this.lists.screenCount1 + this.lists.screenCount2
            }
            // else if (this.lists.screenCount1 == 0) {
            //     num = this.lists.screenCount2
            // } else if (this.lists.screenCount2 == 0) {
            //     num = this.lists.screenCount1
            // } 
            else {
                num = this.lists.screenCount1 + '+' + this.lists.screenCount2
            }
            console.log(num, "num");
            // }
            myTags = removeDuplicateObj(myTags)
            let originType = ""
            originType = this.lists.type_display == "系统自建" ? 1 : 2
            const params = {
                "act": this.infos.act,//必要,操作类型，"add": 新增；"edit":编辑，"del":删除，删除时只有参数"id"必须，"conform:代办确认，只有参数"id"必须
                "opsmarketcode": this.lists.opsmarketname, //必要,营运编号
                "storecode": this.lists.storecode, //必要,门店编号
                "name": this.lists.name, //必要,门店名称
                "jdetradezonecode": this.lists.jdetradezonecode, //必要,商圈编号
                "storetypecode": this.lists.type, //必要,门店类型编号
                "isrsc": this.lists.isrsc, //非必要,是否RSC
                "isie": this.lists.isie, //非必要,是否IE店
                "isdessert": this.lists.isdessert, //非必要,是否甜品店
                "displaycnt": num, //非必要,安装屏幕数量．RSC必填
                "installdate": this.lists.installdate,//非必要,安装日期
                "opendate": this.lists.opendate,//非必要,开业日期
                "openstime": this.lists.openstime,//非必要,开业开始时间
                "openetime": this.lists.openetime,//非必要,开业结束时间　　　　　
                "is24hour": this.lists.is24hour,//是否24小时店　
                "breakfasttime": this.lists.breakfasttime,
                "dinnertime": this.lists.dinnertime,
                "dinnertime": this.lists.dinnertime,
                "lunchtime": this.lists.lunchtime,
                "attrtags": myTags,
                "id": this.lists.id,
                "type": originType, // 数据来源类型, 1> 系统自建 2> 主档中台
                "type_display": this.lists.type_display, // 数据来源类型
            }
            console.log(params, '新开店params');
            opeingnewstoremgmt(params).then(res => {
                console.log(res, '新开店res');
                if (res.rst == 'ok') {
                    this.$message.success(this.infos.message);
                    // 跳转
                    this.$router.replace('/shopManage/openNewShop');
                    sessionStorage.setItem('activepath', '/openNewShop')
                    // 
                } else {
                    // this.$message.info("请检查各项数据")
                    // this.$message.info(res.error_msg)
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        message: res.error_msg
                    });
                }
            })
        },
        back() {
            this.$router.replace('/shopManage/openNewShop');
            sessionStorage.setItem('activepath', '/openNewShop')
        },
    },
};
</script>

<style lang="scss" scoped >
* {
    box-sizing: border-box;
}

.shop_add_edit {
    width: 100%;
    background-color: #F0F2F5;
}

/* 标题 */
.shop_add_edit_title {
    display: flex;
    /* justify-content: center; */
    align-items: center;
    padding: 0 20px;
    height: 58px;
    line-height: 58px;
    border-bottom: 1px solid rgba(217, 217, 217, 1);
}

.back_btn {
    margin-right: 15px;
}

/* 内容 */
.content_wrap {
    width: 100%;
    height: calc(100% - 58px);
    overflow-y: auto;
    flex-direction: column;

}

.introl_content {
    overflow: auto;
    padding: 0 26px 0 36px;
    min-width: 600px;
}

/*修改滚动条样式*/
.introl_content::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.introl_content::-webkit-scrollbar-track {
    background: rgba(167, 166, 166, 0.5);
    border-radius: 2px;
}

.introl_content::-webkit-scrollbar-thumb {
    background: rgba(141, 141, 141, 0.7);
    border-radius: 2px;
}

.introl_content::-webkit-scrollbar-thumb:hover {
    background: rgba(90, 90, 90, 1);
}

.btn_wrap {
    height: 93px;
    align-items: center;
    justify-content: flex-end;
    padding-right: 28px;
}

.content_bg {
    background-color: #fff;
    border-radius: 6px;
    font-size: 14px;
    width: 100%;
    min-width: 690px;
}

.content_title {
    width: 100%;
    height: 25px;
    font-size: 17px;
    margin-top: 20px;
    margin-bottom: 11px;
    font-weight: bold;
    color: rgba(83, 82, 82, 1);
}

.choose_wrap {
    display: flex;
    width: 23%;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start
}

.choose_content {
    display: flex;
    width: 100%;
    height: 32px;
    align-items: center;
    font-size: 14px;
}

.double {
    justify-content: space-around;
    padding: 0 5px !important;
}

.doubleS ::v-deep .el-input--small .el-input__inner {
    /* width: 145px !important; */
    width: 145px !important;
}

.double ::v-deep .el-input-number--mini {
    width: 80px !important;
}

.double ::v-deep .el-input-number--mini .el-input-number__increase,
.double ::v-deep .el-input-number--mini .el-input-number__decrease {
    width: 20px !important;
}

.double ::v-deep .el-input-number--mini .el-input__inner {
    padding-left: 23px !important;
    padding-right: 23px !important;
}

.selection_prompt {
    width: 100%;
    color: rgba(69, 67, 67, 1);
    font-size: 10px;
    margin-top: 5px;
    /* text-align: left; */
}

.option_name {
    margin-right: 11px;
    white-space: nowrap;

    &::before {
        content: '*';
        color: var(--text-color);
    }

}

.option_name .select {
    width: 100px !important;
}

/* 基本设置 */
.basic_settings_content {
    /* height: 289px; */
    padding: 45px 30px;
}

/* 营业属性 */
.business_attribute_content {
    /* height: 238px; */
    padding: 45px 30px;
}

/* attrtags */
.tags_settings_content {
    /* height: 269px; */
    /* display: flex; */
    /* flex-wrap: wrap; */
    padding: 45px 30px;
    padding-bottom: 0;
    /* height: 269px; */
    overflow: auto;
}

.tags_settings_content .el-checkbox {
    margin: 0 120px 0 0;
}

.tags_settings_content::-webkit-scrollbar {
    width: 2px;
    height: 2px;
}

.tags_settings_content::-webkit-scrollbar-track {
    background: rgba(202, 201, 201, 0.5);
    border-radius: 2px;
}

.tags_settings_content::-webkit-scrollbar-thumb {
    background: rgba(141, 141, 141, 0.7);
    border-radius: 2px;
}

.tags_settings_content::-webkit-scrollbar-thumb:hover {
    background: rgba(90, 90, 90, 1);
}

/* 文字超出两行显示省略号 */
.line_feed {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
</style>
<style>
.shop_add_edit .el-checkbox__input.is-checked .el-checkbox__inner,
.shop_add_edit .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--base-color) !important;
    border-color: var(--base-color) !important;
}
</style>
