<template>
    <div class='box'>
      <h4>
        <img src='../../assets/img/home_img/arrow.svg'>
        快捷投放/设置类型
      </h4>
      <div class='step'>
        <el-steps :active="active" align-center>
          <el-step title="设置类型"></el-step>
          <el-step title="选择内容"></el-step>
          <el-step title="发布设置"></el-step>
        </el-steps>
      </div>
      <main>
        <table>
          <tr class='timeSpace'>
            <th colspan='5'>
              <span>联屏组内容</span>
              <p>总播放时长55s</p>
            </th>
          </tr>
          <tr>
            <th>1
              <div>12.04s</div>
            </th>
            <td colspan='4'>
              <!-- <img src='../../assets/img/home_img/breakfast.png' :class="showBg?'alertBgImg':[]"> -->
              <div class='fourRadius'>
                <div @click.stop='toView'>
                  <i class='el-icon-view'></i>
                  <p>预览</p>
                </div>
                <div @click.stop='btnsClick(del)'>
                  <i class='el-icon-delete'></i>
                  <p>清空</p>
                </div>
                <div @click.stop='btnsClick(change)'>
                  <i class='el-icon-circle-plus-outline'></i>
                  <p>替换</p>
                </div>
              </div>
              <!--          预览视频-->
              <div class='viewDiv' v-show='viewDiv'>
                <!-- <img src='../../assets/img/home_img/audio.png'> -->
                <div class='audio'>
                  4:45
                  <span class='el-icon-caret-right'></span>
                  <el-slider v-model="value1" class="voice" :show-tooltip="false"></el-slider>
                  -2:56
                  <img src='../../assets/img/home_img/audio.svg'>
                </div>
                <div class='close' @click.stop='viewDiv=false'>×</div>
              </div>
            </td>
          </tr>
          <tr>
            <th>2
              <div>0s</div>
            </th>
            <td v-for='(item,index) in 4' :key='index' @click.stop='add'>
              <div class='add'>
                <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
                新增
              </div>
            </td>
          </tr>
          <tr>
            <th>3
              <div>0s</div>
            </th>
            <td v-for='(item,index) in 4' :key='index'>
              <div class='add' @click.stop='add'>
                <img src='../../assets/img/home_img/jia.svg' class='addImg'/>
                新增
              </div>
            </td>
          </tr>
        </table>
      </main>
    </div>
</template>

<script>
import ScreenContent from './screenContent'
export default {
  name: 'Selective',
  components: { ScreenContent },
  data(){
    return{
      active:2
    }
  }
}
</script>

<style scoped>
.box table{
  width: 1000px;
}
.box{
  padding:10px 15px 0 23px;
  background-color: #f8f7f7;
  width: 100%;
}
.box>h4{
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  font-weight: bold;
}
.box>h4>img{
  display: inline-block;
  width: 24px;
  vertical-align: middle;
  height: 24px;
  margin-right: 14px;
}
::v-deep .el-step__icon{
  /*background-color: rgba(108, 178, 255, 1);*/
  width: 32px;
  height: 32px;
}
::v-deep .el-step__title{
  /*color: rgba(108, 178, 255, 1);*/
  margin-left: -15px;
}
/*.el-steps*/
.step{
  width: 1253px;
  height: 63px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(216, 232, 250, 1);
  padding: 0 20px;
  font-size: 14px;
}
::v-deep .el-steps--horizontal{
  width: 800px;
  margin-left: 200px;
}
main{
  width: 1253px;
  height: 880px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: center;
  background-color: #fff;
  overflow: hidden;
}
main>.ScreenContent{
  margin:40px;
  width: 1170px;
}
.timeSpace{
  height: 50px;
}
table {
  font-size: 0.14rem;
  width: 9.5rem;
  height: 442px;
  background-color: rgba(255, 255, 255, 1);
  border-spacing: 0;
}

table > tr {
  width: 0.95rem;
  text-align: center;
}

th {
  border: 1px solid rgba(224, 224, 224, 0.99);
  width: 1rem;
  font-weight: 600;
  position: relative;
}

td {
  border: 0.01rem solid rgba(224, 224, 224, 0.99);
  position: relative;
}

table > .timeSpace {
  height: 0.4rem;
}

table > .timeSpace > th {
  font-weight: bold;
  font-size: 14px;
  overflow: hidden;
  position: relative;
}
.add {
  width: 0.51rem;
  height: 0.51rem;
  color: rgba(80, 80, 80, 1);
  border-radius: 0.26rem;
  font-size: 0.12rem;
  border: rgba(229, 229, 229, 1) solid 0.01rem;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
  margin: auto;
  line-height: 0.7rem;
  font-weight: bold;
}
.add > .addImg {
  width: 0.2rem;
  height: 0.2rem;
  position: absolute;
  margin: auto;
  left: 0;
  top: -10px;
  right: 0;
}

.addInfo {
  position: absolute;
  width: 88px;
  right: 0;
  margin-top: 15px;
}
table > .timeSpace > th > p {
  position: absolute;
  right: 10px;
  top: 11px;
  font-weight: 500;
}

td img {
  width: 843px;
  height: 123px;
  position: absolute;
  left: 0;
  top: 0;
  margin: auto;
  right: 0;
  bottom: 0;
  border-radius: 0;
}
.fourRadius {
  display: flex;
  align-items: center;
  z-index: 15;
  position: absolute;
  left: 0;
  top: 0;
  top: 0;
  width: 100%;
  justify-content: center;
  bottom: 0;
  margin: auto;
}

.fourRadius:hover > div {
  display: block;
}

.fourRadius > div {
  width: 52px;
  height: 52px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border: 1px solid #a2a3a3;
  border-radius: 50%;
  font-size: 12px;
  border: rgba(255, 255, 255, 1) solid 1px;
  margin: 0 10px;
  text-align: center;
  display: none;
  cursor: pointer;
}

.fourRadius > div > i {
  font-size: 15px;
  margin: 5px 0;
  color: #fff;
}

.fourRadius > div > p {
  color: rgba(255, 255, 255, 1);
  font-size: 12px;
}

th > div {
  width: 100%;
  height: 40px;
  line-height: 40px;
  color: #fff;
  background-color: rgba(42, 130, 228, 0.3571428571428571);
  font-size: 14px;
  text-align: center;
  position: absolute;
  bottom: 0;
  font-weight: 500;
}

</style>
