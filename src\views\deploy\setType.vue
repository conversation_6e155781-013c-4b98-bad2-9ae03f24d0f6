<template>
  <div class="setType">
    <div class="steps">
      <el-steps :active="active" simple finish-status="success">
        <el-step title="设置类型" icon="el-icon-setting"></el-step>
        <el-step title="选择内容"></el-step>
        <el-step title="发布设置" icon="el-icon-s-promotion"></el-step>
      </el-steps>
    </div>

    <div v-show="active == 0">
      <SetIsuueType @changeActive="changeActive" :nextActice="active" @next="next"></SetIsuueType>
    </div>

    <div v-if="active == 1">
      <!-- 二级路由-->
      <!-- <div class="myset"> -->
      <!-- <SetTypePlayer   /> -->
      <DmbCreate :active="active" v-if="componentActiveNo == 1 && isEditDMB == false" :dmb_spec="dmb_spec"
        :daypartgroup="daypartgroup" ref="DmbCreate" @toPreview="toPreview"></DmbCreate>
      <GeneralCreate :active="active" v-else-if="componentActiveNo == 2" :v_or_h="v_or_h" ref="GeneralCreate" />
      <ContentCreate :active="active" v-else-if="componentActiveNo == 3" :vs_spec="vs_spec" ref="ContentCreate">
      </ContentCreate>
      <!-- <FloatEle /> -->
      <!-- <ScreenContent :toPlayer="result" /> -->
      <createDmbTemplate v-if="isEditDMB != false" :ref_id="dmbRef_id" />
    </div>
    <div>
      <pushlishingSetting v-if="active == 2" ref="pushlishingSetting" @SearchState="SearchState"></pushlishingSetting>
    </div>
    <div></div>
    <div class="bottom">
      <div v-show="isEditDMB == false" style="margin-right: 10px;">
        <el-button @click="prev" v-show="active == 2"
          style="background-color:rgba(201, 227, 255, 0.39);color:rgba(121, 171, 227, 1)">返回上一步</el-button>
        <el-button @click="next" v-show="active == 1 && isNow != false && checkPer(['cm.cf.pubset'])">下一步</el-button>
      </div>
      <div v-if="$store.state.user.PUSCS_AUDIT_OPEN == 1">
        <el-button v-show="active == 2" @click="submitPushLish" :disabled="PushliState">提交</el-button>
      </div>
      <div v-else>
        <el-button v-show="active == 2" @click="publish" :disabled="PushliState">发布</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ScreenContent from "./screenContent";
import SetTypePlayer from "../deployCom/setTypePlayer.vue";
import GeneralCreate from "../deployCom/GeneralCreate.vue";
import DmbCreate from "../deployCom/DmbCreate";
import FloatEle from "../../components/DeployCom/FloatEle.vue";
import SetIsuueType from "./components/SetIsuueType.vue";
import ContentCreate from "../deployCom/ContentCreate.vue";
import pushlishingSetting from "./pushlishingSetting.vue";
import { filterPublishRole } from "@/utils/filterPublishRole.js";

import { get_shop_data } from "@/api/issue/issue";
import { create_btpub } from "@/api/contentdeploy/contentdeploy";
import { datas_filter_cond } from "@/api/commonInterface";
import {
  publish_area,
  publish_launch
} from "@/api/contentdeploy/contentdeploy";
import createDmbTemplate from "../deployComponent/CreateDmbTemplate.vue";
import { submit_btpub_review } from "@/api/audit/audit";
import store from "@/store/index";

export default {
  name: "setType",
  components: {
    SetTypePlayer,
    ScreenContent,
    FloatEle,
    GeneralCreate,
    DmbCreate,
    SetIsuueType,
    pushlishingSetting,
    ContentCreate,
    createDmbTemplate
  },
  data() {
    return {
      active: 0,
      leftActive: 0,
      componentActive: 1,
      isNow: false,
      screenDict: {},
      playerDict: { 轮播: 1, 独占: 2 },
      playMode: {
        按全天: "full_day",
        按星期: "week_range",
        // 按指定时段: "single_use_range"
        按指定时段: "time_range"
      },
      batchinfo: {},
      dataFilters: [],
      publishData: {
        btplan_id: "",
        sel_info: {
          opsmarkets: [], // 营运市场
          storetypes: [], // 店铺类型
          // shop_tags: [], // 店铺标签
          // shop_tags_rela: [], // 店铺标签关系
          exclude_shops: [] // 排除店铺id
        }
      },
      requestPublish: {
        btpub_id: "",
        action: "pub"
      },
      screen_type: "",
      v_or_h: "",
      dmb_spec: "",
      daypartgroup: "",
      submitState: false,
      // 发布状态
      PushliState: true,
      componentActiveNo: "",
      DmbRequestState: false,
      saveParams: null,
      isEditDMB: false,
      dmbRef_id: "",
      PushineStatus: ""
    };
  },
  watch: {
    active(newValue, oldValue) {
      this.active = newValue;
      if (newValue != 2) {
        this.isNow = true;
      }
    }
  },
  created() {
    this.active = localStorage.getItem("active")
      ? localStorage.getItem("active")
      : 0;
    this.componentActiveNo = sessionStorage.getItem("componentActiveNo")
      ? sessionStorage.getItem("componentActiveNo")
      : 1;
    console.log(store.state.user.user.PUSCS_AUDIT_OPEN, "PUSCS_AUDIT_OPEN");
  },
  mounted() { },
  methods: {
    next(params) {
      console.log(params, "params");
      if (!sessionStorage.getItem("btpub_id")) {
        this.create_btpub(params);
        this.saveParams = params;
      } else {
        console.log(this.active, "activeactiveactiveactive");
        console.log(this.componentActiveNo);
        // if (this.active == 0) {
        //   this.active = 1
        // }
        if (this.active == 1 && this.componentActiveNo == 1) {
          this.$refs.DmbCreate.timeframe.forEach(item => {
            item.addContentArray.forEach(item1 => {
              if (item1.imgUrl == "") {
                this.DmbRequestState = true;
              } else {
                this.DmbRequestState = false;
              }
            });
          });
          if (this.DmbRequestState == true) {
            this.$message.warning("内容不可为空");
            return;
          }
        } else if (this.active == 1 && this.componentActiveNo == 3) {
          console.log(this.$refs.ContentCreate.templateScene);
          // this.vs_spec = this.saveParams['vs_spec'];
          if (this.$refs.ContentCreate.templateScene.length != 0) {
            this.DmbRequestState = false;
            this.$refs.ContentCreate.templateScene.forEach(item => {
              item.tile_groups.forEach(item1 => {
                if (item1.percent_text == "等待添加内容...") {
                  this.DmbRequestState = true;
                } else {
                  this.DmbRequestState = false;
                }
              });
            });

            if (this.DmbRequestState == true) {
              this.$message.warning("内容不可为空");
              return;
            }
          } else {
            this.DmbRequestState = true;
            this.$message.warning("内容不可为空");
            return;
          }
          // this.active++
        } else if (this.active == 1 && this.componentActiveNo == 2) {
          console.log(this.saveParams, " this.saveParams");
          // this.v_or_h = this.saveParams["v_or_h"];
          // console.log(this.v_or_h,"v_or_h");
          console.log(this.$refs.GeneralCreate.cs_list);
          if (this.$refs.GeneralCreate.cs_list.length == 0) {
            this.DmbRequestState = true;
          } else {
            this.DmbRequestState = false;
          }

          if (this.DmbRequestState == true) {
            this.$message.warning("内容不可为空");
            return;
          }
        }
        this.active++;
        localStorage.setItem("active", this.active);
      }
    },

    getSelectDataList() {
      const params = {
        classModel: "ContentPub" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        console.log(res["data"], "res");
        this.$store.commit("changeDataFilters", res["data"][0]);
      });
    },
    changeDate(oldDate) {
      var dateee = new Date(oldDate).toJSON(); //这个ie不兼容，会返回1970年的时间
      var date = new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, " ")
        .replace(/\.[\d]{3}Z/, "");
      return date;
    },

    create_btpub(selectinfo) {
      this.submitState = false;
      console.log(selectinfo, "selectinfo");
      console.log(
        this.changeDate(selectinfo["timging"][0]),
        'this.changeDate(selectinfo["timging"][0])'
      );
      const playerState = selectinfo["playerState"] == "轮播" ? 1 : 2;
      let params = {
        name: selectinfo["launchName"], //（string）optional 发布计划名称
        platform: 3, //（int）required 发布平台 2: dmb_plat; 3: general_plat; 4: vs_plat
        func_type: 1, //（int）required 计划类型 1: 投放; 2: 策略
        start_time:
          this.changeDate(selectinfo["timging"][0]) == "1970-01-01 08:00:00"
            ? ""
            : this.changeDate(selectinfo["timging"][0]), //（string）required, 开始时间
        end_time:
          this.changeDate(selectinfo["timging"][1]) == "1970-01-01 08:00:00"
            ? ""
            : this.changeDate(selectinfo["timging"][1]), //（string）required, 结束时间
        play_style: playerState, //（int）required 播放类型 1: 轮播  2: 独占
        play_info: {
          // (dict) required, 具体看play_info说明
        }
      };
      console.log(params, "=][=");
      console.log(this.componentActive);
      switch (this.componentActive) {
        case 1:
          console.log(
            typeof selectinfo["daypartgroup"],
            ' typeof selectinfo["daypartgroup"]'
          );
          params.play_info.daypartgroup =
            typeof selectinfo["daypartgroup"] == "object"
              ? selectinfo["daypartgroup"][0]
              : selectinfo["daypartgroup"];
          this.daypartgroup = params.play_info.daypartgroup;
          // console.log(
          //   "selectinfo['daypartgroup'][0]",
          //   selectinfo["daypartgroup"][0]
          // );
          console.log(
            params.play_info.daypartgroup,
            "params.play_info.daypartgroup"
          );
          localStorage.setItem(
            "daypartgroup",
            params.play_info.daypartgroup ? params.play_info.daypartgroup : ""
          );
          // localStorage.setItem(
          //   "daypartgroup",
          //   selectinfo["daypartgroup"][0] != undefined
          //     ? selectinfo["daypartgroup"][0]
          //     : ""
          // );

          this.dmb_spec = selectinfo["dmb_spec"];
          params.dmb_spec = selectinfo["dmb_spec"]; // (string) required dmb规格的时候
          break;
        case 2:
          const screen_type = selectinfo["screen_type"];
          this.screen_type = screen_type;
          this.v_or_h = selectinfo["v_or_h"];
          console.log(this.v_or_h, " this.v_or_h");
          params.play_info.v_or_h = selectinfo["v_or_h"];
          params.play_info.play_mode = this.playMode[selectinfo["detaliRadio"]];
          switch (selectinfo["detaliRadio"]) {
            case "按全天":
              break;
            case "按星期":
              params.play_info.week_day = [];
              selectinfo["weekCheckout"].forEach(item => {
                params.play_info.week_day.push(Number(item));
              });
              params.play_info.time_ranges = [
                `${selectinfo["time_ranges"][0]}~${selectinfo["time_ranges"][1]}`
              ];
              break;
            case "按指定时段":
              console.log(selectinfo["genearlTimeing"], "genearlTimeing");
              // params.play_info.time_ranges = [
              //   `${dayjs(selectinfo["genearlTimeing"][0]).format(
              //     "YYYY-MM-DD HH:mm:ss"
              //   )}~${dayjs(selectinfo["genearlTimeing"][1]).format(
              //     "YYYY-MM-DD HH:mm:ss"
              //   )}`
              // ];
              // selectinfo["genearlTimeing"].forEach(item => {
              //   params.play_info.time_ranges.push(new Date().toLocaleDateString().replaceAll("/", "-") + " " + item);
              // })
              params.play_info.time_ranges = [
                `${selectinfo["genearlTimeing"][0]}~${selectinfo["genearlTimeing"][1]}`
              ];

              console.log(params.play_info.time_ranges);
            default:
              break;
          }
          params.usage_type = selectinfo["screen_type"]; //（string）required 屏幕类型
          break;
        case 3:
          params.vs_spec = selectinfo["vs_spec"];
          this.vs_spec = selectinfo["vs_spec"];
          console.log("selectinfo", selectinfo);
          sessionStorage.removeItem("vs_spec");
          sessionStorage.setItem("vs_spec", this.vs_spec);
          params.play_info.play_mode = this.playMode[selectinfo["detaliRadio"]];
          params.play_info.waitting_time = selectinfo["waitting_time"];
          params.play_info.scene_type = selectinfo["scene_type"];
          console.log(selectinfo, "selectinfo");
          switch (selectinfo["detaliRadio"]) {
            case "按全天":
              break;
            case "按星期":
              params.play_info.week_range = [];
              selectinfo["weekCheckout"].forEach(item => {
                switch (item) {
                  case "日":
                    item = 1;
                    break;
                  case "一":
                    item = 2;
                    break;
                  case "二":
                    item = 3;
                    break;
                  case "三":
                    item = 4;
                    break;
                  case "四":
                    item = 5;
                    break;
                  case "五":
                    item = 6;
                    break;
                  case "六":
                    item = 7;
                    break;

                  default:
                    break;
                }
                params.play_info.week_range.push(
                  `${item}|${selectinfo["time_ranges"]}`.replace(",", "~")
                );
              });
              params.play_info.week_range.forEach(item => {
                if (item[2] == " ") {
                  this.submitState = true;
                }
              });
              break;
            case "按指定时段":
              console.log(selectinfo["connectTimeing"]);
              // params.play_info.single_use_range = [];
              params.play_info.time_ranges = [];
              // params.play_info.single_use_range =
              //   dayjs(selectinfo["connectTimeing"][0]).format(
              //     "YYYY-MM-DD HH:mm:ss"
              //   ) +
              //   "~" +
              //   dayjs(selectinfo["connectTimeing"][1]).format(
              //     "YYYY-MM-DD HH:mm:ss"
              //   );

              params.play_info.time_range = `${selectinfo["connectTimeing"][0]}~${selectinfo["connectTimeing"][1]}`
              // selectinfo["connectTimeing"].forEach(item => {
              //   params.play_info.single_use_range.push(new Date().toLocaleDateString().replaceAll("/", "-") + " " + item);
              // })
              console.log(params.play_info.single_use_range);
            default:
              break;
          }
          break;
        default:
          break;
      }

      switch (this.componentActive) {
        case 1:
          params["platform"] = 2; //  dmb
          Reflect.deleteProperty(params.play_info, "v_or_h");
          Reflect.deleteProperty(params.play_info, "play_mode");
          Reflect.deleteProperty(params.play_info, "waitting_time");
          Reflect.deleteProperty(params, "usage_type");
          break;
        case 2:
          params["platform"] = 3; // 普通
          console.log(params);
          Reflect.deleteProperty(params.play_info, "daypartgroup");
          Reflect.deleteProperty(params.play_info, "waitting_time");
          Reflect.deleteProperty(params, "dmb_spec");
          break;
        case 3:
          params["platform"] = 4; // 联屏
          break;
        default:
          break;
      }

      console.log(params);

      this.submitStat = false;

      for (const key in params) {
        if (Object.hasOwnProperty.call(params, key)) {
          const element = params[key];
          console.log(element, "element");
          if (element === "") {
            this.submitState = true;
          }
          if (params.play_info) {
            for (const key1 in params.play_info) {
              const element1 = params.play_info[key1];
              console.log(element1, "element1");
              if (element1 === undefined || element1 === "") {
                this.submitState = true;
              }
            }
          }
        }
      }
      console.log("submitState", this.submitState);
      if (this.submitState != true) {
        create_btpub(params)
          .then(res => {
            console.log("res", res);
            if (res.rst == "ok") {
              this.$store.commit("changeDeployInfo", res["data"][0]);
              this.$message.success("创建成功");
              this.batchinfo = this.$store.state.deployCfInfo;
              sessionStorage.removeItem("btpub_id");
              sessionStorage.removeItem("ref_id");
              sessionStorage.removeItem("btplan_id");
              sessionStorage.setItem("btpub_id", res["data"][0].btpub_id);
              sessionStorage.setItem("btplan_id", res["data"][0].btplan_id);
              sessionStorage.setItem("ref_id", res["data"][0].ref_id);
              this.active++;
              localStorage.setItem("active", this.active);
              this.isNow = true;
            } else {
              this.$message.error(res["error_msg"]);
            }
          })
          .then(() => {
            params["componentActive"] = this.componentActive;
            //  this.$store.commit("changeDeploySetDetail", params)
          });
      } else {
        this.$message.warning("请选择内容");
      }
      console.log("params", params);
    },
    // 左侧点击
    changeActive(value) {
      this.componentActive = value;
      sessionStorage.setItem("componentActiveNo", this.componentActive);
      this.componentActiveNo = sessionStorage.getItem("componentActiveNo");
      sessionStorage.removeItem("btpub_id");
    },
    prev() {
      if (this.active == 0) {
        return;
      }
      this.active--;
      localStorage.setItem("active", this.active);
    },
    submitPushLish() {
      console.log(this.componentActive, "isEditDMB");
      this.publishData.btplan_id = sessionStorage.getItem("btplan_id");
      this.requestPublish.btpub_id = sessionStorage.getItem("btpub_id");
      this.publishData.sel_info.opsmarkets = this.$refs.pushlishingSetting.queryList.marketname;
      this.publishData.sel_info.storetypes = this.$refs.pushlishingSetting.queryList.shopname;
      this.publishData.sel_info.screen_tags = this.$refs.pushlishingSetting.screenTagsValue;
      this.publishData.sel_info.shop_tags = this.$refs.pushlishingSetting.shopTagsValue;
      this.publishData.sel_info.exclude_shops = this.$refs.pushlishingSetting.noSelectShops;
      this.publishData.sel_info.shop_tags_rela = this.$refs.pushlishingSetting.shop_tags_rela;
      this.publishData.sel_info.screen_tags_rela = this.$refs.pushlishingSetting.screen_tags_rela;
      if (this.componentActiveNo == 1) {
        this.publishData.sel_info.dpfrom = 3;
        this.publishData.sel_info.storeplaylayout = this.$refs.pushlishingSetting.storeplaylayoutSelected
      }
      publish_area(this.publishData).then(res => {
        if (res.rst == "ok") {
          this.requestPublish.save_strategy = 0;
          let params = {
            btpub_id: sessionStorage.getItem("btpub_id")
          };
          submit_btpub_review(params).then(res => {
            console.log(res, "res");
            if (res.rst == "ok") {
              this.$message.success("发布审核提交成功");
              this.$router.push({
                path: "/deploy/quickHistory"
              });
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        }
      });
    },
    publish() {
      console.log(this.componentActive, "isEditDMB");
      this.publishData.btplan_id = sessionStorage.getItem("btplan_id");
      this.requestPublish.btpub_id = sessionStorage.getItem("btpub_id");
      this.publishData.sel_info.opsmarkets = this.$refs.pushlishingSetting.queryList.marketname;
      this.publishData.sel_info.storetypes = this.$refs.pushlishingSetting.queryList.shopname;
      this.publishData.sel_info.screen_tags = this.$refs.pushlishingSetting.screenTagsValue;
      this.publishData.sel_info.shop_tags = this.$refs.pushlishingSetting.shopTagsValue;
      this.publishData.sel_info.exclude_shops = this.$refs.pushlishingSetting.noSelectShops;
      this.publishData.sel_info.shop_tags_rela = this.$refs.pushlishingSetting.shop_tags_rela;
      this.publishData.sel_info.screen_tags_rela = this.$refs.pushlishingSetting.screen_tags_rela;
      if (this.componentActiveNo == 1) {
        this.publishData.sel_info.dpfrom = 3;
        this.publishData.sel_info.storeplaylayout = this.$refs.pushlishingSetting.storeplaylayoutSelected
      }
      publish_area(this.publishData).then(res => {
        if (res.rst == "ok") {
          if (this.PushineStatus == 1) {
            this.$confirm("保存策略并发布投放?", "提示", {
              confirmButtonText: "保存策略",
              cancelButtonText: "不保存",
              type: "warning"
            })
              .then(() => {
                this.requestPublish.save_strategy = 1;
                publish_launch(this.requestPublish).then(ress => {
                  if (ress.rst == "ok") {
                    this.$message.success("内容发布成功");
                    localStorage.setItem("active", 0);
                    localStorage.removeItem("btpub_id");
                    this.$router.push({
                      path: "/deploy/quickHistory"
                    });
                  } else if (ress.error_msg === "正在发布中...") {
                    this.$router.push({
                      path: "/deploy/quickHistory"
                    });
                  } else {
                    this.$message.warning(ress.error_msg);
                  }
                });
              })
              .catch(() => {
                this.requestPublish.save_strategy = 0;
                publish_launch(this.requestPublish).then(ress => {
                  if (ress.rst == "ok") {
                    this.$message.success("内容发布成功");
                    localStorage.setItem("active", 0);
                    localStorage.removeItem("btpub_id");
                    this.$router.push({
                      path: "/deploy/quickHistory"
                    });
                  } else if (ress.error_msg === "正在发布中...") {
                    this.$router.push({
                      path: "/deploy/quickHistory"
                    });
                  } else {
                    this.$message.warning(ress.error_msg);
                  }
                });
              });
          } else {
            this.requestPublish.save_strategy = 0;
            publish_launch(this.requestPublish).then(ress => {
              if (ress.rst == "ok") {
                this.$message.success("内容发布成功");
                localStorage.setItem("active", 0);
                localStorage.removeItem("btpub_id");
                this.$router.push({
                  path: "/deploy/quickHistory"
                });
              } else if (ress.error_msg === "正在发布中...") {
                this.$router.push({
                  path: "/deploy/quickHistory"
                });
              } else {
                this.$message.warning(ress.error_msg);
              }
            });
          }
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    // 获取经营时段
    getDayPart() { },
    SearchState(shopsList) {
      console.log(shopsList);
      if (shopsList != 0) {
        this.PushliState = false;
      } else {
        this.PushliState = true;
      }
    },
    toPreview(val) {
      console.log(val);
      this.dmbRef_id = val;
      console.log(this.dmbRef_id);
      this.isEditDMB = true;
    },
    roleFilter(value) {
      return filterPublishRole(value);
    }
  },
  destroyed() {
    console.log("destroyed");
    sessionStorage.setItem("componentActiveNo", 1);
    sessionStorage.removeItem("btpub_id");
    sessionStorage.removeItem("ref_id");
    sessionStorage.removeItem("btplan_id");
    setTimeout(() => {
      console.log("...");
      localStorage.setItem("active", 0);
    }, 200);
  }
};
</script>

<style lang="scss" scoped>
.myset {
  height: 100%;
}

::v-deep .el-step__title.is-process {
  color: #fff !important;
}

.setType {
  padding: 10px 15px 0 23px;
  /*background-color: #f8f7f7;*/
  width: 100%;
  position: relative;
  height: 100%;

  .steps {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-top: 20px;

    .el-steps {
      width: 100%;
      padding-left: 10%;
      padding-right: 10%;
      background-color: var(--text-color-light) !important;
      color: #fff;
    }

    ::v-deep .el-step__title.is-wait {
      color: #fff !important;
    }

    ::v-deep .el-steps .el-step__icon-inner {
      color: #fff !important;
    }

    ::v-deep .el-step__title.is-success {
      color: #fff !important;
    }
  }

  .bottom {
    width: 100%;
    text-align: center;
    height: 50px;
    margin-top: 20px;
    display: flex;
    justify-content: center;

    button {
      width: 181px;
      height: 48px;
      color: white;
      font-weight: bold;
      background-color: var(--btn-background-color);
      border-radius: 10px;
    }
  }
}
</style>