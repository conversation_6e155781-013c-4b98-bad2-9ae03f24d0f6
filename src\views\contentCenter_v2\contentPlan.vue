<template>
  <div class="box">
    <div class="left tag_set">
      <el-tabs :tab-position="tabPosition" v-model="activeName">
        <el-tab-pane label="模板" class="control" name="template" v-if="isEdit == false">
          <div slot="label" class="slot">
            <i class="el-icon-menu"></i> <span>模板</span>
          </div>
          <contentModel @SelectTemplate="SelectTemplate" />
        </el-tab-pane>
        <el-tab-pane label="资源" name="resource">
          <div slot="label" class="slot">
            <i class="el-icon-picture"></i> <span>资源</span>
          </div>
          <ContentSources @selectImage="selectImage" />
        </el-tab-pane>
        <el-tab-pane label="价签" name="pricetag">
          <div slot="label" class="slot">
            <i class="el-icon-collection-tag"></i> <span>价签</span>
          </div>
          <ContentPriceTag ref="ContentPriceTag" @addTags="addTags" @changeColor="changeColor"
            @getPriceText="getPriceText" @changePricePosition="changePricePosition" @setAssist="setAssist"
            @itsPrice="itsPrice" @changeBorderValue="changeBorderValue" @ChangeoutlineValue="ChangeoutlineValue"
            @changeLineSize='changeLineSize' @ChangeColorValue="ChangeColorValue" @clearoutLine="clearoutLine"
            @clearcolor="clearcolor" />
        </el-tab-pane>
        <!-- <el-tab-pane label="元素">
          <div slot="label" class="element">
            <i class="el-icon-s-release"></i> <span>元素</span>
          </div>
          <ContentEle />
        </el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="right">
      <!-- {{  iframeUrl}} -->
      <ContentRight :templateType="templateType" :direction="direction" @toResource="toResource"
        :priceTagType="priceTagType" :templateOnePrice="templateOnePrice" :priceState="priceState" :imgsList="imgsList"
        :fileType="FileType" :tpl_id="tpl_id" :iframeUrl="iframeUrl" v-if="title != '编辑'" :price_color="tags_color"
        :assist="assist" :priceText="priceText" ref="ContentRight" />
      <EditRight :templateType="templateType" :direction="direction" @toResource="toResource" :priceTagType="priceTagType"
        :templateOnePrice="templateOnePrice" :priceState="priceState" :imgsList="imgsList" :fileType="FileType"
        :tpl_id="tpl_id" :iframeUrl="iframeUrl" :price_color="tags_color" :assist="assist" :priceText="priceText"
        ref="EditRight" :html_str="html_str" v-else>
      </EditRight>
    </div>
  </div>
</template>

<script>
import ContentEle from "../../components/ContentCenter/contentEle.vue";
import contentModel from "../../components/ContentCenter/contentModel.vue";
import ContentPriceTag from "../../components/ContentCenter/contentPriceTag.vue";
import ContentSources from "../../components/ContentCenter/contentSources.vue";
import ContentRight from "./contentRight.vue";
import EditRight from "./EditRight.vue";
import { get_gongfang_tpl } from "@/api/template/index";
import $ from "jquery"
export default {
  components: {
    contentModel,
    ContentSources,
    ContentPriceTag,
    ContentEle,
    ContentRight,
    EditRight
  },
  data() {
    return {
      // tabs 定位
      tabPosition: "left",
      // 模板类型 0横向第一 1横向第二 2竖向第一
      templateType: null,
      // 模板方向
      direction: null,
      // class
      activeName: "template",
      // 模板的第几张图
      imgIndex: 0,
      // 价签类型 0 / 1
      priceTagType: null,
      // 模板一价签
      templateOnePrice: [],
      // 价签是否显示
      // priceState: false,
      imgsList: '',
      FileType: '',
      tpl_id: "",
      iframeUrl: "",
      isEdit: false,
      html_str: "",
      cs_id: "",
      title: "",
      tags_color: '#ffffff',
      fontNum: "",
      assist: '',
      priceText: '',
      selectChangeWath: null,
      selectIts: null,
      borderValue: '',
      outlineValue: '',
      LineSize: '',
      className: '',
      colorValue: '',
      current: ''
    };
  },
  created() {
    console.log(this.$route.query);
    let that = this;
    if (this.$route.query.title == "编辑") {
      console.log(this.$route.query.templateUrl);
      this.iframeUrl = this.$route.query.templateUrl.replace("do_type=play", 'do_type=edit');
      this.iframeUrl = this.iframeUrl.replace("10.65", '100.165');
      this.html_str = this.$route.query.pricehtml
      console.log(this.iframeUrl, 'xxxxxx');
      this.activeName = "resource"
      this.title = this.$route.query.title
      this.isEdit = true;
      this.cs_id = this.$route.query.cs_id;
      this.tpl_v_or_h = this.$route.query.tpl_v_or_h == 'horizontal' ? 'h' : 'v'
      this.direction = this.$rotue.query.tel_v_or_h == 'horizontal' ? 'across' : ''
      window.getType = function () {
        return "编辑"
      }
      window.setPrice = function () {
        return that.html_str
      }
    } else {
      window.getType = function () {
        return "新增"
      }
    }
  },
  mounted() {
    let that = this;
    this.SelectTemplate(0,'across')
    window.changePricePositionx = function () {
      console.log(that.selectChangeWath, 'this.selectChangeWath;');
      return that.selectChangeWath;
    };
    window.changeItsPricex = function () {
      return that.selectIts;
    }
    window.addEventListener('message', function (event) {
      //此处执行事件
      that.className = event.data.className;
      that.current = event.data.type;
    })

    window.settingBorder = function () {
      return {
        borderValue: that.borderValue,
        className: that.className,
        current: that.current
      };
    }
    window.settingOutLineValue = function () {
      return {
        outlineValue: that.outlineValue,
        className: that.className
      };
    },
      window.settingLineSize = function () {
        return {
          className: that.className,
          lineSize: that.LineSize,
          outlineValue: that.outlineValue,
        }
      },
      window.settingBackColor = function () {
        return {
          current: that.current,
          colorValue: that.colorValue,
          className: that.className
        }
      }
  },
  watch: {
    tags_color(newValue, oldValue) {
      console.log(newValue, 'newValue');
      this.tags_color = newValue
    }
  },
  methods: {
    /**
     * 选择模板
     * @param type 0 横向单屏
     * @param direction 横竖
     */
    SelectTemplate(type, direction) {
      let params = {
        v_h: ""
      }
      this.templateType = type;
      if (direction == "across") {
        params["v_h"] = 0;
        if (type == 0) {
          get_gongfang_tpl(params).then(res => {
            res.data[0]['content'].forEach(item => {
              console.log(item, 'item');
              // if (item.name == 'HD_welposterH_0001') {
              if (item.name == 'HD_video_tplH_0009242') {
                console.log(item,'item');
                this.iframeUrl = item['anim_url'].replace("10.65", "100.165") + '&tpl_view=1'
                this.tpl_id = item["tpl_id"];
              }
            })
            console.log(this.iframeUrl, 'iframeUrl');
          })
        } else if (type == 1) {
          get_gongfang_tpl(params).then(res => {
            this.tpl_id = res["data"][0]["content"][0]["tpl_id"];
            this.iframeUrl = res['data'][0]['content'][0]['anim_url'].replace("10.65", "100.165") + '&tpl_view=1'
          })
        }
      } else {
        params["v_h"] = 1;
        if (type == 2) {
          get_gongfang_tpl(params).then(res => {
            this.tpl_id = res["data"][0]["content"][0]["tpl_id"];
            this.iframeUrl = res['data'][0]['content'][0]['anim_url'].replace("10.65", "100.165") + '&tpl_view=1'
            console.log(this.iframeUrl);
          })
        }
      }
      this.$refs.ContentRight.zoom = $(window).width() / 1920 / 1.5;
      // this.zoom = $('#app').width() / 1920 / 2
      // this.$refs.ContentRight.iframeWidth = 1920 + "px";
      // this.$refs.ContentRight.iframeHeight = 1080 + "px";
      // this.$refs.ContentRight.$refs.iframe.style['transform'] = `scale(${this.$refs.ContentRight.zoom})`
      this.direction = direction;
      console.log(type, "type", direction, "direction");
      this.templateOnePrice = [];
      this.activeName = 'resource'

    },
    /**
     * 跳转到资源
     */
    toResource(index) {
      // this.activeName = "resource";
      this.imgIndex = index;
    },
    /**
     * 选择图片
     */
    selectImage(imgUrl, fileType, activeName) {
      console.log(fileType);
      this.imgsList = imgUrl;
      this.FileType = fileType
    },
    /**
     * 选择价签
     * @param type 0: 第一种价签
     * @param type 1: 第二种价签
     */
    addTags(type) {
      this.priceTagType = type;
      this.priceState = true;
      console.log(this.priceState);
      if (type == 0) {
        this.templateOnePrice.push({
          price: "00",
          priceState: true,
          settingPriceState: false,
          templateId: null,
          price1: "00",
          type: "first",
        });
      } else if (type == 1) {
        this.templateOnePrice.push({
          price: "00",
          priceState: true,
          settingPriceState: false,
          templateId: null,
          price1: "00",
          type: "second",
        });
      }
    },
    changeColor(color) {
      console.log(color);
      this.tags_color = color;
    },



    // 设置辅助样式
    setAssist(assist) {
      if (assist == '无') {
        this.assist = ''
      } else {
        this.assist = assist;
      }

      if (this.$route.query.title == '编辑') {
        if (this.$refs.EditRight.current == 'current') {
          this.$refs.EditRight.$refs.iframe.contentWindow.setSymbol();
        } else {
        }
      } else {
        if (this.$refs.ContentRight.current == 'current') {
          this.$refs.ContentRight.$refs.iframe.contentWindow.setSymbol();
        } else {

        }
      }
    },

    // 设置价格
    getPriceText(price) {
      this.priceText = price;
    },

    // 价签定位
    changePricePosition(item) {
      this.selectChangeWath = item;
      console.log(this.selectChangeWath, 'this.selectChangeWath ');
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.changePricePosition();
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.changePricePosition();
      }
      // this.$refs.ContentRight.$refs.iframe.contentWindow.changePricePosition();
    },

    itsPrice(item) {
      console.log(item, 'item');
      this.selectIts = item;
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.changeItsPrice();
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.changeItsPrice();
      }
      // if (this.$refs.ContentRight.current == "current") {
      // this.$refs.ContentRight.$refs.iframe.contentWindow.changeItsPrice();
      // }
    },
    changeBorderValue(borderValue) {
      this.borderValue = borderValue;
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.settingBorder();
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.settingBorder();
      }
      // this.$refs.ContentRight.$refs.iframe.contentWindow.settingBorder()
    },
    ChangeoutlineValue(outlineValue) {
      this.outlineValue = outlineValue;
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.settingOutlineValue();
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.settingOutlineValue();
      }
      // this.$refs.ContentRight.$refs.iframe.contentWindow.settingOutlineValue()
    },
    changeLineSize(linesize) {
      console.log(linesize, 'linesize');
      this.LineSize = linesize;
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.settingOutLineSize()
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.settingOutLineSize()
      }
    },
    ChangeColorValue(colorValue) {
      this.colorValue = colorValue;

      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.settingBackColor()
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.settingBackColor()
      }
      // if (this.$refs.ContentRight.current == 'classPrice') {
      //   this.$refs.ContentRight.$refs.iframe.contentWindow.settingBackColor()
      // }
    },
    clearoutLine() {
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.clearLine()
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.clearLine()
      }
    },
    clearcolor() {
      if (this.$route.query.title == '编辑') {
        this.$refs.EditRight.$refs.iframe.contentWindow.clearBack(this.current)
      } else {
        this.$refs.ContentRight.$refs.iframe.contentWindow.clearBack(this.current)
      }
    }
  },

};
</script>

<style scoped>
.box {
  height: 100%;
  display: flex;
}

.box>.left {
  height: 100%;
  background: rgba(29, 29, 29, 1);
}

.box .tag_set ::v-deep .el-tabs {
  /* width: 460px; */
  display: flex;
  height: 100%;
}

.left ::v-deep .is-left {
  font-size: 16px;
  text-align: center;
  margin-right: 0 !important;
}

.left .slot {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.left ::v-deep .el-tabs--left .el-tabs__item.is-left {
  height: 84px;
  margin: 20px 0;
}

.left .slot>i {
  display: block;
  font-size: 30px;
}

.left .slot span {
  color: #fff;
}

/* 左侧切换的样式 */
.left ::v-deep .el-tabs__content {
  background: rgba(56, 56, 56, 1);
  color: #fff;
  height: 100%;
  width: 308px;
}

.right {
  width: 100%;
  height: 100%;
}
</style>
<style>
.tab_set .el-tabs__item .is-left {
  background-color: var(--active-color) !important;
}

/* tabs选中的样式 */
.tag_set .is-active {
  color: var(--text-color) !important;
  background-color: transparent !important;
  /* background-color: var(--active-color) !important; */
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-across {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-vertical {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
  /* display: none; */
  background-color: transparent !important;
  width: 0;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
  color: var(--text-color) !important;
}

.el-tabs--left .el-tabs__nav-wrap.is-left::after {
  left: -3px !important;
  flex-direction: column;
}

/* .el-tabs__item{
  color:white !important;
} */
</style>