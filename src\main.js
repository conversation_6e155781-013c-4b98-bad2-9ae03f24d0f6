import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css'
import '../src/assets/css/common.css'
import '../src/assets/css/element-theme/index.css'
import './assets/css/base.css'
import Element from 'element-ui'
import i18n from './i18n/index.js';
//
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// 数据字典
import dict from './components/Dict'

// 权限指令
import checkPer from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'

// 代码高亮
import VueHighlightJS from 'vue-highlightjs'
import 'highlight.js/styles/atom-one-dark.css'

import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control
import 'echarts-gl'
import api from '../src/utils/api.js' // 导入api接口

// 引入全局防抖
import Debounce from './utils/debounce' // 防抖自定义指令
Debounce(Vue)
// 引入日期格式化
import '@/utils/dateFormat'

import Loadmore from  './utils/loadmore.js'

Vue.use(Loadmore)


Vue.prototype.$EventBus = new Vue()
//引入并注册search组件
import Search from '@/components/communal/search'
Vue.component('Search',Search)

// 引入字体文件
import "@/font/font.css"

Vue.prototype.$api = api
Vue.use(checkPer)
Vue.use(VueHighlightJS)
Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)

Vue.config.silent = true
Vue.use(Element, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  i18n,
  router,
  store,
  render: h => h(App)
})
