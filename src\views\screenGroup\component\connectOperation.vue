<template>
  <div class="connectoperat">
    <!-- {{ DetailData }} -->
    <div class="header">操作</div>
    <div class="center">
      <!-- <div class="basics">
        基础操作:
        <el-button type="primary" @click="Detection" :loading="detectionLoading">联屏组检测</el-button>
        <span style="padding-left: 3px">当前设备状态:{{ onlineState == false ? "在线" : "离线" }}</span>
        <el-button type="primary" @click="realTime" :loading="ScreenshotLoading">实时截图</el-button>
      </div> -->
      <div class="facility">
        设备预览:
        <div class="preview1" v-if="PositionState == false">
          <div v-for="(item, index) in DetailData.screen_info" :class="item.v_or_h=='h'?'':'devShu'" :key="index">
            序号 {{ item.screen_index }}
            <span> {{ item.screen_id }} </span>
          </div>
        </div>
        <div class="preview" v-else>
          <el-select v-for="(item, index) in selectScreen" ref="selectDom" v-model="item.label" placeholder=""
            :key="index" :class="item.style == 1 ? 'isactive' : ''"
            @change="ChangeSelect($event, 'selectDom', index, item)">
            <el-option v-for="item1 in DetailData.screen_info" :key="item1.value"
              :label="`序号${item1.screen_index} ${item1.screen_id}`"
              :value="`序号${item1.screen_index} ${item1.screen_id} ${item1.x}`">
            </el-option>
          </el-select>
        </div>
      </div>
      <!-- {{ DetailData.screen_info }} -->
      <div class="depth">
        <!-- 深度操作:<el-button @click="Dismantle" v-if="checkPer(['dm.vs.control'])">联屏拆除</el-button> -->
        深度操作:<el-button @click="Dismantle" v-if="checkPer(['dm.vs.control'])">联屏拆除</el-button>
        <el-button @click="ChangePosition"
          v-if="PositionState == false && checkPer(['dm.vs.control'])">位置调整</el-button>
        <el-button @click="SavePosition" v-else>保存修改</el-button>
        <el-button @click="reStart" v-if="checkPer(['dm.vs.control'])">设备重启</el-button>
      </div>
    </div>
  </div>
</template>

<script>
// import bind_shop from
export default {
  data() {
    return {
      selectScreen: [],
      PositionState: false,
      SaveState: false,
      // 是否在线
      onlineState: false,
      // 设备检测btn
      detectionLoading: false,
      // 截图btn
      ScreenshotLoading: false
    };
  },
  props: {
    DetailData: {
      handler(newValue, oldValue) { },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 修改值
    setSelect() {
      this.selectScreen = this.DetailData.screen_info;
      this.selectScreen.forEach((item, index) => {
        this.$set(item, "label", `序号${item.screen_index} ${item.screen_id} ${index}`);
        this.$set(item, "style", `0`);
      });
      console.log(this.selectScreen, 'selectScreen');
    },
    // 检测
    Detection() {
      const params = {
        sg_key: this.DetailData.sg_key,
        cmd: "vs_index_debug",
      };
      this.$emit("Detection", params);
    },
    // 截图
    realTime() {
      const params = {
        sg_key: this.DetailData.sg_key,
        cmd: "screen_snap",
      };
      this.$emit("realTime", params, this.selectScreen);
    },
    // 重启
    reStart() {
      const params = {
        sg_key: this.DetailData.sg_key,
        cmd: "reboot_device",
      };
      this.$emit("reStart", params);
    },
    // 位置调整
    ChangePosition() {
      this.setSelect();

      this.PositionState = true;
      console.log(this.selectScreen);

    },
    // 修改样色
    ChangeSelect(val, selectedRef, index, item) {
      console.log(this.selectScreen, 'index');
      let color = "";
      if (val) {
        color = "#1890ff";
      } else {
        // color = "#ea3c37";
      }
      // 改变下拉框颜色值
      // console.log(this.$refs[selectedRef]);
      this.$refs[selectedRef][
        index
      ].$el.children[0].children[0].style.backgroundColor = "" + color + "";
      this.$refs[selectedRef][index].$el.children[0].children[0].style.color =
        "#ffffff";
    },
    // 保存修改位置
    SavePosition() {
      var ary = this.selectScreen;
      console.log(ary, 'any');
      console.log(this.selectScreen, 'this.selectScreen');
      for (var i = 0; i < ary.length - 1; i++) {
        console.log(ary[i].label, ary[i + 1].label);
        if (ary[i].label == ary[i + 1].label) {
          this.SaveState = true;
          this.$message.warning("有重复");
          return;
        } else {
          this.SaveState = false;
        }
      }
      if (this.SaveState != true) {
        console.log(this.selectScreen, 'selectScreen');
        this.selectScreen.forEach((item, index) => {
          console.log('item.label', item.label);
          item.screen_id = String(item.label.substring(4, item.label.length - 2));
          item.screen_id = Number(item.screen_id)
          item.x = Number(item.label[item.label.length - 1])
          // console.log(item.label.substring(2, 3));
          item.screen_index = Number(index + 1);
          item.label =  `序号${index + 1} ${item.screen_id} ${index}`
          delete item.screen_model;
          delete item.screen_pmodel;
          delete item.style;
          delete item.screen_tags;
        });
        this.$emit("SavePosition", this.selectScreen);
      }
    },
    // 拆除
    Dismantle() {
      const params = {
        sg_key: this.DetailData.sg_key,
        cmd: "just_vsfree_index",
      };
      this.$emit("Dismantle", params);
    },
  },
  created() {
    console.log(this.selectScreen);
  },
};
</script>

<style lang="scss" scoped>
.connectoperat {
  margin-left: 20px;
  width: 100%;
  height: 331px;
  border-radius: 10px;
  border: rgba(229, 229, 229, 1) solid 1px;

  .header {
    height: 60px;
    width: 100%;
    line-height: 60px;
    padding-left: 16px;
    border-bottom: 1px solid #ededed;
    color: rgba(52, 52, 52, 0.9642857142857143);
    font-size: 14px;
    font-weight: bold;
  }

  .center {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding-left: 18px;

    .basics {
      button {
        height: 37px;
        margin-left: 15px;
        border-radius: 4px;
      }
    }

    .facility {
      display: flex;
      align-items: center;

      .preview {
        margin-left: 15px;
        display: flex;

        div {
          width: 130px;
          height: 70px;
          color: rgba(128, 128, 128, 1);
          background-color: rgba(216, 235, 255, 1);
          font-size: 14px;
          padding: 20px;
          font-weight: bold;
          border: rgba(0, 0, 0, 1) solid 1px;

          span {
            display: block;
            margin-top: 4px;
          }
        }
      }

      .preview1 {
        margin-left: 15px;
        display: flex;

        div {
          width: 130px;
          height: 70px;
          color: #fff;
          background-color: var(--screen-color);
          font-size: 14px;
          padding: 20px;
          font-weight: bold;
          border: rgba(0, 0, 0, .3) solid 1px;

          span {
            display: block;
            margin-top: 4px;
          }
        }
      }
    }

    .depth {
      button {
        margin-left: 15px;
        height: 37px;
        color: rgba(255, 255, 255, 1);
        background-color: var(--text-color);
        font-size: 14px;
        border-radius: 4px;
      }
    }
  }
}

::v-deep .el-select {
  padding: 0 !important;
}

::v-deep .preview {
  div {
    border: 0 !important;
    width: 155px !important;
  }

  .el-input__inner {
    width: 155px;
    height: 70px;
    background-color: rgba(216, 235, 255, 1);
    border: rgba(0, 0, 0, 1) solid 1px;
  }
}

::v-deep .el-input__suffix {
  display: none;
}

// 竖屏
.devShu {
  width: 100px !important;
  text-align: center !important;
  height: 130px !important;
  line-height: 50px !important;
}

.devShu span {
  line-height: 20px !important;
}
</style>