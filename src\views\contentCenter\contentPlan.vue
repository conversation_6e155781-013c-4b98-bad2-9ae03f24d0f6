<template>
  <div class="box">
    <div class="left tag_set">
      <el-tabs :tab-position="tabPosition" v-model="activeName">
        <el-tab-pane label="模板" class="control" name="template">
          <div slot="label" class="slot">
            <i class="el-icon-menu"></i> <span>模板</span>
          </div>
          <contentModel @SelectTemplate="SelectTemplate" :templateHList="templateHList" />
        </el-tab-pane>
        <el-tab-pane label="资源" name="resource">
          <div slot="label" class="slot">
            <i class="el-icon-picture"></i> <span>资源</span>
          </div>
          <ContentSources @selectImage="selectImage" />
        </el-tab-pane>
        <!-- <el-tab-pane label="价签" name="pricetag">
          <div slot="label" class="slot">
            <i class="el-icon-collection-tbag"></i> <span>价签</span>
          </div>
          <ContentPriceTag ref="ContentPriceTag" @addTags="addTags" @changeColor="changeColor"
            @getPriceText="getPriceText" @changePricePosition="changePricePosition" @setAssist="setAssist"
            @itsPrice="itsPrice" @changeBorderValue="changeBorderValue" @ChangeoutlineValue="ChangeoutlineValue"
            @changeLineSize='changeLineSize' @ChangeColorValue="ChangeColorValue" @clearoutLine="clearoutLine"
            @clearcolor="clearcolor" />
        </el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="right">
      <div class="save_header">
        <div style="display:flex;align-items: center;margin-right: 20px;">
          <span>
            H5命名:
          </span>
          <el-input style="width: 140px;margin-left:10px" v-model="saveTemplate.h5_name"></el-input>
        </div>
        <div>
          <el-button type="primary" @click='saveTemplateEvent'>保存</el-button>
        </div>
      </div>

      <p class="tips">按住【空格】后，可使用鼠标滚轮进行缩放，并可拖拽位置。如果无法进行操作，请<span>点击此处重置</span></p>
      <div class="move_wrap" ref="move_wrap" v-show="isMoveWrapShow"></div>
      <div class="tpl_wrap" ref="tpl_wrap" v-if="isIframe">
        <iframe :style="getIframeStyle()" id="iframe" ref="iframe" name="iframe" frameborder="0" height="100%"
          width="100%" scrolling="no" :src="iframeUrl" :class="tpl_v_or_h == 'vertical' ? 'iframeV' : 'iframe'"
          @load="load($event)">
        </iframe>
      </div>

    </div>
  </div>
</template>

<script>
import { event } from "jquery";
import ContentEle from "../../components/ContentCenter/contentEle.vue";
import contentModel from "../../components/ContentCenter/contentModel.vue";
import ContentPriceTag from "../../components/ContentCenter/contentPriceTag.vue";
import ContentSources from "../../components/ContentCenter/contentSources.vue";
import ContentRight from "./contentRight.vue";
import EditRight from "./EditRight.vue";
import { get_gongfang_tpl } from "@/api/template/index";
import {
  add_group_cs,
  edit_group_cs
} from "@/api/template/index";
export default {
  components: {
    contentModel,
    ContentSources,
    ContentPriceTag,
    ContentEle,
    ContentRight,
    EditRight
  },
  data() {
    return {
      // tabs 定位
      tabPosition: "left",
      // 模板类型 0横向第一 1横向第二 2竖向第一
      templateType: null,
      activeName: 'template',
      templateHList: [],
      v_h: 0,
      iframeUrl: '',
      h5_name: '',
      zoom: 0.6,
      isIframe: false,
      hasbeenPress: false,
      pressPos: [0, 0],
      clx: 0,
      cly: 0,
      clxWrap: 0,
      clyWrap: 0,
      isMoveWrapShow: false,
      tranScale: document.body.offsetWidth > 1800 ? 0.7 : document.body.offsetWidth / 1920 * 0.6,
      iframe_rect: {
        width: '0px',
        height: '0px'
      },
      activeClassName: '',
      selectType: '',
      textList: [],
      saveTemplate: {
        group_cs_id: '',
        file_identify: '',
        html_str: '',
        ref_photo_info: {

        },
        h5_name: ''
      },
      activeTemplate: ''
    };
  },
  created() {
    this.getTemplateList(0)
    this.getTemplateList(1)
  },
  mounted() {
    let that = this;
    window.addEventListener('message', (event) => {
      console.log(event.data, 'data');
      if (event.data.className != undefined) {
        // that.toResource(1);
        that.activeClassName = event.data.className;
        that.selectType = event.data.selectType;
      }
      if (event.data.selectType == 'text') {
        console.log(event.data);
        // that.textList = [{
        //   wordLineName: event.data.className,
        //   wordLineText: event.data.textMessage.value
        // }]
        that.textList.forEach(item => {
          if (item.wordLineName == event.data.className) {
            item.wordLineText = event.data.textMessage.value
          } else {
            that.textList.push({
              wordLineName: event.data.className,
              wordLineText: event.data.textMessage.value
            })
          }
        })
      }

    })
    window.addEventListener('keydown', this.onKeyDown);
    window.addEventListener('keyup', this.onKeyUp);

  },
  computed: {
    getIframeStyle() {
      return function () {
        console.log(1);
        return {
          transform: `scale(${this.zoom})`,
          transformOrigin: '0 0',
          width: '100%',
          height: '100%',
          overflow: 'hidden'
        }
      }
    }
  },
  watch: {
    activeName(val) {
      if (val == 'template') {
        this.$refs.contentRight.getGroupList()
      }
    }
  },
  watch: {

  },
  methods: {
    /**
     * @deprecated 获取模板
     * @param v_h 0 横向单屏 1 纵向单屏
     */
    getTemplateList(v_h) {
      let params = {
        v_h: v_h
      }
      this.v_h = v_h

      let pushTemplateList = ["HD_video_tplH_0009242", "HD_welposterH_0009710", "HD4K_welposterH_00010272", "HD4K_welposterH_00010273", "HD4K_welposterH_00010270", "HD4K_welposterH_00010268", "HD4K_welposterH_00010267",
        "HD4K_welposterH_00010269", "HD4K_welposterH_00010271", "HD4K_welposterH_00010255", "HD4K_welposterH_00010263"]
      get_gongfang_tpl(params).then(res => {
        console.log(res);
        res['data'][0].content.forEach(item => {
          if (v_h == 0) {
            console.log(item.name, 'item');

            if (pushTemplateList.indexOf(item.name) > -1) {
              this.templateHList.push(item)
            }
          }
        })
      })
    },


    /**
     * 选择模板
     * @param type 0 横向单屏
     * @param template 模板详情
     */

    SelectTemplate(type, template) {
      // this.iframeUrl = template.anim_url
      console.log(template, 'template');
      this.tpl_id = template.tpl_id
      this.activeTemplate = template.name
      this.addGroupCs()
      setTimeout(() => {
        console.log(this.$refs.iframe.contentDocument);
      }, 3000);

    },

    /**
     * 更新iframecss
     */
    load(e) {
      let dom = e.target;
      this.getIframeRect(dom);
    },
    getIframeRect(dom) {
      let content = dom.contentDocument;
      if (content) {
        let element = dom.contentDocument.getElementById('an-anim');

        if (element) {
          // element.getBoundingClientRect()
          setTimeout(() => {
            // const {width,height} = element.getBoundingClientRect()
            const width = element.offsetWidth;
            const height = element.offsetHeight;
            console.log(width, 'width');
            debugger
            const style_params = {
              width: width + 'px !important',
              height: height + 'px !important'
            }
            this.iframe_rect = style_params;
            // this.tranScale = 1920 / width * 0.7;
            this.tranScale = 1;
            this.$refs.tpl_wrap.style.transform = `scale(${this.tranScale})`;
            console.log(element,'element');
            console.log(this.activeTemplate,'this.activeTemplate');
            
            if (this.activeTemplate.indexOf('4K') > -1) {
              element.style.zoom = 0.5
            }
          }, 100)
        } else {
          console.log('没有dom');
          const style_params = {
            width: '',
            height: ''
          }
          this.iframe_rect = style_params;
          this.$refs.tpl_wrap.style.transform = '0.7';
        }
      } else {
        console.log('没有content');
        const style_params = {
          width: '',
          height: ''
        }
        this.iframe_rect = style_params;
        this.$refs.tpl_wrap.style.transform = '0.7';
      }

    },

    /**
     * 创建cs add_group_cs
     * @param shop_group_id
     * @param act_type n2n_scence
     * @param cs_list :{
     *      tpl_id: this.tpl_id
     *  }
     */

    addGroupCs() {
      let params = {
        shop_group_id: localStorage.getItem('group_id'),
        act_type: 'n2n_scence',
        cs_list: {
          tpl_id: this.tpl_id
        }
      }
      add_group_cs(params).then(res => {
        console.log(res);
        this.isIframe = true
        this.iframeUrl = res['data'][0]['static_url']
        this.group_cs_id = res["data"][0]["group_cs_id"];
      })
    },



    /**
     * 跳转到资源
     */

    /**
     * 选择图片
     */

    selectImage(resource, type) {
      console.log(resource, 'resource');
      console.log(this.selectType);
      if (!this.selectType) {
        return
      }

      let dom = this.activeClassName;
      console.log(this.activeClassName, 'activeClassName');
      if (this.activeClassName == 'VideoPart') {
        if (type != 'video') {
          this.$message.warning("当前是视频模板 请选择视频");
          return
        } else {
          this.saveTemplate['file_identify'] = resource.file_identify
        }
      } else {
        this.saveTemplate['ref_photo_info'][this.activeClassName] = resource.file_id
      }

      this.$refs.iframe.contentWindow.setImgs(dom, resource.original_url ? resource.original_url : resource.thumb);




    },


    FindTemplateWordLineStyle() {
      let changeTemplateWordLineStyle = this.$refs.iframe.contentWindow.changeWordLineStyle();
      return changeTemplateWordLineStyle;
    },

    FindTemplateStyle() {
      let changeTemplateImgStyle = this.$refs.iframe.contentWindow.changeTemplateImgStyle();

      return changeTemplateImgStyle;
    },

    /**
     * 选择价签
     * @param type 0: 第一种价签
     * @param type 1: 第二种价签
     */

    // 设置辅助样式

    // 设置价格


    /**
     * 保存模板
     * @param  
     */
    saveTemplateEvent() {
      console.log(this.saveTemplate, 'saveTemplate')
      this.saveTemplate.group_cs_id = this.group_cs_id;


      let changeWordLineStyle = this.FindTemplateWordLineStyle()
      console.log(changeWordLineStyle, 'changeworlineStyle');

      var saveImgObject = {

      }

      var saveWordLineObject = {

      }

      changeWordLineStyle.forEach(item => {
        saveWordLineObject[item.textClassName] = {
          "style": `top:${item.top};left:${item.left};font-size:${item.fontSize};width:${item.width};height:${item.height};color:${item.color};line-height:${item.lineHeight};letter-spacing:${item.letterSpacing}`
        }
      })

      let diff = this.textList;
      console.log(diff, 'diff');
      const template_vars = {}
      diff.forEach(dif => {
        template_vars[dif.wordLineName] = `<span><pre>${dif.wordLineText}</pre></span>`
      })

      let changeImgStyle = this.FindTemplateStyle();
      changeImgStyle.forEach(item => {
        saveImgObject[item.imgClassName] = {
          "style": `top:${item.top};left:${item.left};width:${item.width};height:${item.height};z-index:${item.zIndex}`
        }
      })

      let delete_dom = this.$refs.iframe.contentWindow.deleteTemplate()

      delete_dom.forEach(item => {
        this.$set(saveImgObject, item, { 'style': 'delete' })
      })

      console.log(saveWordLineObject, 'saveWordLineObject');
      console.log(saveImgObject, 'saveImgObject');

      this.saveTemplate.wordline_style = saveWordLineObject ? saveWordLineObject : {}
      this.saveTemplate.ref_photo_style = saveImgObject ? saveImgObject : {}
      this.saveTemplate.template_vars = template_vars ? template_vars : {}
      console.log(this.saveTemplate, 'saveTemplate');
      edit_group_cs(this.saveTemplate).then(res => {
        console.log(res, 'res');
        if (res['rst'] == 'ok') {
          this.$message.success("保存成功");
          this.$router.push("/contentCenter/srcfiles");
        } else {
          this.$message.warning(res.error_msg);
        }
      })
    },


    onKeyDown(e) {
      if (this.hasbeenPress) {
        return
      } else {
        if (e.code == 'Space') {
          this.isMoveWrapShow = true;
          this.hasbeenPress = true;
          // this.$refs.iframe1.style.border = '1px solid transparent';
          console.log(this.$refs.move_wrap, 'this.$refs.move_wrap');
          console.log(document.getElementsByClassName('move_wrap'));
          this.$refs.move_wrap.style.cursor = 'grab'
          this.$refs.move_wrap.addEventListener('mousedown', this.dragMouseDown)
          this.$refs.move_wrap.addEventListener('mouseup', this.dragMouseUp)
          window.addEventListener('wheel', this.scaleMouseWheel)
        }
      }
    },
    onKeyUp(e) {
      if (e.code == 'Space') {
        this.hasbeenPress = false;
        this.$refs.move_wrap.removeEventListener('mousedown', this.dragMouseDown);
        this.$refs.move_wrap.removeEventListener('mouseup', this.dragMouseUp);
        window.removeEventListener('wheel', this.scaleMouseWheel);
        window.removeEventListener('mousemove', this.mouseMove);
        this.pressPos = [0, 0]
        this.isMoveWrapShow = false;
        this.$refs.move_wrap.style.cursor = 'default'
      }
    },
    dragMouseDown(e) {

      this.pressPos = [e.clientX, e.clientY];
      this.$refs.move_wrap.style.cursor = 'grabbing'
      this.clx = Number(this.$refs.tpl_wrap.style.left.replace('px', ''));
      this.cly = Number(this.$refs.tpl_wrap.style.top.replace('px', ''));

      window.addEventListener('mousemove', this.mouseMove)
    },
    dragMouseUp() {
      this.$refs.move_wrap.style.cursor = 'grab'
      window.removeEventListener('mousemove', this.mouseMove);
      this.pressPos = [0, 0]
    },
    mouseMove(e) {
      const moveX = e.clientX - this.pressPos[0];
      const moveY = e.clientY - this.pressPos[1];
      this.$refs.tpl_wrap.style.top = this.cly + moveY + 'px';
      this.$refs.tpl_wrap.style.left = this.clx + moveX + 'px';
      // this.$refs.move_wrap.style.top =  this.clyWrap +  moveY + 'px';
      // this.$refs.move_wrap.style.left =  this.clxWrap +  moveX + 'px';
    },
    scaleMouseWheel(e) {
      if (e.deltaY > 0) {
        //缩小
        this.tranScale -= 0.02;
        if (this.tranScale <= 0.19) {
          this.tranScale = 0.19;
          return
        }
        // this.$refs.move_wrap.style.transform = `scale(${this.tranScale})`
        this.$refs.tpl_wrap.style.transform = `scale(${this.tranScale})`
      } else {
        //放大
        this.tranScale += 0.02;
        if (this.tranScale >= 3) {
          this.tranScale = 3;
          return
        }
        // this.$refs.move_wrap.style.transform = `scale(${this.tranScale})`
        this.$refs.tpl_wrap.style.transform = `scale(${this.tranScale})`
      }
    },

  },

};
</script>

<style lang="scss" scoped>
.box {
  height: 100%;
  display: flex;
}

.box>.left {
  height: 100%;
  background: rgba(29, 29, 29, 1);
}

.box .tag_set ::v-deep .el-tabs {
  /* width: 460px; */
  display: flex;
  height: 100%;
}

.left ::v-deep .is-left {
  font-size: 16px;
  text-align: center;
  margin-right: 0 !important;
}

.left .slot {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.left ::v-deep .el-tabs--left .el-tabs__item.is-left {
  height: 84px;
  margin: 20px 0;
}

.left .slot>i {
  display: block;
  font-size: 30px;
}

.left .slot span {
  color: #fff;
}

/* 左侧切换的样式 */
.left ::v-deep .el-tabs__content {
  background: rgba(56, 56, 56, 1);
  color: #fff;
  height: 100%;
  width: 308px;
}

.right {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // padding: 0 20px;
  background-color: #eeeeee;
  overflow: hidden;
  position: relative;

  .save_header {
    width: 100%;
    height: 60px;
    display: flex;
    background-color: #fff;
    align-items: center;
    padding: 0 10px;
  }

  iframe {
    margin: 10px;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .tpl_wrap {
    position: absolute;
    top: 50px;
    left: 0px;
    width: 1920px;
    height: 1080px;
    /* transform: scale(0.6); */
    transform-origin: 0 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -webkit-user-drag: none;
    -ms-user-select: none;
    user-select: none;
    box-sizing: border-box;
    padding-top: 10px;
    overflow: hidden;
    /* border: 1px solid #326FFA; */
  }

  .move_wrap {
    position: absolute;
    top: -4500px;
    left: -4500px;
    background: rgba(0, 0, 0, 0.01);
    width: 9000px;
    height: 9000px;
    z-index: 2;
    position: relative;
    /* border: 2px solid transparent; */
    /* transform: scale(1); */
    /* cursor: grab; */
  }

  .tips {
    width: 100%;
    /* border: 1px solid red; */
    position: absolute;
    top: 80px;
    text-align: right;
    padding-right: 20px;
    padding-top: 10px;
    box-sizing: border-box;
    z-index: 5;
    font-size: 14px;
    user-select: none;
    /* color: red; */
  }

  .tips span {
    color: #326FFA;
    cursor: pointer;
  }

  .tips span:active {
    filter: brightness(.8);
  }
}
</style>
<style>
.tab_set .el-tabs__item .is-left {
  background-color: var(--active-color) !important;
}

/* tabs选中的样式 */
.tag_set .is-active {
  color: var(--text-color) !important;
  background-color: transparent !important;
  /* background-color: var(--active-color) !important; */
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-across {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-vertical {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
  /* display: none; */
  background-color: transparent !important;
  width: 0;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
  color: var(--text-color) !important;
}

.el-tabs--left .el-tabs__nav-wrap.is-left::after {
  left: -3px !important;
  flex-direction: column;
}

/* .el-tabs__item{
  color:white !important;
} */
</style>