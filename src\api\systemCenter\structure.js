import { get, post, uploadFile } from '@/utils/request'

// 店铺树状结构
export function simple_get_shop_struct(data) {
    return post('dsadm/api/json',data,'simple_get_shop_struct')
}
// 获取管理者品牌列表
export function get_mgmtuser_branchlist(data) {
    return post('dsadm/api/json',data,'get_mgmtuser_branchlist')
}
//获取品牌下门店数量
export function get_shop_count(data) {
    return post('dsadm/api/json',data,'get_shop_count')
}

export function del_admin_group(data) {
    return post('dsadm/api/json',data,'del_admin_group')
}

export function shop_user_list(data) {
    return post('dsadm/api/json',data,'shop_user_list')
}

export function edit_group_part(data) {
    return post('dsadm/api/json',data,'edit_group_part')
}

export function simple_get_tpl_address(data) {
    return post('dsadm/api/json',data,'simple_get_tpl_address')
}
//获取可绑列表及搜索
export function simple_group_part_create(data) {
    return post('dsadm/api/json',data,'simple_group_part_create')
}

export function get_g_role_user_list(data) {
    return post('dsadm/api/json',data,'get_g_role_user_list')
}

export function get_g_role_list(data) {
    return post('dsadm/api/json',data,'get_g_role_list')
}

export function simple_create_or_edit_shop(data) {
    return post('dsadm/api/json',data,'simple_create_or_edit_shop')
}

export function create_shop(data) {
    return post('store/api/json',data,'create_shop')
}
