<template>
  <div class="screenContent">
    <ul class="innerBox">
      <div class="title">
        <h4>联屏组内容</h4>
        <span>总播放时长55s</span>
      </div>
      <li v-for="(item, index) in 3" :key="index" >
        <div class="sideLists" :class="selectValue.direction=='竖'?'heightSide':''">
          <span>{{ index + 1 }}</span>
          <p>12.04s</p>
        </div>
                <div class="firstBg" v-if="index == 0">
            <!-- <img src="../../assets/img/home_img/breakfast.png" /> -->
          </div>
        <div class="right"  v-if="index == 1 || index==2">
          <div class="quarts" v-for="(item, index) in  Number(selectValue.firstNum)" :key="index" :style="Number(selectValue.firstNum)==3?'width:33%':Number(selectValue.firstNum)==5?'width:20%':''" :class="selectValue.direction=='竖'?'heightShu':''">
            <div class="boxs">
              <img src="../../assets/img/home_img/jia.svg" />
              <p>新增</p>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <!-- <ul
      v-if="selectValue.direction == '横' || selectValue.direction == '3横1竖'"
    >
      <li
        v-for="(item, index) in lists"
        :key="index"
        :style="item.title == '工作日早餐' ? 'height: 60px' : 'height: 100px'"
      >
        <h4>{{ item.title }}{{ selectValue.direction }}</h4>
        <div
          v-for="(items, index) in Number(selectValue.firstNum)"
          style="width: 200px"
          :style="item.title == '工作日早餐' ? 'height: 60px' : 'height: 100px'"
        >
          <img
            :src="imgs2[index]"
            :style="
              selectValue.direction == '3横1竖'
                ? index == 3
                  ? 'width: 50px;height: 80px;margin:0 auto;'
                  : ''
                : ''
            "
            v-if="item.title != '工作日早餐' && imgs2[index] != ''"
          />
          <div
            class="add"
            @click.stop="newAdd"
            v-if="item.title != '工作日早餐'"
          >
            <img src="../../assets/img/home_img/jia.svg" />
            <p>新增</p>
          </div>
          <div
            v-if="item.title == '工作日早餐'"
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
            "
          >
            <span
              :style="
                selectValue.direction == '3横1竖'
                  ? index == 3
                    ? 'width: 18px;height: 28px;line-height:28px'
                    : ''
                  : ''
              "
              >{{ index + 1 }}</span
            >
          </div>
        </div>
      </li>
    </ul> -->
    <!-- <el-button type="primary" @click.stop="addContent" class="addInfo"
      >新增</el-button
    > -->
    <!--    新增内容-->
    <el-dialog
      title="新增内容"
      :visible.sync="isShowContent"
      custom-class="addContent"
      width="70%"
    >
      <div class="conBtn">
        <h5>联屏内容</h5>
        <div class="conRight">
          <el-select v-model="screenValue1" placeholder="屏幕方向">
            <el-option
              v-for="item in screenOption1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select v-model="screenValue2" placeholder="全国市场">
            <el-option
              v-for="item in screenOption2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button
            type="danger"
            style="height: 32px; background: var(--background-color)"
            >搜索</el-button
          >
        </div>
      </div>
      <ul class="conList">
        <li v-for="(item, index) in 3" :key="index">
          <!-- <img src="../../assets/img/home_img/audio.png" /> -->
          <div class="radius" @click.stop="checked(index)"></div>
          <i
            class="el-icon-circle-check"
            v-show="checkedShow"
            @click.stop="checked(index)"
          ></i>
          <div class="bottomDiv">
            <span>7680*1080</span>
            <span>15S</span>
          </div>
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowContent = false">取 消</el-button>
        <el-button type="primary" @click="isShowContent = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "screenContent",
  props: [ 'toPlayer' ],
  data() {
    return {
      viewDiv: false,
      //  下拉框横屏
      screenOption1: [
        {
          value: "选项1",
          label: "横屏1*4 7680*1080",
        },
        {
          value: "选项2",
          label: "横屏1*3 5760*1080",
        },
        {
          value: "选项3",
          label: "横屏1*2 3860*1080",
        },
      ],
      screenValue1: "",
      //  下拉框
      screenOption2: [
        {
          value: "选项1",
          label: "全国市场",
        },
        {
          value: "选项2",
          label: "上海市场",
        },
        {
          value: "选项3",
          label: "北京市场",
        },
        {
          value: "选项4",
          label: "成都市场",
        },
      ],
      screenValue2: "",
      //  新增内容
      isShowContent: false,
      //  select的显示
      checkedShow: false,
      //  音量控制
      value1: 0,
      selectValue: {
        firstNum: "",
        direction: "",
      },
      lists: [
        {
          title: "工作日早餐",
        },
        {
          title: "早餐",
        },
        {
          title: "午餐",
        },
      ],
    };
  },
  methods: {
    toView() {
      this.viewDiv = true;
    },
    //  新增内容
    addContent() {
      this.isShowContent = true;
    },
    //  checked选择框
    checked(item) {
      this.checkedShow = !this.checkedShow;
    },
    //  预览音量
    formatTooltip(val) {
      return val / 100;
    },
    /**
     * 取出小括号内的内容
     */
  },
  watch: {
    toPlayer(newValue, oldValue) {
      console.log(this.toPlayer, "player");
      console.log(newValue, "value");
      // 第一位渲染的数字
      let first = newValue.split("*")[1];
      console.log(first,"4324");
      this.selectValue.firstNum = first.charAt(0);
      console.log(this.selectValue.firstNum,"content");
      this.selectValue.direction = newValue.charAt(0)
      console.log(this.selectValue.direction,"content");
    },
  },
};
</script>

<style scoped>
ul {
  list-style: none;
}

.screenContent {
  position: relative;
  margin-top: 20px;
  height: 75%;
  margin-left: 15px;
  flex: 1;
  padding-right: 18px;
}
.innerBox {
  background-color: rgba(255, 255, 255, 1);
  border: rgba(198, 198, 198, 1) solid 1px;
  position: absolute;
  margin: 0 auto;
  left: 0;
  right: 0;
  width: 65%;
}
.innerBox > .title {
  height: 40px;
  line-height: 40px;
  font-weight: 600;
  widows: 100%;
  padding: 0 10px;
  text-align: center;
  border-bottom: rgba(229, 229, 229, 1) solid 1px;
}
.innerBox > .title > h4 {
  display: inline-block;
}
.innerBox > .title > span {
  color: rgba(80, 80, 80, 1);
  font-weight: 500;
  float: right;
}
.innerBox > li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.innerBox .sideLists {
  width: 139px;
  height: 140px;
  line-height: 100px;
  text-align: center;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  border-right: 1px solid rgba(229, 229, 229, 1);
  position: relative;
}
.innerBox .heightSide{
  height: 260px !important;
}
.innerBox .sideLists > p {
  width: 139px;
  height: 45px;
  background-color: rgba(42, 130, 228, 0.3571428571428571);
  font-size: 14px;
  position: absolute;
  left: 0;
  bottom: 0;
  line-height: 45px;
  color: #fff;
}
.innerBox .right {
  width: calc(100% - 139px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
}
.innerBox .right .quarts {
  width: 25%;
  height: 140px;
  border: 1px solid rgba(229, 229, 229, 1);
  position: relative;
}
.innerBox .right  .heightShu{
  height: 260px !important;
  width: 20% !important;
}
.innerBox  .firstBg {
  padding: 10px;
  width: 90%;
  height: 139px;
}
.innerBox  .firstBg > img {
  width: 100%;
  height: 100%;
}
.innerBox .right .quarts .boxs {
  width: 51px;
  height: 51px;
  color: rgba(80, 80, 80, 1);
  border-radius: 50%;
  text-align: center;
  font-size: 12px;
  border: rgba(229, 229, 229, 1) solid 1px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  cursor: pointer;
}
.innerBox .right .quarts .boxs img {
  margin: 3px 5px 3px;
}
.innerBox .right .quarts img {
  width: 20px;
  height: 20px;
}
.add {
  width: 0.51rem;
  height: 0.51rem;
  color: rgba(80, 80, 80, 1);
  border-radius: 0.26rem;
  font-size: 0.12rem;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
  margin: auto;
  line-height: 0.7rem;
  font-weight: bold;
}
.add > .addImg {
  width: 0.2rem;
  height: 0.2rem;
  position: absolute;
  margin: auto;
  top: 0.05rem;
  left: 0;
  right: 0;
}

.addInfo {
  position: absolute;
  width: 88px;
  right: 0;
  margin-top: 15px;
}

/*新增内容*/
.content {
  width: 990px;
  height: 675px;
  color: rgba(80, 80, 80, 1);
  background-color: #fff;
  border-radius: 16px;
  font-size: 14px;
  position: absolute;
  left: -198px;
  top: -13px;
  z-index: 20;
  padding: 0 30px;
}

/*下拉框*/
::v-deep .el-input__inner {
  width: 196px;
  height: 32px;
  margin: 0 5px;
}

.conBtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0px 0 10px 0;
}

.conBtn > h5 {
  color: rgba(108, 178, 255, 1);
  font-weight: bold;
}

.conRight {
  width: 493px;
}

.conList {
  color: rgba(80, 80, 80, 1);
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  padding: 0 12px;
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
}

.conList > li {
  width: 479px;
  height: 140px;
  border: 1px solid #c4d6e3;
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.2214285714285714);
  margin: 16px 10px 10px 0;
  position: relative;
}

.conList > li > img {
  width: 98%;
  height: 135px;
  margin-top: 3px;
}

.conList > li > .radius {
  width: 29px;
  height: 29px;
  border: 3px solid #fff;
  background-color: #163953;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 10px;
}
.conList > li > i {
  font-size: 30px;
  background: var(--base-color);
  color: #fff;
  position: absolute;
  left: 8px;
  top: 10px;
  border-radius: 50%;
}
.conList > li > .bottomDiv {
  width: 98%;
  margin-left: 5px;
  height: 26px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(0, 0, 0, 0.3642857142857143);
  font-size: 11px;
  opacity: 0.8;
  position: absolute;
  bottom: 0;
  text-align: left;
  display: flex;
  justify-content: space-between;
  color: #fff;
  align-items: center;
}

.conList > li > .bottomDiv > span {
  margin: 0 11px;
}
</style>
<style>
.addContent .el-dialog__body {
  padding: 0 30px;
}
.addContent .el-dialog__title {
  font-size: 14px;
  font-weight: bold;
}
</style>
