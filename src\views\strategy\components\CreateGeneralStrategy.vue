<template>
  <div class="GeneralCreate">
    <div class="actionBtn">
      <el-button type="danger" v-if="!showInput" @click.stop="playerEdit">播放编辑</el-button>
      <el-button type="danger" v-else @click="playerOk">确定</el-button>
      <el-button type="primary" @click="add">新增内容</el-button>
    </div>
    <ul>
      <li v-for="(item, index) in cs_list" :key="index"
        style="padding-bottom:10px;margin-left: 10px;position: relative;" :class="v_or_h == 0 ? 'arcoss' : 'vision'">
        <div class="delete" @click="deleteCard(item)">删除</div>
        <div class="delete1" @click="Preview(item.tpl_name.indexOf('video') == -1 ? 'image' : 'video', item)">预览</div>
        <img :src="item.thumb_url" :class="v_or_h == 0 ? 'arcossimg' : 'visionimg'" />
        <div class="img_tips" :class="v_or_h == 0 ? 'arcoss_tips' : 'vision_tips'">加载中</div>
        <div>内容类型： {{ getTagName(item) }}</div>
        <div v-if="item.h5_name">h5名称：{{ item.h5_name }}</div>
        <div>
          内容时长：
          <span v-show="!showInput">{{ item.duration }} s</span>
          <el-input v-model="item.duration" style="width:100px;" v-show="showInput"
            ></el-input>
        </div>
        <div>
          播放顺序：
          <span v-show="!showInput">{{ item.ordering }}</span>
          <el-input v-model="item.ordering" style="width:100px;" v-show="showInput"></el-input>
        </div>
        <div v-if="item.video_make_info" class="video_make_info">{{ item.video_make_info }}</div>
      </li>
      <div class="pushing" v-show="cs_list.length != 0">
        <el-button @click="pushing" class="ab" type="primary" size="medium">发布</el-button>
      </div>
    </ul>
    <!--    点击后出现-->
    <el-dialog title="新增内容" :visible.sync="showAlertBox" width="61%" custom-class="tabsDatat"
      :before-close="showAlertBox">
      <!--      <div class='top'>-->
      <!--        <h3>新增内容</h3>-->
      <!--        <p @click.stop='showAlertBox=false'>×</p>-->
      <!--      </div>-->
      <div class="threeParts tag_set">
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="图片" name="first">
            <picture-resources :tagsList="tagsList" :delTagsList="delTagsList" @checkedList="checkedList"
              @setImgPreview="setImgPreview" ref="picture"></picture-resources>
          </el-tab-pane>
          <el-tab-pane label="视频" name="second">
            <video-resources @setVideoPreview="setVideoPreview" @checkedList="checkedList"
              ref="video"></video-resources>
          </el-tab-pane>
          <el-tab-pane label="H5" name="third">
            <h5-resources @setH5Preview="setH5Preview" @checkedList="checkedList2" ref="h5"></h5-resources>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="btns dialog_btns">
        <el-button type="primary" @click.stop="showAlertBox = false" slot="right: 100px;">取消</el-button>
        <!-- <el-button type='primary' @click.stop='alertOk'>确定</el-button> -->
        <el-button type="primary" v-debounce.stop="alertOk">确定</el-button>
      </div>
    </el-dialog>
    <!-- 预览mask -->
    <previsualization :isPreviewMaskShow="isPreviewMaskShow" :PreviewSrc="PreviewSrc" :PreviewType="PreviewType"
      @closePreviewMask="closePreviewMask" ref="previsualization"></previsualization>
  </div>
</template>

<script>
// import { self_photo_list } from "@/api/files/pictureResources";
// import { get_video_list } from "@/api/files/videoResources";
import {
  batch_create_draft,
  edit_group_classify_cs,
  get_btpub_detail
} from "@/api/contentdeploy/contentdeploy";
import { edit_group_cs, set_cs_ordering } from "@/api/device/device";
import {
  quick_get_btplan_detail,
  quick_exec_btplan_handler
} from "@/api/strategy/strategy";
import {
  get_daypart_data,
  del_btpub_content,
  create_group_classify,
  add_btpub_content
} from "@/api/contentdeploy/contentdeploy";
import { get_classify_detail } from "@/api/device/device";

import pictureResources from "@/components/ContentCenter/pictureResources";
import videoResources from "@/components/ContentCenter/videoResources";
import h5Resources from "@/components/ContentCenter/h5Resources";
import previsualization from "@/components/communal/previsualization";

export default {
  name: "GeneralCreate",
  components: {
    pictureResources,
    videoResources,
    h5Resources,
    // multiScreenResources,
    // floatsResources,
    previsualization
  },
  computed: {
    getTagName() {
      return (item) => {
        console.log(item, 'item');

        if (item.tpl_name == 'emptyH_0001' || item.tpl_name == 'empty_0001') return '图片'
        if (item.tpl_name == 'HD_welposterH_0002854' || item.tpl_name == 'HD_welposter_0002853') return '图片'
        if (item.tpl_name == 'HD_video_tplH_0007' || item.tpl_name == 'HD_video_tpl_0007') return '视频'
        if (item.tpl_name == 'HD_welposterH_0006421') return '音乐'
        return '模板'
      }
    },
  },
  data() {
    return {
      //选择框
      picList: [
        {
          img_url: "",
          checked: 1
        }
      ],
      checkIndex: -1,
      checkShow: false,
      showAlertBox: false,
      //  图片、视频、H5
      activeName: "first",
      //  大图
      showBg: false,
      showAlertBox: false,
      fileList: [],
      // 搜索框
      searchValue: "",
      activeName: "first",
      imgList: [],
      VideoList: [],
      currentPage: 1, //页码
      totalNum: 0, //总数据数量
      pageSize: 10, //每页显示多少条
      tagsList: [],
      delTagsList: [],
      PreviewType: "",
      PreviewSrc: "",
      loading: false,
      cf_id: "",
      disabled: false,
      cs_list: [],
      pictureList: false,
      generalData: null,
      showInput: "false",
      g_cf_cs: "",
      bt_name: ""
    };
  },
  watch: {},
  props: {
    btplan_id: {
      type: String
    }
  },
  created() {
    this.get_btpub_detail_info(this.btplan_id);
    console.log(this.$route.query, "this.$route.query");
    if (this.$route.query.showInput != "") {
      this.showInput = this.$route.query.showInput;
    } else {
      this.showInput = false;
    }
    console.log(this.showInput, "show1231231");
  },
  methods: {
    // 播放编辑
    playerEdit() {
      this.showInput = true;
    },
    // 播放确定
    playerOk() {
      this.$parent.loading = true;
      this.showInput = false;
      console.log(this.cs_list);
      let n_cs_list = []
      this.cs_list.forEach(item => {
        n_cs_list.push({
          cs_id: item.cs_id,
          duration: item.duration,
          order: item.ordering
        })
      })

      let parmas = {
        cf_id: this.ref_id,
        cs_list: n_cs_list
      }

      set_cs_attrs(parmas).then(res => {
        console.log(res, 'res');
        if (res['rst'] == 'ok') {
          this.$message.success("修改成功");
          this.get_btpub_detail_info(this.$route.query.btplan_id);
        } else {
          this.$message.error(res['msg']);
        }
      })

      // this.cs_list.forEach(item => {
      //   this.g_cf_cs = "g_cf_cs_" + item.cs_id;
      //   const params = {
      //     target_screen: "all",
      //     cs_id: this.g_cf_cs,
      //     ordering: item.ordering,
      //     cf_id: this.cf_id
      //   };
      //   set_cs_ordering(params).then(res => {
      //     if (res.rst == "ok") {
      //       const params1 = {
      //         group_cs_id: item.cs_id,
      //         duration: Number(item.duration)
      //       };
      //       edit_group_cs(params1).then(res => {
      //         if (res.rst == "ok") {
      //           this.$message.closeAll();
      //           this.$message.success("修改成功");
      //           this.get_btpub_detail_info(this.$route.query.btplan_id);
      //         } else {
      //           this.$message.warning("修改失败");
      //           this.$parent.loading = false;
      //         }
      //       });
      //     }
      //   });
      // });
    },
    // 预览
    setImgPreview(val) {
      this.PreviewType = "image";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setVideoPreview(val) {
      this.PreviewType = "video";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    setH5Preview(val) {
      this.PreviewType = "h5";
      this.PreviewSrc = val;
      this.isPreviewMaskShow = true;
    },
    // 关闭预览mask
    closePreviewMask() {
      this.isPreviewMaskShow = false;
      this.PreviewSrc = "";
    },
    checkedList(imgList) {
      console.log(imgList);
      imgList.forEach(item => {
        item.thumb_url = item.thumb;
      });
      console.log(imgList, "imgLists");
      this.imgList = imgList;
      //   this.$emit("selectImage", imgUrl);
    },
    checkedList2(imgList) {
      console.log(imgList);
      imgList.forEach(item => {
        item.thumb_url = item.thumb_url;
      });
      console.log(imgList, "imgLists");
      this.imgList = imgList;
      //   this.$emit("selectImage", imgUrl);
    },
    goBack() {
      this.$router.go(-1);
    },
    //新增
    add() {
      this.showAlertBox = true;
      setTimeout(() => {
        this.$refs.picture.checkedList.forEach(item => {
          item.checked = false;
        });
        this.$refs.video.checkedList.forEach(item => {
          item.checked = false;
        });
        this.$refs.h5.checkedList.forEach(item => {
          item.checked = false;
        });
        this.$refs.h5.batchState = true;
        this.$refs.picture.batchState = true;
        this.$refs.video.batchState = true;
        // this.$refs.picture.isDMB = true;
        // this.$refs.video.isDMB = true;
        // this.$refs.picture.isContent = true;
        // this.$refs.video.isContent = true;
        // this.$refs.h5.isContent = true;
        // this.$refs.h5.isContent = true;
      }, 500);
    },
    //  弹出框取消按钮
    alertOk() {
      this.showAlertBox = false;
      this.$parent.loading = true;
      this.create_draft();
    },
    //tab切换
    handleClick(tab, event) { },
    //选择框
    checked(idx) {
      this.checkShow = !this.checkShow;
      this.checkIndex = idx;
    },
    get_btpub_detail_info(btplan_id) {
      const params = {};
      if (btplan_id) {
        params["btplan_id"] = btplan_id;
      } else {
        params["btplan_id"] = this.$store.state.deployCfInfo["btplan_id"];
      }

      quick_get_btplan_detail(params).then(data => {
        if (data.rst == "ok") {
          console.log(data, "datadatatadasdas");
          this.cf_id = data["data"][0]["contents"]
            ? data["data"][0]["contents"][0]["ref_id"]
            : "";
          this.generalData = data["data"][0];
          this.DeployForm = data["data"][0]["pub_info"]
            ? data["data"][0]["pub_info"]["pub_json"]
            : {};
          this.v_or_h = data["data"][0]["play_info"]["v_or_h"];
          this.bt_name = data["data"][0]["name"];
          if (this.cf_id != "") {
            get_classify_detail({
              g_cf_id: data["data"][0]["contents"][0]["ref_id"],
              offset: 0, limit: 300
            }).then(dataRes => {
              console.log(dataRes, "res");
              this.cs_list = dataRes["data"][0]["cs_list"];
            });
          } else {
          }
          console.log(this.$parent, "$parent");
          this.$parent.loading = false;
        } else {
        }
      });
    },
    create_draft() {
      if (this.disabled) return;
      this.disabled = true;
      const selectImg = [];
      let type = "";
      if (this.activeName == "first") {
        type = "photo";
      } else if (this.activeName == "second") {
        type = "video";
      } else if (this.activeName == "third") {
        type = "h5";
      }
      this.imgList.forEach(item => {
        selectImg.push({
          type: type,
          file_id: item.file_id ? item.file_id : item.cs_id
        });
      });
      // const selectVideo = this.videoList.filter(item => item.selected);
      // const selectTemplate = this.templateList.filter(item => item.selected);
      const selectVideo = [];
      const selectTemplate = [];
      // const selectCounts = this.getCotentLength()
      const selectCounts = 1;
      if (selectCounts == 0) {
        this.disabled = false;
        this.$message.error("请选择投放内容");
        return;
      }
      if (selectCounts <= 5) {
        const params = [...selectImg, ...selectVideo, ...selectTemplate];
        let selectedDraft = [];
        let selectTplList = [];
        params.forEach(select => {
          if (select.type == "photo" || select.type == "video") {
            const result = {
              tpl_name:
                select.type == "photo"
                  ? this.v_or_h == 0
                    ? "HD_welposterH_0002854"
                    : "HD_welposter_0002853"
                  : this.v_or_h == 0
                    ? "HD_video_tplH_0007"
                    : "HD_video_tpl_0007",
              file_id: select.file_id,
              file_type: select.type == "photo" ? "img" : "video"
            };
            selectedDraft.push(result);
          } else {
            selectTplList.push({ file_id: select.file_id });
          }
        });
        //只有图片视频无模板
        this.$parent.loading = true;
        if (selectedDraft.length > 0 && selectTplList.length == 0) {
          console.log("whtaat");
          this.batch_create_draft(selectedDraft, true);
        }
        //只有模板无图片视频
        if (selectTplList.length > 0 && selectedDraft.length == 0) {
          console.log("whtaat?");
          this.edit_group_classify_cs("", selectTplList, true);
        }
        //既有有模板和图片视频
        if (selectedDraft.length > 0 && selectTplList.length > 0) {
          this.batch_create_draft(selectedDraft, true);
          this.edit_group_classify_cs("", selectTplList, false);
        }
      } else {
        this.$message.error("专辑内容不能超过5个");
      }
    },
    //创建卡片并讲卡片添加到专辑
    async batch_create_draft(selected, go) {
      const params = {
        shop_group_id: Number(localStorage.getItem("group_id")),
        //  cf_id:this.cf_id,
        attr_list: selected
      };
      batch_create_draft(params).then(data => {
        this.disabled = false;
        if (data.rst == "ok") {
          console.log("data", data);
          const cs_list = data.data[0].cs_list;
          if (this.cf_id != "") {
            this.edit_group_classify_cs(cs_list, "", go);
          } else {
            create_group_classify({
              group_id: localStorage.getItem("group_id"),
              v_or_h: this.v_or_h,
              cf_label: `${this.bt_name}_${new Date().getTime()}`,
              attributes: {
                play_mode: "full_day"
              }
            }).then(ress => {
              console.log(ress, "resss");
              this.cf_id = ress["data"][0]["g_cf_id"];
              add_btpub_content({
                btplan_id: this.btplan_id,
                ref_id: this.cf_id,
                ref_source: "ds"
              });
              this.edit_group_classify_cs(cs_list, "", go);
            });
          }
        } else {
          this.$message.error("添加失败");
          this.$parent.loading = false;
        }
      });
    },
    //卡片加入专辑
    async edit_group_classify_cs(cs_list, tpl_list, go) {
      console.log(this.activeName, "this.activeName");
      if (this.activeName != "third") {
        const params = {
          g_cf_id: this.cf_id,
          g_group_id: Number(localStorage.getItem("group_id")),
          attrs: {}
        };
        cs_list.length > 0 ? (params.attrs.add_cs_list = cs_list) : "";
        tpl_list && tpl_list.length > 0
          ? (params.attrs.content_list = tpl_list)
          : "";
        console.log(params, "1para");
        edit_group_classify_cs(params).then(data => {
          this.disabled = false;
          console.log("data", data);
          if (data.rst == "ok") {
            if (go) {
              this.$message.success("添加成功");
              this.pictureList = true;
              this.$parent.loading = false;
              this.get_btpub_detail_info(this.btplan_id);
            }
          } else {
            this.$message.error("添加失败", "error");
            this.get_btpub_detail_info(this.btplan_id);
            // this.disabled = false
          }
          setTimeout(() => {
            this.get_btpub_detail_info(sessionStorage.getItem("btplan_id"));
          }, 500);
        });
      } else {
        const params = {
          g_cf_id: this.cf_id,
          g_group_id: Number(localStorage.getItem("group_id")),
          attrs: {
            add_cs_list: []
          }
        };
        console.log(tpl_list, "cs_listcs_listcs_listcs_list");
        console.log(tpl_list, "tpl_list");
        // console.log(cs_list,tpl_list)
        tpl_list.forEach(item => {
          params.attrs.add_cs_list.push(item.file_id);
        });
        // tpl_list.length > 0 ? params.attrs.add_cs_list = tpl_list : ''
        // tpl_list && tpl_list.length > 0 ? params.attrs.content_list = tpl_list : ''
        edit_group_classify_cs(params).then(data => {
          this.disabled = false;
          console.log(12138);
          console.log("data", data);
          if (data.rst == "ok") {
            if (go) {
              this.$parent.loading = false;
              this.$message.success("添加成功");
              this.pictureList = true;
              this.get_btpub_detail_info(this.btplan_id);
            }
          } else {
            this.$message.error("添加失败", "error");
            this.$parent.loading = false;
            // this.disabled = false
            this.get_btpub_detail_info(this.btplan_id);
          }
          setTimeout(() => {
            this.get_btpub_detail_info(sessionStorage.getItem("btplan_id"));
          }, 500);
        });
      }
    },
    // 删除
    deleteCard(item) {
      console.log(item);
      this.$parent.loading = true;
      const params = {
        g_cf_id: item.sche_id,
        g_group_id: item.group_id,
        attrs: {
          del_cs_list: [item.cs_id]
        }
      };

      edit_group_classify_cs(params).then(res => {
        console.log(res);
        if (res.rst == "ok") {
          this.$message.success("删除成功");
          this.get_btpub_detail_info(this.btplan_id);
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    // 预览
    Preview(type, val) {
      if (type == "video") {
        this.setH5Preview(val.dynamic_url);
        setTimeout(() => {
          this.$refs.previsualization.$refs[
            "iframe"
          ].contentWindow.animation_run();
        }, 200);
      } else {
        // console.log(window,"xxxx");
        this.setH5Preview(val.dynamic_url);
      }
    },
    pushing() {
      quick_exec_btplan_handler({
        btplan_id: this.btplan_id
      }).then(res => {
        if (res.rst == "ok") {
          this.$message.success("发布成功");
          this.$router.push("/deploy/quickHistory");
        } else {
          this.$message.error("发布失败");
        }
      });
    }
  }
};
</script>

<style scoped lang='scss'>
.dialog_btns {
  height: 50px;

  button {
    margin: 0 20px 0 0 !important;
  }
}
</style>

<style scoped>
.GeneralCreate {
  width: 100%;
  /* padding: 0 14px 0 13px; */
  /* background-color: #f8f7f7; */
  margin-top: 20px;
  border: rgba(229, 229, 229, 1) solid 1px;
}

.actionBtn {
  position: relative;
  width: 100%;
  height: 12px;
  padding-right: 71px;
}

.pageHead {
  height: 58px;
  line-height: 58px;
  font-size: 14px;
  color: #505050;
  font-weight: bold;
  border-bottom: 1px solid #ecebeb;
}

.el-icon-back {
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}

button {
  position: absolute;
  right: 110px;
  margin: 15px 15px;
}

.el-button--primary {
  right: 0;
}

ul {
  margin-top: 40px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 14px;
  /* border: rgba(229, 229, 229, 1) solid 1px; */
  text-align: center;
  list-style: none;
  padding: 10px 10px;
  flex-wrap: wrap;
  display: flex;
  align-content: flex-start;
}

ul>li>img {
  width: 284px;
  height: 157px;
  margin: 5px 0;
  border: rgba(229, 229, 229, 1) solid 1px;
  position: relative;
  z-index: 1;
}

ul>li {
  width: 292px;
  height: 295px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 15px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin: 10px 9px;
}

.arcoss {
  width: 284px;
  height: 300px;
  margin: 5px 0;
  border: 1px solid rgba(299, 299, 299, 1);
}

.arcossimg {
  object-fit: contain;
}

.vision {
  height: 360px;
  width: 207px;
  margin: 5px 0;
  border: 1px solid rgba(299, 299, 299, 1);
}

.visionimg {
  width: 207px;
  height: 230px;
  object-fit: contain;
}

ul>li>img {
  border: 1px solid #ccc;
}

ul>li>div {
  margin-top: 12px;
  color: rgba(80, 80, 80, 1);
  font-size: 12px;
  text-align: left;
  margin-left: 12px;
  font-weight: bold;
}

/*取消*/
.btns {
  display: flex;
  margin-top: -12px;
  padding-bottom: 10px;
  justify-content: flex-end;
  width: 100%;
}

.arcoss:hover .delete {
  display: block;
}

.vision:hover .delete {
  display: block;
}

.delete {
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  position: absolute;
  left: 46%;
  top: 20%;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  z-index: 2;
  display: none;
  z-index: 3;
}

.img_tips {
  position: absolute;
  top: -7px;
  left: -12px;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 0;
  text-align: center;
  line-height: 156px;
}

.arcoss_tips {
  height: 156px;
  width: 284px;
}

.vision_tips {
  height: 230px;
  width: 207px;
}

.video_make_info {
  position: absolute;
  top: -6px;
  right: -1px;
  color: #fff;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 2;
}

.arcoss:hover .delete1 {
  display: block;
}

.vision:hover .delete1 {
  display: block;
}

.delete1 {
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  position: absolute;
  left: 28%;
  top: 20%;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  display: none;
  z-index: 3;
}

.pushing {
  width: 100%;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 10%;
}

.ab {
  clear: both;
  right: 52% !important;
}
</style>
