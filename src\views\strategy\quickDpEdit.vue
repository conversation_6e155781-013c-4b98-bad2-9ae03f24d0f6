<template>
  <div class="pushlishing_setting flex">
    <!-- 左侧详情 -->
    <i class="el-icon-back cursor"
      style="font-size:24px;color:var(--btn-background-color);margin: 15px 0 0 0px; position: relative;left: 30px;top: -10px;"
      @click="goBack()" v-if="$parent.btnState == true"></i>
    <div class="left">
      <!-- {{DetaliData}}12 -->
      <div class="left_header">
        <span>设置详情</span>
        <span style="color:rgba(42, 130, 228, 1);cursor: pointer;" @click.stop="editDetail"
          v-show="!showInput">编辑</span>
        <span style="color:rgba(42, 130, 228, 1);cursor: pointer;" @click.stop="editOk" v-show="showInput">确定</span>
      </div>
      <!-- 数据展示 -->
      <div class="left_content" v-if="!showInput">
        <p class="left_details">
          <span>发布名称：</span>
          <span>{{ DetaliData.name }}</span>
        </p>
        <p class="left_details">
          <span style="width:70px">发布周期：</span>
          <span>{{ DetaliData.start_time }}--{{ DetaliData.end_time }}</span>
        </p>
        <!-- {{platform}} -->
        <p v-if="DetaliData.platform == 3" class="left_details">
          <span>屏幕类型：</span>
          <!-- <span>
            <span v-if="DetaliData.usage_type == this.typesKey[0]">{{ this.types[0] }}</span>
            <span v-else-if="DetaliData.usage_type == this.typesKey[1]">{{ this.types[1] }}</span>
            <span v-else-if="DetaliData.usage_type == this.typesKey[2]">{{ this.types[2] }}</span>
          </span> -->
          <span v-for="item in typesKey">
            <span v-if="issueMessage.usage_type == item[0]">{{ item[1] }}</span>
          </span>
        </p>

        <p v-else class="left_details">
          <span>屏幕规格：</span>
          <!-- class:  screen_v 竖，screen_h 横 -->
          <span v-if="DetaliData.platform == 2">
            <span v-for="item in dmb_spec" :key="item" :class="item == 0 ? 'screen_h' : 'screen_v'"></span>
            <span v-if="DetaliData.platform == 2" style="margin-left:5px">
              餐牌组
              1*{{ dmb_spec.length }}{{ item }}
            </span>
          </span>
          <span v-else-if="DetaliData.platform == 4">
            <span v-for="item in vs_spec.xCount" :key="item"
              :class="vs_srec.v_or_h == 'h' ? 'screen_h' : 'screen_v'"></span>
            <span style="margin-left:5px">联屏 {{ vs_srec.yCount }}* {{ vs_srec.xCount }}</span>
          </span>
        </p>

        <!-- 经营时段类型 -->
        <p class="left_details" v-if="platform == 2">
          <span>经营时段类型：{{ DetaliData.play_info.daypartgroup }}</span>
        </p>
        <p class="left_details">
          <span>播放类型：</span>
          <span v-if="DetaliData.play_style == 1">轮播</span>
          <span v-else>独占</span>
        </p>
        <!-- v-if="platform == 3" -->
        <!-- {{DetaliData.platform }}
        {{DetaliData.play_info.play_mode}}-->
        <div v-if="DetaliData.platform == 3" class="left_details">
          <span>播放详情：</span>
          <span v-if="DetaliData.play_info.play_mode == 'full_day'">按全天</span>
          <div
            v-else-if="DetaliData.play_info.play_mode == 'week_day' || DetaliData.play_info.play_mode == 'week_range'">
            <span>按星期</span>
            <span v-for="(item, index) in weekDay" :key="index" style="margin:0 5px">星期{{ item }}</span>
          </div>
          <div v-else>
            <span>按指定时段</span>
            <span style="margin-tospan:10px">{{ DetaliData.play_info.time_ranges[0] }}</span>
          </div>
          <!-- <span v-else-if="DetaliData.play_info.play_mode == 'week_day' || DetaliData.play_info.play_mode == 'week_range'" >按星期</span> -->
        </div>
        <!-- {{DetaliData.play_info.play_mode}}
        {{platform}}-->

        <p v-if="DetaliData.platform == 4" class="left_details">
          <span>播放详情：</span>
          <span v-if="DetaliData.play_info.play_mode == 'full_day'">按全天</span>
          <span
            v-else-if="DetaliData.play_info.play_mode == 'week_day' || DetaliData.play_info.play_mode == 'week_range'">按星期</span>
          <span v-else-if="DetaliData.play_info.play_mode == 'single_use_range'">按指定时段</span>
          <!-- <span v-else-if="DetaliData.play_info.play_mode ==2">按星期</span> -->
        </p>

        <!-- <p v-if="DetaliData.platform == 4" class="left_details">
                    <span>播放间隔：</span>
                    {{ waitting_time }} <span style="margin-left:10px"> 秒 </span>
        </p>-->
      </div>
      <!-- 编辑   -->
      <div class="left_content" v-else>
        <p class="left_details">
          <span>发布名称：</span>
          <el-input v-model="setDetail.name" placeholder="请输入发布名称" class="editInput"></el-input>
        </p>
        <div class="left_details">
          <span style="width:70px">发布周期：</span>
          <div class="partTime">
            <el-date-picker v-model="timeDate" type="datetimerange" range-separator="至" @change="changePartTime"
              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </div>
          <!-- <span>{{ setDetail.start_time }}--{{ setDetail.end_time}}</span> -->
        </div>
        <!-- <p v-if="platform == 3" class="left_details">
        </p>-->
        <!-- <p v-if="platform == 3" class="left_details">
        <span>屏幕规格：</span>-->
        <!-- class:  screen_v 竖，screen_h 横 -->
        <!-- <span v-if="platform == 2">   
                        <span v-for="item in dmb_spec" :key="item" :class="item == 0 ? 'screen_h' : 'screen_v'"></span>
                        <span v-if="platform == 2" style="margin-left:5px">DMB
                            1*{{ dmb_spec.length }}{{ item }}</span>

                    </span>
                    <span v-else-if="platform == 4">
                        <span v-for="item in vs_spec.xCount" :key="item"
                            :class="vs_spec.v_or_h == 'h' ? 'screen_h' : 'screen_v'"></span>
                        <span style="margin-left:5px">联屏 {{ vs_spec.yCount }}* {{ vs_spec.xCount }}</span>
                    </span>
        </p>-->

        <p class="left_details">
          <span>播放类型：</span>
          <!-- <span v-if="setDetail.play_style == 1">轮播</span>
          <span v-else>独占</span>-->
          <el-radio v-model="setDetail.play_style" label="1">轮播</el-radio>
          <el-radio v-model="setDetail.play_style" label="2">独占</el-radio>
        </p>
        <!-- {{setDetail.play_info.play_mode}}
        {{setDetail.platform}}-->
        <p v-if="setDetail.platform == 3 || setDetail.platform == 4" class="left_details">
          <span>播放详情：</span>
          <el-radio v-model="setDetail.play_info.play_mode" label="1">按全天</el-radio>
          <!-- <el-radio v-model="setDetail.play_info.play_mode" label="2">周时段</el-radio> -->
          <!-- <span v-if="setDetail.play_info.play_mode == 'full_day'">全天</span>
          <span v-else-if="setDetail.play_info.play_mode == 'week_day'">周时段</span>-->
        </p>
        <!-- {{setDetail.play_info.play_mode}} -->
        <div class="weekStyle" style="display:flex;align-items:center" v-if="setDetail.platform != 2">
          <el-radio v-model="setDetail.play_info.play_mode" label="2" style="vertical-align: middle;">按星期</el-radio>
          <!-- {{weeks}} -->
          <!-- {{checkList}} -->
          <div class="space">
            <!-- <el-checkbox :label="item" v-for="(item,index) in weeks" :key="index" >{{item}}</el-checkbox> -->
            <el-checkbox-group v-model="checkList" v-if="setDetail.play_info.play_mode == 2">
              <el-checkbox :label="item.value" v-for="(item, index) in weeks" :key="index">
                {{ item.label
                }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- 按星期的时间 -->
        <div class="weekPart" v-if="checkList.length > 0 && setDetail.play_info.play_mode == 2">
          <el-time-picker v-model="weekTime.weekStarttime" :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }" value-format="HH:mm:ss" placeholder="任意时间点"></el-time-picker>
          <br />
          <el-time-picker v-model="weekTime.weekEndtime" :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }" value-format="HH:mm:ss" placeholder="任意时间点"></el-time-picker>
        </div>
        <div class="weekStyle" v-if="setDetail.platform != 2">
          <el-radio v-model="setDetail.play_info.play_mode" label="3">按指定时段</el-radio>
          <div class="partSpe" v-if="setDetail.play_info.play_mode == '3'">
            <el-date-picker v-model="toSpeParts" type="datetimerange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" :picker-options="pickerOptions0"></el-date-picker>
            <!-- <el-time-picker
              is-range
              v-model="toSpeParts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            ></el-time-picker>-->
          </div>
        </div>
        <p v-if="DetaliData.platform == 4" class="left_details">
          <span>播放间隔：</span>
          <el-select v-model="waitting_time" filterable @blur="SelectBlur" style="width:100px">
            <el-option v-for="item in timeSelect" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
          <span style="margin-left:10px">秒</span>
        </p>
        <div v-if="setDetail.platform == 4" class="left_details">
          <!-- <span>播放间隔：</span>
                    <div class="doubleSelect">
                        <el-select v-model="timeSelect" placeholder="请选择">
                            <el-option
                            v-for="item in times"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                         <el-select v-model="timesDesSelect" placeholder="请选择">
                            <el-option
                            v-for="item in timesDes"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
          </div>-->
          <!-- <span>15秒</span> -->
        </div>
      </div>
      <div v-if="DetaliData.platform == 2" class="daypart">
        <div class="daypart_header">经营时段详情</div>
        <div v-for="(item, index) in timeframe" :key="item.name" class="daypart_info">
          <span class="num">{{ index + 1 }}</span>
          <span>时段名称： {{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧筛选 -->
    <div class="flex right flex-1">
      <!-- 按市场门店筛选 -->
      <div class="search_wrap">
        <div class="flex flex-1">
          <div>
            <span style="color:rgba(56, 56, 56, 1)">营运市场：</span>
            <el-select v-model="queryList.marketname" clearable multiple filterable placeholder="请选择投放市场" size="small"
              collapse-tags style="width:166px;margin-right:12px" @change="clearMarker">
              <el-option v-for="item in options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
            </el-select>
          </div>
          <div>
            <!-- <span style="color:rgba(56, 56, 56, 1)">按门店：</span>
                        <el-input placeholder="门店ID/名称" size="small" prefix-icon="el-icon-search"
                            @keyup.enter.native='handleSearch' v-model="queryList.search"
            style="width:166px;margin-right:12px"></el-input>-->
          </div>
        </div>
        <!-- <div class="reset" @click="reset(1)">
                    重置
        </div>-->
        <el-button size="small" style="float:right" @click="reset(1)">重置</el-button>
      </div>
      <!-- 按标签筛选 -->
      <div class="tags_filtering">
        <div>
          <!-- <span style="color:rgba(56, 56, 56, 1)">标签筛选：</span>
                    <el-select v-model="queryList.changeCondition"  filterable placeholder="" size="small" style="width:166px;margin-right:12px">
          <el-option v-for="item in condition" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
          <!-- </el-select> -->
          <span style="color:rgba(56, 56, 56, 1)">门店类型：</span>

          <el-select v-model="queryList.shopname" clearable multiple collapse-tags filterable placeholder="请选择投放市场"
            size="small" @change="selectTags" style="width:206px;margin-right:12px">
            <el-option v-for="item in shopOptions" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
          </el-select>
          <el-button size="small" style="float:right" @click="searchShops">查询</el-button>
        </div>
        <div class="tags_wrap flex" v-show="DetaliData.platform == 3 || DetaliData.platform == 2">
          <!-- <div style="margin-top:6px">选中标签：</div>
                    <div class="tags_list flex flex-1">
                        <div v-for="(item, index) in tagsList" class="every_tag" :key="index">
                            <img src="@/assets/img/gray_tag_icon.svg" style="width:18px;height:18px;margin-top:-2px"
                                alt="">
                            <span class="tags_name">{{ item.value }}</span>
                            <i class="el-icon-close remove_tag" @click="removeTag(item, index)"></i>
                        </div>
          </div>-->
          <!-- <div class="reset" @click="reset(2)" style="margin-top:6px">
                        重置
          </div>-->
          <span style="color:rgba(56, 56, 56, 1);padding-top: 8px;">门店标签:</span>
          <el-select v-model="screenTagsValue" placeholder="请选择门店标签类型"
            style="width:206px;margin-left: 15px;margin-bottom: 10px;" multiple="true">
            <!-- <el-option v-for="item in screenTages" :key="item" :label="item" :value="item">
            </el-option>-->
            <el-option v-for="item in shopTages" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <!-- <el-button size="small" style="float:right" @click="reset(2)">重置</el-button> -->
        </div>
        <div class="tags_wrap flex" v-show="DetaliData.platform == 4">
          <span style="color:rgba(56, 56, 56, 1);padding-top: 8px;">联屏标签:</span>
          <el-select v-model="screenTagsValue" placeholder="请选择屏幕标签类型"
            style="width:206px;margin-left: 15px;margin-bottom: 10px;" multiple="true">
            <el-option v-for="item in screenTagesAsArray" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </div>
      </div>
      <!-- 选中的门店 -->
      <div class="shops_wrap flex">
        <div class="shop_title flex">
          <!-- {{totalShop}}
          {{noSelectShops.length}}-->
          <!-- <div>已选择{{ totalShop - noSelectShops.length }}个门店</div> -->
          <div></div>
          <!-- <el-button size="small" style="float:right" @click="reset(3)">重置</el-button> -->
        </div>
        <div class="shop_content flex">
          <div v-for="(item, index) in shopsList" :key="index" class="every_shop flex">
            <el-checkbox v-model="item.checked" style="margin-right:5px" @change="selectCheckout(item)"></el-checkbox>
            <img src="@/assets/img/home_img/shop.png" style="width:30px;height:30px;margin-top:-2px" alt />
            <!-- <div class="flex-1 shop_info" style="width:65%;"> -->
            <div class="flex-1 shop_info" style="width:65%;overflow:hidden;" :title="item.storecode">
              <p>{{ item.storecode }}</p>
              <p class="text_overflow" :title="item.storename">{{ item.storename }}</p>
            </div>
          </div>
        </div>
        <div class="paginat" v-show="shopsList.length != 0">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
            :total="totalShop"></el-pagination>
        </div>
      </div>
      <div style="position: absolute;bottom: 20%;left: 60%;">
        <el-button type="primary" @click="saveEditStrategy">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { get_adm_datas } from "@/api/shopManage/shop";
import {
  get_daypart_data,
  edit_btpubplan
} from "@/api/contentdeploy/contentdeploy";
import { get_btpub_detail } from "@/api/contentdeploy/contentdeploy";
import {
  quick_get_btplan_detail,
  quick_exec_btplan_handler,
  api_create_vs_cf,
  add_btpub_content
} from "@/api/strategy/strategy";

import { formatting, formatDateHms } from "@/utils/formatDate";
import { get_screen_tags } from "@/api/system/label";
import { get_shop_tags, get_tags_list } from "@/api/system/label";
import { datas_filter_cond } from "@/api/commonInterface";
import {
  publish_area,
  publish_launch,
  update_remodified_cf,
  api_vs_cf_notify
} from "@/api/contentdeploy/contentdeploy";

export default {
  components: {},
  data() {
    return {
      queryList: {
        shopname: "",
        marketname: "",
        changeCondition: 1
      },
      options: [],
      timesDesSelect: "时",
      timeSelect: 1,
      times: [
        {
          value: "1",
          label: "1"
        },
        {
          value: "2",
          label: "2"
        },
        {
          value: "3",
          label: "3"
        },
        {
          value: "4",
          label: "4"
        },
        {
          value: "5",
          label: "5"
        }
      ],

      timesDes: [
        {
          value: "时",
          label: "时"
        },
        {
          value: "分",
          label: "分"
        },
        {
          value: "秒",
          label: "秒"
        }
      ],
      shopOptions: [],
      pickerOptions0: {
        disabledDate: time => {
          return (
            formatting(time.getTime()) > formatting(this.timeDate[1]) ||
            formatting(time.getTime()) < formatting(this.timeDate[0])
          );
        }
      },
      condition: [
        {
          label: "且(包含全部已选标签,标签选择不能超过十个)",
          value: 2
        },
        {
          label: "或(包含任何一个已选标签)",
          value: 1
        },
        {
          label: "非(不包含任何已选标签)",
          value: 0
        }
      ],
      tagsList: [],
      shopsList: [],
      dsusage_type: "",
      v_or_h: "",
      vs_spec: {},
      dmb_spec: "",
      vs_specs: "",
      daypartgroup: "",
      DetaliData: {},
      totalShop: 0,
      timeframe: [],
      showInput: false,
      setDetail: [],
      timeDate: [],
      checkList: [],
      weeks: [
        {
          label: "一",
          value: 2
        },
        {
          label: "二",
          value: 3
        },
        {
          label: "三",
          value: 4
        },
        {
          label: "四",
          value: 5
        },
        {
          label: "五",
          value: 6
        },
        {
          label: "六",
          value: 7
        },
        {
          label: "日",
          value: 1
        }
      ],
      weekDay: [],
      optionsType: [],
      // 指定时段
      toSpeParts: [],
      weekTime: {
        weekStarttime: "",
        weekEndtime: ""
      },
      types: [],
      typesKey: [],
      pldStatus: "",
      attr_list: {},
      timeSelect: [
        {
          value: 15,
          label: 15
        },
        {
          value: 30,
          label: 30
        },
        {
          value: 45,
          label: 45
        },
        {
          value: 60,
          label: 60
        }
      ],
      waitting_time: "",
      noSelectShops: [],
      ShopTotal: "",
      page: 0,
      pagesize: 10,
      screenTages: [],
      screenTagsValue: []
    };
  },
  props: {
    screen_type: {
      type: String
    }

    // pubForm: {
    //     type: Object
    // }
  },
  computed: {},
  watch: {
    screen_type: {
      handler(newValue, oldValue) {
        this.dsusage_type = newValue;
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.btplan_id = this.$route.query.btplan_id;
    this.getSelectDataList();
    this.getScreenTags();
    this.get_btpub_detail_info();
    this.get_shop_tags();
    this.$parent.btnState = true;
    setTimeout(() => {
      console.log(
        this.$store.state.deployDataFilters,
        "this.$store.state.deployDataFilters"
      );
      this.options = this.$store.state.deployDataFilters[6]["options"];
      console.log(this.options, "this.options");
      this.shopOptions = this.$store.state.deployDataFilters[5]["options"];
      this.optionsType = this.$store.state.deployDataFilters[3]["options"];
      this.timeDate[0] = this.DetaliData.create_tm;
      this.timeDate[1] = this.DetaliData.end_time;
      // console.log('!',(this.timeDate[1]));
    }, 500);
  },
  mounted() {
    setTimeout(() => {
      console.log(this.pubForm, "thjisp");
    }, 650);
  },
  methods: {
    reset(val) {
      switch (val) {
        case 1:
          console.log("重置门店筛选条件");
          break;
        case 2:
          this.tagsList = [];
          this.queryList.shopname = [];
          break;
        case 3:
          (this.shopsList = []), (this.totalShop = 0);
          break;
      }
    },
    // 移除选中的标签
    removeTag(val, index) {
      this.queryList.shopname.forEach((item, index1) => {
        if (val.key == item) {
          this.queryList.shopname.splice(index1, 1);
        }
      });
      this.tagsList.splice(index, 1);
    },
    getDayPart() {
      const params = {
        classModel: "SysDayPart",
        page: 0,
        size: 100
      };
      get_daypart_data(params).then(res => {
        console.log("res12123", res);
        res.data[0].content.forEach((item, index) => {
          if (item.group_name == this.daypartgroup) {
            this.timeframe = item.attr_list;
          }
        });
      });
    },
    selectTags(val) {
      this.tagsList = [];
      val.forEach(item => {
        this.shopOptions.forEach(item1 => {
          if (item == item1[0]) {
            // console.log(item1);
            this.tagsList.push({ key: item1[0], value: item1[1] });
          }
        });
      });
    },
    searchShops() {
      let params = {
        classModel: "GroupShop",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.page, //起始页码,
        size: this.pagesize, //每页数据量,
        blurry: this.queryList.search, //搜索门店编号或者名称模糊查找
        opsmarkets: this.queryList.marketname, //营运市场编号,多个市场
        itmarket: "", //it市场
        storetypes: this.queryList.shopname, //门店类型
        daypart_groupname: this.daypartgroup, //系统门店day类型
        dmb_spec: this.dmb_spec, //门店dmb规格
        dsusage_type: this.dsusage_type, //门店屏幕类型
        screen_tags: this.screenTagsValue, //门店便签 list
        filtertags_type: "", //门店便签#"must> AND, must_not> OR should > NOT
        v_or_h: this.v_or_h,
        vs_spec: this.vs_spec
        // "":""//其他参数参考　datas_filter_cond　定义
      };
      console.log(params);
      if (this.DetaliData.platform == 4) {
        params.tags_list = this.screenTagsValue;
      } else {
        params.screen_tags = this.screenTagsValue;
      }
      get_adm_datas(params).then(res => {
        console.log(res.data[0].content, "editPushngRes");
        res.data[0].content.forEach(item => {
          item.checked = false;
        });
        this.shopsList = res.data[0].content;
        console.log("this.shopsList", this.shopsList);
        this.$emit("SearchState", this.shopsList);
        this.totalShop = res.data[0].totalElements;

        this.shopsList.forEach(item => {
          if (this.noSelectShops.indexOf(item.shop_id) != -1) {
            item.checked = false;
          } else {
            item.checked = true;
          }
        });
      });
    },
    // 编辑
    editDetail() {
      this.showInput = true;
      this.setDetail = this.DetaliData;
      this.timeDate[0] = this.DetaliData.start_time;
      this.timeDate[1] = this.DetaliData.end_time;
      // console.log(this.DetaliData.play_info.play_mode,'this.DetaliData.play_info.play_mode');
      // 普通
      if (this.DetaliData.platform == 3) {
        this.pldStatus = "pt";
        // alert(this.DetaliData.platform)
        if (this.DetaliData.play_info.play_mode == "full_day") {
          this.setDetail.play_info.play_mode = "1";
          // console.log(this.DetaliData.play_info.play_mode, 'mode');
        } else if (
          this.DetaliData.play_info.play_mode == "week_day" ||
          this.DetaliData.play_info.play_mode == "week_range"
        ) {
          // alert(this.DetaliData.play_info.play_mode)
          this.setDetail.play_info.play_mode = "2";
        } else if (this.DetaliData.play_info.play_mode == "single_use_range") {
          let temp = this.DetaliData.play_info.time_ranges[0].split("~");
          this.setDetail.play_info.play_mode = "3";
          this.toSpeParts[0] = temp[0];
          this.toSpeParts[1] = temp[1];
          console.log(this.toSpeParts, "toSpeParts");
        }
      }
      // 轮播
      if (this.DetaliData.play_style == 1) {
        this.setDetail.play_style = "1";
      } else {
        this.setDetail.play_style = "2";
      }
      let tempWeek = [];
      let tempTime = [];
      // this.setDetail.play_info.week_day
      if (this.setDetail.play_info.week_day) {
        // alert('week_day1`')
        this.setDetail.play_info.week_day.forEach((item, index) => {
          this.weeks.forEach(week => {
            if (item == week.value) {
              this.checkList.push(week.value);
            }
          });
          let times = this.DetaliData.play_info.time_ranges[0].split("~");
          this.weekTime.weekStarttime = times[0];
          this.weekTime.weekEndtime = times[1];
        });
      }
      if (this.DetaliData.platform == 2) {
        this.pldStatus = "dmb";
        // if (this.DetaliData.play_info.play_mode == 'full_day') {
        //     this.setDetail.play_info.play_mode = "1";
        //     // console.log(this.DetaliData.play_info.play_mode, 'mode');
        // } else if (this.DetaliData.play_info.play_mode == 'week_day' || this.DetaliData.play_info.play_mode == 'week_range') {
        //     this.setDetail.play_info.play_mode = "2";
        // }
        // } else {
        //     let temp = this.DetaliData.play_info.time_ranges.split('~')
        //     this.setDetail.play_info.play_mode = "3";
        //     this.toSpeParts[0] = temp[0];
        //     this.toSpeParts[1] = temp[1];
        //     // console.log(this.toSpeParts, 'toSpeParts');
        // }
      }
      // 按全天
      if (this.DetaliData.platform == 4) {
        this.pldStatus = "lp";
        if (
          this.DetaliData.play_info.play_mode == "week_day" ||
          this.DetaliData.play_info.play_mode == "week_range"
        ) {
          this.setDetail.play_info.play_mode = "2";
          let num = "";
          let arr = [];
          if (this.setDetail.play_info.week_range) {
            // alert('week_day1`')
            this.setDetail.play_info.week_range.forEach((item, index) => {
              console.log(item, "[item]");
              num = item.split("|")[0];
              this.weeks.forEach(week => {
                if (num == week.value) {
                  this.checkList.push(week.value);
                }
              });
              arr = this.DetaliData.play_info.week_range[index].split("|")[1];
              let times = arr.split("~");
              this.weekTime.weekStarttime = times[0];
              this.weekTime.weekEndtime = times[1];
            });
          }
          //   this.weeks = this.setDetail.play_info.week_day;

          // console.log(this.DetaliData.play_info.play_mode, 'mode');
        } else if (this.DetaliData.play_info.play_mode == "full_day") {
          this.setDetail.play_info.play_mode = "1";
        } else {
          this.setDetail.play_info.play_mode = "3";
          console.log(this.DetaliData, "this.DetaliData12");
          let temp = this.DetaliData.play_info.single_use_range.split("~");
          this.setDetail.play_info.play_mode = "3";
          this.toSpeParts[0] = temp[0];
          this.toSpeParts[1] = temp[1];
          // 指定时段
          // this.toSpeParts[0] =
          // this.toSpeParts[1] =
        }
        console.log(this.setDetail, "this.setDetail.play_info.week_range");
        //  this.setDetail.play_info.week_range.forEach(item=>{
        //     // console.log(item,'00');
        //     tempWeek.push(item.split("|")[0])
        //     tempTime = item.split("|")[1]
        //     })
        // // }
        // this.weekTime.weekStarttime = tempTime.split(',')[0]
        // this.weekTime.weekEndtime = tempTime.split(',')[1]
        console.log(
          this.weekTime.weekStarttime,
          this.weekTime.weekEndtime,
          "1"
        );
      }
      // console.log(tempWeek,'tempWeek');
      // if(this.setDetail.play_info.week_range){
      //        tempWeek.forEach((item, index) => {
      //         this.weeks.forEach(week => {
      //             if (item == week.label) {
      //                 this.checkList.push(week.value)
      //             }
      //         })
      //     })
      // }
    },
    // 确定
    editOk() {
      // 赋值
      this.showInput = false;
      // this.timeDate[0] = this.timeDate[0].split(" ");
      let start_time = formatDateHms(this.timeDate[0]);
      let end_time = formatDateHms(this.timeDate[1]);
      if (this.DetaliData.platform == 3) {
        if (this.setDetail.play_info.play_mode == "1") {
          this.DetaliData.play_info.play_mode = "full_day";
          this.attr_list.play_mode = "full_day";
        } else if (this.setDetail.play_info.play_mode == "2") {
          this.DetaliData.play_info.play_mode = "week_range";
          this.attr_list.play_mode = "week_range";
          // weekTime.weekEndtime
          this.attr_list.time_ranges = [];
          this.attr_list.time_ranges.push(
            this.weekTime.weekStarttime + "~" + this.weekTime.weekEndtime
          );
          this.attr_list.week_day = [];
          this.attr_list.week_day = Array.from(new Set(this.checkList));
        } else if (this.setDetail.play_info.play_mode == "3") {
          this.DetaliData.play_info.play_mode = "time_range";
          this.attr_list.play_mode = "time_range";
          this.attr_list.time_ranges = [];
          this.attr_list.time_ranges = [
            `${this.toSpeParts[0]}~${this.toSpeParts[1]}`
          ];
          //   this.attr_list.time_ranges.push(
          //     formatDateHms(this.toSpeParts[0]) +
          //       "~" +
          //       formatDateHms(this.toSpeParts[1])
          //   );
          console.log(this.attr_list.time_ranges, "时段1");
        }
      }
      if (this.DetaliData.platform == 2) {
        if (this.setDetail.play_info.play_mode == "1") {
          this.DetaliData.play_info.play_mode = "full_day";
        } else if (this.setDetail.play_info.play_mode == "2") {
          this.DetaliData.play_info.play_mode = "week_day";
          // this.DetaliData.play_info.play_mode = 'week_range'
        } else {
          this.DetaliData.play_info.play_mode = "single_use_range";
        }
      }
      if (this.DetaliData.platform == 4) {
        if (this.setDetail.play_info.play_mode == 1) {
          this.DetaliData.play_info.play_mode = "full_day";
          this.attr_list.play_mode = "full_day";
        } else if (this.setDetail.play_info.play_mode == 2) {
          this.DetaliData.play_info.play_mode = "week_range";
          this.attr_list.play_mode = "week_range";
          // weekTime.weekEndtime
          this.attr_list.time_ranges = [];
          // this.attr_list.time_ranges.push(this.weekTime.weekStarttime + "~" + this.weekTime.weekEndtime)
          this.attr_list.week_range = [];
          console.log(this.checkList, "[]-");
          let zhi = [];
          for (let i = 0; i < this.checkList.length; i++) {
            this.attr_list.week_range.push(
              this.checkList[i] +
              "|" +
              this.weekTime.weekStarttime +
              "~" +
              this.weekTime.weekEndtime
            );
          }
          // this.attr_list.week_range = this.weekTime.weekStarttime+'|'+this.weekTime.weekEndtime
          this.attr_list.week_range = Array.from(
            new Set(this.attr_list.week_range)
          );
          console.log(this.attr_list.week_range, "this.attr_list.week_range");
          console.log(this.attr_list, "pq");
        } else if (this.setDetail.play_info.play_mode == 3) {
          this.DetaliData.play_info.play_mode = "single_use_range";
          this.attr_list.play_mode = "single_use_range";
          this.attr_list.single_use_range = [];
          this.attr_list.single_use_range =
            formatDateHms(this.toSpeParts[0]) +
            "~" +
            formatDateHms(this.toSpeParts[1]);
          // this.attr_list.time_ranges.push(formatDateHms(this.toSpeParts[0]) + "~" + formatDateHms(this.toSpeParts[1]))
        }
        this.attr_list.waitting_time = this.waitting_time;
      }
      this.DetaliData.start_time = formatDateHms(this.timeDate[0]);
      this.DetaliData.end_time = formatDateHms(this.timeDate[1]);
      // 编辑接口
      this.DetaliData.usage_type
        ? this.DetaliData.usage_type.toUpperCase()
        : "";
      // this.setDetail.dmb_spec?this.setDetail.dmb_spec:""
      // let temp = this.DetaliData.usage_type.toUpperCase()
      console.log(this.attr_list, "this.attr_list");
      console.log(this.attr_list.time_ranges, "this.attr_list.time_ranges");

      let tempInfo = {};
      if (this.pldStatus == "pt") {
        this.attr_list.v_or_h = this.setDetail.play_info.v_or_h;
      } else if (this.pldStatus == "dmb") {
        this.attr_list.daypartgroup = this.setDetail.play_info.daypartgroup;
      }
      console.log(this.attr_list, "[]");
      console.log(this.setDetail.dmb_spec, "xxxthis.setDetail.dmb_spec");
      // 普通二次投放编辑
      const params = {
        name: this.setDetail.name,
        start_time: start_time, //（string）optional, 开始时间
        end_time: end_time, //（string）optional, 结束时间
        play_style: this.setDetail.play_style, //（int）optional播放类型 1: 轮播  2: 独占
        btplan_id: this.setDetail.btplan_id,
        play_info: this.attr_list,
        dmb_spec: this.dmb_spec ? this.dmb_spec : "",
        vs_spec: this.vs_specs ? this.vs_specs : ""
      };
      console.log(params, "params编辑参数");
      edit_btpubplan(params).then(res => {
        console.log(res, "确定res");
        if (res.rst == "ok") {
          this.$message.success("编辑成功");
          this.get_btpub_detail_info();
        } else {
          this.$message.error("error");
          this.get_btpub_detail_info();
        }
      });
    },
    get_btpub_detail_info() {
      const params = {
        btplan_id: this.$route.query.btplan_id,
        range: "simple"
      };
      quick_get_btplan_detail(params).then(data => {
        if (data.rst == "ok") {
          console.log("dadatadatadatadatadatadatadatadatata", data);
          this.DetaliData = data["data"][0];
          this.screenTagsValue = data["data"][0].pub_info
            ? data["data"][0].pub_info["pub_json"]
              ? data["data"][0].pub_info["pub_json"]["screen_tags"]
              : []
            : [];
          console.log(this.DetaliData, "this.DetaliData 000");
          if (this.DetaliData.platform == 2) {
            this.daypartgroup = this.DetaliData.play_info.daypartgroup;
            this.dmb_spec = this.DetaliData.dmb_spec;
          } else if (this.DetaliData.platform == 3) {
            this.v_or_h = this.DetaliData.v_or_h;
            this.dsusage_type = this.DetaliData.usage_type;
          } else if (this.DetaliData.platform == 4) {
            this.vs_spec = this.DetaliData.vs_spec;
            this.vs_specs = this.DetaliData.vs_spec;
            const vs_spec = {};
            vs_spec["v_or_h"] = this.vs_spec[0];
            vs_spec["yCount"] = this.vs_spec[2];
            vs_spec["xCount"] = this.vs_spec.split("*")[1] * 1;
            this.waitting_time = data["data"][0]["play_info"]["waitting_time"];
            // this.vs_spec = vs_spec;
            this.vs_srec = vs_spec;
          }
          this.weekDay = [];
          if (this.DetaliData.play_info.week_day) {
            // weekDay
            this.DetaliData.play_info.week_day.forEach(item => {
              // console.log(item,'==');
              this.weeks.forEach(list => {
                // console.log(list,'--');
                if (item == list.value) {
                  this.weekDay.push(list.label);
                }
                // console.log(this.weekDay,'this.weekDay');
              });
            });
          }
          // this.DetaliData.play_info.week_day = []
          this.pubForm = data["data"][0]["pub_info"]
            ? data["data"][0]["pub_info"]["pub_json"]
            : null;

          this.options = this.$store.state.deployDataFilters[6]["options"];
          this.shopOptions = this.$store.state.deployDataFilters[5]["options"];
          this.optionsType = this.$store.state.deployDataFilters[3]["options"];
          console.log(this.optionsType, "optype");
          this.optionsType.forEach(item => {
            this.typesKey.push(item);
          });
          this.timeDate[0] = this.DetaliData.create_tm;
          this.timeDate[1] = this.DetaliData.end_time;
          console.log(
            this.setDetail,
            this.timeDate[0],
            this.timeDate[1],
            "@123"
          );

          if (this.pubForm == null) {
          } else {
            this.queryList.marketname = this.pubForm.opsmarkets;
            console.log(this.queryList);
            this.queryList.shopname = this.pubForm.storetypes;
            this.tagsList = this.pubForm.shop_tags;
            this.noSelectShops = this.pubForm["exclude_shops"]
              ? this.pubForm["exclude_shops"]
              : [];
            console.log(this.pubForm, " this.pubForm");
            console.log(this.noSelectShops, "noSelectShops");
            this.searchShops();
            this.selectTags(this.queryList.shopname);
          }

          this.$parent.loading = false;
          this.getDayPart();
        } else {
        }
      });
    },
    changePartTime(val) {
      console.log(val, ".");
    },
    clearMarker() {
      this.searchShops();
    },
    goBack() {
      this.$router.push("/deploy/quickDeploy");
    },
    SelectBlur(e) {
      this.waitting_time = e.target.value;
    },
    selectCheckout(item) {
      console.log(item, "item");
      if (item.checked == false) {
        console.log("?????");
        this.noSelectShops.push(item.shop_id);
      } else {
        let index = this.noSelectShops.indexOf(item.shop_id);
        this.noSelectShops.splice(index, 1);
      }
      console.log(this.noSelectShops);
      this.noSelectShops = Array.from(new Set(this.noSelectShops));
      console.log(this.noSelectShops);
    },
    handleSizeChange(val) {
      console.log("val", val);
      this.pagesize = val;
      this.searchShops();
    },
    handleCurrentChange(val) {
      console.log("val", val);
      this.page = val - 1;
      this.searchShops();
    },
    // 门店标签
    get_shop_tags() {
      const params = {
        page: 0, //（int）页码
        size: 30 //（int）每页显示的数量
      };
      get_shop_tags(params).then(res => {
        console.log(res, "shopres");
        this.shopTages = res["data"][0]["tags_list"];
      });
    },
    getScreenTags() {
      get_screen_tags({
        page: 0,
        size: 30
      }).then(res => {
        console.log("res", res);
        this.screenTages = res["data"][0]["tags_list"];
        console.log(this.screenTages, "screenTages");
      });
      get_tags_list({
        tag_type: "sngroup",
        page: 0,
        size: 30
      }).then(res => {
        this.screenTagesAsArray = res["data"][0]["tags_list"];
      });
    },
    getSelectDataList() {
      const params = {
        classModel: "ContentPub" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        this.$store.commit("changeDataFilters", res["data"][0]);
      });
    },
    saveEditStrategy() {
      const parmas = {
        btplan_id: this.btplan_id,
        sel_info: {
          opsmarkets: this.queryList.marketname,
          storetypes: this.queryList.shopname,
          exclude_shops: this.noSelectShops,
          screen_tags: this.screenTagsValue
        }
      };
      publish_area(parmas).then(res => {
        if (res.rst == "ok") {
          this.$message.success("修改策略成功");
          this.$router.push("/deploy/quickDeploy");
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.pushlishing_setting {
  margin-top: 10px;
  height: calc(100vh - 196px);
}

.left {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  // width: 27%;
  width: 26%;
  // max-width: 338px;
  border-right: 1px solid rgba(236, 235, 235, 1);

  .left_header {
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    font-weight: bold;
    height: 45px;
    line-height: 45px;
    padding-left: 15px;
    border-bottom: 1px solid rgba(236, 235, 235, 1);
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    align-items: center;
  }

  // doubleSelect
  .doubleSelect {
    display: flex;
    margin-left: 10px;
    justify-content: space-between;
    align-items: center;

    .el-input el-input--small el-input--suffix {
      width: 50px !important;
      display: block;
    }
  }

  .left_content {
    height: 210px;
    overflow: auto;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(236, 235, 235, 1);

    .left_details {
      display: flex;
      align-items: center;
      margin-top: 15px;
      padding-left: 15px;
      font-size: 14px;

      // 周期
      .partTime {
        .el-range-editor.el-input__inner {
          width: 350px !important;
        }
      }

      // input
      .editInput {
        width: 230px;
      }

      .screen_v {
        display: inline-block;
        width: 16px;
        height: 26px;
        font-size: 13px;
        color: rgba(80, 80, 80, 1);
        background-color: rgba(42, 130, 228, 0.16);
        text-align: center;
        line-height: 26px;
        border: rgba(125, 176, 233, 1) solid 1px;
        margin-left: -1px;
      }

      .screen_h {
        display: inline-block;
        width: 25px;
        font-size: 13px;
        height: 18px;
        color: rgba(80, 80, 80, 1);
        background-color: rgba(42, 130, 228, 0.16);
        text-align: center;
        line-height: 18px;
        border: rgba(125, 176, 233, 1) solid 1px;
        margin-left: -1px;
      }
    }
  }

  // 按星期
  .weekStyle {
    display: flex;
    align-items: center;
    margin-top: 20px;
    // 周时段

    .el-radio {
      margin-left: 16px;
    }

    .space {
      margin-left: -10px;

      .el-checkbox {
        width: 20px !important;
        margin-top: 10px;
      }
    }

    .partSpe {
      margin-left: -26px;

      .el-range-editor.el-input__inner {
        width: 370px !important;
      }

      .el-date-editor .el-range-separator {
        width: 6% !important;
      }

      .el-range-editor--small .el-range-input {
        width: 130px !important;
      }
    }
  }

  .weekPart {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-top: 10px;

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      margin-left: -4px;
      width: 300px !important;
    }
  }

  .daypart {
    flex: 1;
    padding: 10px 0 10px 15px;
    overflow: auto;
    color: rgba(80, 80, 80, 1);

    .daypart_header {
      font-weight: bold;
    }

    .daypart_info {
      margin-top: 15px;
      display: flex;
      align-items: center;
      font-size: 14px;

      .num {
        display: inline-block;
        width: 19px;
        height: 19px;
        border-radius: 50%;
        overflow: hidden;
        text-align: center;
        line-height: 19px;
        margin-right: 13px;
        color: rgba(140, 139, 139, 1);
        background-color: rgba(229, 229, 229, 1);
        font-size: 12px;
      }
    }
  }
}

.right {
  margin-top: 30px;
  flex-direction: column;

  .reset {
    color: rgba(80, 80, 80, 1);
    width: 30px;
    font-size: 13px;
    cursor: pointer;
  }

  .reset:hover {
    color: rgba(80, 80, 80, 0.8);
  }

  .search_wrap {
    width: 100%;
    border-bottom: 1px solid rgba(236, 235, 235, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 17px 0 10px;
  }

  .tags_filtering {
    padding: 10px 17px 3px 10px;
    border-bottom: 1px solid rgba(236, 235, 235, 1);

    .tags_wrap {
      margin-top: 10px;

      .tags_list {
        flex-wrap: wrap;
        max-height: 70px;
        overflow-y: auto;
        margin-right: 18px;

        .every_tag {
          display: flex;
          align-items: center;
          // width: 118px;
          padding: 0 8px;
          height: 31px;
          box-sizing: border-box;
          border: rgba(166, 166, 166, 1) solid 1px;
          color: rgba(80, 80, 80, 1);
          border-radius: 24px;
          margin-bottom: 8px;
          margin-right: 8px;
          overflow: hidden;

          .tags_name {
            color: rgba(128, 128, 128, 1);
            font-size: 14px;
            margin-right: 3px;
          }

          .remove_tag {
            font-size: 16px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .shops_wrap {
    padding: 0px 17px 0 10px;
    flex-direction: column;
    max-height: calc(100% - 180px);

    // flex: 1;
    .shop_title {
      height: 40px;
      line-height: 40px;
      justify-content: space-between;

      div:nth-of-type(1) {
        font-size: 14px;
        color: rgba(166, 166, 166, 1);
      }
    }

    .shop_content {
      height: calc(100% - 40px);
      flex-wrap: wrap;
      overflow-y: auto;

      .every_shop {
        box-sizing: border-box;
        width: 163px;
        height: 48px;
        align-items: center;
        padding: 0px 7px;
        margin-right: 15px;
        margin-bottom: 20px;
        border: 1px solid rgba(108, 178, 255, 1);

        .shop_info {
          padding: 5px 0;
          margin-left: 5px;

          p {
            font-size: 12px;
            height: 50%;
            line-height: 17px;
            // border: 1px solid red;
          }
        }
      }
    }
  }
}

.text_overflow {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style lang="scss">
/* 把复选框改为红色 */
.pushlishing_setting .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.pushlishing_setting .el-checkbox__inner:hover {
  border-color: var(--base-color) !important;
}

.pushlishing_setting .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: var(--base-color) !important;
}

.pushlishing_setting .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px !important;
  height: 18px !important;
  border-radius: 50%;
}

.pushlishing_setting .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.pushlishing_setting .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}

.space {
  .el-checkbox__inner {
    border-radius: 0 !important;
  }
}

.paginat {
  margin-top: 20px;
}

.el-pagination {
  float: right;
}
</style>
