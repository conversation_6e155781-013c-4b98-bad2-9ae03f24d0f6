<template>
    <div class="emergency">
        <div class="emergency_header">
            <!-- <el-tabs v-model="activeTabName" @tab-click="handleChangeTab">
                <el-tab-pane :label="item.label" v-for="item in activeViews" :name="item.name" :key="item.name">
                    <component :is="item.component" v-if="activeTabName == item.name"  :ref="item.name">
                    </component>
                </el-tab-pane>
            </el-tabs> -->
            <Standby></Standby>
        </div>
    </div>
</template>
  
<script>
import Standby from './components/Standby.vue';
export default {
    components: {
        Standby
        // SecondStep,
    },
    data() {
        return {
            activeTabName: "first_step",
            activeViews: [
                // {
                //     name: 'first_step',
                //     component: FirstStep,
                //     label: '第一步:预设待机图片',
                // },
                // {
                //     name: 'second_step',
                //     component: SecondStep,
                //     label: '第二步:选择覆盖设备',
                // }
            ],
            isPreviewMaskShow: false,
            PreviewSrc: '',
            PreviewType: ''
        };
    },
    computed: {

    },
    watch: {},
    created() {
    },
    mounted() {

    },
    destroyed() {
    },
    methods: {
        handleChangeTab() {

        },
        // 预览
        setImgPreview(val) {
            this.PreviewType = "image";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        setVideoPreview(val) {
            this.PreviewType = "video";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = "";
        },
    }
};
</script>
  
<style lang='scss' scoped>
* {
    box-sizing: border-box;
}

.emergency {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    /* padding: 0 20px; */
    background-color: #f8f7f7;
}

.emergency_header {
    box-sizing: border-box;
    width: 100%;
    position: relative;
}

.operation_wrap {
    position: absolute;
    right: 0;
    top: 0;
    height: 40px;
    // width: 260px;
    width: 315px;
    /* border: 1px solid blue; */
}

.operation_wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background-color: #e4e7ed;
    z-index: 1;
}

::v-deep .preview_content {
    position: absolute;
    /* background: #fff; */
    width: 1920px;
    height: 1080px;
    transform: translate(-50%, -50%) scale(0.5);
}

::v-deep .el-tabs__item{
    padding: 0 20px !important;
}
</style>





<style>
/* tabs选中的样式 */
.emergency .is-active {
    color: var(--text-color) !important;
    background-color: var(--active-color) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.emergency #tab-images {
    padding: 0 20px !important;
}

.emergency #tab-videos {
    padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.emergency .el-tabs__active-bar {
    /* display: none; */
    background-color: var(--text-color) !important;
    width: 38% !important;
}

/* tabs鼠标移入样式 */
.emergency .el-tabs__item:hover {
    color: var(--text-color) !important;
}

/* tabs取消下边距 */
.emergency .el-tabs__header {
    margin-bottom: 0 !important;
    width: calc(100% - 260px);
}

.el-tabs__nav-wrap::after {
    display: none !important;
}
</style>