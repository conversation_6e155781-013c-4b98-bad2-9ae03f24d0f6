<template>
  <div class="sidebar-logo-container" :class="{ 'collapse': collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <div style="width:100%;height:70px;text-align:center;margin-top:14px;">
          <img v-if="user_logo" :src="user_logo.logo" class="sidebar-logo">
          <!-- <h1 v-else class="sidebar-title">{{ user_logo ? user_logo.branchcn : '' }}</h1> -->
        </div>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <!-- <div style="width:100%;height:70px;text-align:center;margin-top:14px;">
          <img v-if="user_logo" :src="user_logo.logo ? user_logo.logo : Avatar" class="sidebar-logo">
          <div v-else class="no_logo">
            <i class="el-icon-food"></i>
          </div>
        </div>
        <h1 class="sidebar-title">{{ user_logo ? user_logo.branchcn : '' }}</h1> -->
        <div style="width:100%;height:70px;text-align:center;margin-top:14px;">
          <img :src="Avatar" class="sidebar-logo">
        </div>
        <!-- <h1 class="sidebar-title">数拓科技</h1> -->
      </router-link>
    </transition>
  </div>
</template>

<script>
// import Logo from '@/assets/images/showtop.png'
import Logo from '@/assets/images/silder_logo.png'
import { mapGetters } from 'vuex'
// import Avatar from "@/assets/images/showtop.png";
import Avatar from "@/assets/images/silder_logo.png";
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: '星巴克',
      logo: Logo,
      user_name: "总部账号",
      Avatar,
    }
  },
  computed: {
    ...mapGetters(['user_logo'])
  }
}
</script>

<style lang="scss" scoped>
.no_logo {
  width: 70px;
  height: 70px;
  border-radius: 6px;
  vertical-align: middle;
  margin: 0 auto;
  border: 1px solid var(--text-color);
}

.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 140px;
  line-height: 50px;
  text-align: center;
  overflow: hidden;
  // margin-bottom: 10px;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      // width: 70px;
      // height: 70px;
      width: 125px;
      height: 105px;

      vertical-align: middle;
      border-radius: 6px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #000;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
