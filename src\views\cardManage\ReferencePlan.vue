<template>
    <div class="deploy" v-loading="loading">
        <div class="top">
            <i class="el-icon-back cursor" @click="back"></i> <span>引用计划列表</span>
        </div>
        <el-table :height="autoHeight.height" :data="tableData"
            :header-cell-style="{ background: 'var( --text-color-light);' }" style="width: 100%">
            <el-table-column prop="platform" label="发布类型" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.platform == 1">指定店铺投放</div>
                    <div v-else-if="scope.row.platform == 2">餐牌组投放</div>
                    <div v-else-if="scope.row.platform == 3">普通投放</div>
                    <div v-else-if="scope.row.platform == 4">联屏投放</div>
                </template>
            </el-table-column>
            <el-table-column prop="name" align="center" label="发布名称"></el-table-column>
            <el-table-column prop="pre_pubshopcnt" align="center" label="门店数"></el-table-column>
            <el-table-column prop="pre_pubscrcnt" align="center" label="屏幕数"></el-table-column>
            <!-- <el-table-column prop="receive_complete_time" label="推送完成时间" align="center">
        </el-table-column>-->
            <el-table-column prop="pre_pub_sche_cnt" align="center" label="内容数量"></el-table-column>
            <el-table-column label="播放时段" align="center" width="180">
                <template slot-scope="scope">
                    {{ scope.row.start_time }}
                    <br />~
                    <br />
                    {{ scope.row.end_time }}
                </template>
            </el-table-column>
            <!-- <el-table-column prop="pub_complete_time" label="屏幕类型" align="center">
        </el-table-column>-->

            <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                    <el-button type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!--      分页-->
        <div class="block">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage3" :page-size="queryList.page_size"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum" :page-sizes="[10, 20, 50, 100]"
                background></el-pagination>
        </div>
    </div>
</template>

<script>
// import { get_historical_data } from "@/api/historical/historical"
import {
    get_btpub_perform_list,
    check_for_delete_btpub,
    delete_btpub,
    copy_btpub
} from "@/api/quickHistory/quickHistory";
import { publish_area, publish_launch, update_remodified_cf, api_vs_cf_notify } from "@/api/contentdeploy/contentdeploy"

export default {
    name: "TimeFrame",
    data() {
        return {
            inputValue: "",
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit("pick", [start, end]);
                        }
                    }
                ]
            },
            value1: "",
            value2: "",
            //  表格
            tableData: [],
            //  市场运营
            option1: [
                // {
                //   value: 1,
                //   label: "指定门店投放"
                // },
                {
                    value: 2,
                    label: "餐牌组投放"
                },
                {
                    value: 3,
                    label: "普通投放"
                },
                {
                    value: 4,
                    label: "联屏投放"
                }
            ],
            optionValue1: "",
            //  播放模式
            option2: [
                {
                    value: "选项1",
                    label: "播放模式"
                },
                {
                    value: "选项2",
                    label: "全播"
                },
                {
                    value: "选项3",
                    label: "轮播"
                },
                {
                    value: "选项4",
                    label: "插播"
                },
                {
                    value: "选项5",
                    label: "叠加"
                }
            ],
            optionValue2: "",
            //  未投放
            option3: [
                {
                    value: "选项1",
                    label: "未投放"
                },
                {
                    value: "选项2",
                    label: "投放中"
                },
                {
                    value: "选项3",
                    label: "使用中"
                },
                {
                    value: "选项4",
                    label: "已过期"
                }
            ],
            optionValue3: "",
            //  播放类型
            option4: [
                {
                    value: "选项1",
                    label: "单屏内容"
                },
                {
                    value: "选项2",
                    label: "联屏内容"
                },
                {
                    value: "选项3",
                    label: "浮层内容"
                }
            ],
            optionValue4: "",
            //  表格区域高度
            autoHeight: {
                height: "",
                heightNum: ""
            },
            // 获取表单条件
            // 进度条
            progressValue: 0,
            total: "",
            loading: true,
            queryList: {
                name: "",
                platform: "",
                page_num: 0,
                page_size: 10,
                store_code: '',
                tpl_cf:''
            },
            copyParams: {
                btpub_id: '',
                new_bt_name: ''
            },
            dialogFormVisible: false,
            formLabelWidth: '80px'
        };
    },
    methods: {
        back() {
            this.$router.go(-1);
        },
        handleClick(row) {
            console.log(row);
            //  跳转页面
            // this.$router.push({
            //   path: "/deploy/HisEdit",
            // });

            this.$router.push({
                path: "/deploy/mlpubdetail",
                query: { btpub_id: row.btpub_id, platform: row.platform }
            });
            sessionStorage.setItem(
                "historySearchFrom",
                JSON.stringify(this.queryList)
            );
        },
        handleEdit(row) {

            if (row.platform == 3) {
                console.log(this.$store.state.user.PUSCS_AUDIT_OPEN);
                if (this.$store.state.user.PUSCS_AUDIT_OPEN == 1) {
                    this.$router.push({
                        // path: "/deploy/deployEdit",
                        path: "/deploy/editHistory",
                        query: {
                            btplan_id: row.btplan_id,
                            platform: row.platform,
                            btpub_id: row.btpub_id,
                            showInput: false,
                            status: row.status,
                            status_code: row.status_code,
                            pub_flow_status_code: row.pub_flow_status_code,
                            active: row.action
                        }
                    });
                } else {
                    this.$router.push({
                        // path: "/deploy/deployEdit",
                        path: "/deploy/editHistory",
                        query: {
                            btplan_id: row.btplan_id,
                            platform: row.platform,
                            btpub_id: row.btpub_id,
                            showInput: false,
                            status: row.status,
                            status_code: row.status_code,
                            active: row.action
                        }
                    });
                }
            } else {
                if (this.$store.state.user.PUSCS_AUDIT_OPEN == 1) {
                    this.$router.push({
                        path: "/deploy/editHistory",
                        // path: "/deploy/deployEdit",
                        query: {
                            btplan_id: row.btplan_id,
                            platform: row.platform,
                            btpub_id: row.btpub_id,
                            status: row.status,
                            status_code: row.status_code,
                            pub_flow_status_code: row.pub_flow_status_code,
                            active: row.action
                        }
                    });
                } else {
                    this.$router.push({
                        path: "/deploy/editHistory",
                        // path: "/deploy/deployEdit",
                        query: {
                            btplan_id: row.btplan_id,
                            platform: row.platform,
                            btpub_id: row.btpub_id,
                            status: row.status,
                            status_code: row.status_code,
                            active: row.action
                        }
                    });
                }
            }
        },
        // 获取历史投放
        getHistoricalData() {
            console.log(this.queryList);
            const params = this.queryList;
            get_btpub_perform_list(params).then(res => {
                console.log(res);
                this.totalNum = res.data[0].totalElements;
                this.tableData = res.data[0].content;
                this.total = res.data[0].totalElements;
                this.loading = false;
                sessionStorage.removeItem("historySearchFrom");
                console.log(this.tableData);
            });
        },
        getOnlineClass(val) {
            switch (val) {
                case 1:
                    return "off_line";
                case 2:
                    return "error";
                case 3:
                    return "on_line";
            }
        },
        getOnlineStatus(val) {
            switch (val) {
                case 1:
                    return "接收中";
                case 2:
                    return "接收异常";
                case 3:
                    return "接收完成";
            }
        },
        getPubRecordStatus(val) {
            switch (val) {
                case 0:
                    return "处理数据";
                case 1:
                    return "数据异常";
                case 2:
                    return "分发中";
                case 3:
                    return "分发成功";
            }
        },
        //  列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 200 + "px";
            this.autoHeight.heightNum = windowHeight - 160;
        },
        handleSizeChange(val) {
            this.loading = true;
            this.queryList.page_size = val;
            this.getHistoricalData();
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.queryList.page_num = val - 1;
            this.getHistoricalData();
        },
        // 跳转详情
        toDetail() {
            this.$router.push({
                path: "/deploy/pubFail"
            });
        },
        // 复制
        copyQuick(row) {
            this.dialogFormVisible = true;
            this.copyParams.btpub_id = row.btpub_id;
            console.log(row, 'row');
        },


        search() {
            this.queryList.store_code = this.queryList.store_code.toUpperCase()
            this.queryList.page_num = 0;
            this.loading = true;
            this.getHistoricalData();
        },
        requestCopyNewBt() {
            if (this.copyParams.new_bt_name) {
                copy_btpub(this.copyParams).then(res => {
                    console.log(res, 'res');
                    if (res.rst == 'ok') {
                        this.$message.success('复制成功')
                        this.dialogFormVisible = false;
                        this.copyParams.new_bt_name = '';
                        this.getHistoricalData()
                    } else {
                        this.$message.error(res.error_msg)
                    }
                })
            } else {

            }
        }
    },
    created() {
        window.addEventListener("resize", this.getHeight);
        this.getHeight();
        this.queryList.tpl_cf =this.$route.query.tpl_cf;
        if (sessionStorage.getItem("historySearchFrom")) {
            let from = JSON.parse(sessionStorage.getItem("historySearchFrom"));
            this.queryList = from;
            console.log(this.queryList);
            this.currentPage3 = this.queryList.page_num + 1;
            this.search();
        } else {
            this.getHistoricalData();
        }
    },
    destroyed() {
        window.removeEventListener("resize", this.getHeight);
    }
};
</script>

<style scoped lang='scss'>
.deploy {
    padding: 0 20px 0 13px;
    width: 100%;
}

.top {
    display: flex;
    height: 40px;
    align-items: center;
    font-size: 18px;
    font-weight: bold;

    i {
        margin-right: 10px;
    }
}

.top>div {
    margin-right: 8px;
}

.top>.queryTime {
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    text-align: left;
}

.top>.queryTime {
    display: flex;
    align-items: center;
}

.top>.queryTime>span {
    width: 62px;
}

::v-deep .el-select {
    width: 150px;
}

/*日历*/
.el-input__inner {
    width: 243px;
    height: 32px;
    margin-right: 16px;
}

::v-deep .el-range-separator {
    line-height: 23px;
}

::v-deep .el-range__icon {
    line-height: 17px !important;
}

::v-deep .el-date-editor .el-range-separator {
    width: 6%;
    padding: 0;
}

.top>button {
    width: 88px;
    height: 32px;
    line-height: 9px;
}

/*表格*/
::v-deep .has-gutter {
    height: 42px;
    color: rgba(80, 80, 80, 1);
    background-color: var(--text-color-light);
    font-size: 14px;
    text-align: center;
}

::v-deep .el-table__body {
    table-layout: auto;
}

/*分页*/
.el-pagination {
    height: 0.32rem;
    margin-top: 35px;
    text-align: right;
}

.look {
    cursor: pointer;
    color: rgba(42, 130, 228, 1);
    width: 60px;
    background-color: rgba(227, 241, 255, 1);
    height: 26px;
    border-radius: 3px;
    line-height: 26px;
    text-align: center;
    position: absolute;
    right: 4%;
    top: 50%;
}

.tou {
    margin-bottom: 10px;
}

.my-popover {
    padding: 20px;
}

.progress {
    position: relative;
    padding: 10px;
}

::v-deep .el-progress-bar {
    width: 75% !important;
}

.off_line {
    color: var(--text-color);
}

.on_line {
    color: rgba(23, 159, 78, 1);
}

.error {
    color: rgba(56, 56, 56, 1);
}

.event {
    flex-wrap: wrap;

    div {
        margin-left: 10px;
    }
}

.event button {
    width: 50%;
    margin-left: 0 !important;
}

.off {
    background-color: #0c6d48 !important;
    color: #fff;
}

.active {
    background-color: #2fb07e !important;
    color: #fff;
}
</style>