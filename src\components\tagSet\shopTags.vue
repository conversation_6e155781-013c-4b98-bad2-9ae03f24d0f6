<template>
  <div class="shop_tags" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
    <div class="shop_tags_title flex">
      <div>标签选择方式</div>
      <div class="add_btn" @click="addTags">+ 新增标签</div>
    </div>
    <!-- 列表区 -->
    <div class="table_wrap" :style="{ height: autoHeight.height }">
      <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
        :header-cell-style="{
          background: '#24b17d', color: '#fff',
          'font-size': '13px',
          paddingLeft: '35px !important',
        }" :cell-style="{ 'padding-left': '20px' }">
        <el-table-column prop="tags_name" label="标签名称" width="" show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div class="flex" style="align-items: cneter">
              <img src="../../assets/img/tags.png" alt="" style="width: 24px; height: 24px" />
              <span class="table_cell_tags_name">{{ scope.row }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <div class="event">
              <!-- <el-button
                @click.native.prevent="handleEdit(scope.row, tableData)"
                type="text"
                style="color: rgba(80, 80, 80, 1)"
                size="small"
                >修改</el-button
              > -->
              <el-button @click.native.prevent="handleDelete(scope.row, tableData)" type="text"
                style="color: var(--text-color)" size="small">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 底部页码区 -->
    <div class="shop_tags_footer">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
      </el-pagination>
    </div>
    <!-- 批量标签 -->
    <el-dialog :visible.sync="tagsDialogVisible" :title="btnState + '标签'" :close-on-click-modal="false"
      custom-class="batch_tags_dialog" :before-close="handleClose" width="520px">
      <div style="display: flex; align-items: center; padding: 10px 0 40px">
        <!-- <p>标签名称:</p> -->
        <el-form label-width="85px" :model="ruleForm" ref="ruleForm">
          <el-form-item label="标签名称:" prop="newInputTags" :rules="[
            { required: true, message: '请输入标签名称', trigger: 'blur' },
          ]">
            <el-input placeholder="请输入标签名称" v-model="ruleForm.newInputTags" maxlength="40" show-word-limit
              style="display: inline-block; width: 230px; " />
          </el-form-item>
        </el-form>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <!-- <el-button type="primary" v-debounce="handleAdd('ruleForm')">确 定</el-button> -->
        <el-button type="primary" @click="handleAdd('ruleForm')" :loading="loadingbtn">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="deleteDialogState" :close-on-click-modal="false" custom-class="batch_tags_dialog"
      width="400px">
      <p class="icon">
        <i class="el-icon-warning-outline"></i>
        <span>删除该标签</span>
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="CloseDeleteDialog">取 消</el-button>
        <el-button type="primary" @click="requestDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  get_shop_tags,
  get_screen_tags,
  create_or_delete_shop_tags,
} from "@/api/system/label";
export default {
  components: {},
  data() {
    return {
      loading: true,
      currentPage: 1, //页码
      pageSize: 10,
      totalNum: 0, //总数据数量
      tableData: [], //列表数据
      pageNum: "",
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      tagsDialogVisible: false, //批量标签弹框
      activeName: "relation",
      // newInputTags: "",
      ruleForm: {
        newInputTags: "",
      },
      TagsForm: {
        // 新增表单
        tags: [],
        action: "",
        shop_group_id: localStorage.getItem("group_id"), // 获取shop_group_id
      },
      btnState: "新增", // 弹框状态 : 新增 / 编辑
      deleteDialogState: false,
      timer: null,
      myTimer: null,
      loadingbtn: false
    };
  },
  computed: {},
  watch: {},
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.get_shop_tags();
    this.pageNum = this.pageSize;
  },
  mounted() { },
  methods: {
    // 获取列表数据
    get_shop_tags() {
      this.loading = true;
      const params = {
        page: this.currentPage - 1, //（int）页码
        size: this.pageSize, //（int）每页显示的数量
      };
      get_shop_tags(params).then((res) => {
        this.tableData = res.data[0].tags_list;
        this.totalNum = Number(res.data[0].totalElements);
        this.loading = false;
      });
    },

    // 批量标签弹框确定按钮
    handleAdd(formRef) {
      console.log(this.$refs[formRef]);
      this.TagsForm.tags[0] = this.ruleForm.newInputTags;
      this.$refs[formRef].validate((valid) => {
        if (valid) {
          this.loadingbtn = true;
          if (this.TagsForm.tags[0] == "") {
          } else if (
            this.tableData.filter((item) => item == this.TagsForm.tags[0]).length >
            0
          ) {
            this.$message.warning("已有该标签，请重新新增标签");
            this.ruleForm.newInputTags = "";
          } else {

            create_or_delete_shop_tags(this.TagsForm).then((res) => {
              console.log(res, '43534');
              console.log(this.TagsForm, 'form');
              if (res.rst == "ok") {
                if (this.timer) return;
                this.timer = setTimeout(() => {
                  this.timer = null;
                }, 2000);
                this.$message.closeAll()
                setTimeout(() => {
                  this.get_shop_tags();
                }, 800);
                setTimeout(() => {
                  this.$message.success("新增成功");
                }, 850);
                this.handleClose();
                this.loadingbtn = false;
              } else {
                this.$message.warning("异常错误");
              }
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });


    },
    // 批量标签弹框取消按钮关闭弹框
    handleClose() {
      this.ruleForm.newInputTags = "";
      this.tagsDialogVisible = false;
    },
    // 列表编辑
    handleEdit(row) {
      console.log(row);
      this.$message.success("修改");
    },
    // 列表删除
    handleDelete(row) {
      this.TagsForm.tags[0] = row;
      this.TagsForm.action = "del_all";
      this.deleteDialogState = true;
    },
    // 新增标签
    addTags() {
      this.tagsDialogVisible = true;
      this.btnState = "新增"; // 修改弹框title
      this.TagsForm.action = "adduser_tag"; // 新增改变字段
      // console.log(this.TagsForm.action, "this.TagsForm.action");
    },
    // 取消删除弹框
    CloseDeleteDialog() {
      this.deleteDialogState = false;
    },
    // 确认删除
    requestDelete() {
      create_or_delete_shop_tags(this.TagsForm).then((res) => {
        if (res.rst == "ok") {
          this.currentPage = this.currentPage;
          // this.myTimer = setTimeout(() => {
          this.$message.closeAll()
          setTimeout(() => {
            this.get_shop_tags();
          }, 800);
          setTimeout(() => {
            this.$message.success("删除成功");
          }, 850);
          // }, 1000)
          if (this.tableData.length <= 0) {
            this.currentPage = 1;
            this.get_shop_tags();
          }
          this.CloseDeleteDialog();
        } else {
          this.$message.warning(res.rst);
        }
      });
    },
    // 每页多少条
    handleChange(value) {
      this.pageSize = value;
      this.get_shop_tags();
    },
    //页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.get_shop_tags();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.get_shop_tags();
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 208 + "px";
      this.autoHeight.heightNum = windowHeight - 233;
    },
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  },
};
</script>

<style lang="scss" scoped>
.shop_tags {
  padding: 0 20px;
}

.shop_tags_title {
  /* border: 1px solid red; */
  justify-content: space-between;
  align-items: center;
  height: 50px;
  font-size: 14px;
  padding-right: 5px;
}

.add_btn {
  background: var(--text-color);
  color: #fff;
  height: 32px;
  width: 106px;
  text-align: center;
  line-height: 32px;
  border-radius: 6px;
  cursor: pointer;
}

.add_btn:hover {
  background: rgba(211, 57, 57, 0.8);
}

.table_cell_tags_name {
  box-sizing: border-box;
  text-align: start;
  width: calc(100% - 24px);
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部页码区 */
.shop_tags_footer {
  box-sizing: border-box;
  width: 100%;
  height: 66px;
  font-size: 14px;
  padding-top: 17px;
  text-align: right;
  /* border: 1px solid red; */
}

/* 弹框 */
.relation_tags {
  display: flex;
  flex-wrap: wrap;
  height: 279px;
  margin-bottom: 20px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;
  font-size: 14px;
  border-radius: 24px;
  margin-right: 24px;
  margin-bottom: 18px;
  padding: 0 13px;
  border: 1px solid rgba(209, 209, 209, 1);
  cursor: pointer;
  /* 禁止文字选中 */
  /* -moz-user-select:none;
      -webkit-user-select:none;
      -ms-user-select:none;
      -khtml-user-select:none;
      user-select:none; */
}

.tag_active {
  color: rgba(108, 178, 255, 1);
  background-color: rgba(212, 232, 255, 1);
  border: 1px solid rgba(108, 178, 255, 1);
}

.icon {
  font-size: 18px;
  margin-top: 20px;

  i {
    color: rgba(255, 170, 0, 1);
    font-weight: bold;
  }
}

.event {
  text-align: center;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.shop_tags .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.shop_tags .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.shop_tags .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.shop_tags .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}

.shop_tags .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.batch_tags_dialog {
  margin-top: 0px !important;
  border-radius: 4px !important;
}

.batch_tags_dialog .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
  font-size: 16px !important;
  font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
  margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 1px !important;
}

.shop_tags .batch_tags_dialog .el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
  background-color: #fff !important;
}

.batch_tags_dialog .el-button--primary {
  background: rgba(108, 178, 255, 1);
}

.shop_tags .el-tabs__header {
  margin-bottom: 0 !important;
  width: 100% !important;
}

.shop_tags .el-tabs__nav-scroll {
  padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .shop_tags .is-active {
  color: var(--text-color) !important;
  background-color: rgba(255, 255, 255, 0.3) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .shop_tags .el-tabs__nav {
  height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 0px !important;
}
</style>
