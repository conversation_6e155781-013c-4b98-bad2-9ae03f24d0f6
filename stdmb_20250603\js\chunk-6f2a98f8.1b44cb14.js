(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f2a98f8"],{"21f6":function(e,r,s){},2596:function(e,r,s){"use strict";s("743e")},"3e97":function(e,r,s){e.exports=s.p+"img/Starbucks1.3d26876f.png"},"743e":function(e,r,s){},"8de4":function(e,r,s){"use strict";s.d(r,"a",(function(){return i}));var o=s("24e5"),t=s.n(o),n="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGS/SiuRiGndbZ1bayPhSdZqtXtlVw1Cm+DzFU0E8JZqbr88YLyTVD0Etg+H4jSvjJh5yROUUOizAdjFFsH0MpScUSV3/N396OvyWKezdTs+rT8XyCK0mS0PTl2jwYG9X4/DZ64V09j6FKqHumAoPT0AIbcOL4XXmHkjGxl0QiwwIDAQAB";function i(e){var r=new t.a;return r.setPublicKey(n),r.encrypt(e)}},dd7b:function(e,r,s){"use strict";s.r(r);var o=function(){var e=this,r=e._self._c;return r("div",{staticClass:"login"},[r("div",{staticClass:"left"},[r("div",{staticClass:"leftContent"},[r("div",{staticClass:"logo"}),e._v(" "),r("h3",{staticStyle:{"line-height":"40px"}},[e._v(e._s(e.$store.state.base.projuct_Welcome))])])]),e._v(" "),r("div",{staticClass:"right"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}],ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"label-position":"left","label-width":"0px"}},[r("h3",{staticClass:"title"},[e._v("Welcome")]),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.loginError,expression:"loginError"}],staticClass:"error"},[e._v(" 用户名或密码错误 ")]),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.loginError1,expression:"loginError1"}],staticClass:"error"},[e._v(" "+e._s(e.loginErrorMessage)+" ")]),e._v(" "),r("el-form-item",{attrs:{prop:"username"}},[r("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"请输入常用手机号/邮箱"},model:{value:e.loginForm.username,callback:function(r){e.$set(e.loginForm,"username",r)},expression:"loginForm.username"}})],1),e._v(" "),r("el-form-item",{attrs:{prop:"password"}},[r("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"请输入密码"},model:{value:e.loginForm.password,callback:function(r){e.$set(e.loginForm,"password",r)},expression:"loginForm.password"}})],1),e._v(" "),r("div",{staticClass:"remember"},[r("el-checkbox",{staticClass:"checkBox",staticStyle:{margin:"0 0 25px 0"},model:{value:e.loginForm.rememberMe,callback:function(r){e.$set(e.loginForm,"rememberMe",r)},expression:"loginForm.rememberMe"}},[e._v("\n          记住账号\n        ")])],1),e._v(" "),r("SliderUnlock",{attrs:{status:"ruleForm.status",successFun:e.onMpanelSuccess,errorFun:e.onMpanelError}}),e._v(" "),r("el-form-item",{staticStyle:{width:"100%"}},[r("el-button",{class:0==e.isDisable?"isDisableBg":"",staticStyle:{width:"100%","background-color":"rgb(245, 245, 245)","border-color":"rgb(245, 245, 245)","margin-top":"30px",height:"50px",color:"#333"},attrs:{loading:e.loading,size:"medium",type:"primary",disabled:e.isDisable},nativeOn:{click:function(r){return r.preventDefault(),e.handleLogin.apply(null,arguments)}}},[e.loading?r("span",[e._v("登 录 中...")]):r("span",[e._v("登 录")])])],1)],1)],1)])},t=[],n=s("8de4"),i=s("83d6"),a=s.n(i),l=(s("7ded"),s("a78e")),c=s.n(l),u=s("4328"),d=s.n(u),m=s("3e97"),g=s.n(m),p=function(){var e=this,r=e._self._c;return r("div",{staticClass:"jc-component__range"},[r("div",{staticClass:"jc-range",class:e.rangeStatus?"success":""},[r("i",{class:e.rangeStatus?e.successIcon:e.startIcon,on:{mousedown:e.rangeMove}}),e._v("\n    "+e._s(e.rangeStatus?e.successText:e.startText)+"\n  ")])])},f=[],v={props:{successFun:{type:Function},successIcon:{type:String,default:"el-icon-success"},successText:{type:String,default:"验证通过"},startIcon:{type:String,default:"el-icon-d-arrow-right"},startText:{type:String,default:"请拖住滑块，拖动到最右边"},errorFun:{type:Function},status:{type:String}},data:function(){return{disX:0,rangeStatus:!1}},methods:{rangeMove:function(e){var r=this,s=e.target,o=e.clientX,t=s.offsetWidth,n=s.parentElement.offsetWidth,i=n-t;if(this.rangeStatus)return!1;document.onmousemove=function(e){var n=e.clientX;r.disX=n-o,r.disX<=0&&(r.disX=0),r.disX>=i-t&&(r.disX=i),s.style.transition=".1s all",s.style.transform="translateX("+r.disX+"px)",e.preventDefault()},document.onmouseup=function(){r.disX!==i?(s.style.transition=".5s all",s.style.transform="translateX(0)",r.errorFun&&r.errorFun()):(r.rangeStatus=!0,r.status&&(r.$parent[r.status]=!0),r.successFun&&r.successFun()),document.onmousemove=null,document.onmouseup=null}}}},h=v,w=(s("2596"),s("2877")),b=Object(w["a"])(h,p,f,!1,null,"634bc703",null),F=b.exports,S=function(e,r,s){if(console.log(r),!r)return s(new Error("请拖动滑块完成验证"));s()},y={name:"Login",data:function(){return{LoginLogo:g.a,isShow:!0,codeUrl:"",cookiePass:"",loginForm:{username:"",password:"",rememberMe:!1,code:"",uuid:""},status:[{validator:S,trigger:"change"}],isDisable:!0,loginRules:{username:[{required:!0,trigger:"blur",message:"用户名不能为空"}],password:[{required:!0,trigger:"blur",message:"密码不能为空"}],code:[{required:!0,trigger:"change",message:"验证码不能为空"}]},loading:!1,redirect:void 0,loginError:!1,loginError1:!1,loginErrorMessage:"",loginErrorMessage1:""}},watch:{$route:{handler:function(e){var r=e.query;r&&r.redirect&&(this.redirect=r.redirect,delete r.redirect,"{}"!==JSON.stringify(r)&&(this.redirect=this.redirect+"&"+d.a.stringify(r,{indices:!1})))},immediate:!0}},created:function(){this.getCode(),this.getCookie(),this.point()},methods:{getCode:function(){},getCookie:function(){var e=c.a.get("username"),r=c.a.get("password"),s=c.a.get("rememberMe");this.cookiePass=void 0===r?"":r,r=void 0===r?this.loginForm.password:r,this.loginForm={username:void 0===e?this.loginForm.username:e,password:r,rememberMe:void 0!==s&&Boolean(s),code:""}},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(r){var s={username:e.loginForm.username,password:e.loginForm.password,rememberMe:e.loginForm.rememberMe,code:e.loginForm.code,uuid:e.loginForm.uuid};if(s.password!==e.cookiePass&&(s.password=Object(n["a"])(s.password)),r)e.loading=!0,s.rememberMe?(c.a.set("username",s.username,{expires:a.a.passCookieExpires}),c.a.set("password",s.password,{expires:a.a.passCookieExpires}),c.a.set("rememberMe",s.rememberMe,{expires:a.a.passCookieExpires})):(c.a.remove("username"),c.a.remove("password"),c.a.remove("rememberMe")),e.$store.dispatch("Login",s).then((function(r){"ok"==r.rst?(e.$message.success("登录成功"),e.loading=!1,e.loginError=!1,e.loginError1=!1,e.$router.push({path:e.redirect||"/"})):"error"==r.rst&&(e.loginError=!1,e.loginError1=!1,"400"==r.error_code?(e.$message.closeAll(),e.loading=!1,e.loginError=!0):(e.loading=!1,e.loginErrorMessage=r["error_msg"],e.loginError1=!0))})).catch((function(r){e.$message.error("用户名或密码有误,请重新输入"),console.log("e",r),e.$message.closeAll(),e.$message.error("接口请求异常"),e.loading=!1,e.getCode()}));else{if(""!=e.loginForm.password&&""!=e.loginForm.username)return console.log("error submit!!"),!1;e.$message.error("用户名或密码不能为空")}}))},point:function(){var e=void 0!==c.a.get("point");e&&(this.$notify({title:"提示",message:"当前登录状态已过期，请重新登录！",type:"warning",duration:5e3}),c.a.remove("point"))},forgetPassword:function(){},onMpanelSuccess:function(){this.isDisable=!1}},components:{SliderUnlock:F}},x=y,_=(s("f94c"),Object(w["a"])(x,o,t,!1,null,"5140afea",null));r["default"]=_.exports},f94c:function(e,r,s){"use strict";s("21f6")}}]);