<template>
  <div class='box'>
    <!--    头部-->
    <div class='useTop'>
      <!--      左边箭头-->
      <div class='pubArrow'>
        <span class='el-icon-back' @click.stop='goBack'></span>
        设备列表
      </div>
    </div>
    <div class='part'>
      <div class='left'>
        <div class='top'>
          <div>
            <span v-for='(item,index) in 4' :key='index'>{{item}}</span>
            SHK078七宝国际店
          </div>
          <p>联屏组ID：271932</p>
        </div>
        <h3>设备清单</h3>
        <el-table
          :data="tableData"
          style="width: 100%"
          :row-class-name="tableRowClassName">
          <el-table-column
            prop="index"
            label="屏幕序号"
            align='center'
            width="80">
          </el-table-column>
          <el-table-column
            prop="screenId"
            label="屏幕ID"
            align='center'
            width="70">
          </el-table-column>
          <el-table-column
            prop="screenLabel"
            align='center'
            label="屏幕标签">
          </el-table-column>
          <el-table-column
            prop="devType"
            align='center'
            label="类型">
          </el-table-column>
          <el-table-column
            prop="address"
            align='center'
            label="操作">
            <template slot-scope="scope">
              <el-button
                @click.native.prevent="deleteRow(scope.$index, tableData)"
                type="text"
                size="small">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class='right'>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConnectDetail',
  data(){
    return {
      tableData: [{
        screenId:"201573",
        index:1,
        screenLabel:"菜单屏",
        devType:"Android 3288",
      }, {
        screenId:"201573",
        index:2,
        screenLabel:"菜单屏",
        devType:"Android 3288",
      }, {
        screenId:"201573",
        index:3,
        screenLabel:"菜单屏",
        devType:"Android 3288",
      }]
    }
  },
  methods:{
    tableRowClassName({row, rowIndex}) {
      if (rowIndex === 1) {
        return 'warning-row';
      } else if (rowIndex === 3) {
        return 'success-row';
      }
      return '';
    }
  }
}
</script>

<style scoped>
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
.box{
  width: 100%;
  padding:0 25px 80px;
  background-color: #fff;
  font-size: 14px;
}
.useTop{
  display: flex;
  padding: 0.15rem 0 0.15rem 0.12rem;
  justify-content: space-between;
  border-bottom: 1px solid #dcdcdc;
  width: 100%;
}
.el-icon-back{
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}
.useTop>.pubArrow{
  width: 1.5rem;
  height: 0.4rem;
  line-height: 0.4rem;
  font-size: 0.14rem;
}
.part{
  display: flex;
  margin-top: 30px;
}
.part>.left{
  width: 472px;
  height: 331px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 10px;
  font-size: 14px;
  border: rgba(229, 229, 229, 1) solid 1px;
}
.part>.left>.top{
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ededed;
  padding: 0 5px;
  height: 56px;
  line-height: 56px;
}
.part>.left>.top>div{
  color: rgba(80, 80, 80, 1);
  font-weight: bold;
}
.part>.left>.top>div>span{
  line-height: 18px;
  color: #74b6f3;
  display: inline-block;
  width: 28px;
  height: 18px;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}
.part>.left>.top>p{
  color: rgba(166, 166, 166, 1);
  font-weight: bold;
}
.part>.right{
  width: 751px;
  height: 331px;
  color: rgba(80, 80, 80, 1);
  border-radius: 10px;
  border: rgba(229, 229, 229, 1) solid 1px;
  margin-left: 20px;
  text-align: center;
}
</style>
