<template>
  <div class="picture_resources" @keydown.esc="closeMask" v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <div class="content" :style="{ height: autoHeight.height }">
      <!-- 搜索区 -->
      <div class="content_header flex">
        <!-- 左侧筛选条件 -->
        <div class="search_header">
          <el-form :inline="true" :model="searchForm" class="demo-form-inline">
            <el-form-item>
              <el-select v-model="searchForm.auditType" placeholder="审核类型"
                @change="historyAuditPicture(searchForm.auditType)">
                <el-option v-for="item in auditTypeList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item>
              <el-select v-model="searchForm.marketname" placeholder="营运市场" clearable>
                <el-option :label="item[1]" :value="item[0]" v-for="item in MarketList" :key="item"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onSubmit" class="search_button">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 内容区 -->
      <div class="picture_content flex-1">
        <!-- 格子 -->
        <div class="lattice flex" v-show="searchForm.auditType == 1">
          <div class="lattice_header">
            <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox>
            全选
          </div>
          <el-empty description="暂无内容" v-show="dataList.length == 0"></el-empty>
          <div class="lattice_content flex flex-1" v-show="dataList.length > 0">
            <div class="lattice_item " v-for="item in dataList" :key="item">
              <div class="item_image">
                <el-image :src="item.obj_info.src_url" fit="contain" style="width: 100%;height:100%;" alt="">
                  <div slot="placeholder" class="image-slot flex load_img" style="">
                    <img :src="loadImg" alt="" style="width:30px" />
                  </div>
                </el-image>
                <div class="edit_mask flex">
                  <div class="mask_btn" style="" @click="handleShow(item)">
                    <p><i class="el-icon-view"></i></p>
                    <p>预览</p>
                  </div>
                  <!-- <div class="mask_btn" @click="handleDelete(item)">
                                        <p><i class="el-icon-delete-solid"></i></p>
                                        <p>删除</p>
                                    </div> -->
                </div>
              </div>
              <div class="up_date flex" style="">
                <div class="flex-1 txt_ellipsis">
                  上传日期：{{ item.created_tm }}
                </div>
              </div>
              <div class="item_size">
                尺寸：{{ item.obj_info.width }} * {{ item.obj_info.height }}
              </div>
              <div class="item_size">
                安全检测是否通过：
                <span class="consent" v-if="item.obj_info.detect_suggestion == 'pass'">通过</span>
                <span class="reject" v-else>不通过</span>
              </div>
              <div class="audit_event">
                <el-checkbox v-model="item.checked" @change="checkOne(item)"></el-checkbox>
                <div>
                  <span class="consent" @click="consent(item)">同意</span>
                  <span class="reject" @click="refusal(item)">驳回</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表 -->
        <div class="lists" v-show="searchForm.auditType == 4">
          <el-table :data="dataList" height="100%" ref="table_list" @selection-change="handleSelectionChange"
            :header-cell-style="{
            background: '#24b17d', color: '#fff', 
              'font-size': '13px'
            }">
            <el-table-column prop="photo_url" label="图片预览" width="110" align="center">
              <template slot-scope="scope">
                <img :src="scope.row.obj_info.src_url" style="width: 73px;height:73px;object-fit:cover" alt="" />
              </template>
            </el-table-column>
            <el-table-column prop="photo_info" label="尺寸" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.obj_info.width }} *
                {{ scope.row.obj_info.height }}
              </template>
            </el-table-column>
            <el-table-column label="大小" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.obj_info.size }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 5" style="color:var(--background-color)">
                  驳回
                </span>
                <span v-else style="color:rgba(56, 56, 56, 1)">
                  同意
                </span>
              </template>
            </el-table-column>

            <el-table-column label="上传日期" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.created_tm }}
              </template>
            </el-table-column>
            <el-table-column label="上传账户" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.obj_info.author_name }}
              </template>
            </el-table-column>
            <el-table-column label="审核账户" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.job_uid_name }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="账户" width="" show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">
                {{ scope.row.job_uid_name }}
              </template>
            </el-table-column> -->
            <el-table-column fixed="right" label="操作" width="130" align="center">
              <template slot-scope="scope">
                <el-button @click.native.prevent="handleShow(scope.row)" type="text" style="color:#409eff"
                  size="small">预览</el-button>
                <!-- <el-button @click.native.prevent="handleEdit(scope.row)" type="text"
                                    style="color:#409eff" size="small" v-if="isContent == false">编辑</el-button> -->
                <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:red" size="small"
                  v-if="isContent == false && checkPer(['cm.res.del'])">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 底部操作以及页码 -->
    <div class="footer flex" :style="{
      'justify-content':
        searchForm.auditType == '0' ? 'space-between' : 'flex-end'
    }">
      <div class="flex" v-show="searchForm.auditType == '1'">
        <span class="btn btn-agree" @click="batchApproval">批量同意</span>
        <span class="btn btn-err" @click="batchRejection">批量驳回</span>
      </div>
      <div class="flex" v-show="searchForm.auditType != 1">

      </div>
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
        layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  self_photo_list,
  delete_self_photo_statusonly,
  add_or_edit_photo_tags
} from "@/api/files/pictureResources";
import { get_aduit_data, aduit_consent_refusal } from "@/api/audit/audit.js";
import { datas_filter_cond } from "@/api/commonInterface";
import loadImg from "@/assets/img/img_bg.png";
export default {
  props: {},
  components: {},
  data() {
    return {
      loading: false,
      tagsDialogVisible: false, //批量标签弹框
      activeName: "addtag",
      currentPage: 1, //页码
      totalNum: 0, //总数据数量
      pageSize: 10, //每页显示多少条
      loadImg,
      queryList: {
        content: "",
        screenSize: "",
        market: "",
        color: ""
      },
      marketList: [],
      isAllChecked: false,
      dataList: [],
      checkedList: [],
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      searchForm: {
        auditType: 1,
        marketname: ""
      },
      auditTypeList: [
        {
          value: 1,
          label: "待审核"
        },
        {
          value: 4,
          label: "历史审核"
        }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getDataLsit();
  },
  mounted() { },
  methods: {
    // 选择颜色
    changeColors(idx) {
      this.colorList.forEach((item, index) => {
        if (index == idx) {
          item.checked = !item.checked;
        } else {
          item.checked = false;
        }
      });
      const color = this.colorList.filter(item => {
        return item.checked;
      });
      if (color[0]) {
        this.queryList.color = color[0].color;
      } else {
        this.queryList.color = "";
      }
    },
    // 搜索
    handleSearch() {
      console.log(this.queryList);
      this.resetDataList();
    },

    // 获取列表数据
    getDataLsit() {
      // this.loading = true;
      const list = [];
      this.loading = true;
      this.checkedList = [];

      // const params = {
      //   unit: "all", // (String) options: “all”， "in_year", "in_month", "in_day"  // optional
      //   limit: this.pageSize,
      //   offset: (this.currentPage - 1) * this.pageSize,
      //   query_status: [1, 2, 9, 10], // (List)
      //   order_by: "-last_mtime",
      //   root_flag: 0 // (Int) 是否继承总部图片
      // };
      // self_photo_list(params)
      //   .then(res => {
      //     if (res.rst == "ok") {
      //       res.data[0].photos.forEach(item => {
      //         item.checked = false;
      //         item.isShowTage = false;
      //       });
      //       this.dataList = res.data[0].photos;
      //       console.log("dataList", this.dataList);
      //       this.totalNum = res.data[0].unit_info.total;
      //       this.isAllChecked = false;
      //       this.loading = false;
      //     } else {
      //       this.$message.error(res.error_msg);
      //       this.loading = false;
      //     }
      //   })
      //   .catch(rej => {
      //     console.log(123);
      //     this.loading = false;
      //     this.message.error("接口请求异常");
      //   });
      const params = {
        classModel: "AuditPubflow",
        page: this.currentPage - 1,
        size: this.pageSize,
        ref_ct: 32,
        flow_job_type: this.searchForm.auditType
      }
      get_aduit_data(params).then(res => {
        console.log(res, 'res');
        this.loading = false;
        if (res.rst == 'ok') {
          res.data[0]['content'].forEach(item => {
            item.checked = false;
          })
          this.dataList = res.data[0].content;
          console.log("dataList", this.dataList);
          this.totalNum = res.data[0].totalElements;
          this.isAllChecked = false;
          this.loading = false;
        } else {
          this.$message.error(res.error_msg);
          this.loading = false;
        }
      }).catch(rej => {
        console.log(123);
        this.loading = false;
        this.message.error('接口请求异常')
      })
    },
    // })
    resetDataList() {
      this.currentPage = 1;
      this.getDataLsit();
    },
    // 全选
    checkAll() {
      this.dataList.forEach(item => {
        item.checked = this.isAllChecked;
      });
      if (this.isAllChecked) {
        this.checkedList = JSON.parse(JSON.stringify(this.dataList));
      } else {
        this.checkedList = [];
      }
      console.log(this.dataList, "dataList");
    },
    //单选
    checkOne(data) {
      console.log(data);
      this.checkedList = this.dataList.filter(item => item.checked);
      console.log(this.checkedList, "checkedList");
      if (this.checkedList.length == this.dataList.length) {
        this.isAllChecked = true;
      } else {
        this.isAllChecked = false;
      }
    },
    // 预览
    handleShow(val) {
      this.PreviewMaskSrc = val.obj_info.src_url;
      this.$emit("setImgPreview", val.obj_info.src_url);
    },

    //页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataLsit();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataLsit();
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 162 + "px";
      this.autoHeight.heightNum = windowHeight - 162;
    },
    // 获取搜索条件的营运市场
    getMarketSearchData() {
      datas_filter_cond({
        classModel: "GroupShop"
      }).then(res => {
        console.log(res, "res");
        this.MarketList = res["data"][0][1]["options"];
      });
    },
    // 同意
    consent(photo) {
      console.log(photo);
      let params = {
        job_act: "approve",
        job_ids: [photo.id]
      };
      aduit_consent_refusal(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("审核同意成功");
          this.getDataLsit();
        } else {
          this.$message.error("审核同意失败");
        }
      });
    },
    // 驳回
    refusal(photo) {
      let params = {
        job_act: "deny",
        job_ids: [photo.id]
      };
      aduit_consent_refusal(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("审核驳回成功");
          this.getDataLsit();
        } else {
          this.$message.error("审核驳回失败");
        }
      });
    },
    // 批量同意
    batchApproval() {
      let arr = this.dataList.filter(item => item.checked);
      let requestArr = arr.map(item => {
        return item.id;
      });
      let params = {
        job_act: "approve",
        job_ids: requestArr
      };
      aduit_consent_refusal(params).then(res => {
        if (res.rst == "ok") {
          this.$message.success("批量审核同意成功");
          this.getDataLsit();
        } else {
          this.$message.error("批量审核同意失败");
        }
      });
    },
    // 批量驳回
    batchRejection() {
      let arr = this.dataList.filter(item => item.checked);
      if (arr.length != 0) {
        let requestArr = arr.map(item => {
          return item.id;
        });
        let params = {
          job_act: "deny",
          job_ids: requestArr
        };
        aduit_consent_refusal(params).then(res => {
          if (res.rst == "ok") {
            this.$message.success("批量审核驳回成功");
            this.getDataLsit();
          } else {
            this.$message.error("批量审核驳回失败");
          }
        });
      } else {
        this.$message.warning("请选择审核图片");
      }
    },
    historyAuditPicture(e) {
      console.log(e, 'exxx');
      this.getDataLsit()
    },
    onSubmit() {
      this.loading = true;
      this.getDataLsit()
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
    window.removeEventListener("keyup", this.closeScreenFull);
  }
};
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.picture_resources {
  width: 100%;
  padding: 0 14px;
  background: #eeeeee;
}

.content {
  /* border: 1px solid red; */
  display: flex;
  flex-direction: column;
}

.content_header {
  height: 67px;
}

.content_search {
  align-items: center;
}

.content_tabs {
  align-items: center;
}

.tabs_btn {
  border: 1px solid rgba(108, 178, 255, 1);
  font-size: 14px;
  padding: 6px 15px;
  cursor: pointer;
  color: rgba(108, 178, 255, 1);
  background: #fff;
  white-space: nowrap;
}

.tabs_btn:hover {
  border: 1px solid rgba(108, 178, 255, 0.7);
}

.tabs_checked {
  color: #fff;
  background: rgba(108, 178, 255, 1);
}

.picture_content {
  width: 100%;
  height: calc(100% - 67px);
  /* overflow-y:auto ; */
  /* border: 1px solid pink; */
}

.color_disc {
  width: 26px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  border-radius: 50%;
  cursor: pointer;
  margin-right: 10px;
}

// .color_disc_active {
//     /* background: black !important; */
// }

/* 格子 */
.lattice {
  height: 100%;
  flex-direction: column;

  ::v-deep .el-checkbox__inner {
    border-color: #9a9a9a !important;
  }
}

.lattice_header {
  width: 100%;
  height: 42px;
  line-height: 42px;
  background-color: var(--text-color-light);
  padding-left: 10px;
  font-size: 14px;
  color: #fff;
}

.lattice_content {
  flex-wrap: wrap;
  overflow-y: auto;
}

.lattice_item {
  width: 240px;
  height: 363px;
  margin-right: 16px;
  margin-top: 15px;
  font-size: 13px;
  color: rgba(56, 56, 56, 1);
  background-color: #fff;
}

.item_image {
  position: relative;
  width: 100%;
  height: 240px;
  padding: 3px;
}

.item_image:hover .edit_mask {
  display: flex;
}

.edit_mask {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 240px;
  background-color: rgba(0, 0, 0, 0.335);
  color: #fff;
  justify-content: center;
  align-items: center;
}

.mask_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 48px;
  height: 48px;
  background-color: rgba(0, 0, 0, 0.564);
  border-radius: 50%;
  font-size: 11px;
  margin: 0 10px;
}

.mask_btn:hover {
  background-color: rgba(0, 0, 0, 0.464);
}

.each_tags {
  align-items: center;
  margin-right: 3px;
  // max-width: 105px;
  width: 105px;
  flex-shrink: 0;
}

.up_date,
.item_size,
.item_tags {
  margin-top: 10px;
  align-items: center;
  padding: 0 9px;
}

.audit_event {
  display: flex;
  margin-top: 18px;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;

  span {
    margin-right: 10px;
    font-weight: bold;
    cursor: pointer;
  }

}

.consent {
    // color: rgba(42, 130, 228, 1);
    color: var(--btn-success-color);
  }

  .reject {
    color: var(--text-color);
  }
.tags_wrap {
  display: flex;
  overflow: hidden;
  width: 210px;
}

.item_tags {
  margin-top: 13px;
  padding: 0 4px;
  overflow: hidden;
}

/* 列表 */
.lists {
  width: 100%;
  height: 100%;
}

.footer {
  height: 72px;
  align-items: center;
  justify-content: space-between !important;

  .btn {
    display: inline-block;
    width: 106px;
    height: 35px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 35px;
    margin: 0 10px;
  }

  .btn:hover {
    filter: brightness(0.9);
  }

  .btn-agree {
    background-color: var(--btn-success-color);
  }

  .btn-err {
    background-color: var(--background-color);
  }
}

.txt_ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 弹框 */
.relation_tags {
  display: flex;
  flex-wrap: wrap;
  height: 279px;
  margin-bottom: 20px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px;
  font-size: 14px;
  border-radius: 24px;
  margin-right: 24px;
  margin-bottom: 18px;
  padding: 0 13px;
  border: 1px solid rgba(209, 209, 209, 1);
  cursor: pointer;
  /* 禁止文字选中 */
  /* -moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none; */
}

.tag_active {
  color: rgba(108, 178, 255, 1);
  background-color: rgba(212, 232, 255, 1);
  border: 1px solid rgba(108, 178, 255, 1);
}

.show_more_tags {
  box-sizing: border-box;
  height: 24px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.show_more_tags:hover {
  color: #83b3ee;
}

.stow_tags {
  margin-top: 10px;
  padding: 0 9px;
  position: relative;
}

.more_tags_wrap {
  position: absolute;
  width: 255px;
  height: 140px;
  background: #fff;
  padding: 15px 5px 10px;
  top: -142px;
  left: 3px;
  /* max-height: 140px; */
  z-index: 200;
  border-radius: 5px;
  border: 1px solid #e5dfdf;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.135);
}

/* 第一个三角形颜色换成边框颜色 */
.more_tags_wrap::before {
  content: "";
  display: block;
  position: absolute;
  bottom: -7px;
  left: 8px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  // border-bottom: 20px solid #d9d9d9;
  border-top: 7px solid #fff;
  z-index: 50;
}

/* 第二个三角形颜色换成背景色 */
.more_tags_wrap::after {
  content: "";
  display: block;
  position: absolute;
  bottom: -8px;
  left: 7px;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 8px solid rgba(0, 0, 0, 0.135);
  // border-top: 7px solid red;
}

.more_tags {
  width: 100%;
  max-height: 100%;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  overflow-y: auto;
  flex-shrink: 0;
}

.load_img {
  height: 100%;
  align-items: center;
  justify-content: center;
  background-color: #ccc;
}

.search_header {
  height: 60px;
  display: flex;
  padding: 0 10px;
  align-items: center;

  .search_button {
    background-color: var(--text-color);
    color: #fff;
    border: 0;
  }
}

::v-deep .search_header .el-form-item {
  margin-bottom: 0 !important;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.picture_resources .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.picture_resources .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.picture_resources .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.picture_resources .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}

.picture_resources .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.batch_tags_dialog {
  margin-top: 0px !important;
  border-radius: 16px !important;
}

.batch_tags_dialog .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
  font-size: 16px !important;
  font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
  margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 1px !important;
}

.batch_tags_dialog .el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
}

.batch_tags_dialog .el-button--primary {
  background: rgba(108, 178, 255, 1);
}

.picture_resources .el-tabs__header {
  margin-bottom: 0 !important;
  width: 100% !important;
}

.picture_resources .el-tabs__nav-scroll {
  padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .picture_resources .is-active {
  color: var(--text-color) !important;
  background-color: rgba(255, 255, 255, 0.3) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .picture_resources .el-tabs__nav {
  height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 0px !important;
}
</style>
