<template>
  <div class="dmb">
    <el-form ref="form" :model="form" label-width="130px">
      <el-form-item label="发布名称:">
        <el-input v-model="newDmbForm.launchName"></el-input>
      </el-form-item>
      <el-form-item label="投放周期:">
        <el-date-picker v-model="newDmbForm.timging" type="datetimerange" :default-time="['00:00:00', '23:59:59']"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width:300px"></el-date-picker>
      </el-form-item>

      <el-form-item label="餐牌组规格:">
        <div class="Specification">
          <div class="gp">
            <div v-for="(item, index) in speci" class="speci" :key="item">
              <div class="specic" @click="eventActive(index, item)">
                <div v-for="item1 in item.value" :class="item1 == 0 ? 'across' : 'vertical'" :key="item1"></div>
              </div>
              <div class="alternative">
                <el-radio v-model="radio" :label="item.name" @change="changeRadio(index, item)"></el-radio>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>




      <el-form-item label="经营时段类型:" style="margin-top: 35px !important;">
        <div style="display: flex;width: 550px;justify-content: space-between;">
          <el-cascader :options="dayPartOptions" v-model="newDmbForm.daypartgroup" filterable clearable 
            placeholder="请选择经营时段类型"
            style="width:38%" popper-class="daypartpopper" :props="{
              checkStrictly: true, expandTrigger: 'click',
            }" :filter-method="query"></el-cascader>

          <el-input v-model="shopDayPart" placeholder="请输入门店编号" style="width:23% !important"></el-input>
          <el-button style="margin-left:-50px" class="search_button" @click="searchShopDaypart()">搜索</el-button>
        </div>

      </el-form-item>
      <el-form-item label="播放类型:">
        <div>
          <el-radio v-for="item in playerRadio" :key="item[0]" v-model="newDmbForm.playerState" :label="item[1]">
          </el-radio>
        </div>
      </el-form-item>

      <!-- <el-form-item label="屏幕动线:">
        <div class="Specification">
          <el-select v-model="storeplaylayoutSelected" @change="selectChange('layout')">
            <el-option v-for="(item, index) in storeplaylayoutSelectList" :key="'layout_op_' + index"
              :value="item.value" :label="item.name"></el-option>
          </el-select>
        </div>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
import { removeDuplicateObj } from "@/utils/setArray";
import { get_daypart_data } from "@/api/contentdeploy/contentdeploy";
import { datas_filter_cond } from "@/api/commonInterface";
import {
  shop_daypartmgmt,
} from "@/api/shopManage/shop";
export default {
  data() {
    return {
      newDmbForm: {
        launchName: "",
        platform: 2,
        func_type: 1,
        dmb_spec: "",
        // timging: [new Date(), new Date(new Date().getTime() + 86400000)],
        timging: [],
        playerState: "轮播",
        daypartgroup: ""
      },

      // 规格结构处理
      speciData: [],
      /**
       * 右侧餐牌组规格
       * order 0为横 1为竖
       */
      speci: [],
      radio: "00",
      active: 0,
      playerRadio: [],

      dayPartOptions: [],
      oldOptions: null,
      shopDayPart: '',
      storeplaylayoutSelectList: [],
      storeplaylayoutSelected:''
    };
  },
  methods: {
    eventActive(index, item) {
      this.active = index;
      this.radio = item.name;
      this.newDmbForm.dmb_spec = item.value;
    },
    changeRadio(index, item) {
      this.active = index;
      this.radio = item.name;
      this.newDmbForm.dmb_spec = item.value;
    },
    cascaderClick(nodeData) {
      this.addrCode = nodeData.value;
      this.$refs.cascader.checkedValue = nodeData.value;
      this.$refs.cascader.computePresentText();
      this.$refs.cascader.toggleDropDownVisible(false);
      this.$message({
        message: "已选择：" + nodeData.label,
        type: "success",
        duration: 1000
      });
    },
    searchShopDaypart() {
      let params = {
        act: "get",
        store_code: this.shopDayPart,
        dpfrom: 3
      };
      shop_daypartmgmt(params)
        .then((res) => {
          // console.log(res, 'res');
          if (res.rst == "ok") {
            // console.log(res);
            if (res['data'][0]['sys_daypart_info']['current_sys_groupname'] == '') {
              this.$message.warning("当前门店daypart为空")
            }
            this.newDmbForm.daypartgroup = res['data'][0]['sys_daypart_info']['current_sys_groupname']
          } else {
            if (res.error_code == 600005007) {
              this.$message.error(res['error_msg'])
              this.newDmbForm.daypartgroup = '';
            } else {
              this.$message.error('请检查门店编号是否正确')
              this.newDmbForm.daypartgroup = '';
            }
          }
        })

    },
    // 级联选择后的回调
    // handleChange(value) {
    //     console.log("value", value);
    //     localStorage.setItem("daypartgroup", value)
    //     let temp = JSON.parse(JSON.stringify(value));
    //     let result = [];

    //     /*
    //     根据每一次选择的值，查找出只会包含父节点的集合
    //     即处理先选择某一子节点，再选了父节点。要把子节点剔除掉，只保留父节点。
    //     */
    //     temp.map((item) => {
    //         // 长度为1  说明这个节点是顶级节点
    //         if (item.length == 1) {
    //             result.push(item);
    //         } else {
    //             if (result.length > 0) {
    //                 result.map((it) => {
    //                     item.map((x) => {
    //                         if (item.indexOf(x) == -1) {
    //                             result.push(item);
    //                         }
    //                     });
    //                 });
    //             } else {
    //                 result.push(item);
    //             }
    //         }
    //     });
    //     //把处理后的选择结果更新到级联选择器中，回显正确的选择状态。
    //     this.value = result;
    //     console.log(result);
    //     // 过滤出选择父节点的下一级子节点
    //     let selectArr = [];
    //     value.map((item) => {
    //         selectArr.push(item);
    //     });
    //     // 如果过滤后的数组长度为0，说明没有选择任何节点，则所有节点可以选择。反之设置对应子节点的disabled。
    //     if (selectArr.length > 0) {
    //         console.log("selectArr", selectArr);
    //         this.updateTreeDataStatus(selectArr);
    //     } else {
    //         this.setChildrenDisabled(this.dayPartOptions, false);
    //     }
    // },
    // 更新树形数据的状态s
    updateTreeDataStatus(data) {
      let treeData = this.dayPartOptions;
      // 直接把所有节点置为可选，避免父节点取消选择了，子节点还不可选的情况
      this.setChildrenDisabled(treeData, false);
      // 根据上面选择的节点，把不可选的子节点及所有后代节点置为不可选
      data.map(item => {
        let sub = this.filter(treeData, node => {
          // console.log("node", node, item);
          return node.value == item;
        });
        // console.log(sub);
        if (sub.children) {
          const subChild = this.setChildrenDisabled(sub.children, true);
          sub.children = subChild;
        }
      });
    },
    //递归设置子级部门不可选择
    setChildrenDisabled(tree, status) {
      tree.map(item => {
        this.$set(item, "disabled", status);
        if (item.children) {
          this.setChildrenDisabled(item.children, status);
        }
      });
      return tree;
    },
    /**
     * 根据传入的条件 返回json数据中符合条件的数据
     * params: treeList  需要处理的原始数据
     * params: callback 条件function 如:(node)=>node.id==id
     *  */
    filter(treeList, callback) {
      const queue = [...treeList];
      while (queue.length > 0) {
        const cur = queue.shift();
        if (callback(cur)) {
          return cur;
        }
        if (cur.children && cur.children.length > 0) {
          for (const item of cur.children) {
            queue.push(item);
          }
        }
      }
    },

    // daypart
    async getDayPart() {
      const params = {
        classModel: "SysDayPart",
        page: 0,
        size: 10
      };
      await get_daypart_data(params).then(res => {
        // console.log("res", res);
        res.data[0].content.forEach((item, index) => {
          this.dayPartOptions.push({
            value: item.group_name,
            label: item.group_name,
            children: []
          });
          item.attr_list.forEach(item1 => {
            this.dayPartOptions[index].children.push({
              value: item1.name,
              label: item1.name,
              disabled: true
            });
          });
        });
      });
      this.oldOptions = JSON.parse(JSON.stringify(this.dayPartOptions));
      // console.log(this.oldOptions, "oldOptions");
    },

    query(value, old) {
      if (value.text == old) {
        return true;
      } else {
        return false;
      }
    },
    filterPlayLayoutList(data) {
      const list = []
      data.options.forEach(item => {
        const obj = {
          value: item[0],
          name: item[1]
        };
        list.push(obj);
      })
      this.storeplaylayoutSelectList = list;
      this.storeplaylayoutSelected = list[0].value;
    },
    getSelectDataList() {
      const params = {
        classModel: "ContentPub" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        console.log(res["data"], "res");
        this.$store.commit("changeDataFilters", res["data"][0]);
        this.playerRadio = this.$store.state.deployDataFilters[2].options;
        this.speciData = this.$store.state.deployDataFilters[0].options;
        this.$forceUpdate();
        console.log(this.speciData, 'speciData');
        let layout_data = [];

        res.data[0].forEach(item => {
          switch (item.filterkey) {
            case 'storeplaylayout':
              layout_data = item;
              break;
          }
        });

        this.filterPlayLayoutList(layout_data);

        this.speciData.forEach(item => {
          this.speci.push({
            name: item[1],
            value: item[0],
            order: item[1][0] == "横" ? 0 : 1
          });
        });

        this.speci = removeDuplicateObj(this.speci);
        console.log(this.speci);
      });
    }
  },
  created() {
    this.getSelectDataList();
    this.getDayPart();
  },
  mounted() { }
};
</script>

<style lang="scss" scoped>
.dmb {
  box-sizing: border-box;
  margin-top: 20px;
  padding-left: 25px;

  ::v-deep .el-input {
    width: 300px !important;
  }

  .Specification {
    // flex-wrap: wrap;
    width: 100%;

    .speci {
      width: 138px !important;
      height: 77px;
      margin-left: 10px;
      margin-top: 32px;
      cursor: pointer;

      .specic {
        height: 100%;
        border: rgba(215, 215, 215, 1) solid 1px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        // .across {
        //     height: 100%;
        //     display: flex;
        //     align-items: center;
        //     justify-content: center;

        //     div {
        //         width: 26px;
        //         height: 16px;
        //         background-color: rgba(229, 229, 229, 1);
        //         border: rgba(166, 166, 166, 1) solid 1px;
        //     }
        // }

        // .vertical {
        //     height: 100%;
        //     display: flex;
        //     align-items: center;
        //     justify-content: center;

        //     div {
        //         width: 16px;
        //         height: 26px;
        //         background-color: rgba(229, 229, 229, 1);
        //         border: rgba(166, 166, 166, 1) solid 1px;
        //     }
        // }
      }

      .alternative {
        height: 30px;
        display: flex;
        align-items: center;
        margin-top: 5px;
        justify-content: center;
      }
    }
  }
}

.active {
  background-color: var(--text-color-light) !important;
  ;
}

.el-form-item:nth-of-type(1) {
  margin-top: 0 !important;
}

.el-form-item {
  margin-top: 22px !important;
}

.gp {
  width: 100%;
  display: flex;
  display: inline-black;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-top: -25px;
}

.sort {
  display: flex;
  align-items: center;
}

.across {
  width: 26px;
  height: 16px;
  background-color: rgba(229, 229, 229, 1);
  border: rgba(166, 166, 166, 1) solid 1px;
}

.vertical {
  width: 16px;
  height: 26px;
  background-color: rgba(229, 229, 229, 1);
  border: rgba(166, 166, 166, 1) solid 1px;
}

::v-deep .el-scrollbar .el-cascader-node__postfix {
  display: none !important;
}

.search_button {
  width: 64px;
  top: 99px;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
}

.search_button:hover {
  background-color: rgba(211, 57, 57, .8);
}
</style>