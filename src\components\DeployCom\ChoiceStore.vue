<template>
  <div class="choiceScreen">
    <!--    左部-->
    <div class="pubLeft">
      <p>组织机构筛选</p>
      <el-tree
        :data="leftdata"
        :props="defaultProps"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>
    <div class="pubRight">
      <div class="topBtns">
        <el-input
          style="
            width: 195px;
            height: 32px;
            border-radius: 4px;
            position: relative;
          "
          placeholder="请输入门店名称"
          prefix-icon="el-icon-search"
          v-model="searchValue"
        >
        </el-input>
        <div>
          <el-select v-model="choiceScreenValue1" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          <el-select v-model="choiceScreenValue2" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          <el-select v-model="choiceScreenValue3" placeholder="请选择">
            <el-option
              v-for="item in choiceScreen3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="ScreensSearch">搜索</div>
      </div>
      <div class="bottomList">
        <h4
          style="font-size: 17px; font-weight: 800; color: rgba(83, 82, 82, 1)"
        >
          标签设置
        </h4>
        <ul class="contents">
          <li v-for="(item,index) in labelLists" :key="index">
            <span>{{item.title}}</span>
             <el-checkbox v-model="allCheckBox.checked"></el-checkbox>
            <!-- <p></p>
            <img src="../../assets/img/home_img/checked.svg" /> -->
          </li>
        </ul>
        <div class="lists">
          <div>已选择20个门店 <span>重置</span></div>
          <ul class="storeData">
            <li v-for="(item, index) in storeLists" :key="index">
              <img src="../../assets/img/home_img/shop.png" />
              <p>{{item.title}}</p>
              <span class="el-icon-close" @click.stop='closeStore(index)'></span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChoiceStore",
  data() {
    return {
      // 搜索框
      searchValue:"",
      //  第三部分  左侧数控数据
      //左侧的数控结构
      leftdata: [
        {
          label: "星巴克总部",
          children: [
            {
              label: "上海市场",
              children: [
                {
                  label: "标准门店",
                },
                {
                  label: "特殊门店",
                  children: [
                    {
                      label: "虹桥机场店SKA273",
                    },
                  ],
                },
              ],
            },
            {
              label: "浙江市场",
              children: [
                {
                  label: "三级选项1",
                  children: [
                    {
                      label: "四级选项1",
                    },
                    {
                      label: "四级选项2",
                    },
                  ],
                },
              ],
            },
            {
              label: "北京市场",
              children: [
                {
                  label: "西北区",
                },
                {
                  label: "华北区",
                },
                {
                  label: "北京",
                },
                {
                  label: "西安",
                },
                {
                  label: "上海",
                },
              ],
            },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      //  第三部分 的三个select选择器
      choiceScreen1: [
        {
          value: "选项1",
          label: "上海市场",
        },
        {
          value: "选项2",
          label: "浙江市场",
        },
        {
          value: "选项3",
          label: "北京市场",
        },
        {
          value: "选项4",
          label: "深圳市场",
        },
      ],
      choiceScreenValue1: "上海市场",
      //  第二个select
      choiceScreen2: [
        {
          value: "选项1",
          label: "机场店",
        },
        {
          value: "选项2",
          label: "常规店",
        },
        {
          value: "选项3",
          label: "高铁店",
        },
        {
          value: "选项4",
          label: "特殊门店",
        },
      ],
      choiceScreenValue2: "机场店",
      //  第三个select
      choiceScreen3: [
        {
          value: "选项1",
          label: "DMB",
        },
        {
          value: "选项2",
          label: "大厅电视",
        },
        {
          value: "选项3",
          label: "儿童屏",
        },
        {
          value: "选项4",
          label: "联组屏",
        },
      ],
      allCheckBox:{
        checked:false,
      },
      choiceScreenValue3: "DMB",
      //  点击投放
      labelLists:[
        {
          title:"是否小镇餐厅",
        },
         {
          title:"是否小镇餐厅",
        },
         {
          title:"是否小镇餐厅",
        }
      ],
      storeLists:[
         {
          title:"新曹杨餐厅1",
        },
         {
          title:"新曹杨餐厅2",
        },
         {
          title:"新曹杨餐厅3",
        },
         {
          title:"新曹杨餐厅4",
        },
         {
          title:"新曹杨餐厅5",
        },
         {
          title:"新曹杨餐厅",
        },
         {
          title:"新曹杨餐厅",
        },
         {
          title:"新曹杨餐厅6",
        },
         {
          title:"新曹杨餐厅",
        }
      ]
    };
  },
  methods: {
    closeStore(idx){
      this.storeLists.forEach((item,index) => {
        if(idx==index){
          this.storeLists.splice(index,1);
          this.$message.success("删除成功")
        }
      })
    }
  },
};
</script>

<style scoped>
/*选择屏幕*/
ul {
  list-style: none;
}
.choiceScreen {
  background-color: #fff;
  display: flex;
}
.pubLeft {
  width: 277px;
  height: 100%;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  font-size: 0.14rem;
  text-align: center;
  border-right: 0.02rem solid #f8f7f7;
}

.choiceScreen > .pubLeft > p {
  color: rgba(56, 56, 56, 1);
  font-size: 0.16rem;
  text-align: left;
  border-bottom: 0.01rem solid rgba(236, 235, 235, 1);
  padding: 0.37rem 0.84rem 0.08rem 0.2rem;
}

::v-deep .el-tree {
  padding: 0.2rem 0.04rem 0 0.06rem;
}

.el-tree-node__content {
  background-color: #fff;
}

::v-deep .el-tree-node__label {
  color: rgba(79, 77, 77, 1);
}

/*符号*/
::v-deep .el-tree-node__expand-icon {
  color: #595959;
}
/*右边*/
.pubRight {
  height: 100%;
  width: calc(100% - 295px);
}
.pubRight > .topBtns {
  padding: 16px 10px;
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(236, 235, 235, 1);
}
.pubRight > .topBtns > div {
  margin: 0 10px;
}
.pubRight > .topBtns .el-select {
  width: 160px;
  height: 32px;
}
.pubRight > .topBtns > .ScreensSearch {
  width: 88px;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
  color: #fff;
  background-color: var(--text-color);
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}
.pubRight > .screenBtn {
  position: absolute;
  bottom: 100px;
  margin: auto;
  left: 100px;
  right: 0;
  width: 181px;
  height: 48px;
  line-height: 48px;
  color: #fff;
  background-color: rgba(108, 178, 255, 1);
  border-radius: 10px;
  font-size: 14px;
  text-align: center;
}
.pubRight > .bottomList {
  width: 100%;
  height: 300px;
}
.pubRight > .bottomList > h4 {
  padding: 14px 14px 0;
}
.pubRight > .bottomList > .contents {
  color: rgba(77, 147, 226, 1);
  font-size: 16px;
  display: flex;
  flex-wrap: wrap;
  padding: 0 14px;
}
.pubRight > .bottomList > .contents > li {
  width: 80px;
  height: 60px;
  margin: 10px 118px 0 0;
}
.pubRight > .bottomList > .contents > li>span{
  line-height: 25px;
}
.pubRight > .bottomList > .contents > li ::v-deep .el-checkbox{
  margin-left: 10px;
    width: 17px;
  height: 17px;
}
    /* 把element table的复选框改为红色 */
    .contents ::v-deep .el-checkbox__input .el-checkbox__inner{
       width: 20px;
        height: 20px;
        margin: 0 5px;
    }
.contents ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background: var(--base-color) !important;
        border-color:var(--base-color) !important;
        width: 20px;
        height: 20px;
        text-align: center;
        margin: 0 5px;
    }
    .contents ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after{
      margin: 3px;
    }
.pubRight > .bottomList > .lists {
  margin-top: 5px;
  border-top: 1px solid #ebebeb;
  padding: 18px 21px;
}
.pubRight > .bottomList > .lists > div {
  color: rgba(166, 166, 166, 1);
  font-size: 14px;
  text-align: left;
}
.pubRight > .bottomList > .lists > div > span {
  width: 28px;
  height: 21px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: left;
  cursor: pointer;
}
.pubRight > .bottomList > .lists > ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.pubRight > .bottomList > .lists > .storeData > li {
  width: 150px;
  height: 47px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  margin: 18px 25px 0 0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
}
.pubRight > .bottomList > .lists > .storeData > li > img {
  width: 24px;
  height: 24px;
}
.pubRight > .bottomList > .lists > .storeData > li > .el-icon-close {
  font-size: 25px;
  color: rgba(108, 178, 255, 1);
  cursor: pointer;
}

</style>
