import Vue from 'vue'
/**
 * 自定义指令
 */
let MyPlugin = {}
export default MyPlugin.install = function (vue, options) {
  Vue.directive('loadmore', {
    bind(el, binding) {
      // 获取element-ui定义好的scroll盒子
      const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
      SELECTWRAP_DOM.addEventListener('scroll', function () {
        const CONDITION = this.scrollHeight - Math.ceil(this.scrollTop) <= (this.clientHeight+1)
   
        if (CONDITION && this.scrollTop !== 0) {
          binding.value();
        }

      })
    }
  })
}
