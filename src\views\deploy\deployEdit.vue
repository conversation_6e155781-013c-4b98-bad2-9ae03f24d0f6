<template>
    <div class="deployEdit" v-loading="loading">
        <i class="el-icon-back cursor" style="font-size:24px;color:var(--btn-background-color);margin: 15px 0 0 10px;"
            @click="$router.push('/deploy/quickHistory')"></i>
        <EditGeneral v-show="platform == 3 && editActive == 0" :btpub_id="btpub_id" ref="generalParams"></EditGeneral>
        <editDmb v-show="platform == 2 && editActive == 0" :btpub_id="btpub_id" ref="DMBParams"></editDmb>
        <editContent v-show="platform == 4 && editActive == 0" :btpub_id="btpub_id" ref="ContentParams">
        </editContent>
        <editPushlishing :v_or_h="v_or_h" v-if="editActive == 1" :screen_type="screen_type" :dmb_spec="dmb_spec"
            :deploySetDetail="deploySetDetail" :platform="platform" :vs_spec="vs_spec" :DetaliData="DetaliData"
            :pubForm="pubForm" ref="pushing">
        </editPushlishing>

        <div class="event">
            <!-- <el-button class="cancel" @click="cancel" >取消</el-button> -->
            <div v-if="pub_flow_status_code != 4">
                <!-- <el-button class="cancel" @click="updateContent" v-show="status == '下发完成' && editActive == 0"
                    style="margin-right: 10px;">更新内容
                </el-button> -->
                <el-button class="cancel" @click="updateContent" v-show="updateStatus" style="margin-right: 10px;">更新内容
                </el-button>
            </div>
            <div v-else>
                <!-- <el-button class="cancel" @click="updateContent" v-show="status == '下发完成' && editActive == 0">
                </el-button> -->
            </div>
            <el-button class="next" @click="next" v-if="editActive != 1">编辑投放区域</el-button>
            <!-- <el-button class="pushing" @click="publish" v-else>发布</el-button> -->
            <div v-else>
                <div v-if="$store.state.user.PUSCS_AUDIT_OPEN == 1">
                    <div v-if="status_code == 1">
                        <el-button class="pushing" @click="submitPushLish" :disabled="PushliState">提交</el-button>
                    </div>
                    <div v-else>
                        <el-button class="pushing" @click="publish" :disabled="PushliState">发布</el-button>
                    </div>
                </div>
                <div v-else>
                    <el-button class="pushing" @click="publish">发布</el-button>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
// import Preview from './components/preview.vue'
import EditGeneral from "./components/editGeneral"
import editPushlishing from "./components/editPushlishing.vue";
import editDmb from "./components/editDmb.vue";
import editContent from "./components/editContent"
import { publish_area, publish_launch, update_remodified_cf, api_vs_cf_notify, get_btpub_detail } from "@/api/contentdeploy/contentdeploy"
import { submit_btpub_review } from "@/api/audit/audit"
import { datas_filter_cond } from "@/api/commonInterface";

export default {
    data() {
        return {
            btplan_id: null,
            platform: "",
            btpub_id: "",
            v_or_h: "",
            screen_type: "",
            editActive: 0,
            deploySetDetail: {

            },
            requestPublish: {
                btpub_id: "",
                action: "pub"
            },
            publishData: {
                sel_info: {

                }
            },
            dmb_spec: {

            },
            DetaliData: {

            },
            loading: true,
            DmbRequestState: null,
            pubForm: null,
            contents: [],
            status: "",
            btnState: false,
            PushineStatus: '',
            status_code: '',
            pub_flow_status_code: '',
            updateStatus: false
        }
    },
    components: {
        EditGeneral,
        editPushlishing,
        editDmb,
        editContent
    },
    methods: {
        cancel() {
            this.$router.push("/deploy/quickHistory")
        },
        next() {
            this.platform = Number(this.$route.query.platform)
            switch (this.platform) {
                case 2:
                    // dmb
                    this.dmb_spec = this.$refs.DMBParams.dmb_spec;
                    this.DetaliData = this.$refs.DMBParams.dmbData;
                    console.log(this.DetaliData, "this.DetaliData");
                    this.$refs.DMBParams.timeframe.forEach(item => {
                        item.addContentArray.forEach(item1 => {
                            if (item1.imgUrl == "") {
                                this.DmbRequestState = true;
                            } else {
                                this.DmbRequestState = false;
                            }
                        })
                    })
                    if (this.DmbRequestState == true) {
                        this.$message.warning("内容不可为空")
                    } else {
                        this.editActive = 1;
                        sessionStorage.setItem("editActive", this.editActive)
                    }
                    break;
                case 3:
                    // 普通
                    console.log(this.$refs.generalParams, 'this.$refs.generalParams');

                    this.v_or_h = this.$refs.generalParams.generalData.play_info.v_or_h;
                    this.screen_type = this.$refs.generalParams.generalData.usage_type;
                    this.deploySetDetail = this.$refs.generalParams.generalData;
                    this.DetaliData = this.$refs.generalParams.generalData;
                    this.pubForm = this.$refs.generalParams.DeployForm;
                    console.log("pubForm", this.pubForm);
                    this.editActive = 1;
                    sessionStorage.setItem("editActive", this.editActive)
                    break;
                case 4:
                    // 联屏
                    console.log(this.$refs.ContentParams, "this.$refs.ContentParams");
                    this.vs_spec = this.$refs.ContentParams.vs_spec;
                    this.DetaliData = this.$refs.ContentParams.EditContent;
                    if (this.$refs.ContentParams.templateScene.length != 0) {
                        this.DmbRequestState = false;
                        this.$refs.ContentParams.templateScene.forEach(item => {
                            item.tile_groups.forEach(item1 => {
                                if (item1.percent_text == "等待添加内容...") {
                                    this.DmbRequestState = true;
                                } else {
                                    this.DmbRequestState = false;
                                }
                            })
                        })
                        if (this.DmbRequestState == true) {
                            this.$message.warning("内容不可为空")
                        } else {
                            this.editActive = 1;
                            sessionStorage.setItem("editActive", this.editActive)
                        }
                    } else {
                        this.DmbRequestState = true;
                        this.$message.warning("内容不可为空")
                    }
                    console.log(this.DmbRequestState);
                    break;
                default:
                    break;
            }

        },
        submitPushLish() {
            console.log(this.componentActive, 'isEditDMB');
            this.publishData.btplan_id = this.btplan_id;
            this.publishData.sel_info.opsmarkets = this.$refs.pushing ? this.$refs.pushing.queryList.marketname : '';
            this.publishData.sel_info.storetypes = this.$refs.pushing ? this.$refs.pushing.queryList.shopname : '';
            this.publishData.sel_info.exclude_shops = this.$refs.pushing.noSelectShops
            this.publishData.sel_info.screen_tags = this.$refs.pushing.screenTagsValue
            this.publishData.sel_info.shop_tags = this.$refs.pushing.shopTagsValue;
            this.publishData.sel_info.shop_tags_rela = this.$refs.pushing.shop_tags_rela
            this.publishData.sel_info.screen_tags_rela = this.$refs.pushing.screen_tags_rela
            
            if (this.platform == 2) {
                this.publishData.sel_info.dpfrom = 3
                this.publishData.sel_info.storeplaylayout = this.$refs.pushing.storeplaylayoutSelected
            }
            publish_area(this.publishData).then(res => {
                if (res.rst == "ok") {
                    let params = {
                        btpub_id: this.btpub_id
                    }
                    submit_btpub_review(params).then(res => {
                        console.log(res, 'res');
                        if (res.rst == 'ok') {
                            this.$message.success('发布审核提交成功')
                            this.$router.push({
                                path: "/deploy/quickHistory"
                            })
                        } else {
                            this.$message.warning(res.error_msg)
                        }
                    })
                }
            })
        },
        publish() {
            this.publishData.btplan_id = this.btplan_id;
            this.requestPublish.btpub_id = this.btpub_id;
            this.publishData.sel_info.opsmarkets = this.$refs.pushing ? this.$refs.pushing.queryList.marketname : '';
            this.publishData.sel_info.storetypes = this.$refs.pushing ? this.$refs.pushing.queryList.shopname : '';
            console.log( this.$refs.pushing.noSelectShops,' this.$refs.pushing.noSelectShops');
            
            this.publishData.sel_info.exclude_shops = this.$refs.pushing.noSelectShops
            this.publishData.sel_info.screen_tags = this.$refs.pushing.screenTagsValue
            this.publishData.sel_info.shop_tags = this.$refs.pushing.shopTagsValue;
            this.publishData.sel_info.shop_tags_rela = this.$refs.pushing.shop_tags_rela
            this.publishData.sel_info.screen_tags_rela = this.$refs.pushing.screen_tags_rela
            if (this.platform == 2) {
                this.publishData.sel_info.dpfrom = 3
                this.publishData.sel_info.storeplaylayout = this.$refs.pushing.storeplaylayoutSelected
            }
            console.log(this.publishData, 'publishData');
            publish_area(this.publishData).then(res => {
                if (res.rst == "ok") {
                    // publish_launch(this.requestPublish).then(ress => {
                    //     if (ress.rst == "ok") {
                    //         this.$message.success("内容发布成功")
                    //         localStorage.setItem("active", 0);
                    //         this.$router.push({
                    //             path: "/deploy/quickHistory"
                    //         })
                    //     } else if (ress.error_msg === "正在发布中...") {
                    //         this.$router.push({
                    //             path: "/deploy/quickHistory"
                    //         })
                    //     } else {
                    //         this.$message.warning(ress.error_msg)
                    //     }
                    // })
                    if (this.PushineStatus == 1) {
                        this.$confirm('保存策略并发布投放?', '提示', {
                            confirmButtonText: '保存策略',
                            cancelButtonText: '不保存',
                            type: 'warning'
                        }).then(() => {
                            this.requestPublish.save_strategy = 1;
                            publish_launch(this.requestPublish).then(ress => {
                                if (ress.rst == "ok") {
                                    this.$message.success("内容发布成功")
                                    localStorage.setItem("active", 0);
                                    localStorage.removeItem("btpub_id")
                                    this.$router.push({
                                        path: "/deploy/quickHistory"
                                    })
                                } else if (ress.error_msg === "正在发布中...") {
                                    this.$router.push({
                                        path: "/deploy/quickHistory"
                                    })
                                } else {
                                    this.$message.warning(ress.error_msg)
                                }
                            })
                        }).catch(() => {
                            this.requestPublish.save_strategy = 0;
                            publish_launch(this.requestPublish).then(ress => {
                                if (ress.rst == "ok") {
                                    this.$message.success("内容发布成功")
                                    localStorage.setItem("active", 0);
                                    localStorage.removeItem("btpub_id")
                                    this.$router.push({
                                        path: "/deploy/quickHistory"
                                    })
                                } else if (ress.error_msg === "正在发布中...") {
                                    this.$router.push({
                                        path: "/deploy/quickHistory"
                                    })
                                } else {
                                    this.$message.warning(ress.error_msg)
                                }
                            })
                        });
                    } else {
                        this.requestPublish.save_strategy = 0;
                        publish_launch(this.requestPublish).then(ress => {
                            if (ress.rst == "ok") {
                                this.$message.success("内容发布成功")
                                localStorage.setItem("active", 0);
                                localStorage.removeItem("btpub_id")
                                this.$router.push({
                                    path: "/deploy/quickHistory"
                                })
                            } else if (ress.error_msg === "正在发布中...") {
                                this.$router.push({
                                    path: "/deploy/quickHistory"
                                })
                            } else {
                                this.$message.warning(ress.error_msg)
                            }
                        })
                    }
                } else {
                    this.$message.warning(res.error_msg)
                }
            })
        },
        getSelectDataList() {
            const params = {
                classModel: "ContentPub", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
            };
            datas_filter_cond(params).then((res) => {
                this.$store.commit("changeDataFilters", res["data"][0])
            });
        },
        updateContent() {
            this.platform = Number(this.$route.query.platform)
            let params = {
                cf_id: "",
                state: "notify"
            }
            console.log(this.platform);
            switch (this.platform) {
                case 2 || "2":
                    // dmb
                    this.dmb_spec = this.$refs.DMBParams.dmb_spec;
                    this.DetaliData = this.$refs.DMBParams.dmbData;
                    console.log(this.DetaliData, "this.DetaliData");
                    this.$refs.DMBParams.timeframe.forEach(item => {
                        item.addContentArray.forEach(item1 => {
                            if (item1.imgUrl == "") {
                                this.DmbRequestState = true;
                            } else {
                                this.DmbRequestState = false;
                            }
                        })
                    })
                    if (this.DmbRequestState == true) {
                        this.$message.warning("内容不可为空")
                    } else {
                        console.log(this.contents, "this.contents");
                        // this.DetaliData.contents.forEach(item => {
                        // params.cf_id = item.ref_id
                        let params = {
                            "btpub_id": this.btpub_id,
                            "action": "update"
                        }
                        publish_launch(params).then(res => {
                            console.log(res);
                            if (res.rst == "ok") {
                                this.$message.closeAll()
                                this.$message.success("更新成功")
                                this.$router.push("/deploy/quickHistory")
                            }
                        })
                        // })
                    }
                    break;
                case 3 || "3":
                    // 普通
                    console.log(this.$refs.generalParams, 'this.$refs.generalParams.generalData');
                    console.log("??");
                    this.v_or_h = this.$refs.generalParams.generalData.play_info.v_or_h;
                    this.screen_type = this.$refs.generalParams.generalData.usage_type;
                    this.deploySetDetail = this.$refs.generalParams.generalData;
                    this.DetaliData = this.$refs.generalParams.generalData;
                    this.pubForm = this.$refs.generalParams.DeployForm;
                    console.log("pubForm", this.pubForm);
                    console.log("DetaliData", this.DetaliData);
                    console.log(this.contents, " this.contents");
                    this.DetaliData.contents.forEach(item => {
                        params.cf_id = item.ref_id
                        update_remodified_cf(params).then(res => {
                            console.log(res);
                            if (res.rst == "ok") {
                                this.$message.closeAll()
                                this.$message.success("更新成功")
                                this.$router.push("/deploy/quickHistory")
                            }
                        })
                    })
                    break;
                case 4 || "4":
                    // 联屏
                    console.log(this.$refs.ContentParams, "this.$refs.ContentParams");
                    this.vs_spec = this.$refs.ContentParams.vs_spec;
                    this.DetaliData = this.$refs.ContentParams.EditContent;
                    if (this.$refs.ContentParams.templateScene.length != 0) {
                        this.DmbRequestState = false;
                        this.$refs.ContentParams.templateScene.forEach(item => {
                            item.tile_groups.forEach(item1 => {
                                if (item1.percent_text == "等待添加内容...") {
                                    this.DmbRequestState = true;
                                } else {
                                    this.DmbRequestState = false;
                                }
                            })
                        })
                        console.log(this.DetaliData, 'DetaliData');
                        if (this.DmbRequestState == true) {
                            this.$message.warning("内容不可为空")
                        } else {
                            this.DetaliData.contents.forEach(item => {
                                params.cf_id = item.ref_id
                                console.log(params, 'params1');
                                api_vs_cf_notify(params).then(res => {
                                    console.log(res, 'res--');
                                    if (res.rst == "ok") {
                                        this.$message.closeAll()
                                        this.$message.success("更新成功")
                                        this.$router.push("/deploy/quickHistory")
                                    }
                                })
                            })
                        }
                    } else {
                        this.DmbRequestState = true;
                        this.$message.warning("内容不可为空")
                    }
                    console.log(this.DmbRequestState);
                    break;
                default:
                    break;
            }
        }
    },
    created() {
        this.btplan_id = this.$route.query.btplan_id;
        this.platform = this.$route.query.platform;
        this.btpub_id = this.$route.query.btpub_id;
        this.editActive = sessionStorage.getItem("editActive") == null ? 0 : sessionStorage.getItem("editActive");
        this.status = this.$route.query.status
        this.status_code = this.$route.query.status_code
        this.pub_flow_status_code = this.$route.query.pub_flow_status_code;
        get_btpub_detail({
            btpub_id: this.btpub_id
        }).then(res => {
            this.updateStatus = res['data'][0]['active']
            console.log(res, 'resss');

        })
        this.getSelectDataList()
    },
    mounted() {
    },
    destroyed() {
        sessionStorage.clear()
    }

}
</script>

<style lang="scss" scoped>
.deployEdit {
    height: 100%;
}

.event {
    display: flex;
    justify-content: center;
    padding-top: 20px;

    .cancel {
        background-color: #eaf4ff;
        color: #7daee4;
        font-weight: bold;
        width: 205px;
        height: 52px;
        border-radius: 20px;
        font-size: 18px;
    }

    .next {
        background-color: var(--btn-background-color);
        color: #fff;
        font-weight: bold;
        width: 205px;
        height: 52px;
        border-radius: 20px;
        font-size: 18px;
    }

    .pushing {
        background-color: var(--btn-background-color);
        color: #fff;
        font-weight: bold;
        width: 205px;
        height: 52px;
        border-radius: 20px;
        font-size: 18px;
    }
}
</style>