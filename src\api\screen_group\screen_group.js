import { get, post } from '@/utils/request'

/**
 * @description 获取联屏数据
 * @param classModel VsGroup
 * @param page 0  (Int) 页码,从0开始
 * @param size（int）每页显示个数
 * @param itmarket (String) 非必要it市场查询
 * @param opsmarlet (String) 搜索内容
 * @param blurry : all 非必要模糊查找
 * @param dire : 0  (Int) 0:横屏;1:竖屏
 * @param tags_list : ["999"],  // (List) 标签查询
 * @param ky_flag : 0,  // (Int) 0:非查询; 1:或查询 2:且查询
 */
const get_screen_group = p => post('/dmb/api/json', p, 'get_adm_datas')

/**
 * @description 查询店铺
 * @param g_group_id group_id
 * @param shop_name 名称
 */
const search_screen_group = p =>
  post('/store/api/json', p, 'search_shop_by_name')

/**
 * @description 查询店铺数据
 * @param g_group_id group_id
 * @param shop_id shop_id
 */
const get_shop_data = p => post('/store/api/json', p, 'load_shop_edit_info')

/**
 * @description 绑定屏幕
 * @param shop_id 店铺id
 * @param sg_dire 0 横竖
 * @param sg_spec 1*n ?
 * @param screens 绑定的屏幕
 */
const bind_shop = p => post('/store/api/json', p, 'set_screen_of_sg')

/**
 * @description 解绑屏幕
 * @param sg_key 屏幕key
 */
const unbind_shop = p => post('/store/api/json', p, 'delete_screen_group')

/**
 * @description 屏幕标签
 * @param tags
 * @g_group_id ids
 * @shop_id 店铺id
 */
const bind_shop_tags = p => post('/store/api/json', p, 'update_shop_edit_info')

/**
 * @description 筛选条件
 * @param classModel
 *          //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件, VsGroup: 联屏组帅选条件
            //BatchUpgrade:批量升级；SysLog 系统日志
 */
const search_dma = p => post('/dmb/api/json', p, 'datas_filter_cond')

/**
 * @description 获取联屏详情
 * @param sg_key 屏幕key
 */
const get_dma_detail = p => post('/dsadm/api/json', p, 'get_vs_info')

/**
 * @description 联屏检测
 * @param sg_key 联屏key
 * @param cmd vs_index_debug 固定
 */
const dma_detection = p => post("/geo/api_ds/json", p, "send_mutli_vs_cmd");

/**
 * @description 联屏截图
 * @param sg_key 联屏key
 * @param cmd screen_snap 固定
 */
const dma_screenshot = p => post("/geo/api_ds/json", p, "send_mutli_vs_cmd");

/**
 * @description 检测截图是否成功
 * @param screen_id 屏幕id
 */
const dma_detection_screenshot = p =>
  post("/geo/api_ds/json", p, "check_screen_snap_ack");

/**
 * @description 设备重启
 * @param sg_key 联屏key
 * @param cmd reboot_device 固定
 */
const dma_restart = p => post("/geo/api_ds/json", p, "send_mutli_vs_cmd");

/**
 * @description 联屏拆除
 * @param sg_key 联屏key
 * @param cmd just_vsfree_index
 */
const dma_dismantle = p => post("/geo/api_ds/json", p, "send_mutli_vs_cmd");

/**
 * @description 位置调整
 * @param sg_key 联屏key
 * @screens 屏幕组
 */
const dma_position = p =>
  post("/geo/api_digital_signage/json", p, "setup_screen_of_vs");
// const dma_position = p =>
//   post("/dsadm/api/json", p, "justify_position_of_sg_screen");

/**
 * @description 获取联屏播放内容
 * @param sg_id  (int) 联屏组id
 */
const get_dma_content = p => post("/dsadm/api/json", p, "get_vs_content");

/**
 * @description 新增删除联屏组标签
 * @param sg_ids 联屏id
 * @param tags 标签列表
 * @param action "add" or "del"
 */
const add_dma_or_delete = p => post("/geo/api_ds/json", p, "screen_group_tags_mgmt");

export {
  get_screen_group,
  search_screen_group,
  get_shop_data,
  bind_shop,
  unbind_shop,
  bind_shop_tags,
  search_dma,
  get_dma_detail,
  dma_detection,
  dma_screenshot,
  dma_detection_screenshot,
  dma_restart,
  dma_dismantle,
  dma_position,
  get_dma_content,
  add_dma_or_delete
};
