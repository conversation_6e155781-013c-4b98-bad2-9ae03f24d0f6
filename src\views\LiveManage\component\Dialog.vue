<template>
  <div class="dialog">
    <el-dialog
      title="新增设备"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form :model="newDevicesForm">
        <el-form-item label="门店编号:">
          <el-input v-model="newDevicesForm.shop_id" autocomplete="off"></el-input>
          <el-button @click="searchShopScreen" type="primary">搜索</el-button>
        </el-form-item>
        <el-form-item label="直播类型:">
          <el-select v-model="newDevicesForm.usage_type">
            <el-option label="实时直播" value="1"></el-option>
            <el-option label="录播" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="门店屏幕:" v-if="serachShopSreenList.length != 0">
          <div class="screen">
            <div
              v-for="item in serachShopSreenList"
              class="screen_item"
              :key="item.displaynum"
              :class="active_scree_num == item.displaynum ? 'active' : '' "
              @click="selectScreen(item)"
            >{{item.displaynum}}</div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="request_Add_devices">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    serachShopSreenList: {
      type: Array,
      default: []
    }
  },
  watch: {},
  data() {
    return {
      newDevicesForm: {
        shop_id: "",
        usage_type: "1"
      },
      active_scree_num: "",
      active_screen_id: []
    };
  },
  methods: {
    handleClose() {
      this.newDevicesForm.shop_id = " ";
      this.newDevicesForm.usage_type = " ";
      this.$emit("newhandleClose");
    },
    searchShopScreen() {
      if (this.newDevicesForm.shop_id != "") {
        // this.newDevicesForm.shop_id = this.newDevicesForm.shop_id.toLocaleUpperCase();
        this.newDevicesForm.shop_id = this.newDevicesForm.shop_id;
        this.newDevicesForm.shop_id = this.newDevicesForm.shop_id.trim();
        this.$emit("searchShopScreen", this.newDevicesForm.shop_id);
      } else {
        this.$message.warning("请输入门店编号");
      }
    },
    selectScreen(screen) {
      this.active_scree_num = screen.displaynum;
      this.active_screen_id = screen.screen_id;
      // console.log(this.active_screen_id.indexOf(screen.screen_id));
      // let del_index = this.active_screen_id.indexOf(screen.screen_id);
      // if (this.active_screen_id.indexOf(screen.screen_id) != -1) {
      //   this.active_screen_id.splice(del_index, 1);
      // } else {
      //   this.active_screen_id.push(screen.screen_id);
      // }
    },
    request_Add_devices() {
      if (this.active_screen_id.length == 0) {
        this.$message.warning("请选择设备");
        return;
      }
      let params = {
        screen_id: [this.active_screen_id],
        usage_type: this.newDevicesForm.usage_type
      };
      this.$emit("request_Add_devices", params);
    }
  }
};
</script>

<style lang='scss' scoped>
.screen {
  display: flex;
  margin-left: -10px;
  .screen_item {
    margin-left: 10px;
    height: 60px;
    width: 90px;
    text-align: center;
    border-radius: 5px;
    line-height: 60px;
    background-color: #eaeaea;
    cursor: pointer;
    &:nth-child(1) {
      margin-left: 0 !important;
    }
  }
}
.active {
  background-color: #c9dcfc !important;
  color: #fff !important;
}
</style>