<template>
  <div id="app">

    <router-view />
  </div>
</template>
<style>
#app {
  width: 100vw;
  height: 100vh;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei;
  /* overflow: hidden; */
}
</style>
<script>
export default {
  name: "App",
  created() {
    // this.$store.state.zoom = 0.47 * (document.body.clientWidth / 1920);
    // window.onresize = () => {
    //   this.$store.state.zoom = (document.body.clientWidth / 1920) * 0.47
    // };

  },
};
</script>
<style>
@import "./assets/css/base.css";

.popperOptions {
  height: 140px;
  overflow: auto;
}

.popperOptions1 {
  height: 170px;
  overflow: auto;
}

.el-date-editor .el-range-separator {
  padding: 0 !important;
}

.daypartpopper i {
  display: none !important;
}

.el-color-dropdown__btns {
  display: none;
}
</style>