<template>
    <div class="assign">
        <div class="center">
            <img src="@/assets/img/EmptyState.png" alt="">
            <p>功能正在研发中,马上就来</p>
        </div>
    </div>
</template>

<script>
export default {

}
</script>

<style lang="scss" scoped>
.assign {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 170px);
    overflow-y: auto;

    img {
        width: 150px;
        height: 150px;
    }

    p {
        color: rgba(56, 56, 56, 1);
        font-size: 14px;
    }
}
</style>