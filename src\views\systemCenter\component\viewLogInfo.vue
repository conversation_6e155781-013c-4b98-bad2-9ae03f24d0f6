<template>
    <el-dialog title="日志详情" :visible.sync="show" center width="1049px" top="75px" custom-class='batch_tags_dialog'
        :close-on-click-modal='false' :before-close="handleClose">
        <div class="dialog_wrap" v-loading="loading" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(255,255,255,0.85)">

            <!-- {{ detailsData }} -->

            <!-- <p v-if="type === 2" class="release_name">发布名称：上海市场春节上新</p> -->
            <!-- 表格 -->
            <!-- <div class="table_wrap">
                <p class="title">{{ type === 2 ? '发布详情' : '详情' }}</p>
                <div class="table">
                    <el-table :data="tableData" border style="width: 100%" :header-cell-style="tableHeaderStyle"
                        :row-style="{ height: '80px' }">
                        <el-table-column v-for="item in tableColumList[type - 1]" :key="item.props" :prop="item.prop"
                            :label="item.label" width="" align="center"> </el-table-column>
                    </el-table>
                </div>
            </div> -->
            <!-- 物料内容 -->
            <div v-for="item in detailsData">
                <div class="picture_wrap" v-if="item.type == 'desc'">
                    <p class="title"> 日志名称: {{ item.text }}</p>
                </div>
                <div v-if="item.type == 'src_list'">
                    <div class="picture_wrap">
                        <p class="title"> {{ item.title }} </p>
                        <div class="picture_list">
                            <div class="img" v-for="item1 in item.info" :key="item.id"
                                :class="type === 3 ? 'standby' : 'normal'">
                                <el-image :src="item1.url" fit="cover" style="width:100%;height:100%">
                                    <div slot="error" class="image-slot">
                                        <i class="el-icon-picture" style="font-size:26px;color:#a2a2a2"></i>
                                    </div>
                                </el-image>
                                <!-- <p class="img_size" v-show="type != 3">{{ item.size }}</p> -->
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div v-if="isTable">
                <div class="picture_wrap">
                    <div class="shop_list" v-for="(item, index) in tableList" style="margin-top:20px">
                        <p class="title"> {{ item[0].type }} </p>
                        <table cellspacing="0" style="width: 100%;" border="0px solid #ccc">
                            <thead style="width: 100%; background-color: var( --text-color-light);;height: 40px;">
                                <th style="border:1px solid #999" v-for="item1 in tableList[index]"> {{ item1.label }}
                                </th>
                            </thead>
                            <tbody style="width: 100%;height: 50px;">
                                <tr>
                                    <td style="text-align: center;border:1px solid #999"
                                        v-for="item1 in tableList[index]">
                                        {{ item1.value }} </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- <el-table :data="tableArr" border style="width: 100%" :header-cell-style="tableHeaderStyle"
                            :row-style="{ height: '80px' }">
                            <el-table-column v-for="item in tableArr" :key="item.value" prop="value"
                            :label="item.label" width="" align="center"> </el-table-column>
                        </el-table> -->
                    </div>
                </div>
            </div>
            <!-- 门店及屏幕 -->
            <!-- <div class="shop_wrap" v-if="type !== 1">
                <p class="title">{{ shopTitle }}</p>
                <div class="shop_list">
                    <el-table :data="pubTableData" border style="width: 100%" :header-cell-style="tableHeaderStyle"
                        :row-style="{ height: '80px' }">
                        <el-table-column v-for="item in pubConditionCol" :key="item.props" :prop="item.prop"
                            :label="item.label" width="" align="center"> </el-table-column>
                    </el-table>
                </div>
            </div> -->
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        //日志类型:1-资源详情,2-发布详情,3-应急详情
        type: {
            type: Number,
            default: 1
        },
        detailsData: {
            type: Object
        }
    },
    components: {

    },
    data() {
        return {
            loading: false,
            tableColumList: [
                [
                    { prop: 'test', label: '名称(Psid)' },
                    { prop: 'test', label: '操作时间' },
                    { prop: 'test', label: '操作类型' },
                    { prop: 'test', label: 'ip地址' }
                ],
                [
                    { prop: 'test', label: '投放类型' },
                    { prop: 'test', label: '门店数' },
                    { prop: 'test', label: '屏幕数' },
                    { prop: 'test', label: '内容数' },
                    { prop: 'test', label: '播放周期' },
                    { prop: 'test', label: '经营时段详情' },
                    { prop: 'test', label: '播放类型' }
                ],
                [
                    { prop: 'test', label: '名称(Psid)' },
                    { prop: 'test', label: '操作时间' },
                    { prop: 'test', label: '操作类型' },
                    { prop: 'test', label: 'ip地址' },
                ],
            ],
            pubConditionCol: [
                { prop: 'market', label: '运营市场' },
                { prop: 'shoptype', label: '门店类型' },
                { prop: 'tags', label: '设备标签' }
            ],
            tableData: [],  // 详情表格
            imageList: [],  // 涉及物料/待机素材图片列表
            pubTableData: [],   // 发布触发范围条件表格
            isTable: false
        };
    },
    computed: {
        title() {
            switch (this.type) {
                case 1:
                    return '资源详情';
                case 2:
                    return '发布详情';
                case 3:
                    return '应急详情';

            }
        },
        materialTitle() {
            switch (this.type) {
                case 1:
                    return '涉及物料';
                case 2:
                    return '发布包含物料';
                case 3:
                    return '待机素材';

            }
        },
        shopTitle() {
            switch (this.type) {
                case 2:
                    return '发布触发范围条件';
                case 3:
                    return '发布触发范围条件';

            }
        }
    },
    watch: {
        show(newVal, oldVal) {
            if (newVal) {
                this.loading = true;
                this.getTableData();
                this.tableList = []
                this.detailsData.forEach(item => {
                    console.log(item, 'item');
                    this.tableArr = []
                    if (item.type == 'table') {
                        this.isTable = true;
                        console.log(item.col1[0], 'item.col1');
                        item.col0.forEach((item1, index) => {
                            console.log(index, 'index');
                            this.tableArr.push({
                                label: item1,
                                value: item.col1[index],
                                type: item.type == 'table' ? item.title : ''
                            })
                        })
                        this.tableList.push(this.tableArr)
                    }
                })
                console.log(this.tableList, 'tableList');
                this.loading = false;
            }
        },
    },
    created() {

    },
    mounted() {
    },
    methods: {
        getTableData() {
            // const list = [
            //     { test: '王小虎' }
            // ];
            // const list2 = [
            //     { id: 1, url: 'https://img.xiaopiu.com/userImages/img4437417fbff35e40.png', size: '1920*1080' },
            //     { id: 2, url: 'https://img.xiaopiu.com/userImages/img4437417fbff35e40.png', size: '1920*1080' },
            // ]
            // const list3 = [
            //     { market: '上海市场', shoptype: '标准店', tags: '品宣屏' },
            //     { market: '上海市场', shoptype: '标准店', tags: '品宣屏' },
            //     { market: '上海市场', shoptype: '标准店', tags: '品宣屏' },
            // ]

            // setTimeout(() => {
            //     this.tableData = list;
            //     this.imageList = list2;
            //     this.pubTableData = list3;
            // }, 1000);
            this.loading = false;
        },
        handleClose() {
            this.$emit('close', false)
        }
    },
};
</script>

<style lang="scss" scoped>
::v-deep .batch_tags_dialog {
    // margin-top: -25px !important;
    border-radius: 16px !important;
}

::v-deep .batch_tags_dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 30px !important;
}

::v-deep .batch_tags_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
    text-align: center;
}

.batch_tags_dialog {
    margin-top: 75px !important;
    font-size: 14px;

    .dialog_wrap {
        max-height: 768px;
        padding: 10px 8px 15px 8px;
        box-sizing: border-box;
        overflow-y: auto;

        .release_name {
            font-weight: bold;
            margin: 15px 0 25px 0;
        }

        .title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .table_wrap {
            margin-bottom: 21px;

            .table {
                width: 100%;
                min-height: 120px;
                max-height: 240px;
            }
        }

        .picture_wrap {
            margin-bottom: 25px;

            .picture_list {
                width: 100%;
                min-height: 225px;
                max-height: 400px;
                display: flex;
                flex-wrap: wrap;
                overflow-y: auto;
                padding: 10px 0;
                border: 1px solid #e5e5e5;

                .standby {
                    width: 320px;
                    height: 160px;
                }

                .normal {
                    width: 185px;
                    height: 185px;
                }

                .img {
                    position: relative;
                    box-sizing: border-box;
                    padding: 4px;
                    // border: 1px solid #f1f1f1;
                    margin: 5px;
                    box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.13);
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    ::v-deep .image-slot {
                        width: 100% !important;
                        height: 100% !important;
                        display: flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                    }

                    .img_size {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: calc(100% - 8px);
                        height: 26px;
                        color: #fff;
                        font-size: 12px;
                        line-height: 26px;
                        text-align: center;
                        background-color: rgba(0, 0, 0, 0.36);
                        margin: 0 4px 4px;
                    }
                }

            }
        }

        .shop_wrap {
            margin-bottom: 21px;

            .shop_list {
                width: 100%;
                min-height: 111px;
                margin-top: 20px;
                max-height: 200px;
            }
        }
    }
}

::v-deep .el-image {
    img {
        object-fit: contain !important;
    }
}
</style>
