<template>
    <div class="to_do_history" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"  element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <!-- 列表区 -->
        <div class="table_wrap" :style="{height:autoHeight.height}">
            <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"  :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px','text-align':'center'}" :cell-style="{'text-align':'center'}">
                <el-table-column prop="isUnified"  label="" width="50" >
                    <template slot-scope="scope">
                        <!-- <img v-if="scope.row.isUnified" src="../../assets/img/checked.png" alt="" style="width:16px;height:16px;position:absolute;top:0;left:0">
                        <img v-else src="../../assets/img/nochecked.png" alt="" style="width:16px;height:16px;position:absolute;top:0;left:0"> -->
                        <div class="angle_mark_wrap" v-if="scope.row.isUnified">
                            <div class="angle_mark mark_red"></div>
                            <div class="angle_mark_content">NEW</div>
                        </div>
                        <div class="angle_mark_wrap" v-else>
                            <div class="angle_mark mark_green"></div>
                            <div class="angle_mark_content">变更</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="num"  label="No." width="70" >
                       <template slot-scope="scope">
                        {{scope.$index+1}}
                    </template>
                </el-table-column>
                <el-table-column prop="storecode"  label="餐厅编号" width=""></el-table-column>
                <el-table-column prop="name"  label="餐厅名称" width=""></el-table-column>
                <el-table-column prop="opsmarketname"  label="营运市场" width="" ></el-table-column>
                <!-- <el-table-column prop="name"  label="城市" width=""></el-table-column> -->
                <el-table-column prop="storetypename"  label="类型" width=""></el-table-column>
                <el-table-column prop="displaycnt"  label="屏幕数量" width=""></el-table-column>
                <el-table-column prop=""  label="变更项" width=""></el-table-column>
                <el-table-column prop="data_from"  label="系统匹配" width=""></el-table-column>
                <el-table-column prop="optuser_email"  label="操作账户" width=""></el-table-column>
                <el-table-column prop=""  label="操作时间" width=""></el-table-column>
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="to_do_history_footer">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :pager-count='5'
                :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper"
                :total="totalNum">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {getNewShopInfo,opeingnewstoremgmt} from "../../api/shopManage/shop"
import {EventBus} from "../../utils/eventBus"
export default {
    components: {

    },
    data() {
        return {
            loading:false,
            currentPage:1, //页码
            totalNum:20, //总数据数量
            pageSize:10,
            tableData: [  //列表数据
                // {
                // num:1,
                // date: '2016-05-02',
                // name: '王小虎',
                // address: '系统自建',
                // isUnified:0,
                // delShow:0
                // }, 
                // {
                // num:2,
                // date: 'sss',
                // name: 'aaaaa',
                // address: '主档中台',
                // isUnified:1,
                // delShow:1
                // }, 
                
            ],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getInfo()
    },
    mounted() {
        EventBus.$on("todoSth",this.getInfo)
    },
    methods: {
                  // 获取列表信息
        getInfo(){
            const params = {
                 "classModel": "OpeningNewStore",
                // "sort": "",//非必要，排序规则，storecode,createdAtS
                "page": this.currentPage - 1, //起始页码,
                "size": this.pageSize, //每页数据量,
                "status": "waitinghis", 
                // "blurry": "",//搜索门店编号或者名称模糊查找　
            }
            getNewShopInfo(params).then(res=>{
                console.log(res,'获取数据res历史');
                if(res.rst=='ok'){
                    this.tableData = res.data[0].content;
                    this.totalNum = res.data[0].totalElements;
                }
            })
        },
        //页码改变
        handleCurrentChange(val) {
            console.log(`现在是第${val}页`);
            this.currentPage = val;
            this.getInfo();
        },
        handleSizeChange(val){
            console.log(`每页${val}条`);
            this.pageSize = val;
            this.getInfo();
        },
        // 列表区高度自适应
        getHeight(){
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 168 + 'px';
            this.autoHeight.heightNum = windowHeight - 168;
        },
    },
    destroyed () {
        window.removeEventListener('resize', this.getHeight);
        EventBus.$off("todoSth")
    },
};
</script>

<style scoped>
    *{
        box-sizing: border-box;
    }
    .to_do_history{
        width: 100%;
        padding: 11px;
    }/* 角标 */
    .angle_mark{
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 0;
        border-right: 40px solid transparent;
        z-index: 9;
    }
    .mark_red{
        border-top: 40px solid red;
    }
    .mark_green{
        border-top: 40px solid green;
    }
    .angle_mark_content{
        position: absolute;
        width: 40px;
        height: 40px;
        z-index: 10;
        text-align: center;
        line-height: 40px;
        top: -5px;
        left: -6px;
        transform: rotate(-45deg) scale(.8);
        font-size: 12px;
        color: #fff;
    }
    
    /* 底部页码区 */
    .to_do_history_footer{
        box-sizing: border-box;
        width: 100%;
        height: 66px;
        font-size: 14px;
        padding-top: 17px;
        text-align: right;
        padding-right: 20px;
    }
</style>
