import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const privateKey='MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKa/1T4sZeGMPGp0aNJd8srTzgbXGh6cu0IRKbysMnIYq5X3Ea9mCKYrqnjcjbuLqU0RbTSBWP8eVFMYAnNfbLX5V+4+FxyG8W4YIS1NKzzCSCez27t3ffi5wu/vO6ipS65z2HR880rzvI+RMPosYrRt37CBAACxcEGgi2GvqsqdAgMBAAECgYBn+kLcTA8Tf8DEmOYsF00oUL6t4T92S0ExR0aBUdb68os8Lh4nAzbtd1kLmGwAwGvHlNBF50o5ew4V6mjMzHoaVSnEdzHfvp7rWhMHSVkZrAy8kYHvaBKRzy53pDrT9nrwEGXhEH84gHt21s8k0IunYniRhcy1JAR/qunmtK3/+QJBANP7I18IJBUXwnP3hT1B/VA4y+6IO0yKKh4979x677zef6xUjp0+Eo2rzlnmzhh8n0M8FizsQoY8SSqAiC+54n8CQQDJYDCpUg5auwZ7JCXZNUp9G5he8CRU/IpTzZ/SsLwFwQWQXS/92aw3Yi7w6nRyHK70ynEoGYmrMvH2XVQqRwzjAkBwnqIiIO8aja7vcXdGHkL4VdRye8oVVeoeewVt+5pWm1b1NU83Vv0Aa2CL/sVL2Rm73LZjU3ncWzb776FqybT9AkEAr928wbLy7FQXteV81HRRCG/YI1mQMVSYLdYPdT56BoBrLofjyMuYNoodurLXXW0lco12Q3ICJCyK2gDp5z5DxwJBAMznBp0bjTDwNrr+KaGOcvVCYHPIHBUTC6pcYjVGIoBMlshqBI/RPKqHsuCXISd00gRM5ofgHQFMYYGHgKqGZto='

// 解密
export function decrypt(txt) {
    const encryptor = new JSEncrypt()
    encryptor.setPrivateKey(privateKey) // 设置私钥
    return encryptor.decrypt(txt) // 对数据进行解密
}
