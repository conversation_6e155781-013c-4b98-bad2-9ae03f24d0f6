<template>
    <div class="first_step">
        <div class="header">
            <i class="el-icon-warning"></i>
            <span>为保证全部门店预设素材生效,请提前一天上传或更新素材</span>
            <!-- <img src="@/assets/icons/warning.svg" alt=""> -->
        </div>
        <div class="center">
            <p class="center_title">添加预设待机素材</p>
            <div class="center_message">
                <p>应急画面会在设备<span> 设备画面异常时 </span>进行播放</p>
                <p>为保证应急画面能在设备上正常播放</p>
                <p>请确保图片小于 <span> 4M</span>,分辨率低于 <span> 1920*1080 </span> 或 <span> 1080*1920</span> </p>
                <p>上传图片仅支持 <span> jpg、jpeg、png</span>格式</p>
            </div>
        </div>
        <div class="upload">
            <div class="upload_style">
                <div class="upload_left">
                    <div class="title"> <img :src="EmergencyCrosswiseImage" style="width:20px" alt=""> 横屏应急画面</div>
                    <div class="imgs">
                        <div class="block">
                            <el-image :src="EmergencyHImage" style="width: 100%;height: 100%;"
                                v-if="EmergencyHImage"></el-image>
                            <el-image v-else>
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture"></i>
                                </div>
                            </el-image>
                        </div>
                    </div>
                    <div class="event"> <el-button @click="changeStandbyImage('cross')">替换画面</el-button> </div>
                </div>
                <div class="upload_right">
                    <div class="title"> <img :src="EmergencyVerticalImage" style="width:20px" alt=""> 竖屏应急画面</div>
                    <div class="imgs">
                        <div class="block">
                            <el-image :src="EmergencyVImage" style="width: 100%;height: 100%;"
                                v-if="EmergencyVImage"></el-image>
                            <el-image v-else>
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture"></i>
                                </div>
                            </el-image>
                        </div>
                    </div>
                    <div class="event"> <el-button @click="changeStandbyImage('vertical')">替换画面</el-button> </div>
                </div>
            </div>
        </div>

        <el-dialog title="更换应急画面" :visible.sync="dialogStandbyVisible" width="60%" :before-close="handleClose">
            <div class="image_center">
                <!-- <pictureResources></pictureResources> -->
                <picture-resources v-if="dialogStandbyVisible" @checkedList='checkedList' @setImgPreview='setImgPreview'
                    ref="picture"></picture-resources>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogStandbyVisible = false">取 消</el-button>
                <el-button type="primary" @click="settingStandbyImage()">确认设置</el-button>
            </div>
        </el-dialog>
        <!-- 预览mask -->
        <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
            @closePreviewMask='closePreviewMask'>
        </previsualization>
    </div>
</template>

<script>
import { get_emergency_image, set_emergency_image } from "@/api/emergency/emergency"
import pictureResources from '@/components/ContentCenter/pictureResources'
import previsualization from "@/components/communal/previsualization";

export default {
    data() {
        return {
            EmergencyCrosswiseImage: require('@/assets/icons/circle.png'),
            EmergencyVerticalImage: require('@/assets/icons/circle.png'),
            dialogStandbyVisible: false,
            ImagePageSize: 10,
            offset: 0,
            imageList: [],
            StandbySort: '',
            isPreviewMaskShow: false,
            PreviewSrc: '',
            PreviewType: '',
            seleceImage: [],
            StandById: '',
            EmergencyHImage: '',
            EmergencyVImage: ''
        }
    },
    methods: {
        getEmergemCyImage() {
            let parmas = {
                act: 'urgentplay'
            }
            get_emergency_image(parmas).then(res => {
                console.log(res);
                this.StandById = res['data'][0]['id']
                this.EmergencyHImage = res['data'][0]['urgent_play_thumbh']
                this.EmergencyVImage = res['data'][0]['urgent_play_thumbv'] ? res['data'][0]['urgent_play_thumbv'] : ''
            })
        },
        changeStandbyImage(sort) {
            this.StandbySort = sort;
            this.dialogStandbyVisible = true;
            setTimeout(() => {
                console.log(this.$refs.picture, ' this.$refs.picture');
                this.$refs.picture.isContent = true;
            }, 100);
        },
        checkedList(image) {
            this.seleceImage = image;
        },
        settingStandbyImage() {
            let params = {
                setid: this.StandById,
                // src_urgent_playh: "", //横屏图片的key
                // src_urgent_playv: "", //竖屏图片的key
                // urgent_play_thumbh: "", //横屏
                // urgent_play_thumbv: "",  //竖屏
            }
            console.log(this.seleceImage);
            if (this.seleceImage.length == 0) {
                this.$message.warning('请选择应急图片')
            } else {
                if (this.StandbySort == 'cross') {
                    params.src_urgent_playh = this.seleceImage[0].file_key
                    params.urgent_play_thumbh = this.seleceImage[0].photo_url
                } else {
                    params.src_urgent_playv = this.seleceImage[0].file_key
                    params.urgent_play_thumbv = this.seleceImage[0].photo_url
                }
                set_emergency_image(params).then(res => {
                    console.log(res, 'res');
                    if (res.rst == 'ok') {
                        this.$message.success('设置成功')
                        this.dialogStandbyVisible = false;
                        this.getEmergemCyImage()
                    }
                })
            }
        },
        // 预览
        setImgPreview(val) {
            this.PreviewType = "image";
            this.PreviewSrc = val;
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = "";
        },
    },
    created() {
        this.getEmergemCyImage()
    },
    components: {
        pictureResources,
        previsualization
    }
}
</script>

<style lang="scss" scoped>
.first_step {
    background-color: #f8f7f7;

    .header {
        height: 40px;
        background-color: #f9efe2;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 20px;

        i {
            color: #ff9900 !important;
            font-size: 20px;
        }

        span {
            padding-left: 10px;
            color: rgba(0, 0, 0, 0.65);
        }
    }

    .center {
        padding: 0 20px;

        .center_title {
            color: rgba(80, 80, 80, 1);
            font-size: 16px;
            margin-top: 18px;
            font-weight: bold;
        }

        .center_message {
            height: 300px;
            width: 100%;
            background-color: var(--background-color);
            border-radius: 20px;
            margin-top: 11px;
            text-align: center;
            padding-top: 20px;

            p {
                color: #fff;
                line-height: 2;
                font-size: 17px;

                span {
                    color: #fed431;
                }
            }
        }
    }

    .upload {
        padding: 0 20px;

        .upload_style {
            width: 100%;
            display: flex;
            justify-content: space-around;
            position: relative;
            top: -100px;

            .upload_left {
                width: 45%;
                background-color: #fff;
                border-radius: 10px;
                padding: 20px 40px;

                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;
                z-index: 2;

                .imgs {
                    width: 400px;
                    height: 200px;
                    margin: 30px auto;
                }

                &::before {
                    content: '';
                    width: 95%;
                    height: 15px;
                    left: 2.5%;
                    background-color: #fff;
                    position: absolute;
                    top: -15px;
                    border-radius: 12px 12px 0 0;
                    z-index: 0;
                }
            }

            .upload_right {
                width: 45%;
                background-color: #fff;
                border-radius: 10px;
                padding: 20px 40px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                position: relative;

                .imgs {
                    width: 200px;
                    height: 400px;
                    margin: 30px auto;


                }

                &::before {
                    content: '';
                    width: 95%;
                    height: 15px;
                    left: 2.5%;
                    background-color: #fff;
                    position: absolute;
                    top: -15px;
                    border-radius: 12px 12px 0 0;
                    z-index: 0;
                }
            }

            .title {
                font-size: 22px;
                display: flex;
                align-items: center;

                img {
                    margin-right: 10px;
                }
            }

            .block {
                background-color: #ccc;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                i {
                    font-size: 40px;
                    color: #a2a2a2;
                }
            }

            .event {
                margin: 0 auto;

                .el-button {
                    background-color: var(--background-color);
                    color: #fff;
                    border: 0;
                    border-radius: 5px;
                    font-weight: bold;
                }
            }
        }
    }
}

::v-deep .lattice_header {
    display: none !important;
}

::v-deep .btns {
    display: none !important;
}
</style>