<template>
  <div class="navbar">
    <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" /> -->
    <div
      class="topTitle color-white"
      style="
        font-size: 14px;
        height: 50px;
        line-height: 72px;
        margin-left: 20px;
        float: left;
      "
    >
      <img
        style="width: 180px; height: 43px; border-radius: 6px"
        src="../../assets/images/navLogo1.png"
        alt
      />
    </div>
    <div class="right-menu">
      <!-- 消息通知 -->
      <!-- <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        ref="infoDrop"
        trigger="click"
        style="margin-right:0"
      >
        <div class="avatar-wrapper">
          <el-badge  class="badge" :is-dot="withNotice" >
            <i class="el-icon-bell" style="font-size:22px"></i>
          </el-badge>
        </div>
        <el-dropdown-menu slot="dropdown" style="padding: 0">
          <div class="info-drop-cont">
            <div class="drop-cont-head">
              <span>消息通知</span>
            </div>
            <ul class="drop-content">
              <li v-for="item in notificationList" :key="item.date" class="drop-content-list">
                <p>{{ item.value }}</p>
                <p>{{ item.date }}</p>
              </li>
            </ul>
          </div>
        </el-dropdown-menu>
      </el-dropdown>-->

      <div style="display: flex;align-items:center">
        <!-- <a
          href="https://grayds.instwall.com/djstatics/gmplay/ShowRoom/startbucks/index.html#/newBasics"
          style="display: flex;align-items:center"
          target="_blank"
          title="千店千面系统"
        >
          <img
            src="../../assets/images/qiandian_logo.png"
            style="width: 50px;margin-right:20px"
            @click="openNew('qiandian')"
            alt
          />
        </a>
        <a
          href="https://grayds.instwall.com/cateringControl/index.html#/index"
          style="display: flex;align-items:center"
          target="_blank"
          title="快饮"
        >
          <img src="../../assets/images/showtop.png" style="width: 30px;margin-right:20px" alt />
        </a>


        <img
          title="智图系统"        
          src="../../assets/images/smart_draw_logo.png"
          style="width: 120px;margin-right:20px"
          @click="openNew('smartDraw')"
          alt
        /> -->
      </div>

      <!-- 全屏缩放 -->
      <template v-if="device !== 'mobile'">
        <!-- <search id="header-search" class="right-menu-item" /> -->

        <!-- <el-tooltip content="项目文档" effect="dark" placement="bottom">
          <Doc class="right-menu-item hover-effect" />
        </el-tooltip>-->

        <el-tooltip content="全屏缩放" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip>

        <!-- <el-tooltip content="布局设置" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>-->
      </template>

      <!-- 显示/切换品牌 -->
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        ref="messageDrop"
        trigger="click"
        @visible-change="visibleChange"
      >
        <div class="avatar-wrapper">
          <img
            :src="user.avatarName ? baseApi + '/avatar/' + user.avatarName : Avatar
            "
            class="user-avatar"
          />
          <span class="nick_name">{{ nick_name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" style="padding: 0">
          <!-- <span style="display:block;" @click="show = true">
            <el-dropdown-item>
              布局设置
            </el-dropdown-item>
          </span>
          <router-link to="/user/center">
            <el-dropdown-item>
              个人中心
            </el-dropdown-item>
          </router-link>-->
          <div class="drop_list">
            <div class="current_store">
              <img :src="user_logo.logo ? user_logo.logo : Avatar" style="width: 47px" alt />
              <!-- <div class="qiehuan" @click="changeStore">
                <span>切换品牌</span>
                <i class="el-icon-sort"></i>
              </div>-->
            </div>
            <div
              class="store_list"
              v-if="branch_list.length > 0"
              v-loading="checkLoading"
              element-loading-text="切换中"
              element-loading-spinner="el-icon-loading"
            >
              <div
                class="every_store"
                v-for="item in branch_list"
                :key="item.admin_uid"
                @click="checkStore(item)"
                :class="item.checked ? 'store_checked' : ''"
              >
                <img :src="item.logo" alt />
              </div>
            </div>
            <div class="exit_wrap">
              <span @click="open" class="exit">退出登录</span>
            </div>
          </div>

          <!-- <span style="display:block;" @click="open">
            <el-dropdown-item divided>
              
            </el-dropdown-item>
          </span>-->
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import store from "@/store";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Doc from "@/components/Doc";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import Avatar from "@/assets/images/avatar.png";
import { buildMenus } from "@/api/system/menu";
import Cookies from "js-cookie";
import { sso_for_smarttpl_os } from "@/api/sso/sso";
import { filterAsyncRouter } from "@/store/modules/permission";
export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    Doc
  },
  data() {
    return {
      Avatar: Avatar,
      dialogVisible: false,
      activeStore: {},
      checkLoading: false,
      withNotice: true,
      notificationList: []
    };
  },
  computed: {
    ...mapGetters([
      "sidebar",
      "device",
      "user",
      "baseApi",
      "branch_list",
      "user_logo",
      "nick_name"
    ]),
    show: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val
        });
      }
    }
  },
  created() {
    this.getNotiveInfo();
  },
  methods: {
    openNew(title) {
      if (title == "smartDraw") {
        sso_for_smarttpl_os({}).then(res => {
          if (res.rst == "ok") {
            let { status, rt_service, sessionid } = res.data[0];
            if (status == 0) {
              this.$message.warning(
                "本账号未开通智图系统，若要使用该功能，请联系管理员开通"
              );
            } else {
              const origin = window.location.origin;
              // const origin = 'https://smp.instwall.com';
              const url = `${rt_service}?sekkkey=${sessionid}&cb_service=${origin}/cateringControl/index.html#/index`;
              // console.log(url);
              window.open(url, "_blank");
            }
          } else {
            this.$message.error(res.error_msg);
          }
        });
      } else if (title == "qiandian") {
        // window.open("https://grayds.instwall.com/SmartMenu/index#/newBasics");
      }
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    open() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.logout();
      });
    },
    logout() {
      console.log(Cookies, "Cookies");
      this.$store.dispatch("LogOut").then(() => {
        this.delCookie();
        location.reload();
      });
    },
    delCookie() {
      var cookies = document.cookie.split(";");
      for (var i = 0; i < cookies.length; i++) {
        var cookie = cookies[i];
        var eqPos = cookie.indexOf("=");
        var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie =
          name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
      }
      if (cookies.length > 0) {
        for (var i = 0; i < cookies.length; i++) {
          var cookie = cookies[i];
          var eqPos = cookie.indexOf("=");
          var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
          var domain = location.host.substr(location.host.indexOf("."));
          document.cookie =
            name +
            "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=" +
            domain;
          console.log(document.cookie, "document.cookie");
        }
      }
    },

    // dropdown出现/隐藏时触发
    visibleChange(val) {
      if (!val) {
        this.branch_list.forEach(item => {
          item.checked = false;
          this.activeStore = {};
        });
      }
    },
    // 点击店铺切换选中状态
    checkStore(val) {
      this.checkLoading = true;
      this.branch_list.forEach(item => {
        item.checked = false;
      });
      val.checked = true;
      this.changeStore(val);
      // this.activeStore = val;
    },
    // 切换品牌事件
    changeStore(val) {
      // if (Object.keys(this.activeStore).length == 0) {
      //   this.$message.error("请先选择切换的品牌");
      //   return;
      // }
      const params = {
        group_id: val.group_id,
        admin_uid: val.admin_uid,
        branchcode: val.branchcode
      };
      this.$store.dispatch("switchBranch", params).then(() => {
        //
        if (this.$route.path == "/dashboard") {
          console.log(buildMenus);
          store.dispatch("GetInfo").then(() => {
            this.$store.state.user.canRefresh = true;
          });
          buildMenus().then(res => {
            console.log(res, "asdoijjjj");
            const sdata = JSON.parse(JSON.stringify(res["data"][0]));
            const rdata = JSON.parse(JSON.stringify(res["data"][0]));
            const sidebarRoutes = filterAsyncRouter(sdata);
            const rewriteRoutes = filterAsyncRouter(rdata, false, true);
            // rewriteRoutes.push({ path: "*", redirect: "/404", hidden: true });
            rewriteRoutes.push({
              path: "*",
              redirect: "/dashboard",
              hidden: true
            });
            store.dispatch("GenerateRoutes", rewriteRoutes).then(() => {
              // 存储路由
              this.$router.addRoutes(rewriteRoutes); // 动态添加可访问路由表
              this.$router.push("/dashboard");
            });
            store.dispatch("SetSidebarRouters", sidebarRoutes);
          });
        } else {
          this.$router.push("/dashboard");
        }
        this.$refs.messageDrop.hide();
        this.checkLoading = false;
      });
    },
    getNotiveInfo() {
      this.notificationList = [
        {
          value:
            "<EMAIL> 驳回了您上传的资源(资源名称)  请检查上传内容细节",
          date: "2022-12-34 12:10 "
        },
        {
          value:
            "<EMAIL> 驳回了您上传的资源(资源名称)  请检查上传内容细节",
          date: "2022-12-34 12:10 "
        },
        {
          value:
            "<EMAIL> 驳回了您上传的资源(资源名称)  请检查上传内容细节",
          date: "2022-12-34 12:10 "
        }
      ];
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: fixed;
  z-index: 2001;
  width: 100%;
  // background: #fff;
  background-color: var(--background-color);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    align-items: center;
    display: flex;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 10px;
        }

        .nick_name {
          font-size: 16px;
          margin-left: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.drop_list {
  font-size: 14px;
  width: 340px;

  .current_store {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    border-bottom: 1px solid rgba(239, 239, 239, 1);
    color: var(--background-color);
    padding: 0 15px;

    .qiehuan {
      cursor: pointer;

      i {
        transform: rotate(90deg);
      }
    }

    .qiehuan:hover {
      color: #eb8181;
    }
  }

  .store_list {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid rgba(239, 239, 239, 1);
    padding: 14px 15px;

    .every_store {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 47px;
      height: 47px;
      margin-right: 30px;
      margin-bottom: 16px;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid rgba(204, 204, 204, 1);
      // box-sizing: border-box;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
  }
}

.store_checked {
  border: 1px solid var(--background-color) !important;
}

.exit_wrap {
  width: 100%;
  height: 52px;
  line-height: 52px;
  text-align: center;
}

.exit {
  display: inline-block;
  border: 1px solid var(--background-color);
  font-size: 14px !important;
  cursor: pointer;
  width: 107px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 25px;
  color: var(--background-color);
  background: #fff;
}

.exit:hover {
  border: 1px solid #eb8181;
  color: #eb8181;
}

.badge {
  height: 20px;
  display: flex;
  align-items: center;
}

.info-drop-cont {
  width: 348px;
  height: 207px;
  font-size: 12px;

  .drop-cont-head {
    width: 100%;
    height: 42px;
    line-height: 42px;
    padding: 0 9px;
    box-sizing: border-box;
    border-bottom: 1px solid #e5e5e5;

    span {
      display: inline-block;
      height: 100%;
      position: relative;
      margin-left: 10px;
    }

    span::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      border-radius: 40px;
      background: var(--background-color);
    }
  }

  .drop-content {
    width: 100%;
    height: calc(100% - 42px);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 15px 0 0 0;

    .drop-content-list {
      list-style: none;
      border-bottom: 1px dashed #eeeeee;
      padding: 0 10px 0 20px;
      box-sizing: border-box;
      margin-bottom: 16px;

      p {
        width: 100%;
        color: rgba(56, 56, 56, 1);
      }

      p:nth-of-type(2) {
        text-align: right;
        margin: 8px 0;
      }
    }
  }
}
</style>
