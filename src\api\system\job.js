import request from '@/utils/request'
import { post, uploadFile } from '@/utils/request'
export function getAllJob(id) {
  const params = {
      'classModel': 'GroupTreeJob',
      'sort': '', // 非必要，排序规则，id,desc
      'createdAtS': '', // 非必要，查询创建时间开始日期
      'createdAtE': '', // 非必要，查询创建时间结束日期
      'page': 0, // 起始页码,
      'size': 100 // 每页数据量
      // "blurry": 1
  }
  return post('dmb/api/json', params, 'get_adm_datas')
}

export function add(data) {
  return request({
    url: 'api/job',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/job',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/job',
    method: 'put',
    data
  })
}

export default { add, edit, del }
