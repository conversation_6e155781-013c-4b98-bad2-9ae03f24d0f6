<template>
  <div class="tag">
    <div class="header">
      <!-- <h4>门店时段设置</h4>
      <el-button
        type="danger"
        @click.stop="addTags"
        style="margin-right: 20px; background: var(--text-color)"
        >新增类型</el-button
      > -->
      <el-form label-position="left" :inline="true" :model="dayPartFrom" class="elForm">
        <el-form-item label="时段类型设置" label-width="100px">
          <el-select v-model="dayPartFrom.setTimeType" placeholder="请选择时段类型" @change="selectChange">
            <el-option :label="item.label" :value="item.value" v-for="(item,index) in selectList" :key="index">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div style="display:flex;align-items:center">
        <div style="width:345px;display:flex;align-items:center">
          新增类型：<el-input v-model="inputAdd" placeholder="请输入类型名称" style="width:200px;height:33px;height:33px;">
          </el-input>
          <el-button type="danger" @click.stop="handleAdd"
            style="display:inline-block;background:var(--text-color);border-radius:4px">确认</el-button>
        </div>
        <el-button type="danger" style="margin-right:20px;background:var(--text-color);border-radius:4px"
          @click="addPart">新增时段</el-button>
      </div>
    </div>

    <div class="line"></div>
    <!-- 抽屉 -->
    <el-drawer title="新增时段" :visible.sync="drawer" :direction="direction" :show-close='false'
      custom-class='addTagsStyle' :before-close="handleClose">
      <ul class="lists">
        <li>
          时段名称:<el-input class="inputPart" placeholder="请输入时段名称" v-model="addList.name"></el-input>
        </li>
        <li>
          开始时间:
          <el-time-picker v-model="addList.starttime" format="HH:mm" style="margin-left:10px" value-format="HH:mm"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:59'
            }" placeholder="任意时间点">
          </el-time-picker>
        </li>
        <li>
          结束时间:
          <el-time-picker v-model="addList.endtime" format="HH:mm" style="margin-left:10px" value-format="HH:mm"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:59'
            }" placeholder="任意时间点">
          </el-time-picker>
        </li>
      </ul>
      <div class="btns dialog_btns">
        <el-button @click="drawer=false">取消</el-button>
        <el-button style="background:#24b17d;color:var(--btn-color);" @click="savePart">保存</el-button>
      </div>
    </el-drawer>
    <div class="content">
      <el-form label-position="left" :inline="true" :model="dayPartFrom" class="demo-form-inline">
        <!-- <el-form-item label="时段设置：" label-width="100px">
            <el-select v-model="dayPartFrom.setTimeType" placeholder="请选择时段">
              <el-option label="时间类型1" value="1"></el-option>
              <el-option label="时间类型2" value="2"></el-option>
            </el-select>
          </el-form-item> -->
        <div class="selectTime">
          <div v-for="(item, index) in dayPartFrom.mealTimeInterval" :key="index" class="select_children">
            <img src="../../assets/img/daypart.svg"
              style="width:22px;height:22px; vertical-align: middle;margin-right:10px" />
            <el-form-item :label="item.name" label-width="100px">
              <el-time-picker v-model="item.starttime" :picker-options="{
                selectableRange: '00:00:00 - 23:59:59',
              }" format="HH:mm" :placeholder="'请选择' + item.name" value-format="HH:mm">
              </el-time-picker>
              <span> 至 </span>
              <el-time-picker v-model="item.endtime" :picker-options="{
                selectableRange: '00:00:00 - 23:59:59',
              }" format="HH:mm" :placeholder="'请选择' + item.name" value-format="HH:mm">
              </el-time-picker>
            </el-form-item>
            <!-- <i class="el-icon-circle-plus addDayPart"></i> -->
            <i class="el-icon-remove-outline deleteDayPart" @click="deleteDayPart(item,index)"></i>
          </div>
        </div>
      </el-form>
    </div>
    <!-- <el-empty description="描述文字" v-show="!this.dayPartFrom.setTimeType"></el-empty> -->
    <!-- <el-dialog
      :visible.sync="tagsDialogVisible"
      :title="btnState + '标签'"
      :close-on-click-modal="false"
      custom-class="batch_tags_dialog"
      :before-close="handleClose"
      width="520px"
    >
      <div style="display: flex; align-items: center; padding: 10px 0 40px">
        <p>新增时段类型:</p>
        <el-input
          placeholder="请输入时段名称"
          v-model="newInputTags"
          maxlength="40"
          show-word-limit
          style="display: inline-block; width: 230px; margin-left: 20px"
        >
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </span>
    </el-dialog> -->
    <div class="bottom">
      <div class="saveBtn">
        <el-button class="cancel" @click="cancel">取消</el-button>
        <el-button type="primary" class="saveDayPary" @click="saveDayPart">保存</el-button>
      </div>
    </div>
    <!-- 保存新时段类型 -->
    <div class="dialogBox">
      <el-dialog style="text-align:center" title="" :visible.sync="isDialog" width="30%" custom-class='isSavePart'
        :show-close="false" :before-close="handleClose">
        <span style="text-align:center;font-weight:600">是否保存为新时段类型</span>
        <div slot="footer" class="dialog-footer" style="text-align:center">
          <el-button @click="isDialog = false" style="height:26px;line-height:6px;margin:0 10px;">不保存</el-button>
          <el-button type="primary" @click="cateSave" style="height:26px;line-height:6px;margin:0 10px;">保存</el-button>
        </div>
        <div class=" close" @click="isDialog=false">
          <i class="el-icon-close"></i>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { get_adm_datas,add_screen_type_tags} from "@/api/system/label";
import { param } from '../../utils';
export default {
  components: {},
  data() {
    return {
      dayPartFrom: {
        setTimeType: "",
        mealTimeInterval: [],
      },
      selectList:[],
      dayPartList:[],
      addList:{
          name:"",
          starttime:"",
          endtime:""
        }
      ,
      replaceAddList:[],
      value1: new Date(2016, 9, 10, 18, 40),
      drawer: false,
      direction: 'rtl',
      inputAdd:"",
      isDialog:false,
      tagsDialogVisible: false, //批量标签弹框
      btnState: "新增", // 弹框状态 : 新增 / 编辑
      tempList:{
        label:"",
        value:""
      },
      arrList:[],
      pageSize:10,
      myArr:[]
      // newInputTags: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getData();
  },
  mounted() {},
  methods: {
    /*
     * 保存事件
     */
    saveDayPart() {
      console.log(this.dayPartFrom);
      this.isDialog = true;
    },
    cancel(){
      this.$message.success("取消成功");
      this.getData()
    },
    // 新增时段
    addPart(){
      if(!this.dayPartFrom.setTimeType){
        this.$message.warning('请先选择时段或新增类型')
        return
      }
      this.drawer = true;
      // this.clearContent();
    },
    /*
     * 删除事件
     */
    deleteDayPart(item,index) {
        let arr = []
        this.dayPartFrom.mealTimeInterval.splice(index,1);
        this.$message.info("删除该数据，请点击保存按钮进行保存");
      //   this.myArr = this.dayPartFrom.mealTimeInterval
      //   console.log(this.myArr,'myArr');
      //   for(let i=0;i<this.myArr.length;i++){
      //     arr.push({
      //           starttime:this.myArr[i].starttime,
      //           endtime:this.myArr[i].endtime,
      //           name:this.myArr[i].name,
      //     })
      // }
      // console.log(item,'item');
      // console.log(arr,'arr');
      // console.log( this.dayPartFrom.mealTimeInterval,' this.dayPartFrom.mealTimeInterval');
      // const params = {
      //   act:"edit",
      //   classModel:"SysDayPart",
      //   id:item.id,
      //   group_name:item.daypartname,
      //   attr_list: arr
      // }
      // console.log(params,'paramsdeleteDayPart');
      // add_screen_type_tags(params).then(res=>{
      //   console.log(res);
      //   if(res.rst == "ok"){
      //     this.$message.success("删除成功,请点击保存按钮进行保存");
      //     // this.getData()
      //   }
      // })
    },
    // 批量标签弹框取消按钮关闭弹框
    handleClose() {
      // this.newInputTags = "";
      this.tagsDialogVisible = false;
    },
    // addTags() {
    //   this.tagsDialogVisible = true;
    // },
      savePart(){
              //  const params = {
              // act:"add",
              // classModel: "SysDayPart", 
              // group_name:this.addList.name,
              // attr_list: this.addList
              // };
              if(this.addList.name==""){
                this.$message.warning("请输入要新增的名称");
              }else if(this.addList.starttime=="" || this.addList.endtime==""){
                 this.$message.warning("请选择开始或结束时间");
              }else{
                this.dayPartFrom.mealTimeInterval.push({
                  name:this.addList.name,
                  starttime:this.addList.starttime,
                  endtime:this.addList.endtime,
                })
                this.drawer =false;
                this.$message.success("新增成功");
                let timer = null;
                timer = setTimeout(()=>{
                this.$message.info("点击保存按钮保存该时段类型");
                },1000)
                this.clearContent()
                // this.dayPartFrom.mealTimeInterval.push(this.addList)
                // // this.dayPartFrom.mealTimeInterval = [...this.dayPartFrom.mealTimeInterval,...this.addList]
                // console.log( this.dayPartFrom.mealTimeInterval,878787);
                
                // this.addList.name = ""
                return
                  add_screen_type_tags(params).then((res) => {
                  if(res.data[0].code==200){
                     this.dayPartFrom.mealTimeInterval.push({
                      ...this.addLists[0]
                    });
                    console.log( this.dayPartFrom.mealTimeInterval," this.dayPartFrom.mealTimeInterval");
                    this.drawer =false;
                    this.$message.success("新增成功");
                  }
                });
              }
            
      
      },
      // 保存新时段类型
            cateSave(){
                 // this.isDialog = false;
                if(this.selectList[0].value==''){
                  return ;
                }
                console.log(this.dayPartList[0].group,'mewwerfwe');
                let arr = [];
                for(let i=0;i<this.dayPartFrom.mealTimeInterval.length;i++){
                }
                     // this.tempList.value = this.dayPartFrom.mealTimeInterval
               arr =  this.dayPartFrom.mealTimeInterval.map((item)=>{
                  return {starttime:item.starttime,endtime:item.endtime,name:item.name}
                })
                // this.tempList.value = this.dayPartFrom.mealTimeInterval[0].group_name;
                // this.tempList.label = this.dayPartFrom.mealTimeInterval[0].group_name;
                // this.tempList.value = this.dayPartFrom.setTimeType;
                // this.tempList.label = this.dayPartFrom.setTimeType;
                console.log(this.dayPartFrom.setTimeType,'dayPartFrom.setTimeType');
                console.log(arr,'arr');
                const params = {
                    act:"add",
                    classModel: "SysDayPart", 
                    group_name:this.dayPartFrom.setTimeType,
                    attr_list: arr
                    };
                console.log(params,'inputAddparams');
                // console.log(this.dayPartList,'sfdsfs');
                 add_screen_type_tags(params).then((res) => {
                    console.log(res,'保存策略的接口返回值');
                  if(res.rst=='ok'){
                      this.$message.success("保存成功");
                      this.isDialog = false;
                      // this.getData();
                    // alert("接口链接成功",this.selectList);
                    //  console.log(this.selectList[0].value,this.dayPartFrom.mealTimeInterval,'this.selectList[0].value');
                  }
                });
              },
                // 确定
                handleAdd() {
                  // console.log(this.selectList,'1@@@@');
              
                  // this.selectList.forEach((item)=>{
                  //   if(item.label==this.inputAdd){
                  //     this.$message.warning("已有该时段类型,请重新添加您要新增的时段类型");
                  //   }
                  // });
                  // console.log(this.inputAdd,'@@@1@');
                    if(this.inputAdd==''){
                      this.$message.warning('请输入新增的类型');
                    }
                    else{
                      //给下拉框的option加入数据 绑定label和value
                      this.selectList.push({
                          label:this.inputAdd,
                          value:this.inputAdd
                      })
                      this.dayPartList.push({
                          name:this.inputAdd,
                          list:[]
                      })
                      this.$message.success('时段类型新增成功');
                       let timer = null;
                      timer = setTimeout(()=>{
                      this.$message.info("请选择时段类型，且新增时段并进行保存");
                      },1000)
                      this.inputAdd = '';
                    }
    },
    clearContent(){
      this.addList.name = "";
      this.addList.starttime = "";
      this.addList.endtime = "";
    },
    // 获取数据
    getData() { 
      const params = {
            classModel: "SysDayPart",
            page: 0, //起始页码,
            size: this.pageSize, //每页数据量,
      };
      get_adm_datas(params).then((res) => {
        if(res.rst=='ok'){
          console.log(res,'daypart');
          res.data[0].content.forEach((item,idx)=>{
            // 获取到数据给select框的option  push数据
            this.selectList.push({
              label:item.group_name,
              value:item.group_name
            })
            // 将group_name和列表的数据push到dayPartList中
            this.dayPartList.push({
              name:item.group_name,
              list:item.attr_list
            })
            // console.log(this.dayPartList,'dayList');
          })
         for(let i=0;i<this.dayPartList.length;i++){
            if(this.dayPartList[0].name==this.dayPartList[i].name){
                  this.dayPartFrom.mealTimeInterval = this.dayPartList[i].list
                }
             }
        this.dayPartFrom.setTimeType = this.dayPartList[0].name;
        }
      });
    },
    selectChange(val){
      this.tempList.value = val;
      this.tempList.label = val;
      // 下拉框选择：循环dayPartList之中的数据
      for(let i in this.dayPartList){
        // name的值是否与下拉选中的值一样  等于则将list数组数据赋给dayPatFrom
        if(val == this.dayPartList[i]['name']){
          this.dayPartFrom.mealTimeInterval = this.dayPartList[i].list
          // console.log(this.dayPartList[i],'changeDaypart');
        }
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.dialog_btns {
  height: 50px;
  button {
    margin: 0 20px 0 0 !important;
  }
}
.tag {
  .header {
    margin: 17px 0 0px 25px;
    color: rgba(80, 80, 80, 1);
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .elForm{
      align-self: center;
      height: 33px;
    }
  }
  .line {
    margin-top: 20px;
    width: 100%;
    border-bottom: 1px solid rgba(229, 229, 229, 1);
  }
  .content {
    margin: 30px 25px;
    .selectTime {
      margin-top: 30px;
      display: flex;
      flex-wrap: wrap;
      .select_children {
        width: 50%;
        span {
          padding: 0 7px;
        }
        i {
          cursor: pointer;
          padding-left: 10px;
        }
        .addDayPart {
          color: red;
          font-size: 33px;
        }
        .deleteDayPart {
          color: var(--background-color);
          font-size: 25px;
        }
      }
    }
  }
  .bottom {
    width: 100%;
    height: 100px;
    border-top: 1px solid rgba(229, 229, 229, 1);
    position: fixed;
    bottom: 0;
    .saveBtn {
      position: absolute;
      right: 300px;
      display: flex;
      height: 100%;
      align-items: center;
      .cancel {
        border-radius: 6px;
        border: rgba(166, 166, 166, 1) solid 1px;
        font-size: 14px;
        color: rgba(128, 128, 128, 1);
        font-weight: bold;
        width: 88px;
        height: 32px;
        text-align: center;
      }
      .saveDayPary {
        width: 88px;
        height: 32px;
        left: 1323px;
        top: 837px;
        color: rgba(80, 80, 80, 1);
        background-color: rgba(39, 177, 126,1);
        border-radius: 6px;
        font-size: 14px;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        font-weight: bold;
        margin-left: 18px;
      }
    }
  }
}
.dialogBox{
  height: 300px;
}
 .isSavePart{
   position: relative;
    .el-dialog__footer{
      text-align: center !important;
    }
    .el-dialog__body{
      text-align: center !important;
    }
  }
  .close{
   width: 35px;
   height: 35px;
    position: absolute;
    right: -12px;
    color: #fff;
    top: -12px;
    background: #000;
    z-index: 20;
    border-radius: 50%;
    cursor: pointer;
    .el-icon-close{
      display: block;
      font-size: 20px;
      margin: 7px;
    }
  }
.btns{
  position: absolute;
  bottom: 20px;
  height: 30px;
  right: 20px;
}
.addTagsStyle{
  position: relative;
  .lists{
    margin:  10px;
    display: flex;
    flex-direction: column;
    list-style: none;
    li{
      margin: 20px 13px;
      .inputPart{
        width:200px;
        margin-left:10px;
        border:none;
        border-left:3px solid rgba(166, 166, 166, 1);
         ::v-deep .el-input__inner{
           border: none !important;
           height: 20px;
           line-height: 20px;
         }
      }
    }
  }
}
::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 160px;
}
.batch_tags_dialog {
  margin-top: 0px !important;
  border-radius: 4px !important;
}
.batch_tags_dialog .el-dialog__body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.batch_tags_dialog .el-dialog__header .el-dialog__title {
  font-size: 16px !important;
  font-weight: bold !important;
}
.batch_tags_dialog .el-tabs__header {
  margin: 0 !important;
}
.batch_tags_dialog .el-tabs__nav-wrap::after {
  height: 1px !important;
}
.tag .batch_tags_dialog .el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
  background-color: #fff !important;
}
.batch_tags_dialog .el-button--primary {
  background: rgba(108, 178, 255, 1);
}
.tag .el-tabs__header {
  margin-bottom: 0 !important;
  width: 100% !important;
}
.tag .el-tabs__nav-scroll {
  padding-left: 50px;
}
.tag .el-dialog__wrapper {
  position: absolute;
  margin: auto;
  // height: 200px;
}
</style>
<style>
</style>
