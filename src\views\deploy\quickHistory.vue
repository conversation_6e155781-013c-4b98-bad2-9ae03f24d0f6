<template>
  <div class="deploy" v-loading="loading">
    <div class="top">
      <el-input placeholder="发布名称" style="width: 150px" prefix-icon="el-icon-search" v-model="queryList.name"
        clearable></el-input>
      <el-select v-model="queryList.platform" placeholder="投放类型" clearable>
        <el-option v-for="item in option1" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-input placeholder="门店编号" style="width: 150px" prefix-icon="el-icon-search" v-model="queryList.store_code"
        clearable></el-input>
      <!--      市场运营-->
      <!-- <el-select v-model="optionValue1" placeholder="运营市场">
                <el-option v-for="item in option1" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
      </el-select>-->
      <!--  投放状态-->
      <!-- <el-select v-model="optionValue3" placeholder="投放状态">
                <el-option v-for="item in option3" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
      </el-select>-->
      <!--  推送状态-->
      <!-- <el-select v-model="optionValue4" placeholder="推送状态">
                <el-option v-for="item in option4" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
      </el-select>-->
      <!-- <div class="queryTime">
                <span>时段筛查</span>
                <div class="block">
                    <el-date-picker v-model="value1" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
      </div>-->
      <!--         搜索按钮-->
      <el-button type="primary" @click="search">搜索</el-button>
    </div>
    <!--      表格-->
    <el-table :height="autoHeight.height" :data="tableData"
      :header-cell-style="{ background: 'var( --text-color-light);' }" style="width: 100%">
      <el-table-column prop="platform" label="发布类型" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.platform == 1">指定店铺投放</div>
          <div v-else-if="scope.row.platform == 2">餐牌组投放</div>
          <div v-else-if="scope.row.platform == 3">普通投放</div>
          <div v-else-if="scope.row.platform == 4">联屏投放</div>
        </template>
      </el-table-column>
      <el-table-column prop="name" align="center" label="发布名称"></el-table-column>
      <el-table-column prop="pre_pubshopcnt" align="center" label="门店数"></el-table-column>
      <el-table-column prop="pre_pubscrcnt" align="center" label="屏幕数"></el-table-column>
      <!-- <el-table-column prop="receive_complete_time" label="推送完成时间" align="center">
      </el-table-column>-->
      <el-table-column prop="pre_pub_sche_cnt" align="center" label="内容数量"></el-table-column>
      <el-table-column label="播放时段" align="center" width="180">
        <template slot-scope="scope">
          {{ scope.row.start_time }}
          <br />~
          <br />
          {{ scope.row.end_time }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="pub_complete_time" label="屏幕类型" align="center">
      </el-table-column>-->
      <el-table-column prop="pub_ok_sche_cnt" label="内容下发成功数" align="center"></el-table-column>
      <el-table-column prop="status" label="下发状态" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.status == '下发完成'" class="on_line">{{ scope.row.status }}</div>
          <div v-else class="off_line">{{ scope.row.status }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="complete_tm" label="下发完成时间" align="center"></el-table-column>
      <el-table-column prop="pub_flow_status" label="审核状态" align="center"
        v-if="$store.state.user.PUSCS_AUDIT_OPEN == 1"></el-table-column>
      <!-- <el-table-column align="center" label="投放进度" v-show="0">
        <template slot-scope="scope">
          <div>
            <el-popover placement="top" width="300" trigger="hover" popper-class="my-popover">
              <p @click="toDetail" class="look">查看详情</p> 
              <div class="progress">
                <p class="tou">投放进度
                  <span v-show="scope.row.progressStatus == '发布成功'" style="color:rgba(41, 160, 90, 1)">{{
                      scope.row.progressStatus
                  }}</span>
                  <span v-show="scope.row.progressStatus == '发布失败'" style="color:var(--background-color)">{{
                      scope.row.progressStatus
                  }}</span>
                </p>
                v-show="scope.row.progressStatus !== '发布成功'" 
                <el-progress width="100" v-show="scope.row.progressStatus !== '发布成功'" :percentage="scope.row.progressValue">
                </el-progress>
                <p class="look" @click="toDetail"  v-show="scope.row.progressStatus !== '发布成功'">去处理</p>
              </div>
              <span slot="reference" class="cursor">
                  {{scope.row.progressValue}} %
                </span>
            </el-popover>
          </div>
        </template>
      </el-table-column>-->

      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <div>
            <el-button @click="HandlePause(scope.row)" :class="scope.row.active ? 'off' : 'active'" size="small"> {{
              scope.row.active ? '暂停' : '播放' }}
            </el-button>
          </div>
          <div style="display:flex;" class="event">
            <div>
              <el-button @click="handleClick(scope.row)" type="text" size="small"
                v-if="checkPer(['dm.cf.pubhis.view'])">查看</el-button>
            </div>
            <div v-if="$store.state.user.PUSCS_AUDIT_OPEN == 1">
              <el-button @click="handleEdit(scope.row)" type="text" size="small"
                v-if="scope.row.pub_flow_status_code != 3">编辑</el-button>
            </div>
            <div v-else>
              <el-button @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
            </div>
            <div>
              <el-button @click="handleDelete(scope.row)" type="text" size="small">删除</el-button>
            </div>
            <div>
              <el-button @click="copyQuick(scope.row)" type="text" size="small">复制</el-button>
            </div>
          </div>

          <!-- 暂时隐藏功能 -->
          <!-- <el-button type="text" size="small">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="发布名称" width="20%" :visible.sync="dialogFormVisible">
      <el-form :model="copyParams">
        <el-form-item label="发布名称" :label-width="formLabelWidth">
          <el-input v-model="copyParams.new_bt_name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="画面方向" v-if="planForm == 2" :label-width="formLabelWidth">
          <el-select v-model="copyParams.storeplaylayout" clearable style="width: 100%;">
            <el-option v-for="(item, index) in storeplaylayoutSelectList" :key="'layout_op_' + index"
              :value="item.value" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false; copyParams.new_bt_name = ''">取 消</el-button>
        <el-button type="primary" @click="requestCopyNewBt">确 定</el-button>
      </div>
    </el-dialog>
    <!--      分页-->
    <div class="block">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="currentPage3" :page-size="queryList.page_size" layout="total,sizes,prev,pager, next, jumper"
        :total="totalNum" :page-sizes="[10, 20, 50, 100]" background></el-pagination>
    </div>
  </div>
</template>

<script>
// import { get_historical_data } from "@/api/historical/historical"
import {
  get_btpub_perform_list,
  check_for_delete_btpub,
  delete_btpub,
  copy_btpub
} from "@/api/quickHistory/quickHistory";
import { publish_area, publish_launch, update_remodified_cf, api_vs_cf_notify } from "@/api/contentdeploy/contentdeploy"
import { datas_filter_cond } from "@/api/commonInterface";

export default {
  name: "TimeFrame",
  data() {
    return {
      inputValue: "",
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      value1: "",
      value2: "",
      //  表格
      tableData: [],
      //  市场运营
      option1: [
        // {
        //   value: 1,
        //   label: "指定门店投放"
        // },
        {
          value: 2,
          label: "餐牌组投放"
        },
        {
          value: 3,
          label: "普通投放"
        },
        {
          value: 4,
          label: "联屏投放"
        }
      ],
      optionValue1: "",
      //  播放模式
      option2: [
        {
          value: "选项1",
          label: "播放模式"
        },
        {
          value: "选项2",
          label: "全播"
        },
        {
          value: "选项3",
          label: "轮播"
        },
        {
          value: "选项4",
          label: "插播"
        },
        {
          value: "选项5",
          label: "叠加"
        }
      ],
      optionValue2: "",
      //  未投放
      option3: [
        {
          value: "选项1",
          label: "未投放"
        },
        {
          value: "选项2",
          label: "投放中"
        },
        {
          value: "选项3",
          label: "使用中"
        },
        {
          value: "选项4",
          label: "已过期"
        }
      ],
      optionValue3: "",
      //  播放类型
      option4: [
        {
          value: "选项1",
          label: "单屏内容"
        },
        {
          value: "选项2",
          label: "联屏内容"
        },
        {
          value: "选项3",
          label: "浮层内容"
        }
      ],
      optionValue4: "",
      //  表格区域高度
      autoHeight: {
        height: "",
        heightNum: ""
      },
      // 获取表单条件
      // 进度条
      progressValue: 0,
      total: "",
      loading: true,
      queryList: {
        name: "",
        platform: "",
        page_num: 0,
        page_size: 10,
        store_code: ''
      },
      copyParams: {
        btpub_id: '',
        new_bt_name: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '80px',
      storeplaylayoutSelectList: [],
      planForm: ''
    };
  },
  methods: {
    handleClick(row) {
      console.log(row);
      //  跳转页面
      // this.$router.push({
      //   path: "/deploy/HisEdit",
      // });

      this.$router.push({
        path: "/deploy/mlpubdetail",
        query: { btpub_id: row.btpub_id, platform: row.platform }
      });
      sessionStorage.setItem(
        "historySearchFrom",
        JSON.stringify(this.queryList)
      );
    },
    handleEdit(row) {
      console.log(row, "编辑");


      if (row.platform == 3) {
        console.log(this.$store.state.user.PUSCS_AUDIT_OPEN);
        if (this.$store.state.user.PUSCS_AUDIT_OPEN == 1) {
          this.$router.push({
            // path: "/deploy/deployEdit",
            path: "/deploy/editHistory",
            query: {
              btplan_id: row.btplan_id,
              platform: row.platform,
              btpub_id: row.btpub_id,
              showInput: false,
              status: row.status,
              status_code: row.status_code,
              pub_flow_status_code: row.pub_flow_status_code,
              active: row.action
            }
          });
        } else {
          this.$router.push({
            // path: "/deploy/deployEdit",
            path: "/deploy/editHistory",
            query: {
              btplan_id: row.btplan_id,
              platform: row.platform,
              btpub_id: row.btpub_id,
              showInput: false,
              status: row.status,
              status_code: row.status_code,
              active: row.action
            }
          });
        }
      } else {
        if (this.$store.state.user.PUSCS_AUDIT_OPEN == 1) {
          this.$router.push({
            path: "/deploy/editHistory",
            // path: "/deploy/deployEdit",
            query: {
              btplan_id: row.btplan_id,
              platform: row.platform,
              btpub_id: row.btpub_id,
              status: row.status,
              status_code: row.status_code,
              pub_flow_status_code: row.pub_flow_status_code,
              active: row.action
            }
          });
        } else {
          this.$router.push({
            path: "/deploy/editHistory",
            // path: "/deploy/deployEdit",
            query: {
              btplan_id: row.btplan_id,
              platform: row.platform,
              btpub_id: row.btpub_id,
              status: row.status,
              status_code: row.status_code,
              active: row.action
            }
          });
        }
      }
    },
    // 获取历史投放
    getHistoricalData() {
      console.log(this.queryList);
      const params = this.queryList;
      get_btpub_perform_list(params).then(res => {
        console.log(res);
        this.totalNum = res.data[0].totalElements;
        this.tableData = res.data[0].content;
        this.total = res.data[0].totalElements;
        this.loading = false;
        sessionStorage.removeItem("historySearchFrom");
        console.log(this.tableData);
      });
    },
    getOnlineClass(val) {
      switch (val) {
        case 1:
          return "off_line";
        case 2:
          return "error";
        case 3:
          return "on_line";
      }
    },
    getOnlineStatus(val) {
      switch (val) {
        case 1:
          return "接收中";
        case 2:
          return "接收异常";
        case 3:
          return "接收完成";
      }
    },
    getPubRecordStatus(val) {
      switch (val) {
        case 0:
          return "处理数据";
        case 1:
          return "数据异常";
        case 2:
          return "分发中";
        case 3:
          return "分发成功";
      }
    },
    //  列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 200 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    handleSizeChange(val) {
      this.loading = true;
      this.queryList.page_size = val;
      this.getHistoricalData();
    },
    handleCurrentChange(val) {
      this.loading = true;
      this.queryList.page_num = val - 1;
      this.getHistoricalData();
    },
    // 跳转详情
    toDetail() {
      this.$router.push({
        path: "/deploy/pubFail"
      });
    },
    // 复制
    copyQuick(row) {
      console.log(row, 'row');
      this.planForm = row.platform
      this.dialogFormVisible = true;
      this.copyParams.btpub_id = row.btpub_id;
      console.log(row, 'row');
    },
    HandlePause(row) {
      this.$confirm(`此操作将${row.active ? '暂停' : '播放'}该发布, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let parmas = {
          btpub_id: row.btpub_id,
          action: row.active ? 'pause' : 'pub',
        }
        publish_launch(parmas).then(res => {
          if (res.rst == 'ok') {
            this.$message.success('操作成功')
            this.getHistoricalData()
          } else {
            this.$message.error(res.error_msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },
    // 删除
    handleDelete(item) {
      check_for_delete_btpub({ btpub_id: item.btpub_id }).then(res => {
        console.log(res, "res");
        if (res.rst == "ok") {
          this.$confirm("此操作将删除此专辑, 是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              delete_btpub({ btpub_id: item.btpub_id }).then(res => {
                if (res.rst == "ok") {
                  this.loading = true;
                  this.$message({
                    type: "success",
                    message: "删除成功!"
                  });
                  this.getHistoricalData();
                } else {
                  this.$message.error("删除失败");
                }
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除"
              });
            });
        } else {
          this.$message.error("该专辑不可删除");
        }
      });
    },
    search() {
      this.queryList.store_code = this.queryList.store_code.toUpperCase()
      this.queryList.page_num = 0;
      this.loading = true;
      this.getHistoricalData();
    },
    requestCopyNewBt() {
      if (this.copyParams.new_bt_name) {
        copy_btpub(this.copyParams).then(res => {
          console.log(res, 'res');
          if (res.rst == 'ok') {
            this.$message.success('复制成功')
            this.dialogFormVisible = false;
            this.copyParams.new_bt_name = '';
            this.getHistoricalData()
          } else {
            this.$message.error(res.error_msg)
          }
        })
      } else {

      }
    },
    getSelectDataList() {
      const params = {
        classModel: "ContentPub" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        console.log(res["data"], "res");
        let layout_data = []
        res.data[0].forEach(item => {
          switch (item.filterkey) {
            case 'storeplaylayout':
              layout_data = item;
              break;
          }
        });

        this.filterPlayLayoutList(layout_data);
      });
    },
    filterPlayLayoutList(data) {
      const list = []
      data.options.forEach(item => {
        const obj = {
          value: item[0],
          name: item[1]
        };
        list.push(obj);
      })
      this.storeplaylayoutSelectList = list;
      this.storeplaylayoutSelectList.forEach(item=>{
        if(item.name == '从左到右'){
          item.name = '默认'
        }else{
          item.name = '反向'
        }
      })
    },
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getSelectDataList();
    if (sessionStorage.getItem("historySearchFrom")) {
      let from = JSON.parse(sessionStorage.getItem("historySearchFrom"));
      this.queryList = from;
      console.log(this.queryList);
      this.currentPage3 = this.queryList.page_num + 1;
      this.search();
    } else {
      this.getHistoricalData();
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  }
};
</script>

<style scoped lang='scss'>
.deploy {
  padding: 0 20px 0 13px;
  width: 100%;
}

.top {
  display: flex;
  height: 40px;
  align-items: center;
  padding: 27px 0 30px 0;
  margin-bottom: 15px;
}

.top>div {
  margin-right: 8px;
}

.top>.queryTime {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  text-align: left;
}

.top>.queryTime {
  display: flex;
  align-items: center;
}

.top>.queryTime>span {
  width: 62px;
}

::v-deep .el-select {
  width: 150px;
}

/*日历*/
.el-input__inner {
  width: 243px;
  height: 32px;
  margin-right: 16px;
}

::v-deep .el-range-separator {
  line-height: 23px;
}

::v-deep .el-range__icon {
  line-height: 17px !important;
}

::v-deep .el-date-editor .el-range-separator {
  width: 6%;
  padding: 0;
}

.top>button {
  width: 88px;
  height: 32px;
  line-height: 9px;
}

/*表格*/
::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-table__body {
  table-layout: auto;
}

/*分页*/
.el-pagination {
  height: 0.32rem;
  margin-top: 35px;
  text-align: right;
}

.look {
  cursor: pointer;
  color: rgba(42, 130, 228, 1);
  width: 60px;
  background-color: rgba(227, 241, 255, 1);
  height: 26px;
  border-radius: 3px;
  line-height: 26px;
  text-align: center;
  position: absolute;
  right: 4%;
  top: 50%;
}

.tou {
  margin-bottom: 10px;
}

.my-popover {
  padding: 20px;
}

.progress {
  position: relative;
  padding: 10px;
}

::v-deep .el-progress-bar {
  width: 75% !important;
}

.off_line {
  color: var(--text-color);
}

.on_line {
  color: rgba(23, 159, 78, 1);
}

.error {
  color: rgba(56, 56, 56, 1);
}

.event {
  flex-wrap: wrap;

  div {
    margin-left: 10px;
  }
}

.event button {
  width: 50%;
  margin-left: 0 !important;
}

.off {
  background-color: #0c6d48 !important;
  color: #fff;
}

.active {
  background-color: #2fb07e !important;
  color: #fff;
}
</style>