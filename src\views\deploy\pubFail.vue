<template>
  <div class='pub'>
    <div class='top'>
      <span class='el-icon-back' @click.stop='goBack'></span>
      历史投放/315上海上新
    </div>
    <el-table
      :data="tableData"
      :header-cell-style="{background:'var( --text-color-light);'}"
      style="width: 100%">
      <el-table-column
        prop="screenId"
        label="屏幕ID"
        align='center'
        width="150">
      </el-table-column>
      <el-table-column
        prop="storeId"
        label="门店编号"
        align='center'
        width="180">
      </el-table-column>
      <el-table-column
        prop="it"
        align='center'
        label="IT市场">
      </el-table-column>
      <el-table-column
        prop="screenState"
        align='center'
        label="屏幕状态">
        <template slot-scope="scope">
          <span class='offline'>离线</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="devType"
        align='center'
        label="设备类型">
      </el-table-column>
      <el-table-column
        prop="devId"
        align='center'
        label="设备编号">
        <template slot-scope="scope">
          <el-button @click="handleClick(scope.row)" type="text" size="small" class='connect'>1</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="devLabel"
        align='center'
        label="设备标签">
        <template slot-scope="scope">
          <img src='../../assets/img/home_img/little_label.svg' class='label'>
          菜单屏
        </template>
      </el-table-column>
      <el-table-column
        prop="pubTime"
        align='center'
        label="投放时间">
      </el-table-column>
    </el-table>
    <div class='bottom'>
      <div class='btns'>
        <div class='excelAll'>Excel导出全部</div>
        <div class='excelCurrent'>Excel导出当前页</div>
      </div>
      <!--      分页-->
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="5"
          layout="prev, pager, next, jumper"
          :total="25">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PubFail',
  data(){
    return{
      //  表格
      tableData: [{
        screenId: '201573',
        storeId: 'SH1098七宝国际店',
        it: '上海市场',
        screenState: '离线',
        devType: 'Android3399',
        devId: '1',
        devLabel:'菜单屏',
        pubTime: '2022-03-17 15:00',
      }, {
          screenId: '201573',
          storeId: 'SH1098七宝国际店',
          it: '上海市场',
          screenState: '离线',
          devType: 'Android3399',
          devId: '1',
          devLabel:'菜单屏',
          pubTime: '2022-03-17 15:00',
        },
        {
          screenId: '201573',
          storeId: 'SH1098七宝国际店',
          it: '上海市场',
          screenState: '离线',
          devType: 'Android3399',
          devId: '1',
          devLabel:'菜单屏',
          pubTime: '2022-03-17 15:00',
        },
        {
          screenId: '201573',
          storeId: 'SH1098七宝国际店',
          it: '上海市场',
          screenState: '离线',
          devType: 'Android3399',
          devId: '1',
          devLabel:'菜单屏',
          pubTime: '2022-03-17 15:00',
        }]
      }
    },
  methods: {
    /*返回*/
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.pub{
  width: 100%;
  background-color: #fff;
  padding: 0 20px 20px;
}
.top{
  height: 60px;
  line-height: 60px;
  color: rgba(91, 91, 91, 1);
  font-weight: bold;
}
.top>.el-icon-back{
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}
.bottom{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 50px;
}
.bottom>.btns>div{
  width: 106px;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  text-align: center;
  border: 1px solid var(--background-color);
  color: var(--background-color);
}
.bottom>.btns>.excelAll{
  border-right: none;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.bottom>.btns>.excelCurrent{
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.connect{
  width: 28px;
  height: 18px;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
  line-height: 2px;
}
.offline{
  color: var(--text-color);
}
.label{
  width: 23px;
  height: 23px;
  vertical-align: middle;
}
/*分页*/
.block{
  width: 4.85rem;
  height: 0.32rem;
  position: absolute;
  right: 0.22rem;
}
.el-pager{
  font-weight: 500;
}
.el-pagination{
  height: 0.32rem;
}

::v-deep .number{
  margin: 0 0.05rem;
  border: 1px solid #cfcfcf;
}
::v-deep .has-gutter{
  color: rgba(91, 91, 91, 1);
  font-weight: bold;
}
::v-deep .el-table__body-wrapper{
  color: rgba(91, 91, 91, 1);
}
</style>
