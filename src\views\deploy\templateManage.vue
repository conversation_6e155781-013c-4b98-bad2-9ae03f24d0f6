<template>
    <div class="templateManage">
        <div class="template_list">
            <div class="search">
                <el-input v-model="template_name" placeholder="请输入模板名称/ID"></el-input>
                <el-button type="primary" class="search_btn" @click="search()">搜索</el-button>
            </div>
            <div class="tem_list">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="横屏" name="first">
                    </el-tab-pane>
                    <el-tab-pane label="竖屏" name="second"></el-tab-pane>
                </el-tabs>
                <div class="template_body" v-loading='tem_loading' v-infinite-scroll="loadMore">
                    <div v-for="item in templateList" :key="item.id" class="template_item"
                        @click="selectTemplate(item)">
                        <div class="template_info" :class="currentTemplate == item.id ? 'active' : ''">
                            <img :src="item.thumb_url" class="template_img" alt="">
                            <div class="info">
                                <p>模板名称:{{ item.name }}</p>
                                <p>模板ID:{{ item.id }}</p>
                                <p>上传日期:{{ item.tpl_author }}</p>
                            </div>
                        </div>
                    </div>
                    <!-- 加载更多状态提示 -->
                    <div v-if="isLoadingMore" class="loading-more">
                        <i class="el-icon-loading"></i>
                        <span>加载中...</span>
                    </div>
                    <div v-else-if="!hasMore && templateList.length > 0" class="no-more">
                        <span>没有更多数据了</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="template_prevew">
            <div class="search">
                <el-input v-model="store_name" style="width:200px" placeholder="请输入门店名称/编号"></el-input>
                <el-input v-model="price_date" style="width:200px;margin-left: 10px;" placeholder="请选择价格生效日期"
                    clearable></el-input>
                <el-button type="primary" class="search_btn" @click="start_animation()">搜索</el-button>
            </div>
            <iframe :src="iframe_url" frameborder="0" ref='iframe'></iframe>
        </div>
    </div>
</template>

<script>
import { search_tpl_list } from "@/api/template/index"
export default {
    data() {
        return {
            template_name: '',
            activeName: 'first',
            searchFrom: {
                offset: 0,
                limit: 10,
                name: '',
                desc: '',
                v_or_h: 0
            },
            templateList: [],
            currentTemplate: '',
            tem_loading: true,
            isLoadingMore: false,  // 是否正在加载更多
            hasMore: true,         // 是否还有更多数据
            loadMoreTimer: null,    // 防抖定时器
            iframe_url: ''
        }
    },
    methods: {
        getTemplateList() {
            // 重置分页参数
            this.searchFrom.offset = 0;
            this.hasMore = true;
            this.isLoadingMore = false;

            search_tpl_list(this.searchFrom).then(res => {
                console.log(res, 'res')
                const data = res['data'][0]['data'];
                this.templateList = data;
                this.tem_loading = false;

                // 判断是否还有更多数据
                if (data.length < this.searchFrom.limit) {
                    this.hasMore = false;
                    console.log('初始加载：没有更多数据了');
                }
            })
        },
        selectTemplate(item) {
            this.currentTemplate = item.id;
            this.iframe_url = item.anim_url;
            console.log(this.$refs.iframe.contentWindow, 'iframe')
            console.log(this.$refs.iframe.contentWindow.init_video, 'iframe')
        },

        search() {
            // 设置搜索参数
            this.searchFrom.name = this.template_name;
            // 重新获取数据
            this.getTemplateList();
        },

        handleClick(tab) {
            // 切换tab时重置数据
            this.searchFrom.v_or_h = tab.name === 'first' ? 0 : 1;
            this.getTemplateList();
        },
        loadMore() {
            // 如果正在加载或没有更多数据，直接返回
            if (this.isLoadingMore || !this.hasMore) {
                return;
            }

            // 清除之前的定时器
            if (this.loadMoreTimer) {
                clearTimeout(this.loadMoreTimer);
            }

            // 防抖处理，300ms内只执行一次
            this.loadMoreTimer = setTimeout(() => {
                this.loadMoreData();
            }, 300);
        },

        async loadMoreData() {
            console.log('开始加载更多数据，当前已有数据:', this.templateList.length);
            this.isLoadingMore = true;

            try {
                // 正确计算偏移量：当前已有数据的数量
                this.searchFrom.offset = this.templateList.length;
                console.log('请求参数 offset:', this.searchFrom.offset, 'limit:', this.searchFrom.limit);

                const res = await search_tpl_list(this.searchFrom);
                const newData = res['data'][0]['data'];
                console.log('返回新数据数量:', newData ? newData.length : 0);

                if (newData && newData.length > 0) {
                    // 追加新数据到现有列表
                    this.templateList = [...this.templateList, ...newData];
                    console.log('累加后总数据量:', this.templateList.length);

                    // 如果返回的数据少于请求的数量，说明没有更多数据了
                    if (newData.length < this.searchFrom.limit) {
                        this.hasMore = false;
                        console.log('没有更多数据了，返回数据量:', newData.length, '小于请求数量:', this.searchFrom.limit);
                    }
                } else {
                    // 没有返回数据，说明已经到底了
                    this.hasMore = false;
                    console.log('没有返回数据，已到底');
                }
            } catch (error) {
                console.error('加载更多数据失败:', error);
            } finally {
                this.isLoadingMore = false;
            }
        },
        start_animation(){
            this.$refs.iframe.contentWindow.init_video()
        }
    },
    mounted() {
        this.getTemplateList()
    },
    created() {
    },
    beforeDestroy() {
        // 清理定时器
        if (this.loadMoreTimer) {
            clearTimeout(this.loadMoreTimer);
        }
    }
}
</script>

<style lang="scss" scoped>
.templateManage {
    width: 100%;
    height: calc(100vh - 55px);
    display: flex;

    .search {
        padding: 10px 20px;
        display: flex;

        .search_btn {
            margin-left: 10px;
        }
    }

    .template_list {
        flex: 1;
        background-color: #a8a8aa;
        box-sizing: border-box;


        .tem_list {
            height: calc(100% - 55px);
            overflow: auto;

            .template_body {
                width: 100%;

                .template_item {
                    width: 100%;
                    margin-bottom: 20px;
                    box-sizing: border-box;
                    padding: 0 15px;
                    cursor: pointer;

                    .template_info {
                        position: relative;
                    }

                    .template_img {
                        width: 100%;
                    }

                    .info {
                        width: 100%;
                        background-color: rgba($color: #000000, $alpha: 0.5);
                        color: #fff;
                        padding: 10px;
                        position: absolute;
                        bottom: 0;

                        p {
                            margin-top: 5px;
                            font-size: 16px;
                            font-weight: bold;
                        }
                    }
                }
            }
        }

    }

    .template_prevew {
        flex: 4.5;

        iframe {
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            padding: 20px;
        }
    }
}

::v-deep .el-tabs__nav-wrap::after {
    display: none;
}

::v-deep .el-tabs__nav-wrap {
    display: flex;
    justify-content: center;
}

::v-deep .el-tabs__nav {
    width: auto !important;
    display: flex;
    justify-content: center;
}

::v-deep .el-tabs__item {
    padding: 0 20px !important;
    text-align: center;
    color: #fff !important;
    font-weight: bold;
}

/* 隐藏默认的下划线 */
::v-deep .el-tabs__active-bar {
    display: none !important;
}

/* 为激活的tab项添加自定义下划线 */
::v-deep .el-tabs__item.is-active {
    position: relative;
    background-color: #286042;
}

::v-deep .el-tabs__item.is-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background-color: #fff;
    border-radius: 1px;
}

.template_body {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.active {
    border: 8px solid #286042;
}

.loading-more,
.no-more {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 14px;
}

.loading-more i {
    margin-right: 8px;
    animation: rotating 2s linear infinite;
}

@keyframes rotating {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>