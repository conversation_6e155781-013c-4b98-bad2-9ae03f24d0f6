<template>
    <div class="templateManage">
        <div class="template_list">
            <div class="search">
                <el-input v-model="template_name" placeholder="请输入模板名称/ID"></el-input>
                <el-button type="primary" class="search_btn" @click="search()">搜索</el-button>
            </div>
            <div class="tem_list">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="用户管理" name="first">用户管理</el-tab-pane>
                    <el-tab-pane label="配置管理" name="second">配置管理</el-tab-pane>
                    <el-tab-pane label="角色管理" name="third">角色管理</el-tab-pane>
                    <el-tab-pane label="定时任务补偿" name="fourth">定时任务补偿</el-tab-pane>
                </el-tabs>

            </div>
        </div>
        <div class="template_prevew">

        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            template_name: ''
        }
    }
}
</script>

<style lang="scss" scoped>
.templateManage {
    width: 100%;
    height: 100vh;
    display: flex;

    .template_list {
        flex: 1;
        background-color: #a8a8aa;
        box-sizing: border-box;

        .search {
            padding: 10px 20px;
            display: flex;
        }

        .search_btn {
            margin-left: 10px;
        }
    }

    .template_prevew {
        flex: 4.5
    }
}
</style>