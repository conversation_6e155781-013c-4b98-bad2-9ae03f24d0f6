<template>
    <div class="templateManage">
        <div class="template_list">
            <div class="search">
                <el-input v-model="template_name" placeholder="请输入模板名称/ID"></el-input>
                <el-button type="primary" class="search_btn" @click="search()">搜索</el-button>
            </div>
            <div class="tem_list">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="横屏" name="first">
                        <div class="template_body" v-loading='tem_loading' v-infinite-scroll="loadMore">
                            <div v-for="item in templateList" :key="item.id" class="template_item"
                                @click="selectTemplate(item)">
                                <div class="template_info" :class="currentTemplate == item.id ? 'active' : ''">
                                    <img :src="item.thumb_url" class="template_img" alt="">
                                    <div class="info">
                                        <p>模板名称:{{ item.name }}</p>
                                        <p>模板ID:{{ item.id }}</p>
                                        <p>上传日期:{{ item.tpl_author }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="竖屏" name="second">竖屏</el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <div class="template_prevew">

        </div>
    </div>
</template>

<script>
import { search_tpl_list } from "@/api/template/index"
export default {
    data() {
        return {
            template_name: '',
            activeName: 'first',
            searchFrom: {
                offset: 0,
                limit: 10,
                name: '',
                desc: '',
                v_or_h: 0
            },
            templateList: [],
            currentTemplate: '',
            tem_loading: true
        }
    },
    methods: {
        getTemplateList() {
            search_tpl_list(this.searchFrom).then(res => {
                console.log(res, 'res')
                this.templateList = res['data'][0]['data'];
                this.tem_loading = false;
            })
        },
        selectTemplate(item) {
            this.currentTemplate = item.id
        },
        loadMore(){
            
        }
    },
    mounted() {
        this.getTemplateList()
    },
    created() {
    }
}
</script>

<style lang="scss" scoped>
.templateManage {
    width: 100%;
    height: calc(100vh - 55px);
    display: flex;

    .template_list {
        flex: 1;
        background-color: #a8a8aa;
        box-sizing: border-box;

        .search {
            padding: 10px 20px;
            display: flex;

            .search_btn {
                margin-left: 10px;
            }
        }

        .tem_list {
            height: calc(100% - 55px);
            overflow: auto;

            .template_body {
                width: 100%;

                .template_item {
                    width: 100%;
                    margin-bottom: 20px;
                    box-sizing: border-box;
                    padding: 0 15px;
                    cursor: pointer;

                    .template_info {
                        position: relative;
                    }

                    .template_img {
                        width: 100%;
                    }

                    .info {
                        width: 100%;
                        background-color: rgba($color: #000000, $alpha: 0.5);
                        color: #fff;
                        padding: 10px;
                        position: absolute;
                        bottom: 0;

                        p {
                            margin-top: 5px;
                            font-size: 16px;
                            font-weight: bold;
                        }
                    }
                }
            }
        }

    }

    .template_prevew {
        flex: 4.5
    }
}

::v-deep .el-tabs__nav-wrap::after {
    display: none;
}

::v-deep .el-tabs__nav-wrap {
    display: flex;
    justify-content: center;
}

::v-deep .el-tabs__nav {
    width: auto !important;
    display: flex;
    justify-content: center;
}

::v-deep .el-tabs__item {
    padding: 0 20px !important;
    text-align: center;
    color: #fff !important;
    font-weight: bold;
}

/* 隐藏默认的下划线 */
::v-deep .el-tabs__active-bar {
    display: none !important;
}

/* 为激活的tab项添加自定义下划线 */
::v-deep .el-tabs__item.is-active {
    position: relative;
    background-color: #286042;
}

::v-deep .el-tabs__item.is-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background-color: #fff;
    border-radius: 1px;
}

.template_body {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.active {
    border: 8px solid #286042;
}
</style>