<template>
  <div style="height: 78%; position: relative" class="qua">
    <div class="innerBox">
      <ul v-if="selectValue.direction=='竖'">
        <li v-for="(item, index) in lists" :key="index">
          <h4>{{ item.title }}</h4>
          <div v-for="(items, index) in Number(selectValue.firstNum)">
            <img :src="imgs2[index]" v-if="item.title != '工作日早餐' && imgs2[index]!=''" />
            <div class="add" @click.stop="newAdd" v-if="item.title != '工作日早餐'">
              <img src="../../assets/img/home_img/jia.svg" />
              <p>新增</p>
            </div>
            <div v-if="item.title == '工作日早餐'">
              <span>{{ index + 1 }}</span>
            </div>
          </div>
        </li>
      </ul>
      <ul v-if="selectValue.direction=='横'||selectValue.direction=='3横1竖'">
        <li
          v-for="(item, index) in lists"
          :key="index"
          :style="item.title == '工作日早餐'?'height: 60px':'height: 180px'"
        >
          <!-- <h4>{{ item.title }}{{ selectValue.direction }}</h4> -->
          <h4>{{ item.title }}</h4>
          <div
            v-for="(items, index) in Number(selectValue.firstNum)"
            style="width:350px"
            :style="item.title == '工作日早餐'?'height: 60px':'height: 180px'"
          >
            <img
              :src="imgs2[index]"
              :style="selectValue.direction=='3横1竖'?index==3?'width: 50px;height: 80px;margin:0 auto;':'':''"
              v-if="item.title != '工作日早餐' && imgs2[index]!=''"
            />
            <div class="add" @click.stop="newAdd" v-if="item.title != '工作日早餐' || imgs2[index]==''">
              <img src="../../assets/img/home_img/jia.svg" />
              <p>新增</p>
            </div>
            <div
              v-if="item.title == '工作日早餐'"
              style="display:flex;align-items:center;justify-content:center;height:100%;"
            >
              <span
                :style="selectValue.direction=='3横1竖'?index==3?'width: 18px;height: 28px;line-height:28px':'':''"
              >{{ index + 1 }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <!--    点击后出现-->
    <el-dialog title="新增内容" :visible.sync="showAlertBox" width="60%" :before-close="handleClose">
      <!-- <div class="top">
        <h3>新增内容</h3>
        <p @click.stop="showAlertBox = false">×</p>
      </div>-->
      <div class="threeParts">
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="图片" name="first">
            <div class="content">
              <div class="chunk">
                <div>
                  <img src="/img/home_img/breakfast.png" />
                  <!-- <div></div> -->
                  <el-checkbox v-model="isAllChecked"></el-checkbox>
                </div>
                <p>1920*1080</p>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="视频" name="second">
            <div class="content">视频管理</div>
          </el-tab-pane>
          <el-tab-pane label="H5" name="third">
            <div class="content">角色管理</div>
          </el-tab-pane>
        </el-tabs>
        <div class="selectAll">
          <el-select v-model="selectValue1" placeholder="请选择" class="select1">
            <el-option
              v-for="item in selectType1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-select v-model="selectValue2" placeholder="请选择" class="select1">
            <el-option
              v-for="item in selectType2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="view">查看</div>
      </div>
      <el-upload
        class="upload-demo"
        action="https://jsonplaceholder.typicode.com/posts/"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        multiple
        :limit="3"
        :on-exceed="handleExceed"
        :file-list="fileList"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <div class="btns dialog_btns">
        <div class="close" @click.stop="showAlertBox = false">取消</div>
        <div @click.stop="alertOk">确定</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "SetTypePlayer",
  props: ["toPlayer"],
  data() {
    return {
      showAlertBox: false,
      //  图片、视频、H5
      activeName: "first",
      //  新增内容弹出框的两个select
      selectType1: [
        {
          value: "选项1",
          label: "横屏1920*1080"
        },
        {
          value: "选项2",
          label: "竖屏1920*1080"
        },
        {
          value: "选项3",
          label: "其他"
        }
      ],
      selectValue1: "横屏1920*1080",
      selectType2: [
        {
          value: "选项1",
          label: "全国市场"
        },
        {
          value: "选项2",
          label: "上海市场"
        },
        {
          value: "选项3",
          label: "北京市场"
        },
        {
          value: "选项3",
          label: "成都市场"
        }
      ],
      lists: [
        {
          title: "工作日早餐"
        },
        {
          title: "早餐"
        },
        {
          title: "午餐"
        }
      ],
      imgs1: [
        "/img/home_img/breakfast.png",
        "/img/home_img/breakfast.png",
        "/img/home_img/breakfast.png",
        "/img/home_img/breakfast.png"
      ],
      imgs2: ["/img/home_img/breakfast.png", "/img/home_img/breakfast.png"],
      selectValue2: "全国市场",
      selectValue: {
        firstNum: "",
        direction: ""
      },
      isAllChecked: false
    };
  },

  methods: {
    //  新增
    newAdd() {
      this.showAlertBox = true;
    },
    //  上传文件系列
    handleRemove(file, fileList) {
      // console.log(file, fileList)
    },
    handlePreview(file) {
      // console.log(file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 3 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      );
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    /**
     * 取出小括号内的内容
     */
    getContent(text) {
      let start = text.indexOf("(");
      let end = text.indexOf(")");
      let result = text.substring(start + 1, end);
      return result;
    }
  },
  watch: {
    toPlayer(newValue, oldValue) {
      // console.log(this.toPlayer, 'player')
      // console.log(newValue, 'value')
      // 第一位渲染的数字
      this.selectValue.firstNum = newValue.charAt(0);
      this.selectValue.direction = this.getContent(newValue);
    }
  },
  created() {
    // this.selectValue.firstNum = this.toPlayer.charAt(0)
    // this.selectValue.direction = this.getContent(this.toPlayer)
  }
};
</script>
<style lang="sass" scoped>
.dialog_btns {
  height: 50px;
  button {
    margin: 0 20px 0 0 !important;
  }
}
</style>
<style scoped>
.innerBox {
  position: absolute;
  margin: 0 auto;
  left: 0;
  right: 0;
  width: 80%;
  margin-top: 20px;
}
.innerBox ul {
  width: 100%;
}
.innerBox ul li {
  border: 1px solid rgba(198, 198, 198, 1);
  display: flex;
  align-items: center;
  list-style: none;
  height: 270px;
}
.innerBox ul h4 {
  width: 10%;
  text-align: center;
}
.innerBox ul li > div {
  width: 28%;
  height: 100%;
  text-align: center;
  padding: 10px;
  position: relative;
  border-left: 1px solid rgba(198, 198, 198, 1);
  margin: 0 auto;
}
.innerBox ul li:first-of-type {
  height: 50px;
}
.innerBox ul li:first-of-type > div {
  line-height: 50px;
}
.innerBox ul li > div .add {
  width: 51px;
  height: 51px;
  color: rgba(80, 80, 80, 1);
  border-radius: 26px;
  font-size: 12px;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 0;
  bottom: 0;
  cursor: pointer;
}
.innerBox ul li > div div span {
  width: 28px;
  height: 18px;
  line-height: 18px;
  display: block;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
  margin: 5px auto;
}
.innerBox ul li > div > img {
  width: 100% !important;
  height: 100% !important;
  z-index: 20;
}
.innerBox ul li > div .add img {
  width: 20px;
  height: 20px;
  margin: 5px;
  z-index: 2;
}
/*点击后弹出*/
.alertBox {
  width: 10rem;
  height: 6.75rem;
  position: absolute;
  margin: auto;
  left: 0px;
  right: 0;
  top: -1rem;
  bottom: 0;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.16rem;
  font-size: 0.14rem;
  line-height: 150%;
  text-align: center;
  padding: 0 0.15rem 0.21rem 0.3rem;
  border: 1px solid #ddd;
  z-index: 20;
}
.alertBox > .top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.5rem;
  border-bottom: 0.01rem solid #e5e5e5;
}
.alertBox > .top > h3 {
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
}
.alertBox > .top > p {
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.3rem;
  color: #999999;
}
/*浮层选择*/
.floatAlert .selectAll {
  width: 290px;
}
.threeParts {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.threeParts > .el-tabs {
  width: 100%;
}
::v-deep .el-tabs__header {
  margin: 0;
}
.selectAll {
  width: 380px;
  position: absolute;
  right: 116px;
  display: flex;
  align-items: center;
  height: 40px;
}
/*!*下拉框内容*!*/
.select1 {
  margin: -10px 8px;
  width: 160px;
  height: 32px;
  border: 1px solid #ebebeb;
  color: rgba(80, 80, 80, 1);
}
::v-deep .el-input__inner {
  height: 32px;
}
.view {
  width: 88px;
  height: 32px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--background-color);
  border-radius: 5px;
  text-align: center;
  right: 20px;
  top: 3px;
  color: #fff;
  line-height: 32px;
  position: absolute;
  cursor: pointer;
}
.content {
  min-height: 400px;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  padding: 0.23rem 0 0 0.19rem;
}
.content > .chunk {
  width: 1.85rem;
  height: 1.85rem;
  position: relative;
  margin-right: 0.1rem;
  box-shadow: 0rem 0.03rem 0.03rem 0rem rgba(0, 0, 0, 0.12857142857142861);
}
.content > .chunk > div img {
  width: 100%;
  height: 100%;
}
.content > .chunk > div:hover + p {
  display: block;
}
.content > .chunk > div {
  width: 100%;
  height: 100%;
}
.content > .chunk > p {
  height: 0.26rem;
  width: 100%;
  line-height: 0.26rem;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(0, 0, 0, 0.35);
  text-align: center;
  position: absolute;
  opacity: 0.5;
  bottom: 0;
  display: none;
  background-color: #a29d86;
  font-size: 0.11rem;
}
.upload-demo {
  position: absolute;
  left: 30px;
  height: 32px;
}
.el-button {
  background-color: var(--background-color);
  border-color: var(--background-color);
  height: 40px;
}
/*取消*/
.btns {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.btns > div {
  width: 0.81rem;
  height: 0.36rem;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  border: rgba(166, 166, 166, 1) solid 1px;
  text-align: center;
  line-height: 0.36rem;
  color: #fff;
  font-weight: bold;
  cursor: pointer;
  background-color: rgba(108, 178, 255, 1);
}
.btns > .close {
  margin-right: 0.2rem;
  color: rgba(128, 128, 128, 1);
  background-color: #fff;
}
.el-checkbox {
  position: absolute;
  left: 10px;
  top: 10px;
}
::v-deep .el-dialog {
  border-radius: 10px;
}
.qua ::v-deep .el-dialog__body {
  padding: 10px 20px !important;
}
</style>
