<template>
    <div class="all_restaurants" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <!-- 搜索 -->
        <div class="all_restaurants_search flex">
            <div class="left_search flex flex-1">
                <el-input placeholder="请输入门店编号/名称" size="small" prefix-icon="el-icon-search"
                    v-model="queryList.shopName" style="width:186px;margin-right:12px"
                    @keyup.native.enter="getInfo"></el-input>
                <el-select v-model="queryList.operatingMarket" clearable placeholder="营运市场" size="small"
                    style="width:160px;margin-right:12px">
                    <el-option v-for="item in operatingMarketList.options" :key="item[0]" :label="item[1]"
                        :value="item[0]"></el-option>
                </el-select>
                <el-select v-model="queryList.storeType" clearable size="small" placeholder="餐厅类型"
                    style="width:160px;margin-right:12px">
                    <el-option v-for="item in storeTypeList" :key="item[0]" :label="item[1]"
                        :value="item[0]"></el-option>
                </el-select>
                <!-- <el-select v-model="queryList.itMarket" clearable placeholder="IT市场" size="small" style="width:160px;margin-right:12px">
                    <el-option v-for="item in itMarketList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-select v-model="queryList.allEquipment" clearable placeholder="全部设备" size="small" style="width:160px;margin-right:12px">
                    <el-option v-for="item in allEquipmentList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <div class="btn_style" style="width:88px" @click="handleSearch">搜索</div>
            </div>
            <div class="right_add">
                <div class="btn_style" style="width:106px" @click="addShop">+ 新增门店</div>
            </div>
        </div>
        <!-- 列表区 -->
        <div class="table_wrap" :style="{ height: autoHeight.height }">
            <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <el-table-column type="index" index="scope.index" label="No." width="70"></el-table-column>
                <el-table-column prop="opsmarketname" label="营运市场" width=""></el-table-column>
                <el-table-column prop="storecode" label="餐厅编号" width=""></el-table-column>
                <el-table-column prop="name" label="餐厅名称" width=""></el-table-column>
                <!-- <el-table-column prop="opsmarketname"  label="地区" width=""></el-table-column> -->
                <el-table-column prop="storetypename" label="类型" width=""></el-table-column>
                <el-table-column prop="jdetradezonename" label="商圈" width=""></el-table-column>
                <el-table-column prop="isrsc" label="是否统一管理" width="98">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.isrsc" disabled="true"></el-checkbox>
                        <!-- <img  src="../../assets/img/checked.png" alt="" style="width:16px;height:16px;">
                        <img v-else src="../../assets/img/nochecked.png" alt="" style="width:16px;height:16px;"> -->
                    </template>shanc
                </el-table-column>
                <!-- <el-table-column prop="isUnified"  label="甜品站" width=""> -->
                <!-- scope="scope" -->
                <!-- <template slot-scope="scope"> -->
                <!-- <img v-if="scope.row.isUnified" src="../../assets/img/checked.png" alt="" style="width:16px;height:16px;">
                        <img v-else src="../../assets/img/nochecked.png" alt="" style="width:16px;height:16px;"> -->
                <!-- <el-checkbox v-model="scope.row.isdessert" ></el-checkbox> -->
                <!-- </template> -->
                <!-- </el-table-column> -->
                <el-table-column prop="installdate" label="安装日期" width=""></el-table-column>
                <el-table-column prop="is24hour" label="24小时" width="">
                    <template slot-scope="scope">
                        <!-- <img v-if="scope.row.isUnified" src="../../assets/img/checked.png" alt="" style="width:16px;height:16px;">
                        <img v-else src="../../assets/img/nochecked.png" alt="" style="width:16px;height:16px;"> -->
                        <el-checkbox v-model="scope.row.is24hour" disabled="true"></el-checkbox>

                    </template>
                </el-table-column>
                <!-- <el-table-column prop="name"  label="今日定价" width=""></el-table-column> -->
                <el-table-column prop="displaycnt" label="设备数量" width=""></el-table-column>
                <!-- <el-table-column prop="breakfasttime"  label="早餐时间" width=""></el-table-column>
                <el-table-column prop="lunchtime"  label="午餐时间" width=""></el-table-column>
                <el-table-column prop="dinnertime"  label="晚餐时间" width=""></el-table-column> -->
                <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="handleEidt(scope.row)" type="text" style="color:#409eff"
                            size="small">编辑</el-button>
                        <el-button v-show="scope.row.delShow" @click.native.prevent="handleDelete(scope.row)"
                            type="text" style="color:red" size="small">删除</el-button>
                        <el-button v-show="!scope.row.delShow" @click.native.prevent="handleDelete(scope.row)"
                            type="text" style="color:red" size="small">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="all_restaurants_footer">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5' :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { getNewShopInfo, opeingnewstoremgmt } from "../../api/shopManage/shop"
import { datas_filter_cond } from '@/api/commonInterface'

export default {
    components: {

    },
    data() {
        return {
            loading: false,
            queryList: {
                shopName: '', //门店编号/名称
                operatingMarket: '', //运营市场
                storeType: '', //门店类型
                // ItMarket:'', //IT市场
                // allEquipment:'', //设备类型
            },
            operatingMarketList: [], //运营市场下拉数据
            storeTypeList: [], //门店类型下拉数据
            tempType: [],
            itMarketList: [], //IT市场下拉数据
            allEquipmentList: [], //全部设备下拉数据
            currentPage: 1, //页码
            totalNum: "", //总数据数量
            pageSize: 10,
            tableData: [],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getInfo()
        this.getSelectDataList();
    },
    mounted() {

    },
    methods: {
        // 获取列表信息
        getInfo() {
            this.loading = true;
            const params = {
                "classModel": "OpeningNewStore",
                // "sort": "",//非必要，排序规则，storecode,createdAtS
                "page": this.currentPage - 1, //起始页码,
                "size": 10, //每页数据量,
                "status": "all",
                "blurry": this.queryList.shopName,//搜索门店编号或者名称模糊查找　
                "opsmarket": this.queryList.operatingMarket, //运营市场
                "storetype": this.queryList.storeType, //门店类型
            }
            getNewShopInfo(params).then(res => {
                // console.log(res,'获取数据res12');
                if (res.rst == 'ok') {
                    this.tableData = res.data[0].content;
                    this.totalNum = res.data[0].totalElements
                    this.loading = false;
                } else {
                    this.$message.error(res.error_msg);
                    this.loading = false;
                }
            })
        },
        // 获取下拉数据
        getSelectDataList() {
            const params = {
                classModel: 'OpeningNewStore',
            }
            datas_filter_cond(params).then(res => {
                if (res.rst == 'ok') {
                    // console.log(res,'[resxxx]');
                    this.operatingMarketList = res.data[0][0];
                    // this.storeTypeList = res.data[0][2].options.concat(res.data[0][3].options)
                    this.storeTypeList = res.data[0][2].options
                } else {
                    this.$message.error(res.error_msg);
                }
            })
        },
        // 搜索
        handleSearch() {
            this.currentPage = 1;
            this.getInfo()
        },
        // 新增门店
        addShop() {
            this.$router.push('/shopManage/shopAddEdit');
            sessionStorage.setItem('activepath', '/shopAddEdit')
            // this.$message.success('新增')
        },
        // 点击列表编辑
        handleEidt(row) {
            console.log(row);
            // this.$message.success('编辑')
            this.$router.push({
                path: '/shopManage/shopAddEdit',
                query: {
                    edit: true,
                    row: row
                }
            });
            sessionStorage.setItem('activepath', '/shopAddEdit')
            // this.$router.push('/shopDetials');
            // sessionStorage.setItem('activepath','/shopDetials')
            // 需要将点击的那条数据存到本地
            // localStorage.setItem('shop_detials',JSON.stringify(row))
        },
        // 点击列表删除
        handleDelete(row) {
            const params = {
                "act": "del",//必要,操作类型，"add": 新增；"edit":编辑，"del":删除，删除时只有参数"id"必须，"conform:代办确认，只有参数"id"必须
                "id": row.id,//非必要,编辑时必填
            }
            this.$confirm('此操作将会删除该条数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                opeingnewstoremgmt(params).then(res => {
                    console.log(res, '删除res');
                    if (res.rst == 'ok') {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getInfo();
                    }
                })

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
            console.log(params, '删除的params');

        },
        //页码改变
        handleCurrentChange(val) {
            // console.log(`现在是第${val}页`);
            this.currentPage = val;
            this.getInfo()
        },
        handleSizeChange(val) {
            // console.log(`每页${val}条`);
            this.pageSize = val;
            this.getInfo()
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 226 + 'px';
            this.autoHeight.heightNum = windowHeight - 226;
        },
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style lang="scss" scoped>
* {
    box-sizing: border-box;
}

.all_restaurants {
    width: 100%;
}

.all_restaurants_search {
    width: 100%;
    height: 70px;
    align-items: center;
    padding: 0 20px;
}

.btn_style {
    background: var(--text-color);
    color: #fff;
    height: 32px;
    /* width: 106px; */
    text-align: center;
    line-height: 32px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.btn_style:hover {
    background: rgba(211, 57, 57, .8);
}

/* 底部页码区 */
.all_restaurants_footer {
    box-sizing: border-box;
    width: 100%;
    height: 66px;
    font-size: 14px;
    padding-top: 17px;
    text-align: right;
    padding-right: 20px;
    /* border: 1px solid black; */
}

.el-checkbox__inner {
    width: 20px !important;
    height: 20px !important;
}

// .el-table__body{
// ::v-deep .el-checkbox__inner{
//     width: 20px !important;
//     height: 20px !important;
//     }
// }</style>
<style>
.table_wrap .el-checkbox__inner {
    width: 14px !important;
    height: 14px !important;
}

.table_wrap .el-checkbox__input.is-checked .el-checkbox__inner,
.table_wrap .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--base-color) !important;
    border-color: var(--base-color) !important;
}
</style>
