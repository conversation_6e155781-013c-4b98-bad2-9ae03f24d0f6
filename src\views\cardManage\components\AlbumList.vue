<template>
    <div class="AlbumList" v-loading="Loading">
        <div class="album_header">
            <div>
                <el-input placeholder="请输入专辑名称" v-model="queryList.classify_label" clearable
                    style="width: 230px;margin-bottom: 10px">
                </el-input>
                <el-select v-model="queryList.tags" placeholder="请选择专辑标签" clearable multiple collapse-tags>
                    <el-option v-for="item in CardTagsList" :key="item.value" :label="item.name" :value="item.name">
                    </el-option>
                </el-select>
                <el-select v-model="queryList.v_or_h" placeholder="请选择专辑方向" clearable>
                    <el-option label="横" value="h"></el-option>
                    <el-option label="竖" value="v"></el-option>
                </el-select>
                <!-- <el-button type="primary" @click="getCardContent()">搜索</el-button> -->
                <el-button type="primary" @click="searchCard()">搜索</el-button>
            </div>
            <div>
                <el-button type="primary" v-if="is_quoted" @click="openNewCard()">新建专辑</el-button>
            </div>
        </div>
        <div class="album_body">
            <el-table :data="tableData" style="width: 100%" :height="autoHeight.height"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <el-table-column type="selection" align="center">
                </el-table-column>
                <el-table-column label="专辑名称" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.classify_label }}
                    </template>
                </el-table-column>
                <el-table-column label="近期内容" align="center">
                    <template slot-scope="scope">
                        <div class="card" v-if="scope.row.cs_list.length > 0">
                            <div v-for="item in scope.row.cs_list" :key="item.id" class="card_item">
                                <img :src="item.thumb_url" alt="">
                            </div>
                        </div>
                        <div v-else>
                            暂无内容
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="内容方向" width="80" align="center">
                    <template slot-scope="scope">
                        <div class="card_info">
                            {{ scope.row.v_or_h == 0 ? '横屏' : '竖屏' }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="专辑标签" align="center">
                    <template slot-scope="scope">
                        <div class="flex"
                            style="flex-wrap: wrap;width: 100%;justify-content: center; align-items:center">
                            <div class="flex" style="flex-direction:column;align-items: center;"
                                v-if="scope.row.content_tags.length > 2">
                                <div style="display:flex;align-items:center">
                                    <img src="@/assets/img/home_img/little_label.svg"
                                        style="width: 24px; height: 24px" />
                                    {{ scope.row.content_tags[0] }}
                                </div>
                                <div style="display:flex;align-items:center">
                                    <img src="@/assets/img/home_img/little_label.svg"
                                        style="width: 24px; height: 24px" />
                                    {{ scope.row.content_tags[1] }}
                                </div>
                                <el-popover placement="top-start" title="门店标签" popper-class="popperOptions" width="200"
                                    trigger="hover">
                                    <div v-for="item in (new Set(scope.row.content_tags))" :key="item"
                                        style="display:flex;align-items:center">
                                        <img src="@/assets/img/home_img/little_label.svg"
                                            style="width: 24px; height: 24px" />
                                        <span>
                                            {{
                                                item
                                            }}
                                        </span>
                                    </div>
                                    <span class="cursor" slot="reference">...</span>
                                </el-popover>
                            </div>
                            <div class="flex" style="flex-direction:column;align-items: center"
                                v-else-if="scope.row.content_tags.length > 0 && scope.row.content_tags.length <= 2">

                                <div style="display:flex;align-items:center"
                                    v-for="item in (new Set(scope.row.content_tags))" :key="item">
                                    <img src="@/assets/img/home_img/little_label.svg"
                                        style="width: 24px; height: 24px" />
                                    {{ item }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="状态" align="center">
                    <template slot-scope="scope">
                        <div class="card_status" v-if="scope.row.is_quoted">
                            有引用<br>
                            <span class="look_detail" @click="lookCardDetail(scope.row)">查看详情</span>
                        </div>
                        <div v-else>
                            无引用
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="240">
                    <template slot-scope="scope">
                        <div class="card_event">
                            <!-- <div class="edit" @click="add_or_edit_content(scope.row)" v-if="is_quoted">编辑</div> -->
                            <!-- <div class="del_text" v-if="is_quoted">删除</div> -->
                            <el-button @click="add_or_edit_content(scope.row)" type="primary" v-if="is_quoted"
                                class="edit_ev" :disabled="scope.row.is_quoted">编辑</el-button>
                            <el-button @click="copy_content(scope.row)" type="primary" class="edit_ev" v-if="is_quoted">复制</el-button>
                            <el-button @click="prviewCard(scope.row)" type="primary" v-if="!is_quoted">预览</el-button>
                            <el-button @click="relevan(scope.row)" type="primary" v-if="!is_quoted">关联</el-button>
                            <el-button type="danger" :disabled="scope.row.is_quoted" v-if="is_quoted"
                                @click="deleteAlbum(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="album_footer flex flex-1">
                <div class="left_button_wrap flex-1">
                    <el-button type="primary" class="EventButton" @click="openBatchTagDialog"
                        v-if="is_quoted">批量标签</el-button>
                </div>
                <div class="right_page_wrap">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page.sync="currentPage" :page-size="queryList.page_size"
                        :page-sizes="[10, 20, 50, 100]" layout="total,sizes,prev,pager, next, jumper"
                        :total="totalNum"></el-pagination>
                </div>
            </div>
        </div>



        <el-dialog title="新增专辑" :visible.sync="dialogFormVisible" width="30%">
            <el-form :model="CardForm" :rules="rules" label-width="100px" ref="CardForm">
                <el-form-item label="专辑方向:" :label-width="formLabelWidth">
                    <el-radio v-model="CardForm.direction" label="横屏">横屏</el-radio>
                    <el-radio v-model="CardForm.direction" label="竖屏">竖屏</el-radio>
                </el-form-item>
                <el-form-item label="专辑名称:" :label-width="formLabelWidth" prop="cardName">
                    <el-input v-model="CardForm.cardName" autocomplete="off" style="width: 280px;"></el-input>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancelDialog">取 消</el-button>
                <el-button type="primary" @click="requestAddClassIfy">确 定</el-button>
            </div>
        </el-dialog>

        <el-dialog title="复制专辑" :visible.sync="dialogCopyVisible" width="30%">
            <el-form :model="CopyForm" :rules="rules" label-width="100px" ref="CardCopyForm">
                <el-form-item label="专辑名称:" :label-width="formLabelWidth" prop="cardName">
                    <el-input v-model="CopyForm.cardName" autocomplete="off" style="width: 280px;"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="cancelCopyDialog">取 消</el-button>
                <el-button type="primary" @click="requestCopyClassIfy">确 定</el-button>
            </div>
        </el-dialog>


        <el-dialog title="批量标签" :visible.sync="dialogVisible" width="30%" :before-close="cancelTagDialog"
            :close-on-click-modal='false' custom-class='batch_tags_dialog'>
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="新增标签" name="addTag">
                    <div class="relation_tags">
                        <el-form label-width="100px">
                            <el-form-item label="标签名称:" :label-width="formLabelWidth" prop="cardName">
                                <el-input v-model="TagsForm.tagName" autocomplete="off"
                                    style="width: 280px;"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="关联标签" name="relateTag">
                    <el-input placeholder="请输入标签" style="width: 230px;margin-bottom: 10px" v-model="inputTagValue"
                        @input="inputTags">
                        <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
                    </el-input>
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in CardTagsList" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active" :key="item.name">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="取消关联标签" name="cancelrelateTag">
                    <el-input placeholder="请输入标签" style="width: 230px;margin-bottom: 10px" v-model="delTagValue"
                        @input="delInputTags">
                        <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
                    </el-input>
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in deleteTags" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active; $forceUpdate()" :key="item.name">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="删除标签" name="deleteTag">
                    <el-input placeholder="请输入标签" style="width: 230px;margin-bottom: 10px" v-model="inputTagValue"
                        @input="inputTags">
                        <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
                    </el-input>
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in CardTagsList" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active" :key="item.name">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelTagDialog">取 消</el-button>
                <el-button type="primary" @click="requestAddTag">确 定</el-button>
            </span>
        </el-dialog>


    </div>
</template>

<script>
import { get_classify_detail } from "@/api/device/device"
import { create_group_classify, get_tpl_classify_list } from "@/api/contentdeploy/contentdeploy"
import { delete_tpl_classify } from "@/api/template/index"
import { gcontent_tags_mgmt, copy_tpl_classify } from "@/api/task/task"
import { active } from "sortablejs";

export default {
    data() {
        return {
            Loading: true,
            autoHeight: {
                //列表区高度
                height: "",
                heightNum: ""
            },
            queryList: {
                page_size: 10, //每页显示条数
                page_num: 0,
                tags: [],
                v_or_h: 'h',
                tags_rel: "or",
                classify_label: ''
            },
            options: [],
            select_value: '',
            tableData: [],
            totalNum: 0,
            CardForm: {
                cardName: '',
                direction: '横屏'
            },
            CopyForm: {
                tpl_cf: '',
                new_cf_name: ''
            },
            dialogFormVisible: false,
            rules: {
                cardName: [
                    { required: true, message: '请输入专辑名称', trigger: 'blur' }
                ],
            },
            dialogVisible: false,
            selectTagsList: [],
            TagsForm: {
                tagName: ''
            },
            activeName: 'addTag',
            CardTagsList: [],
            searchCardTags: '',
            inputTagValue: '',
            delTagValue: '',
            searchTags: [],
            deleteTags: [],
            saveDeleteTags: [],
            copy_cf_id: '',
            dialogCopyVisible:false
        }
    },
    props: {
        is_quoted: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 330 + "px";
            this.autoHeight.heightNum = windowHeight - 330;

            if (!this.is_quoted) {
                this.autoHeight.height = windowHeight - 530 + "px";
            }

        },
        //页码改变
        handleCurrentChange(val) {
            // console.log(`现在是第${val}页`);
            this.queryList.page_num = val - 1;
            this.getCardContent()
        },
        handleSizeChange(val) {
            // console.log(`每页${val}条`);
            this.queryList.page_size = val;
            this.getCardContent()
        },
        searchCard(){
            this.queryList.page_num = 0;
            this.getCardContent()
        },
        getCardContent() {
            get_tpl_classify_list(this.queryList).then(res => {
                console.log(res, 'resxx');
                if (res.rst == 'ok') {
                    this.tableData = res['data'][0]['content']
                    this.totalNum = res['data'][0]['totalElements']
                    this.Loading = false;
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        getCardTags() {
            get_tpl_classify_list({ "page_size": 1000, "page_num": 0 }).then(res => {
                console.log(res, 'resxx');
                if (res.rst == 'ok') {
                    let cpTags = res['data'][0]['all_tags'].map(item => {
                        return {
                            active: false,
                            name: item
                        }
                    })
                    this.searchTags = cpTags
                    this.CardTagsList = cpTags
                } else {
                    this.$message.error(res.error_msg)
                }
            })
        },
        openNewCard() {
            this.dialogFormVisible = true
        },
        lookCardDetail(row) {
            this.$router.push({
                path: "/deploy/ReferencePlan",
                query: {
                    'tpl_cf': row.sche_id
                }
            })
        },
        cancelDialog() {
            this.dialogFormVisible = false;
            this.CardForm.cardName = '';
            this.CardForm.direction = '横屏'
        },
        requestCopyClassIfy() {
            this.$refs['CardCopyForm'].validate((valid) => {
                if (valid) {
                    // CardCopyForm
                    let parmas = {
                        new_cf_name: this.CopyForm.cardName,
                        tpl_cf:this.CopyForm.tpl_cf
                    }
                    // this.CopyForm.new_cf_name = this.CopyForm.cardName
                    copy_tpl_classify(parmas).then(res => {
                        console.log(res, 'res');
                        if (res['rst'] == 'ok') {
                            this.$message.success('复制成功');
                            this.dialogCopyVisible = false;
                            this.getCardContent()
                        }else{
                            this.$message.error(res['msg'])
                        }
                    })
                } else {

                }
            })
        },
        cancelCopyDialog() {
            this.dialogCopyVisible = false;
            this.CopyForm.new_cf_name = '';
            this.CopyForm.tpl_cf = '';
        },

        requestAddClassIfy() {
            this.$refs['CardForm'].validate((valid) => {
                if (valid) {
                    let params = {
                        group_id: localStorage.getItem('group_id'),
                        v_or_h: this.CardForm.direction == '横屏' ? 'h' : 'v',
                        cf_label: this.CardForm.cardName,
                        create_from: 14,
                        inster_type: 200,
                        attributes: {
                            "play_mode": "full_day",
                        }
                    }
                    create_group_classify(params).then(res => {
                        console.log(res, 'res');
                        if (res.rst == 'ok') {
                            this.$message.success('创建专辑成功');
                            this.dialogFormVisible = false;
                            this.getCardContent()
                        } else {
                            this.$message.error(res.error_msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        add_or_edit_content(val) {
            console.log(val, 'val');

            this.$router.push({
                path: "/deploy/AddContentCard",
                query: {
                    ref_id: val.sche_id
                }
            })
        },
        copy_content(row) {
            console.log(row, 'row');
            this.CopyForm.tpl_cf = row.sche_id;
            this.dialogCopyVisible = true;
            this.$forceUpdate()
            console.log(this.dialogCopyVisible,'dialogCopyVisible');
            
        },
        prviewCard(row) {
            this.$router.push({
                path: "/deploy/AddContentCard",
                query: {
                    ref_id: row.sche_id,
                    status: 'preview'
                }
            })
        },
        relevan(row) {
            this.$emit("relevan", row)
        },
        deleteAlbum(row) {
            this.$confirm('此操作将删除该专辑, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                console.log(row, 'row');

                delete_tpl_classify({
                    tpl_cf: row.sche_id
                }).then(res => {
                    if (res['rst'] == 'ok') {
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getCardContent()
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        handleSelectionChange(e) {
            console.log(e, 'e');
            this.selectTagsList = e;
        },
        openBatchTagDialog() {
            // if (this.selectTagsList.length == 0) return this.$message.warning('请选择要批量专辑的标签');
            this.activeName = 'addTag'
            this.dialogVisible = true;
            this.deleteTags = []
            let tags = []
            this.CardTagsList.forEach(item => {
                item.active = false
            })
            this.selectTagsList.forEach(item => {
                console.log(item, 'item');
                if (item.content_tags.length > 0) {
                    tags.push(...item.content_tags)
                }
            })
            let delTags = new Set(tags)
            delTags.forEach(item => {
                this.deleteTags.push({
                    'checked': false,
                    'name': item
                })
            })

            this.deleteTags = JSON.parse(JSON.stringify(this.deleteTags))
            this.saveDeleteTags = JSON.parse(JSON.stringify(this.deleteTags))
        },
        cancelTagDialog() {
            this.dialogVisible = false;
        },
        requestAddTag() {
            let resAlbumIds = this.selectTagsList.map(item => item.sche_id)

            if (this.activeName == 'addTag') {
                let params = {
                    group_id: localStorage.getItem('group_id'),
                    tags: [this.TagsForm.tagName],
                    action: 'addcontent_tag',
                    // content_ids: resAlbumIds,
                    // contenttype: 's'
                }
                gcontent_tags_mgmt(params).then(res => {
                    console.log(res, 'res');
                    if (res['rst'] == 'ok') {
                        if (res['data'][0]['succ'] == 1) {
                            this.$message.success('添加成功')
                            this.TagsForm.tagName = ''
                        }
                    }
                })
            } else if (this.activeName == 'relateTag') {
                if (this.selectTagsList.length == 0) return this.$message.warning('请选择要批量专辑的标签');
                let select_tags = this.CardTagsList.map(item => {
                    if (item.active) {
                        return item.name
                    }
                })
                select_tags = select_tags.filter(item => item != null)
                let params = {
                    group_id: localStorage.getItem('group_id'),
                    tags: select_tags,
                    action: 'addcontent_tag',
                    content_ids: resAlbumIds,
                    contenttype: 's'
                }
                gcontent_tags_mgmt(params).then(res => {
                    if (res['rst'] == 'ok') {
                        this.$message.success('关联成功')
                        setTimeout(() => {
                            this.cancelTagDialog()
                            this.getCardContent()
                        }, 500);
                    } else {
                        this.$message.error(res['msg'])
                    }
                })
            } else if (this.activeName == 'cancelrelateTag') {
                if (this.selectTagsList.length == 0) return this.$message.warning('请选择要批量专辑的标签');
                let select_tags = this.deleteTags.map(item => {
                    if (item.active) {
                        return item.name
                    }
                })
                select_tags = select_tags.filter(item => item != null)
                console.log(select_tags, 'select_tags');

                let params = {
                    group_id: localStorage.getItem('group_id'),
                    tags: select_tags,
                    action: 'del',
                    content_ids: resAlbumIds,
                    contenttype: 's'
                }
                gcontent_tags_mgmt(params).then(res => {
                    console.log(res, 'res');
                    if (res['rst'] == 'ok') {
                        this.$message.success('删除成功')
                        setTimeout(() => {
                            this.cancelTagDialog()
                            this.getCardContent()
                            this.getCardTags()
                        }, 1000)
                    }
                })
            } else if (this.activeName == 'deleteTag') {
                let select_tags = this.CardTagsList.map(item => {
                    if (item.active) {
                        return item.name
                    }
                })
                select_tags = select_tags.filter(item => item != null)
                let params = {
                    group_id: localStorage.getItem('group_id'),
                    tags: select_tags,
                    action: 'del_all',
                    content_ids: resAlbumIds,
                    contenttype: 's'
                }
                gcontent_tags_mgmt(params).then(res => {
                    if (res['rst'] == 'ok') {
                        this.$message.success('删除成功')
                        setTimeout(() => {
                            this.cancelTagDialog()
                            this.getCardContent()
                        }, 500);
                    } else {
                        this.$message.error(res['msg'])
                    }
                })
            }
        },
        handleClick(e) {
            console.log(e.name, 'eeee');
            if (e.name == 'relateTag') {
                this.getCardTags();
            } else if (e.name == 'addTag') {
                this.TagsForm.tagName = '';
            } else if (e.name == 'cancelrelateTag') {
                this.getCardTags();
            } else if (e.name == 'deleteTag') {
                this.getCardTags()
            }
        },
        inputTags(e) {
            const lowerCaseQuery = this.inputTagValue;
            this.CardTagsList = this.searchTags.filter(item =>
                item.name.includes(lowerCaseQuery)
            );
        },
        delInputTags(e) {
            console.log(e, 'e');
            const lowerCaseQuery = this.delTagValue;
            this.deleteTags = this.saveDeleteTags.filter(item =>
                item.name.includes(lowerCaseQuery)
            );
        },

    },
    created() {
        window.addEventListener("resize", this.getHeight);
        this.getHeight();
        this.getCardContent()
        this.getCardTags()

    }
}
</script>

<style lang="scss" scoped>
.AlbumList {
    background-color: #eeeeee;
    padding: 20px 40px;

    .album_header {
        display: flex;
        justify-content: space-between;
    }

    .album_body {
        background-color: #fff;
        margin-top: 30px;
        padding: 20px;

        .card {
            display: flex;
            // justify-content: space-between;
            justify-content: center;

            .card_item {
                width: 60px;
                height: 60px;
                margin-right: 10px;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                }
            }

        }

        .card_info {
            display: flex;
            justify-content: space-around;

            .left {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                text-align: left;
            }

            .right {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                text-align: left;
            }
        }

        .card_status {
            .look_detail {
                cursor: pointer;
                font-weight: bold;
                color: #30b07f;
            }
        }

        .card_event {
            display: flex;
            // flex-wrap: nowrap;
            justify-content: space-around;

            button {
                // margin-right: 5px !important;
                margin-bottom: 5px;
            }

            .edit {
                color: #30b07f;
                cursor: pointer;
            }

            .delete {
                color: #fc172c;
                cursor: pointer;
            }
        }
    }

    .album_footer {
        height: 50px;
        display: flex;
        align-items: center;
    }
}

.prview {
    cursor: pointer;
}

.batch_tags_dialog {
    margin-top: 0px !important;
    border-radius: 16px !important;
}

.batch_tags_dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
    margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 1px !important;
}

.batch_tags_dialog .el-tabs__item {
    height: 50px !important;
    line-height: 50px !important;
}

.batch_tags_dialog .el-button--primary {
    background: rgba(108, 178, 255, 1);
}

/* 弹框 */
.relation_tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 150px;
    height: 38px;
    font-size: 14px;
    border-radius: 8px;
    margin-right: 24px;
    color: #24b17d;
    border: 1px solid #24b17d;
    font-weight: bold;
    margin-bottom: 18px;
    padding: 0 13px;
    cursor: pointer;
    /* 禁止文字选中 */
    /* -moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none; */
    user-select: none;
}

.tag_active {
    color: #fff;
    background-color: #24b17d;
    // background-color: var(--text-color-light);
    // border: 1px solid var(--text-color-light);
}


::v-deep .card_event {
    .edit_ev.is-disabled {
        background-color: #92d8be !important;
        border-color: #92d8be !important;
    }
}
</style>