// 资源管理,图片资源
import { get, post, uploadFile } from '@/utils/request'


//图片-列表读取
export function self_photo_list(data) {
    return post('/file/api/json',data,'self_photo_list')
}

//图片-删除
export function delete_self_photo_statusonly(data) {
    return post('file/api/json',data,'delete_self_photo_statusonly')
}

//(批量)新增or删除图片标签
export function add_or_edit_photo_tags(data) {
    return post('dsadm/api/json',data,'add_or_edit_photo_tags')
}
