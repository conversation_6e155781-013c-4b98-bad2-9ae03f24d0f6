<template>
    <div class="goods_sell_out">
        <div class="top_back">
            <i class="el-icon-back" @click="back2managent"></i>
        </div>
        <div class="sell_out_content">
            <div class="left_overview">
                <div class="l_header">{{$route.query.type == 'sold_out' ? '新增售罄' : '取消售罄'}}</div>
                <div class="l_content">
                    <div class="goods_img">
                        <div v-if="!product_info.thumb_img_url || product_info.thumb_img_url == ''" class="no_img">
                            暂无商品图
                        </div>
                        <div v-else class="img_url">
                            <img :src="product_info.thumb_img_url" alt="">
                        </div>
                        
                    </div>
                    <div class="goods_info">
                        <div class="info_item"><span>商品ID：</span><b>{{product_info.prod_id}}</b></div>
                        <div class="info_item"><span>商品编码：</span><b>{{product_info.code ? product_info.code : '--'}}</b></div>
                        <div class="info_item"><span>商品名称：</span><b>{{product_info.name}}</b></div>
                        <div class="info_item"><span>商品分类：</span><b>{{product_info.cate_type_display}}</b></div>
                        <div class="info_item"><span>商品状态：</span><b>{{product_info.active_status_display}}</b></div>
                    </div>
                </div>
            </div>
            <div class="right_shop_edit">
                <div class="shop_content">
                    <div class="content_center_wrap" >
                        <div class="sswr shop_unselected">
                            <div class="title">
                                未选门店 
                            </div>
                            <div class="filter_content">
                                <div>
                                    <label for="ipt_1">门店名称/编码 </label>
                                    <el-input id="ipt_1" style="width: 180px;" v-model="searchInfo.name" clearable placeholder="请输入门店名称/编码"></el-input>
                                </div>
                                <div>
                                    <label for="ipt_1">门店标签 </label>
                                    <el-select id="ipt_1" style="width: 180px;" multiple collapse-tags clearable v-model="searchInfo.tags" placeholder="请选择门店标签">
                                        <el-option v-for="(tag,tag_index) in tags_list" :key="'tag_' + tag_index "  :value="tag" :label="tag"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <label for="ipt_1">门店类型 </label>
                                    <el-select id="ipt_1" style="width: 180px;" v-model="searchInfo.type" clearable placeholder="请选择门店类型">
                                        <el-option v-for="opt in storetypeOptions" :key="'type_opt_' + opt[0]" :label="opt[1]" :value="opt[0]"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button type="primary" size="small" @click="searchShopList">搜索</el-button>
                                    <el-button  size="small" @click="reset">重置</el-button>
                                </div>
                            </div>
                            <div class="table_content">
                                <div class="table_header">
                                    <div class="header_row">
                                        <div class="check_box cell"></div>
                                        <div class="check_box cell">门店名称</div>
                                        <div class="check_box cell">门店编码</div>
                                        <div class="check_box cell">分区</div>
                                    </div>
                                </div>
                                <div class="table_body" v-loading="loading">
                                    <div class="body_row" v-for="item in shop_list" :key="item.id">
                                        <div class="check_box cell">
                                            <el-checkbox v-model="item.checked" @change="selsetChecked('ltr',item)"></el-checkbox>
                                        </div>
                                        <div class="check_box cell">{{item.storename}}</div>
                                        <div class="check_box cell">{{item.storecode}}</div>
                                        <div class="check_box cell">{{item.marketname}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="footer">
                                <el-pagination
                                background
                                layout="total,prev, pager, next"
                                pager-count="5"
                                @current-change="handleCurrentChange"
                                :page-size="pageSize"
                                :current-page="page"
                                :total="total">
                                </el-pagination>
                            </div>
                        </div>
                        <div class="sswr shop_selected">
                            <div class="title">
                                已选门店 （{{SelectedStores.length}}个）
                            </div>
                            <div class="table_content">
                                <div class="table_header">
                                    <div class="header_row">
                                        <div class="check_box cell"></div>
                                        <div class="check_box cell">门店名称</div>
                                        <div class="check_box cell">编号</div>
                                        <div class="check_box cell">分区</div>
                                    </div>
                                </div>
                                <div class="table_body">
                                    <div class="body_row" v-for="(item,index) in SelectedStores" :key="item.id">
                                        <div class="check_box cell">
                                            <el-checkbox v-model="item.checked" @change="selsetChecked('rtl',item,index)"></el-checkbox>
                                        </div>
                                        <div class="check_box cell">{{item.storename}}</div>
                                        <div class="check_box cell">{{item.storecode}}</div>
                                        <div class="check_box cell">{{item.marketname}}</div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    
                    <div class="btns">
                        <el-button size="medium" @click="cancel">取消</el-button>
                        <el-button size="medium" type="primary" @click="confirm" :loading="isConfirm">
                            {{ $route.query.type == 'sold_out' ? '新增' : '保存'}}
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {get_adm_datas,dmb_sellprod_mgmt,datas_filter_cond,sellprod_soldout_by_shop_by_prod,get_shop_tags} from '@/api/goods/goods'
export default {
    components: {

    },
    data() {
        return {
            loading:false,
            isConfirm:false,
            shop_list:[],
            searchInfo:{
                name:'',
                tags:[],
                type:''
            },
            storetypeOptions:[],
            tags_list:[],
            total:0,
            page:1,
            pageSize:20,
            SelectedStores:[],
            SelectedStoresIds:[],
            product_info:{}
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.getShopTags()
        this.getSearchCondition();
        this.getProductInfo();
        this.getShopList();
    },
    mounted() {

    },
    methods: {
        getProductInfo(){
            const params = {
                classModel:'SellProds',
                page:0,
                size:10,
                prod_id:this.$route.query.prod_id
            }
            get_adm_datas(params).then(res=>{
                if(res.rst == 'ok'){
                    if(res.data[0].content && res.data[0].content.length > 0){
                        this.product_info = res.data[0].content[0];
                        console.log(this.product_info,'商品信息');
                    }
                    
                }else{
                    this.$message.error(res.error_msg)
                }
            })
        },
        getShopTags(){
            const params = {
                page:0,
                size:1000
            }
            get_shop_tags(params).then(res=>{
                if(res.rst == 'ok'){
                    this.tags_list = res.data[0].tags_list;
                }else{
                    this.$message.error(res.error_msg)
                }
            })
        },
        getSearchCondition(){
            const params = {
                classModel:'GroupShop'
            }
            datas_filter_cond(params).then(res=>{
                if(res.rst == 'ok'){
                    console.log(res.data[0]);
                    const storetype = res.data[0].find(item=>item.filterkey == 'storetype');
                    this.storetypeOptions = storetype.options;
                    console.log(this.storetypeOptions);
                }else{
                    this.$message.error(res.error_msg)
                }
            })
        },
        searchShopList(){
            this.page = 1;
            this.getShopList();
        },
        getShopList(){
            this.loading = true;
            if(this.$route.query.type == 'sold_out'){
                // 售罄
                this.getAllShopList();
            }else if(this.$route.query.type == 'cancel_sold_out'){
                // 取消售罄
                this.getSoldOutShopList();
            }

        },
        getAllShopList(){
            const params = {
                classModel:'GroupShop',
                page:this.page - 1,
                size:this.pageSize,
                blurry:this.searchInfo.name,      // 非必要，搜索门店编号或者名称模糊查找
                shop_tags: this.searchInfo.tags,  // 非必要，门店便签 list
                storetype:this.searchInfo.type,   // 门店类型
            }
            get_adm_datas(params).then(res=>{
                if(res.rst == 'ok'){
                    this.total = res.data[0]['totalElements'];
                    this.shop_list = res.data[0].content;

                    if(this.SelectedStores.length == 0){
                        this.shop_list.forEach((item,index)=>{
                            item.checked = false
                        })
                    }else{
                        this.shop_list.forEach((item,index)=>{
                            if(this.SelectedStoresIds.indexOf(item.shop_id) != -1){
                                item.checked = true;
                            }else{
                                item.checked = false;
                            }
                        })
                    }
                    
                    
                }else{
                    this.$message.error(res.error_msg)
                }
                this.loading = false;
            }).catch(err=>{
                this.loading = false;
            })
        },
        getSoldOutShopList(){
            const params = {
                classModel:'ProdSoldOutShops',
                page:this.page - 1,
                size:this.pageSize,
                prod_id:this.$route.query.prod_id,
                store_filter_cond:{
                    blurry:this.searchInfo.name,      // 非必要，搜索门店编号或者名称模糊查找
                    shop_tags: this.searchInfo.tags,  // 非必要，门店便签 list
                    storetype:this.searchInfo.type,   // 门店类型
                }
            }
            get_adm_datas(params).then(res=>{
                if(res.rst == 'ok'){
                    this.total = res.data[0]['totalElements'];
                    this.shop_list = res.data[0].content;

                    if(this.SelectedStores.length == 0){
                        this.shop_list.forEach((item,index)=>{
                            item.checked = false
                        })
                    }else{
                        this.shop_list.forEach((item,index)=>{
                            if(this.SelectedStoresIds.indexOf(item.shop_id) != -1){
                                item.checked = true;
                            }else{
                                item.checked = false;
                            }
                        })
                    }
                }else{
                    this.$message.error(res.error_msg)
                }
                this.loading = false;
            }).catch(err=>{
                this.loading = false;
            })
        },
        selsetChecked(type,item,item_index){
            if(type == 'ltr'){
                // 选择门店
                if(item.checked){
                    this.SelectedStores.push(JSON.parse(JSON.stringify(item)));
                    this.SelectedStoresIds.push(item.shop_id);
                }else{
                    const idx = this.SelectedStores.findIndex(i=>i.code == item.code);
                    this.SelectedStores.splice(idx,1);
                    this.SelectedStoresIds.splice(idx,1);
                }
            }else {
                // 取消选择
                const info = this.shop_list.find(i=>i.shop_id == item.shop_id);
                if(info){
                    info.checked = false;
                }
                item.checked = false;
                
                this.SelectedStores.splice(item_index,1);
                this.SelectedStoresIds.splice(item_index,1);
            }
        },
        handleCurrentChange(val){
            console.log(val);
            this.page = val;
            this.getShopList();
        },
        back2managent(){
            this.$router.replace('/goodsManage/product_center')
        },
        confirm(){
            const shop_ids = this.SelectedStores.map(item=>item.shop_id);
            if(shop_ids.length == 0){
                this.$message.warning('请选择要售罄的门店')
                return;
            }
            this.isConfirm = true;
            let params = {
                opttype: this.$route.query.type == 'sold_out' ? 'pub_by_shop' : 'cancel_by_shop',
                prod_id:this.$route.query.prod_id,
                shop_ids
            }
            
            sellprod_soldout_by_shop_by_prod(params).then(res=>{
                if(res.rst == 'ok'){
                    const tips = this.$route.query.type == 'sold_out' ? '新增售罄门店成功' : '取消售罄门店成功';
                    this.$message.success(tips);
                    this.cancel();
                }else{
                    this.$message.error(res.error_msg)
                }
                this.isConfirm = false;
            }).catch(err=>{
                this.isConfirm = false;
            })
        },
        cancel(){
            this.SelectedStores = []
            this.shop_list.forEach(item=>item.checked = false)
            if(this.$route.query.type == 'cancel_sold_out'){
                this.page = 1;
                this.getShopList();
            }
        },
        reset(){
            this.page = 1;
            this.searchInfo.name = '';
            this.searchInfo.tags = [];
            this.searchInfo.type = '';
            this.getShopList();
        }
    },
};
</script>

<style scoped lang="scss">
    .goods_sell_out{
        width: 100%;
        height: 100%;
        min-height: 700px;
        padding: 20px;
        padding-right: 40px;
        box-sizing: border-box;
        font-size: 14px;
        background-color: var(--light-gray);
        .top_back{
            width: 100%;
            height: 30px;
            font-size: 26px;
            color: var(--btn-background-dark);
            margin-bottom: 20px;
            i{
                cursor: pointer;
            }
        }

        .sell_out_content{
            display: flex;
            height: calc(100vh - 150px);
            min-height: 600px;
            .left_overview{
                margin-right: 35px;
                height: 100%;
                .l_header{
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .l_content{
                    width: 230px;
                    min-height: 240px;
                    background-color: #fff;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 10px 0;
                    .goods_img{
                        width: 200px;
                        min-height: 200px;
                        background-color: var(--light-gray);
                        border-radius: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        .no_img{
                            color: #8b8b8b;
                        }
                        .img_url{
                            width: 100%;
                            height: 200px;
                            img{
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                        .img_title{
                            width: 100%;
                            padding-left: 10px;
                            p:first-child{
                                font-size: 16px;
                                font-weight: bold;
                                margin-bottom: 5px;
                            }
                            p:last-child{
                                color: var(--text-gray);
                            }
                        }
                    }
                    .goods_info{
                        width: 200px;
                        min-height: 150px;
                        max-height: 100%;
                        margin-top: 10px;
                        // display: grid;
                        // place-content: end start;
                        .info_item{
                            margin-top: 15px;
                            color: var(--text-gray);
                            display: flex;
                            b{
                                color: #000;
                                display: inline-block;
                                width: 100px;
                            }
                        }
                    }
                }
            }
            
            .right_shop_edit{
                flex: 1;
                min-height: 550px;
                min-width: 300px;
                display: flex;
                padding-top: 40px;
                box-sizing: border-box;
                flex-direction: column;
                .r_header{
                    font-size: 18px;
                    font-weight: bold;
                    // color: var(--text-color);
                    margin-bottom: 20px;
                    padding-left: 20px;
                    opacity: 0;
                }
                .shop_content{
                    min-height: calc(100vh - 200px);
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    .content_center_wrap{
                        flex: 1;
                        height: calc(100% - 50px);
                        display: flex;
                        justify-content: space-between;
                        .sswr{
                            
                            // min-width: 450px;
                            width: 50%;
                            // margin: 0 20px;
                            box-sizing: border-box;
                            padding: 20px 10px 10px 10px;
                            border: 2px solid var(--dev-border);
                            font-size: 14px;
                            border-radius: 20px;
                            display: flex;
                            flex-direction: column;
                            height: 100%;
                            box-sizing: border-box;
                            .title{
                                font-size: 16px;
                                font-weight: bold;
                            }
                            .table_content{
                                flex:1;
                                overflow-y: hidden;
                                .table_header{
                                    height: 50px;
                                    box-sizing: border-box;
                                    padding-right: 5px;
                                    .header_row{
                                        width: 100%;
                                        height: 50px;
                                        display: flex;
                                        align-items: center;
                                    }
                                }
                                .table_body{
                                    height: calc(100% - 50px);
                                    overflow-y: auto;
                                    .body_row{
                                        width: 100%;
                                        height: 50px;
                                        display: flex;
                                        align-items: center;
                                    }
                                }
                                .header_row,.body_row{
                                    border-bottom: 1px solid var(--gray);
                                    .cell{
                                        text-align: center;
                                        flex: 1;
                                        &:first-child{
                                            flex: 0 0 45px;
                                        }
                                    }
                                }
                                ::v-deep .el-checkbox__inner{
                                    width: 18px;
                                    height: 18px;
                                    &::after{
                                        height: 10px;
                                        width: 4px;
                                        left: 6px;
                                    }
                                }
                            }
                            .footer{
                                height: 50px;
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;
                                box-sizing: border-box;
                                margin-top: 10px;
                            }
                        }
                        .shop_unselected{
                            margin-right: 30px;
                            .filter_content{
                                margin-top: 20px;
                                margin-bottom: 10px;
                                display: flex;
                                flex-wrap: wrap;
                                & > div{
                                    margin-right: 20px;
                                    margin-bottom: 10px;
                                }
                            }
                        }
                        .shop_selected{
                            .title{
                                margin-bottom: 20px;
                            }
                        }
                    }
                    
                    
                    .btns{
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-end;
                        padding-right: 30px;
                    }
                }
                
            }
        }
    }
</style>
