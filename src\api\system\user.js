import request from '@/utils/request'
import { post, get, uploadFile } from '@/utils/request'
import { encrypt } from '@/utils/rsaEncrypt'

export function usermgmt(act, username, phone, email, userPass) {
  var params = {
    'act': act, // 必要,操作类型，"add": 新增菜单；"edit":编辑菜单，"del":删除菜单，删除时只有参数"id"必须
    'username': username, // 必要,用户名标题
    // "nickName": //非必要,昵称
    'phone': phone, // 必要,手机号
    'email': email, // 必要,邮箱
    'signinPasswd': userPass, // 非必要,密码，默认"Dmb1q2w3e"
    'dept': { 'id': 11 }, // 必要,部门ID
    'gender': 女 | 男, // 非必要
    'roles': [{ 'id': 1, 'id': 2 }], // 非必要,是否外部链接
    'enabled': 1, // bool, 非必要,状态，是否可见
    'id': 11 // 非必要,用户身份id
  }
  return post('/dmb/api/json', params, 'usermgmt')
}
export function add(params) {
  params['act'] = 'add'
  if (params['enabled'] == 'true') {
     params['enabled'] = true
  } else {
     params['enabled'] = false
  }
  // params["dept"]["id"] = params["dept"]["id"].split("g")[1]
  delete params['id']
  return post('/dmb/api/json', params, 'usermgmt')
}

export function del(ids) {
  const params = {
    'act': 'del',
    'id': ids[0]
  }
  return post('dmb/api/json', params, 'usermgmt')
}

export function edit(params) {
  params['act'] = 'edit'
  if (params['enabled'] == 'true') {
    params['enabled'] = true
 } else {
    params['enabled'] = false
 }
  return post('dmb/api/json', params, 'usermgmt')
}

export function editUser(data) {
  return request({
    url: 'api/users/center',
    method: 'put',
    data
  })
}

export function updatePass(user) {
  const data = {
    oldPass: encrypt(user.oldPass),
    newPass: encrypt(user.newPass)
  }
  return request({
    url: 'api/users/updatePass/',
    method: 'post',
    data
  })
}

export function updateEmail(form) {
  const data = {
    password: encrypt(form.pass),
    email: form.email
  }
  return request({
    url: 'api/users/updateEmail/' + form.code,
    method: 'post',
    data
  })
}

export default { add, edit, del }

