<template>
    <div class="video_resources" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <div class="content" :style="{ minHeight: '700px' }">
            <!-- 搜索区 -->
            <div class="content_header flex">
                <!-- 左侧筛选条件 -->
                <div class="content_search flex flex-1">
                    <el-input placeholder="请输入视频名称" clearable size="small" prefix-icon="el-icon-search"
                        v-model="queryList.fileName" style="width:170px;margin-right:7px"
                        @keyup.enter.native="handleSearch"></el-input>
                    <el-select v-model="queryList.tagName" multiple clearable placeholder="请选择标签" size="small"
                        style="width:170px;margin-right:7px">
                        <el-option v-for="item in tagsList" :key="item.name" :label="item.name" :value="item.name">
                        </el-option>
                    </el-select>
                    <el-select v-model="queryList.type" clearable placeholder="审核状态" size="small"
                        v-show="canExamineShow" style="width:170px;margin-right:7px">
                        <el-option v-for="item in examineTypeList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <div class="search_btn" @click="handleSearch"><span>搜索</span></div>
                    <div class="search_btn" @click="handleResert"><span>重置</span></div>
                </div>
                <!-- 右侧切换格子/列表 -->
                <div class="content_tabs flex">
                    <div class="tabs_btn" @click="contentShow = 0" :class="contentShow == 0 ? 'tabs_checked' : ''">
                        <span>格子</span>
                    </div>
                    <div class="tabs_btn" style="margin-left:-1px" @click="contentShow = 1"
                        :class="contentShow == 1 ? 'tabs_checked' : ''"><span>列表</span></div>
                </div>
            </div>
            <!-- 内容区 -->
            <div class="video_content flex-1">
                <!-- 格子 -->
                <div class="lattice flex" v-show="contentShow == 0">
                    <div class="lattice_header">
                        <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox> 全选
                    </div>
                    <div class="lattice_content flex flex-1">
                        <div class="lattice_item " v-for="item in dataList" :key="item"
                            :style="{ 'height': canExamineShow ? '380px' : '360px' }">
                            <div class="item_image">
                                <img :src="item.thumb" style="width: 100%;height:100%;object-fit:contain" alt="">
                                <img src="../../assets/img/video_icon.png" class="video_icon" style="" alt="">
                                <div class="edit_mask flex">
                                    <div class="mask_btn" style="" @click="handleShow(item)">
                                        <p><i class="el-icon-view"></i></p>
                                        <p>预览</p>
                                    </div>
                                    <div class="mask_btn hidden_mask" @click="handleDelete(item)"
                                        v-if="isContent == false && checkPer([$store.state.user.roles[0], 'cm.res.del'])">
                                        <p><i class="el-icon-delete-solid"></i></p>
                                        <p>删除</p>
                                    </div>
                                </div>
                            </div>
                            <div class="up_date flex" :title="item.name" style="">
                                <div class="flex-1 txt_ellipsis" style="font-weight:bold">{{ item.name }}</div>
                                <el-checkbox v-model="item.checked" @change="checkOne(item)" :disabled='!canExamineShow && item.detect_suggestion != "pass"'></el-checkbox>
                            </div>
                            <div class="item_size">
                                视频时长：{{ item.duration }} s
                            </div>
                            <div class="item_size">
                                视频尺寸：{{ item.width }} × {{ item.height }}
                            </div>
                            <div class="item_size">
                                上传日期：{{ item.last_modified }}
                            </div>
                            <div class="item_status" v-if="canExamineShow">
                                状态：
                                <span class="examine_type" :style="getStyleWithStatus(item.pub_flow_status)">{{
                                    getoubFlowStatus(item.pub_flow_status) }}</span>
                                <span class="revoke" @click="handleDelete1(item)"
                                    v-if="item.pub_flow_status == 2 || item.pub_flow_status == 3">撤销</span>
                            </div>
                            <!-- v-if="canExamineShow" -->
                            <div class="item_status" >
                                安全监测是否通过：
                                <span class="examine_type" :style="getStyleWithPassStatus(item.detect_suggestion)">{{
                                    getoubPassStatus(item.detect_suggestion) }}</span>
                            </div>
                            <div class="item_tags flex" v-show="!item.isShowTage && item.tags.length > 0">
                                <div class="tags_wrap">
                                    <div class="each_tags flex" v-for="val in item.tags" :key="val">
                                        <img src="../../assets/img/tags.png" style="width:24px;height:24px">
                                        <span style="display: inline-block;height: 24px;line-height: 24px;">{{ val
                                            }}</span>
                                    </div>
                                </div>
                                <div class="show_more_tags" @click="showMoreTags(item)">
                                    <i class="el-icon-more"></i>
                                </div>
                            </div>
                            <div class="stow_tags" v-show="item.isShowTage && item.tags.length > 0">
                                <div class="more_tags_wrap">
                                    <div class="more_tags" style="">
                                        <div class="each_tags flex" v-for="val in item.tags" :key="val"
                                            style="margin:0 8px 10px 0;width:auto">
                                            <img src="../../assets/img/tags.png" style="width:24px;height:24px">
                                            <span
                                                style="display: inline-block;height: 24px;line-height: 24px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{
                                                    val
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                                <el-button type="text" @click="closeMoreTags(item)">收起</el-button>
                            </div>
                            <div class="item_tags flex" v-show="item.tags.length == 0"
                                style="padding-left:9px;margin-top:18px">
                                暂无标签
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 列表 -->
                <div class="lists" v-show="contentShow == 1">
                    <el-table :data="dataList" height="100%" ref="table_list" @selection-change="handleSelectionChange"
                        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px' }">
                        <el-table-column width="50" align="center">
                            <template slot="header">
                                <el-checkbox v-model="isAllChecked" @change="checkAll"></el-checkbox>
                            </template>
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.checked" @change="checkOne(scope.row)"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="img_url" label="图片预览" width="110" align="center">
                            <template slot-scope="scope">
                                <img :src="scope.row.thumb" style="width: 73px;height:73px;object-fit:contain" alt="">
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="视频名称" width="" show-overflow-tooltip="true" align="center">
                        </el-table-column>
                        <el-table-column prop="dimensions" label="大小" width="" show-overflow-tooltip="true"
                            align="center">
                            <template slot-scope="scope">
                                <div>{{ getVideoSize(scope.row.size) }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="dimensions" label="尺寸" width="" show-overflow-tooltip="true"
                            align="center">
                            <template slot-scope="scope">
                                <div>{{ scope.row.width }} × {{ scope.row.height }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="duration" label="时长" width="" show-overflow-tooltip="true"
                            align="center">
                            <template slot-scope="scope">
                                {{ scope.row.duration }} s
                            </template>
                        </el-table-column>
                        <el-table-column prop="tags" label="标签" width="" show-overflow-tooltip="true" align="center">
                            <template slot-scope="scope">
                                <div class="flex">
                                    <div v-for="(item, index) in scope.row.tags" :key="index"
                                        class="flex align-items-center" style="margin-right:2px">
                                        <img src="../../assets/img/tags.png" style="width:24px;height:24px;" alt="">
                                        <span>{{ item }}</span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="last_modified" label="上传日期" width="" show-overflow-tooltip="true"
                            align="center"></el-table-column>
                        <el-table-column prop="creator" label="账户" width="" show-overflow-tooltip="true" align="center">
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="130" align="center">
                            <template slot-scope="scope">
                                <el-button @click.native.prevent="handleShow(scope.row)" type="text"
                                    style="color:#409eff" size="small">预览</el-button>
                                <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:red"
                                    v-if="isContent == false && checkPer(['cm.res.del'])" size=" small">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <!-- 底部操作以及页码 -->
        <div class="footer flex" >
            <div class="flex" v-if="canExamineShow" v-show="batchState == false">
                <div class="btns btns_blue" style="background-color: var(--screen-color);" @click="batchAddTags">
                    <span>批量标签</span>
                </div>
                <div class="btns btns_red" @click="batchDelete">
                    <span>批量删除</span>
                </div>
            </div>
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5' :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
            </el-pagination>
        </div>

        <!-- 批量标签 -->
        <el-dialog title="标签管理" :visible.sync="tagsDialogVisible" :close-on-click-modal='false' width="697px"
            custom-class='batch_tags_dialog' :before-close="handleClose">

            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="添加标签" name="addtag">
                    <div class="relation_tags">
                        <div class="flex" style="align-items:center;height:50px;">
                            <span>添加标签：</span>
                            <el-input v-model="inputTag" style="width:300px" placeholder="请输入标签"></el-input>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="关联标签" name="relation">
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in tagsList" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active" :key="item.label">
                            <img src="../../assets/img/tags.png" alt="" style="width:24px;height:24px;margin-right:7px">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="删除标签" name="deltag">
                    <div class="relation_tags">
                        <div class="every_tag" v-for="item in delTagsList" :class="item.active ? 'tag_active' : ''"
                            @click="item.active = !item.active" :key="item.name">
                            <img src="../../assets/img/tags.png" alt="" style="width:24px;height:24px;margin-right:7px">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button style="background-color: var(--btn-background-color);color:#fff" @click="handleAdd">确
                    定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { get_video_list, del_content_files_by_id, create_tag_batch, delete_video_tags } from '@/api/files/videoResources'
import { get_tags_list } from '@/api/files/files'
export default {
    components: {

    },
    data() {
        return {
            p: '',
            loading: false,
            tagsDialogVisible: false, //批量标签弹框
            activeName: 'addtag',
            contentShow: 0, //显示的是列表还是格子
            currentPage: 1,  //页码
            totalNum: 0, //总数据数量
            pageSize: 10,//每页显示多少条
            queryList: {
                fileName: '',
                screenSize: '',
                market: '',
                tagName: [],
                type: ''
            },
            screenSizeList: [
                { value: '1920*1080', label: '横屏1920*1080' },
                { value: '1080*1920', label: '竖屏1080*1920' },
                { value: 'qita', label: '其他尺寸' },
            ],
            marketList: [
                { value: 'quanguo', label: '全国市场' },
                { value: 'shanghai', label: '上海市场' },
                { value: 'beijing', label: '北京市场' },
                { value: 'chengdu', label: '成都市场' },
            ],
            examineTypeList: [
                { value: 4, label: '已审核' },
                { value: 3, label: '待审核' },
                { value: 7, label: '驳回' }
            ],
            isAllChecked: false,
            dataList: [],
            tagsList: [],
            delTagsList: [],
            checkedList: [],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
            batchState: false,
            isContent: false,
            isDMB: false,
            tagTimer: '',
            inputTag: '',
            canExamineShow: false
        };
    },
    computed: {
        getoubFlowStatus() {
            return function (val) {
                switch (val) {
                    case 7:
                        return '驳回';
                    case 2:
                        return '待审核';
                    case 3:
                        return '待审核';
                    case 4:
                        return '通过审核';
                }
            }
        },
        getStyleWithStatus() {
            return function (val) {
                switch (val) {
                    case 7:
                        return 'color:var(--background-color)';
                    case 2:
                        return 'color:rgba(56, 56, 56, 1)';
                    case 3:
                        return 'color:rgba(56, 56, 56, 1)';
                    case 4:
                        return 'color:rgba(34, 181, 94, 1)';
                }
            }
        },
        getoubPassStatus() {
            return function (val) {
                if (val == 'pass') {
                    return '通过'
                } else if (val == 'submit') {
                    return '审核中'
                } else {
                    return '未通过'
                }
            }
        },
        getStyleWithPassStatus() {
            return function (val) {
                if (val == 'pass') {
                    return 'color:rgba(34, 181, 94, 1)';
                } else if (val == 'submit') {
                    return 'color:#999999';
                } else {
                    return 'color:var(--btn-danger-color)';
                }
            }
        }
    },
    watch: {

    },
    created() {
        this.canExamineShow = this.$route.path == '/contentCenter/srcfiles' || this.$route.path == '/deploy/gasket_management' ? true : false;
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getDataLsit();
        this.getTagsList();
    },
    mounted() {
    },
    methods: {
        // 搜索
        handleSearch() {
            this.currentPage = 1;
            this.getDataLsit();
        },
        handleResert() {
            this.currentPage = 1;
            this.queryList.fileName = '';
            this.queryList.tagName = [];
            this.queryList.screenSize = '';
            this.queryList.market = '';
            this.getDataLsit();
        },
        // 获取标签列表
        getTagsList() {
            let params = {
                user_id: localStorage.getItem('user_id'),
                tag_type: 'video'
            }
            let arr = []
            get_tags_list(params).then(res => {
                if (res.rst == 'ok') {
                    res.data[0].tags_list.forEach(item => {
                        // item.active = false;
                        arr.push({ name: item, active: false })
                    })
                    this.tagsList = JSON.parse(JSON.stringify(arr));
                    this.delTagsList = JSON.parse(JSON.stringify(arr));
                }
            })
        },
        // 获取列表数据
        getDataLsit() {
            this.loading = true;
            const params = {
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                tags: this.queryList.tagName.join('_'), // 用于标签查询
                filename: this.queryList.fileName, //视频名筛选,
                pub_flow_status: this.queryList.type
            }
            if (this.canExamineShow) {

            } else {
                params.pub_flow_status = 4
            }
            get_video_list(params).then(res => {
                if (res.rst == 'ok') {
                    // console.log(res.data[0].video_list.total);
                    // console.log(res.data[0].video_list);
                    this.totalNum = res.data[0].video_list.total;
                    res.data[0].video_list.video_list.forEach(item => {
                        item.checked = false;
                        item.isShowTage = false;
                    })
                    this.dataList = res.data[0].video_list.video_list;
                    this.loading = false;
                    this.isAllChecked = false;
                } else {
                    this.$message.error(res.error_msg);
                    this.loading = false;
                }
            })
            // this.loading = false;
        },
        handleSelectionChange(val) {
            // console.log(val);
        },
        // 全选
        checkAll() {
            this.dataList.forEach(item => {
                item.checked = this.isAllChecked
            })
            if (this.isAllChecked) {
                this.checkedList = JSON.parse(JSON.stringify(this.dataList))
            } else {
                this.checkedList = []
            }
            this.$emit("checkedList", this.checkedList)
        },
        //单选
        checkOne(data) {
            if (this.isContent == false) { // no 联屏
                this.checkedList = this.dataList.filter(item => item.checked)
                if (this.checkedList.length == this.dataList.length) {
                    this.isAllChecked = true;
                } else {
                    this.isAllChecked = false;
                }
                // add by Ethan
                this.$emit('checkedList', this.checkedList)
            } else if (this.isDMB == true) {
                this.checkedList = this.dataList.filter(item => item.checked)
                if (this.checkedList.length == this.dataList.length) {
                    this.isAllChecked = true;
                } else {
                    this.isAllChecked = false;
                }
                // add by Ethan
                this.$emit('checkedList', this.checkedList)
            } else {
                // console.log(data);
                // data.checked = true;
                this.dataList.forEach(item => {
                    item.checked = false
                })
                this.$set(data, "checked", true)
                this.isAllChecked = false;
                this.$emit('checkedList', [data])
            }
        },
        // 预览
        handleShow(val) {
            // this.PreviewMaskSrc = val.video_url;
            console.log(val.src_url, 'val.src_url');

            this.$emit('setVideoPreview', val.src_url)
        },
        // 编辑
        handleEdit(val) {
            this.$message.success('编辑')
            // console.log('编辑', val);
        },
        // 删除
        handleDelete(val) {
            this.$confirm('是否删除该内容?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                const params = { file_id: val.file_id }
                del_content_files_by_id(params).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success('删除成功');
                        this.getDataLsit();
                    } else {
                        this.$message.error(res.error_msg);
                        this.loading = false;
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        handleDelete1(val) {
            this.$confirm('是否撤销该内容?', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                const params = { file_id: val.file_id }
                del_content_files_by_id(params).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success('撤销成功');
                        this.getDataLsit();
                    } else {
                        this.$message.error(res.error_msg);
                        this.loading = false;
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消撤销'
                });
            });
        },
        // 批量标签
        batchAddTags() {
            if (this.checkedList.length == 0) {
                if (this.tagTimer) {
                    return
                }
                this.tagTimer = setTimeout(() => {
                    this.tagTimer = null
                }, 3000)
                this.$message.warning('请先选择需要关联标签的内容');
                return
            }
            this.tagsDialogVisible = true;
            // console.log(this.checkedList);
        },
        // 批量标签弹框确定按钮
        handleAdd() {
            if (this.activeName == 'addtag' && this.inputTag.trim() == '') {
                this.$message.warning('请先输入标签')
                return
            }
            let tagsArr = [];
            let operationType = '';
            let msg = '';

            if (this.activeName == 'addtag') {
                operationType = 'add';
                msg = '添加';
                tagsArr = [this.inputTag];
            } else if (this.activeName == 'relation') {
                operationType = 'add';
                msg = '关联';
                this.tagsList.forEach(item => {
                    if (item.active) {
                        tagsArr.push(item.name)
                    }
                })
            } else if (this.activeName == 'deltag') {
                operationType = 'del';
                msg = '删除';
                this.delTagsList.forEach(item => {
                    if (item.active) {
                        tagsArr.push(item.name)
                    }
                })
            }

            if (tagsArr.length == 0) {
                this.$message.warning('请先选择标签')
                return
            }

            // this.loading = true;
            const videoKeyList = this.checkedList.map(item => {
                if (item.checked) {
                    return item.file_id
                }
            })
            const params = {
                "video_list": videoKeyList, // (List) 视频id列表
                "tag_name": tagsArr, // (List) 图片tags列表
            }

            if (operationType == 'add') {
                create_tag_batch(params).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success(`${msg}标签成功`);
                        if (this.activeName == 'addtag') {
                            this.getTagsList();
                        }
                        this.resetDataList()
                    } else {
                        this.$message.error(`${msg}标签失败`);
                        this.loading = false;
                    }
                })
            } else if (operationType == 'del') {
                delete_video_tags(params).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success(`${msg}标签成功`);
                        this.resetDataList();
                    } else {
                        this.$message.error(`${msg}标签失败`);
                        this.loading = false;
                    }
                })
            }
            this.checkedList = [];
            this.handleClose();
        },
        resetDataList() {
            this.currentPage = 1;
            this.getDataLsit();
            this.isAllChecked = false;
            this.inputTag = '';
        },
        // 批量标签弹框取消按钮关闭弹框
        handleClose() {
            this.tagsDialogVisible = false;
            this.tagsList.forEach(item => item.active = false);
            this.delTagsList.forEach(item => item.active = false)
            this.activeName = 'addtag';
            this.inputTag = '';
        },
        // 批量删除
        batchDelete() {
            if (this.checkedList.length == 0) {
                this.$message.warning('请先选择需要删除的内容');
                return
            }
            this.$confirm('是否删除已选内容?', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                let arr = this.checkedList.map(item => {
                    return item.file_id
                })
                let params = { file_id: arr }
                del_content_files_by_id(params).then(res => {
                    if (res.rst == 'ok') {
                        this.$message.success('删除成功');
                        this.getDataLsit();
                    } else {
                        this.$message.error(res.error_msg);
                        this.loading = false;
                    }
                })
            }).catch(() => {
                this.$message.info('已取消删除');
            });

        },
        // 点击显示所有标签
        showMoreTags(val) {
            val.isShowTage = true;
        },
        //收起标签
        closeMoreTags(val) {
            val.isShowTage = false;
        },
        //页码改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getDataLsit()
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getDataLsit()
        },
        // 视频大小
        getVideoSize(val) {
            if (val < 1024 * 1024) {
                return `${(val / 1024).toFixed(2)}KB`
            } else if (val >= 1024 * 1024) {
                return `${(val / 1024 / 1024).toFixed(2)}MB`
            }
        },
        // 撤销
        revoke() {
            this.$message.info('那我可要狠狠的撤销你')
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 162 + 'px';
            this.autoHeight.heightNum = windowHeight - 162;
        },
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
        window.removeEventListener('keyup', this.closeScreenFull)
    },
};
</script>

<style scoped lang="scss">
* {
    box-sizing: border-box;
}

.video_resources {
    width: 100%;
    padding: 0 14px;
    background: #EEEEEE;
}

.content {
    /* border: 1px solid red; */
    display: flex;
    flex-direction: column;
}

.content_header {
    height: 67px;
}

.content_search {
    align-items: center;
}

.content_tabs {
    align-items: center;
}

.tabs_btn {
    border: 1px solid var(--btn-background-color);
    font-size: 14px;
    padding: 6px 15px;
    cursor: pointer;
    color: var(--text-color-light);
    background: #fff;
    white-space: nowrap;
}

.tabs_btn:hover {
    border: 1px solid var(--btn-background-color);
}

.tabs_checked {
    color: #fff;
    background: var(--btn-background-color);
}

.video_content {
    width: 100%;
    height: calc(100% - 67px);
    /* overflow-y:auto ; */
    /* border: 1px solid pink; */
}

.color_disc {
    width: 26px;
    height: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 50%;
    cursor: pointer;
    margin-right: 10px;
}

/* .color_disc_active {
    background: black !important;
} */

/* 格子 */
.lattice {
    height: 100%;
    flex-direction: column;
}

.lattice_header {
    width: 100%;
    height: 42px;
    line-height: 42px;
    background-color: var(--text-color-light);
    padding-left: 10px;
    font-size: 14px;
    color: #fff;
}

.lattice_content {
    flex-wrap: wrap;
    overflow-y: auto;
}

.lattice_item {
    width: 300px;
    height: 380px;
    margin-right: 16px;
    margin-top: 15px;
    font-size: 13px;
    color: rgba(56, 56, 56, 1);
    background-color: #fff;
}

.item_image {
    position: relative;
    width: 100%;
    height: 176px;
    padding: 3px;
}

.item_image:hover .edit_mask {
    display: flex;
}

.video_icon {
    position: absolute;
    width: 34px;
    height: 34px;
    top: 13px;
    right: 13px;
    object-fit: cover
}

.edit_mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 176px;
    background-color: rgba(0, 0, 0, 0.335);
    color: #fff;
    justify-content: center;
    align-items: center;
}

.mask_btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.564);
    border-radius: 50%;
    font-size: 11px;
    margin: 0 10px;
}

.mask_btn:hover {
    background-color: rgba(0, 0, 0, 0.464);
}

.each_tags {
    align-items: center;
    margin-right: 11px;
}

.up_date,
.item_size,
.item_tags {
    margin-top: 10px;
    align-items: center;
    padding: 0 9px;
}

.tags_wrap {
    display: flex;
    overflow: hidden;
    width: 250px;
}

.item_tags {
    margin-top: 13px;
    padding: 0 4px
}

/* 列表 */
.lists {
    width: 100%;
    height: 100%;
}

.footer {
    height: 72px;
    align-items: center;
    justify-content: space-between;
}

.txt_ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 弹框 */
.relation_tags {
    display: flex;
    flex-wrap: wrap;
    height: 279px;
    margin-bottom: 20px;
    padding: 20px;
    overflow-y: auto;
    border: 1px solid rgba(229, 229, 229, 1);
}

.every_tag {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 38px;
    font-size: 14px;
    border-radius: 24px;
    margin-right: 24px;
    margin-bottom: 18px;
    padding: 0 13px;
    border: 1px solid rgba(209, 209, 209, 1);
    cursor: pointer;
    /* 禁止文字选中 */
    /* -moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none; */
}

.tag_active {
    color: #fff;
    background-color: var(--text-color-light);
    border: 1px solid var(--text-color-light);
}

.show_more_tags {
    box-sizing: border-box;
    height: 24px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.show_more_tags:hover {
    color: #83B3EE;
}

.stow_tags {
    margin-top: 10px;
    padding: 0 9px;
    position: relative;
}

.more_tags_wrap {
    position: absolute;
    width: 305px;
    height: 140px;
    background: #fff;
    padding: 15px 5px 10px;
    top: -142px;
    left: 3px;
    /* max-height: 140px; */
    z-index: 200;
    border-radius: 5px;
    border: 1px solid #e5dfdf;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.135);
}

/* 第一个三角形颜色换成边框颜色 */
.more_tags_wrap::before {
    content: '';
    display: block;
    position: absolute;
    bottom: -7px;
    left: 8px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 7px solid #fff;
    z-index: 50;
}

/* 第二个三角形颜色换成背景色 */
.more_tags_wrap::after {
    content: '';
    display: block;
    position: absolute;
    bottom: -8px;
    left: 7px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 8px solid rgba(0, 0, 0, 0.135);
}

.more_tags {
    width: 100%;
    max-height: 100%;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    overflow-y: auto;
    flex-shrink: 0;
}

.item_status {
    margin-top: 10px;
    align-items: center;
    padding: 0 9px;

    .examine_type {}

    .revoke {
        color: #ff0000;
        cursor: pointer;
        margin-left: 22px;
    }

    .rerevokeject:hover {
        color: #f05959;
    }
}

.search_btn {
    height: 30px;
    top: 99px;
    color: #fff;
    background-color: var(--text-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    margin-top: -1px;
    padding: 0 20px;
    margin-left: 17px;
    white-space: nowrap;
}
</style>
<style>
/* 把element table的复选框改为红色 */
.video_resources .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: var(--base-color) !important;
    border-color: var(--base-color) !important;
}

.video_resources .el-checkbox__inner {
    /* border-color:red !important; */
    width: 18px;
    height: 18px;
    border-radius: 50%;
}

.video_resources .el-checkbox__inner::after {
    left: 6px !important;
    top: 3px !important;
}

.video_resources .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    left: 0px !important;
    top: 7px !important;
}

.video_resources .el-dialog__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.batch_tags_dialog {
    margin-top: 0px !important;
    border-radius: 16px !important;
}

.batch_tags_dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.batch_tags_dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
    font-weight: bold !important;
}

.batch_tags_dialog .el-tabs__header {
    margin: 0 !important;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 1px !important;
}

.batch_tags_dialog .el-tabs__item {
    height: 50px !important;
    line-height: 50px !important;
}

.batch_tags_dialog .el-button--primary {
    background: rgba(108, 178, 255, 1);
}

.video_resources .el-tabs__header {
    margin-bottom: 0 !important;
    width: 100% !important;
}

.video_resources .el-tabs__nav-scroll {
    padding-left: 50px;
}

/* tabs选中的样式 */
.files_management .video_resources .is-active {
    color: var(--text-color) !important;
    background-color: rgba(255, 255, 255, 0.3) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

.files_management .video_resources .el-tabs__nav {
    height: 45px;
}

.batch_tags_dialog .el-tabs__nav-wrap::after {
    height: 0px !important;
}
</style>
