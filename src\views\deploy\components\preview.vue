<template>
    <div class="preview">
        <!-- 主店 -->
        <!-- {{ lists }} -->
        <div class="table_wrap" :style="{ height: autoHeightShop.height }" v-loading='loadingDay'
            element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
            element-loading-spinner="el-icon-loading">
            <div class="shop_table flex" style="overflow:auto">
                <div class="empty" v-show="!hasData">
                    <img src="@/assets/img/empty.png" alt="" style="width:110px">
                    <span>暂无内容</span>
                </div>
                <table v-show="hasData">
                    <thead>
                        <tr>
                            <td>时段</td>
                            <td v-for="item in contentScreenInfon.screenInfo" :key="item.screen_id"
                                :colspan="item.all_displayinfo.length">
                                <span class="multiple_num scrren_h" v-for="dis in item.all_displayinfo"
                                    :key="dis.screen_id">{{ dis.displaynum }}</span>
                                <span class="mg-left-10">ID：{{ item.screen_id }}</span>
                            </td>
                        </tr>
                    </thead>
                    <!-- <tr>
                            <td>时段</td>
                            <td v-for="item in contentScreenInfon.screenInfo" :key="item.screen_id" :colspan="item.all_displayinfo.length">
                                <span class="multiple_num scrren_h" v-for="dis in item.all_displayinfo" :key="dis.screen_id">{{dis.displaynum}}</span>
                                <span class="mg-left-20">ID：{{item.screen_id}}</span>
                            </td>
                        </tr> -->
                    <tbody>
                        <tr v-for="item in lists" :key="item.name">
                            <td>
                                <p style="font-size:13px;margin-bottom:5px">{{ item.name }}</p>
                                <p style="font-size:13px">{{ item.time }}</p>
                            </td>
                            <td v-for="(items, index) in item.screen" :key="index"
                                :style="getWidth(item.screen.length)">
                                <div v-if="items.length > 0" class="flex img_td" style="position: relative;">
                                    <img :src="items[0] ? items[0]['thumb_list'][0].thumb_url : ''" alt=""
                                        style="width:100%;object-fit:contain;" class="content_image imgs_h">
                                    <span class="release_status release_suc"
                                        v-if="items[0] && items[0]['process'] >= 0.6" :style="tagPosition('12')">下发成功 <i
                                            class="el-icon-check"></i></span>
                                    <span class="release_status release_err"
                                        v-else-if="items[0] && items[0]['process'] < 0.6"
                                        :style="tagPosition('12')">下发失败
                                        !</span>
                                    <div class="td_mask">
                                        <div class="img_preview" @click="handlePreview(items)">
                                            <p>
                                                <i class="el-icon-view"></i>
                                            </p>
                                            <p>预览</p>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="flex"
                                    style="height:80px;position: relative;align-items:center;justify-content: center;"
                                    v-show="items.length == 0">
                                    暂无
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="export_btn_wrap">
                <!-- 暂时隐藏功能 -->
                <!-- <div class="export_btn" @click="handleExport">批量导出内容</div> -->
            </div>

            <!-- 预览mask -->
            <previsualization :isPreviewMaskShow='isPreviewMaskShow' :PreviewSrc='PreviewSrc' :PreviewType='PreviewType'
                @closePreviewMask='closePreviewMask' :carouselUrl="carouselUrl">
            </previsualization>
        </div>
    </div>
</template>
<script>
import { load_shop_content, get_adm_datas } from '@/api/shopManage/shop'
import previsualization from "@/components/communal/previsualization"
export default {
    components: {
        previsualization
    },
    data() {
        return {
            contentScreenInfon: {
                screenInfo: [],
            }, //主店内容
            queryList: {
                shopName: 'DGB045',
                screenType: '餐牌组',
                nowDate: '2022-06-16', //tabs为今天时的日期选择内容
                monthDate: '', //tabs为本月时的日期选择内容
            },
            // 
            lists: [

            ],
            // 自适应高
            autoHeightShop: {
                height: '',
                heightNum: '',
            },
            // loading
            loadingDay: true,
            hasData: false,
            isPreviewMaskShow: false,
            PreviewSrc: '',
            PreviewType: 'carousel',
            carouselUrl: 'thumb_url',
        }
    },
    methods: {
        // 获取数据
        getShopContent() {
            let params = {
                // "shop_id": "89569",
                // "shop_id": this.shopId,
                "store_code": this.queryList.shopName,
                "date_str": this.queryList.nowDate,
                "screen_type": this.queryList.screenType
            }
            load_shop_content(params).then(res => {
                if (res.rst == 'ok') {
                    if (this.isEmptyShow) {
                        this.isEmptyShow = false;
                    }
                    if (!res.data[0]['daypart_info'] || res.data[0]['daypart_info'].length == 0) {
                        // this.isEmptyShow = true;
                        this.hasData = false;
                        this.$message.warning('暂无数据')
                        this.loadingDay = false;
                        return
                    }
                    this.hasData = true;
                    this.contentScreenInfon.screenInfo = res.data[0].screen_info;
                    // console.log(this.contentScreenInfon.screenInfo);
                    this.formatContentData(res).then(val => {
                        console.log(val, "val");
                        this.lists = val;
                        this.loadingDay = false;
                        // let oImg = document.getElementsByClassName('imgs_h')
                        // console.log(oImg[0].offsetLeft,oImg[0].offsetTop,9999);
                        // this.tagsPosL = oImg[0].offsetLeft;
                        // this.tagsPosT = oImg[0].offsetTop;
                        this.resizePos()
                    })
                } else {
                    this.$message.warning(res.error_msg)
                    this.loadingDay = false
                }
            }).catch(e => {
                this.loadingDay = false;
            })
        },
        // 格式化接口数据
        formatContentData(data) {
            return new Promise((resolve, reject) => {
                let screen_list = []
                let screen_direct = {}
                data["data"][0]["screen_info"].sort((a, b) => {
                    return a.displaynum - b.displaynum
                })
                data["data"][0]["screen_info"].forEach(item => {
                    if (item["displaynuminfo"].length > 1) {
                        this.shopType = 0; //主店
                    } else {
                        // this.shopType = 1; //甜品店
                    }
                    item["all_displayinfo"].forEach(item => {

                        screen_list.push({ v_or_h: item.display.v_or_h, screen_id: item.screen_id })
                        screen_direct[item.screen_id] = item.display.v_or_h
                        // screen_list[item["displaynum"]-1] = item["screen_id"]

                    })
                })
                // console.log(screen_list);
                let screen_display_list = []
                let daypart_info = data["data"][0]["daypart_info"]
                let daypart_cf_info = data["data"][0]["daypart_cf_info"]
                daypart_info.forEach(item => {
                    screen = []
                    item.stime = item.stime.split(':')[0] + ':' + item.stime.split(':')[1]
                    item.etime = item.etime.split(':')[0] + ':' + item.etime.split(':')[1]
                    // item.stime = dayjs(item.tiems).format('HH:mm')
                    // item.etime = dayjs(item.eiems).format('HH:mm')
                    screen_list.forEach(items => {
                        // console.log(items);
                        let data_info = daypart_cf_info[String(items.screen_id)]
                        let cf_info = []
                        data_info.forEach(cf_item => {
                            if (cf_item["daypartname"] == item["daypartname"] && cf_item["cs_list"]) {
                                let cs_list = cf_item["cs_list"]
                                let process = cf_item["cf_bar"]["process"]
                                cf_info.push({ "thumb_list": cs_list, "process": process, "v_or_h": screen_direct[items.screen_id] })
                            }
                        })
                        screen.push(cf_info)
                    })
                    screen_display_list.push({ "name": item["daypartname"], "time": `${item["stime"]}-${item["etime"]}`, screen })
                })

                resolve(screen_display_list)
                // console.log(this.shopType, screen_list, screen_display_list)  
            })
        },
        resizePos() {
            this.$nextTick(() => {
                let oImg = document.getElementsByClassName('imgs_h')

                if (oImg.length > 0) {
                    if (oImg[0].offsetWidth == 0) {
                        setTimeout(() => {
                            this.resizePos()
                        }, 800);
                        return
                    }
                    this.tagsPosL = oImg[0].offsetLeft;
                    this.tagsPosT = oImg[0].offsetTop;
                    console.log(this.tagsPosL, this.tagsPosT, '定位');
                }
            })
        },
        // 店铺高度自适应
        getHeightShop() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeightShop.height = windowHeight - 77 + 'px';
            this.autoHeightShop.heightNum = windowHeight - 172;
        },
        getWidth(val) {
            // return `width:(calc(100% - 120px))/${val};max-width:(calc(100% - 120px))/${val};`
            let oTable = document.querySelector('.shop_table');
            let w = oTable.offsetWidth / val;
            let styleStr = `width:${w}px;max-width:${w}px`
            return styleStr
        },
        // 标签定位
        tagPosition(val) {
            return `left:${this.tagsPosL}px;top:${this.tagsPosT}ps`
        },
        // 预览
        handlePreview(cs_list) {
            let thumb_list = []
            cs_list.forEach(item => {
                thumb_list = thumb_list.concat(item.thumb_list)
            })
            this.PreviewSrc = thumb_list;
            this.isPreviewMaskShow = true;
        },
        // 关闭预览mask
        closePreviewMask() {
            this.isPreviewMaskShow = false;
            this.PreviewSrc = '';
        },
    },
    created() {
        this.getShopContent()
        window.addEventListener('resize', this.getHeightShop);
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeightShop);
        window.removeEventListener('resize', this.resizePos);
        window.addEventListener('resize', this.resizePos)
    },
}
</script>
<style lang="scss" scoped>
.table_wrap {
    width: 100%;
}

.preview {
    height: 100%;
}

.multiple_num {
    display: inline-block;
    /* width: 32px;
        height: 21px; */
    box-sizing: border-box;
    text-align: center;
    font-size: 12px;
    border: var(--text-color-light);
    margin-right: -1px;
    color: #fff;
    background-color: var(--text-color-light)
}

.scrren_h {
    display: inline-block;
    width: 32px;
    height: 21px;
    line-height: 21px;
}

.screen_v {
    display: inline-block;
    width: 18px;
    height: 25px;
    line-height: 25px;
}

.release_status {
    position: absolute;
    top: 0;
    left: 0;
    width: 90px;
    height: 23px;
    line-height: 23px;
    color: #fff;
    padding-right: 10px;
    font-size: 10px;
    border-radius: 0 20px 20px 0;
}

.release_suc {
    background: rgba(37, 195, 101, 1);
}

.release_err {
    background: rgba(166, 166, 166, 1);
}

.shop_table {
    width: 100%;
    border-collapse: collapse;
    height: calc(100% - 100px);
    /* overflow-y: auto; */
    min-height: 158px;
    // max-width: 1708px;
    flex-direction: column;
}

.table_header {
    width: 100%;
    margin-bottom: -1px;
}

.header_tr {
    height: 59px;
    line-height: 59px;
}

.tr_colspan {
    width: calc((100% - 135px)/2);
    line-height: 59px;
    border: 1px solid rgba(236, 235, 235, 1);
    border-left: none;
}

.row_name {
    width: 135px;
    border: 1px solid rgba(236, 235, 235, 1);
}

.main_store_td {
    border: 1px solid rgba(236, 235, 235, 1);
}

.table_body {
    height: calc(100% - 59px);
    overflow-y: auto;
    border-bottom: 1px solid rgba(236, 235, 235, 1);
}

.table_cell_name {
    display: flex;
    flex-direction: column;
    width: 135px;
    /* height:156px; */
    border: 1px solid rgba(236, 235, 235, 1);
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: bold;
}

.table_cell {
    width: calc((100% - 135px)/4);
    /* height:156px; */
    border: 1px solid rgba(236, 235, 235, 1);
    border-left: none;
    text-align: center;
    position: relative;
    padding: 5px;
    overflow: hidden;
}

.table_cell:hover .table_cell_hover {
    display: block;
}

.table_cell_hover {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(56, 56, 56, 0.52);
}

.edit {
    display: flex;
    flex-direction: column;
    position: absolute;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    left: 50%;
    top: 50%;
    background: rgba(0, 0, 0, 0.46);
    transform: translateX(-30px) translateY(-30px);
    border-radius: 50%;
    cursor: pointer;
    color: #fff;
    font-size: 14px;

}

img[src=""] {
    opacity: 0;
}

/* 月历 */
.calendar {}

.export_btn_wrap {
    display: none;
    justify-content: flex-end;
    align-items: center;
    height: 95px;
    padding-right: 24px;
}

.export_btn {
    width: 132px;
    height: 30px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    background-color: rgba(108, 178, 255, 1);
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.export_btn:hover {
    background-color: rgba(108, 178, 255, .8);
}

/*修改滚动条样式*/
.table_body::-webkit-scrollbar {
    width: 0px;
    /* display: none; */
    height: 3px;
}

.table_body::-webkit-scrollbar-track {
    background: rgba(239, 239, 239, .5);
    border-radius: 2px;
}

.table_body::-webkit-scrollbar-thumb {
    background: rgba(191, 191, 191, .7);
    border-radius: 2px;
}

.table_body::-webkit-scrollbar-thumb:hover {
    background: rgba(191, 191, 191, 1);
}

/* 无内容 */
.empty {
    /* position: relative; */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100vh - 107px);
    /* min-height: 140px; */
}

.empty span {
    display: block;
    width: 110px;
    text-align: center;
    color: rgba(134, 134, 134, 1);
    font-size: 14px;
}

.shop_table {
    table {
        border-collapse: collapse;
        height: 100%;
        width: 100%;

        // border: 1px solid red;
        thead {
            td {
                height: 56px;
                padding: 0;
            }

            td:nth-of-type(1) {
                font-weight: bold;
            }
        }

        tr {
            td:nth-of-type(1) {
                width: 120px;
                max-width: 120px;
                font-weight: bold;
                padding: 0;
            }

            td {
                overflow: hidden;
            }
        }

        td {
            text-align: center;
            border: 1px solid rgba(198, 198, 198, 1);
            padding: 3px;
            position: relative;
        }

    }

    .img_td {
        position: relative;
    }

    .img_td:hover .td_mask {
        display: flex;
    }

    .td_mask {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, .335);
        color: #fff;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }

    .img_preview {
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        cursor: pointer;
        width: 48px;
        height: 48px;
        background-color: rgba(0, 0, 0, .564);
        border-radius: 50%;
        font-size: 11px;
    }
}

/* 预览mask */
.preview_mask {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    z-index: 9999;
    overflow-y: auto;
}

.preview_content {
    position: absolute;
    left: 50%;
    top: 50%;
    /* background: #fff; */
    width: 1920px;
    height: 1080px;
    transform: translate(-50%, -50%) scale(.5);

}

.close_preview_mask {
    position: absolute;
    right: 40px;
    top: 40px;
    height: 40px;
    width: 40px;
    font-size: 18px;
    text-align: center;
    border-radius: 50%;
    line-height: 40px;
    cursor: pointer;
    color: #fff;
    background: #606266;
}

.close_preview_mask:hover {
    /* color: rgba(0, 0, 0, 0.6); */
    background: #6d6d6e;
}
</style>