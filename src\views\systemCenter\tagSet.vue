<template>
    <div class="tag_set">
        <!-- 顶部tabs -->
        <div class="tag_set_header">
            <el-tabs v-model="activeTabName" @tab-click="handleChangeTab">
                <el-tab-pane v-for="item in tabsData" :label="item.label" :name="item.name" :key="item.name">
                    <component :is="item.com" v-if="activeTabName == item.name"></component>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import shopTags from "@/components/tagSet/shopTags";
import ScreenTags from "@/components/tagSet/screenTags.vue";
import ConnectTags from "@/components/tagSet/connectTags.vue";
import IntorTags from "@/components/tagSet/intorTags.vue";
import TagDayPart from "@/components/tagSet/tagDayPart.vue";
import ScreenTypeTags from "../../components/tagSet/ScreenTypeTags.vue";
import connectStrategy from "@/components/tagSet/connectStrategy.vue";

export default {
    components: {
        shopTags,
        ScreenTags,
        ConnectTags,
        IntorTags,
        TagDayPart,
        ScreenTypeTags,
        connectStrategy
    },
    data() {
        return {
            activeTabName: 'first',
            tabsData: [
                {
                    com: shopTags,
                    label: '门店标签',
                    name: "first"
                },
                {
                    com: ScreenTags,
                    label: '屏幕标签',
                    name: "second"
                },
                {
                    com: ConnectTags,
                    label: '联屏组标签',
                    name: "third"
                },
                // {
                //     com: IntorTags,
                //     label: 'Intor标签',
                //     name: "fourth"
                // },
                {
                    com: ScreenTypeTags,
                    label: '屏幕类型配置',
                    name: "fifth"
                },
                {
                    com: TagDayPart,
                    label: '经营时段设置',
                    name: "sixth"
                },
                {
                    com: connectStrategy,
                    label: '联屏切换策略',
                    name: "seventh"
                },
            ]
        };
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
        setTimeout(() => {
            this.shows = true
        }, 5000);
    },
    methods: {
        handleChangeTab(tab, event) {
            console.log(tab, event);
            console.log(event.name);
            // this.$refs.first.getHeight(); 
        }
    },

};
</script>

<style scoped>
.tag_set {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    position: relative;

    /* padding: 0 20px; */
    .is-active {
        padding-right: 20px !important;
    }
}
</style>
<style>
/* tabs选中的样式 */
.tag_set .is-active {
    color: var(--text-color) !important;
    background-color: var(--active-color) !important;
    /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.tag_set #tab-first {
    padding: 0 20px !important;
}

/* 给最后一个设置padding */
.tag_set #tab-sixth {
    padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
.tag_set .el-tabs__active-bar {
    /* display: none; */
    background-color: var(--text-color) !important;
}

/* tabs鼠标移入样式 */
.tag_set .el-tabs__item:hover {
    color: var(--text-color) !important;
}

/* tabs取消下边距 */
.tag_set .el-tabs__header {
    margin-bottom: 0 !important;
}
</style>
