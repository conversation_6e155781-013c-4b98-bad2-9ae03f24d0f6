import router from "./routers";
import store from "@/store";
import Config from "@/settings";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "@/utils/auth"; // getToken from cookie
import { buildMenus } from "@/api/system/menu";
import { filterAsyncRouter } from "@/store/modules/permission";
import { Message } from 'element-ui'
NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login", "/login_password", "/jump"]; // no redirect whitelist

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + " - " + Config.title;
  }
  NProgress.start();
  if (getToken()) {
    // 已登录且要跳转的页面是登录页
    if (to.path === "/login" || to.path === "/login_password" || to.path == '/jump') {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch("GetInfo")
          .then((res) => {
            if (res.data[0].curent_branch || (!res.data[0].curent_branch && res.data[0].ismgmtuser == 0)) {
              // loadMenus(next, to);
              loadMenus(next, to);
            } else {
              let p = {}
              let status = 0;
       
              if (!status && res.data[0].branch_list.length > 0) {
                p.group_id = res.data[0].branch_list[0].group_id;
                p.admin_uid = res.data[0].branch_list[0].admin_uid;
                p.branchcode = res.data[0].branch_list[0].branchcode;
              }
              store.dispatch('switchBranch', p).then(() => {
                store.dispatch('GetInfo').then(() => {
                  loadMenus(next, to);
                })
              })
            }
            // return
            // 拉取user_info
            // 动态路由，拉取菜单

          })
          .catch(() => {
            console.log('退出退出');
            Message({
              message: '登录过期，请重新登录',
              type: 'warning',
              duration: 1000,
              forbidClick: true
            })
            setTimeout(() => {
              store.dispatch("LogOut").then(() => {
                location.reload(); // 为了重新实例化vue-router对象 避免bug
              });
            }, 500);
          });
        // 登录时未拉取 菜单，在此处拉取
      } else if (store.getters.loadMenus) {
        // 修改成false，防止死循环
        store.dispatch("updateLoadMenus");
        loadMenus(next, to);
      } else {
        next();
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

export const loadMenus = (next, to) => {
  buildMenus().then(res => {
    // // console.log("res",JSON.stringify(res));
    res = {
      "rst": "ok",
      "req_method": "get_adm_menus",
      "data": [
        [
          {
            "redirect": "noredirect",
            "name": "门店管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "门店列表",
                "pid": 1,
                "component": "shopManage/shop",
                "meta": {
                  "title": "门店列表",
                  "noCache": false,
                  "icon": ""
                },
                "path": "storelist",
                "hidden": false,
                "type": 1,
                "id": 2
              },
              {
                "name": "门店详情",
                "pid": 1,
                "component": "shopManage/shopDetials",
                "meta": {
                  "title": "门店详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "shopDetials",
                "hidden": true,
                "type": 1,
                "id": 6
              },
              {
                "name": "远程巡店",
                "pid": 1,
                "component": "shopManage/framePreview",
                "meta": {
                  "title": "远程巡店",
                  "noCache": false,
                  "icon": ""
                },
                "path": "framePreview",
                "hidden": false,
                "type": 1,
                "id": 7
              },
              {
                "name": "新开店",
                "pid": 1,
                "component": "shopManage/openNewShop",
                "meta": {
                  "title": "新开店",
                  "noCache": false,
                  "icon": ""
                },
                "path": "openNewShop",
                "hidden": true,
                "type": 1,
                "id": 9
              },
              {
                "name": "画面编辑",
                "pid": 1,
                "component": "shopManage/frameEdit",
                "meta": {
                  "title": "画面编辑",
                  "noCache": false,
                  "icon": ""
                },
                "path": "frameEdit",
                "hidden": true,
                "type": 1,
                "id": 8
              },
              {
                "name": "新增开店",
                "pid": 1,
                "component": "shopManage/shopAddEdit",
                "meta": {
                  "title": "新增开店",
                  "noCache": false,
                  "icon": ""
                },
                "path": "shopAddEdit",
                "hidden": true,
                "type": 1,
                "id": 13
              }
            ],
            "meta": {
              "title": "门店管理",
              "noCache": false,
              "icon": "shop_icon"
            },
            "alwaysShow": false,
            "path": "/shopManage",
            "hidden": false,
            "type": 0,
            "id": 1
          },
          {
            "redirect": "noredirect",
            "name": "设备管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "屏幕管理",
                "pid": 14,
                "component": "deviceManage/screenGroup",
                "meta": {
                  "title": "屏幕管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "screenGroup",
                "hidden": false,
                "type": 1,
                "id": 15
              },
              {
                "name": "联屏列表",
                "pid": 14,
                "component": "screenGroup/ScreenGroup",
                "meta": {
                  "title": "联屏列表",
                  "noCache": false,
                  "icon": ""
                },
                "path": "vslist",
                "hidden": false,
                "type": 1,
                "id": 24
              },
              {
                "name": "屏幕详情",
                "pid": 14,
                "component": "deviceManage/screenDevice",
                "meta": {
                  "title": "屏幕详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "screenDetail",
                "hidden": true,
                "type": 1,
                "id": 21
              },
              {
                "name": "屏幕截图",
                "pid": 14,
                "component": "screenDevice/shotRecord",
                "meta": {
                  "title": "屏幕截图",
                  "noCache": false,
                  "icon": ""
                },
                "path": "shotRecord",
                "hidden": true,
                "type": 1,
                "id": 22
              },
              {
                "name": "远程桌面",
                "pid": 14,
                "component": "screenDevice/setRemoteDev",
                "meta": {
                  "title": "远程桌面",
                  "noCache": false,
                  "icon": ""
                },
                "path": "setRemoteDev",
                "hidden": true,
                "type": 1,
                "id": 23
              },
              {
                "name": "新建联动组",
                "pid": 14,
                "component": "screenGroup/newConnectScreen",
                "meta": {
                  "title": "新建联动组",
                  "noCache": false,
                  "icon": ""
                },
                "path": "newConnectScreen",
                "hidden": true,
                "type": 1,
                "id": 26
              },
              {
                "name": "联动详情",
                "pid": 14,
                "component": "screenGroup/connectDetail",
                "meta": {
                  "title": "联动详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "connectDetail",
                "hidden": true,
                "type": 1,
                "id": 27
              }
            ],
            "meta": {
              "title": "设备管理",
              "noCache": false,
              "icon": "screen_icon"
            },
            "alwaysShow": false,
            "path": "/deviceManage",
            "hidden": false,
            "type": 0,
            "id": 14
          },
          {
            "redirect": "noredirect",
            "name": "内容管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "资源管理",
                "pid": 28,
                "component": "contentCenter/files",
                "meta": {
                  "title": "资源管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "srcfiles",
                "hidden": false,
                "type": 1,
                "id": 29
              },
              {
                "name": "内容制作",
                "pid": 28,
                "component": "contentCenter/contentPlan",
                "meta": {
                  "title": "内容制作",
                  "noCache": false,
                  "icon": ""
                },
                "path": "/contentCenter",
                "hidden": false,
                "type": 1,
                "id": 33
              },
              {
                "name": "内容调整",
                "pid": 28,
                "component": "speedyTool/speedyTool",
                "meta": {
                  "title": "内容调整",
                  "noCache": false,
                  "icon": ""
                },
                "path": "tool",
                "hidden": true,
                "type": 1,
                "id": 37
              },
              {
                "name": "资源审核",
                "pid": 28,
                "component": "auditManage/resourceAudit/resourceAudit",
                "meta": {
                  "title": "资源审核",
                  "noCache": false,
                  "icon": ""
                },
                "path": "resourceAudit",
                "hidden": false,
                "type": 1,
                "id": 38
              }
            ],
            "meta": {
              "title": "内容管理",
              "noCache": false,
              "icon": "content_icon"
            },
            "alwaysShow": false,
            "path": "/contentCenter",
            "hidden": false,
            "type": 0,
            "id": 28
          },
          {
            "redirect": "noredirect",
            "name": "发布管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "新建投放",
                "pid": 39,
                "component": "deploy/setType",
                "meta": {
                  "title": "新建投放",
                  "noCache": false,
                  "icon": ""
                },
                "path": "newquick",
                "hidden": false,
                "type": 1,
                "id": 40
              },
              {
                "name": "快捷发布",
                "pid": 39,
                "component": "deploy/quickDeploy",
                "meta": {
                  "title": "快捷发布",
                  "noCache": false,
                  "icon": ""
                },
                "path": "quickDeploy",
                "hidden": false,
                "type": 1,
                "id": 42
              },
              {
                "name": "投放历史",
                "pid": 39,
                "component": "deploy/quickHistory",
                "meta": {
                  "title": "投放历史",
                  "noCache": false,
                  "icon": ""
                },
                "path": "quickHistory",
                "hidden": false,
                "type": 1,
                "id": 43
              },
              {
                "name": "发布审核",
                "pid": 39,
                "component": "auditManage/publishAudit/publishAudit",
                "meta": {
                  "title": "发布审核",
                  "noCache": false,
                  "icon": ""
                },
                "path": "publishAudit",
                "hidden": true,
                "type": 1,
                "id": 45
              },
              {
                "name": "垫片管理",
                "pid": 39,
                "component": "deploy/gasket_management",
                "meta": {
                  "title": "垫片管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "gasket_management",
                "hidden": false,
                "type": 1,
                "id": 102
              },
              {
                "name": "模板预览",
                "pid": 39,
                "component": "deploy/templateManage",
                "meta": {
                  "title": "模板预览",
                  "noCache": false,
                  "icon": ""
                },
                "path": "templateManage",
                "hidden": false,
                "type": 1,
                "id": 102
              },
              {
                "name": "模板管理",
                "pid": 39,
                "component": "cardManage/cardManage",
                "meta": {
                  "title": "模板管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "cardManage",
                "hidden": false,
                "type": 1,
                "id": 104
              },
              {
                "name": "新增/编辑内容",
                "pid": 39,
                "component": "CardContent/AddContentCard",
                "meta": {
                  "title": "新增内容",
                  "noCache": false,
                  "icon": ""
                },
                "path": "AddContentCard",
                "hidden": true,
                "type": 1,
                "id": 106
              },
              {
                "name": "引用计划列表",
                "pid": 39,
                "component": "cardManage/ReferencePlan",
                "meta": {
                  "title": "引用计划列表",
                  "noCache": false,
                  "icon": ""
                },
                "path": "ReferencePlan",
                "hidden": true,
                "type": 1,
                "id": 105
              },
              {
                "name": "关联卡片",
                "pid": 39,
                "component": "deployAssociatedContent/AssociatedContent",
                "meta": {
                  "title": "关联卡片",
                  "noCache": false,
                  "icon": ""
                },
                "path": "associatedContent",
                "hidden": true,
                "type": 1,
                "id": 103
              },
              {
                "name": "中台推送",
                "pid": 39,
                "component": "deploy/deployHist",
                "meta": {
                  "title": "中台推送",
                  "noCache": false,
                  "icon": ""
                },
                "path": "deployHist",
                "hidden": true,
                "type": 1,
                "id": 66
              },
              {
                "name": "卡片编辑",
                "pid": 39,
                "component": "deploy/useShortEdit",
                "meta": {
                  "title": "卡片编辑",
                  "noCache": false,
                  "icon": ""
                },
                "path": "contentedit",
                "hidden": true,
                "type": 1,
                "id": 48
              },
              {
                "name": "内容编辑",
                "pid": 39,
                "component": "deploy/hisEdit",
                "meta": {
                  "title": "内容编辑",
                  "noCache": false,
                  "icon": ""
                },
                "path": "hisedit",
                "hidden": true,
                "type": 1,
                "id": 49
              },
              {
                "name": "发布失败",
                "pid": 39,
                "component": "deploy/pubFail",
                "meta": {
                  "title": "发布失败",
                  "noCache": false,
                  "icon": ""
                },
                "path": "pubfail",
                "hidden": true,
                "type": 1,
                "id": 50
              },
              {
                "name": "选择内容",
                "pid": 39,
                "component": "deployCom/setTypePlayer",
                "meta": {
                  "title": "选择内容",
                  "noCache": false,
                  "icon": ""
                },
                "path": "settype",
                "hidden": true,
                "type": 1,
                "id": 51
              },
              {
                "name": "发布设置",
                "pid": 39,
                "component": "deployCom/choiceStore",
                "meta": {
                  "title": "发布设置",
                  "noCache": false,
                  "icon": ""
                },
                "path": "choiceStore",
                "hidden": true,
                "type": 1,
                "id": 52
              },
              {
                "name": "浮层设置",
                "pid": 39,
                "component": "deployCom/floatEle",
                "meta": {
                  "title": "浮层设置",
                  "noCache": false,
                  "icon": ""
                },
                "path": "floatEle",
                "hidden": true,
                "type": 1,
                "id": 53
              },
              {
                "name": "发布状态",
                "pid": 39,
                "component": "deploy/pubDetail",
                "meta": {
                  "title": "发布状态",
                  "noCache": false,
                  "icon": ""
                },
                "path": "pubdetail",
                "hidden": true,
                "type": 1,
                "id": 54
              },
              {
                "name": "发布屏幕详情",
                "pid": 39,
                "component": "deploy/pushScreen",
                "meta": {
                  "title": "发布屏幕详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "pushscreen",
                "hidden": true,
                "type": 1,
                "id": 55
              },
              {
                "name": "发布内容查看",
                "pid": 39,
                "component": "deploy/contentView",
                "meta": {
                  "title": "发布内容查看",
                  "noCache": false,
                  "icon": ""
                },
                "path": "contentview",
                "hidden": true,
                "type": 1,
                "id": 56
              },
              {
                "name": "ml发布状态",
                "pid": 39,
                "component": "deploy/mlPubDetail",
                "meta": {
                  "title": "ml发布状态",
                  "noCache": false,
                  "icon": ""
                },
                "path": "mlpubdetail",
                "hidden": true,
                "type": 1,
                "id": 57
              },
              {
                "name": "ml发布屏幕详情",
                "pid": 39,
                "component": "deploy/mlPushScreen",
                "meta": {
                  "title": "ml发布屏幕详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "mlPushScreen",
                "hidden": true,
                "type": 1,
                "id": 58
              },
              {
                "name": "ml发布内容查看",
                "pid": 39,
                "component": "deploy/mlContentView",
                "meta": {
                  "title": "ml发布内容查看",
                  "noCache": false,
                  "icon": ""
                },
                "path": "mlContentView",
                "hidden": true,
                "type": 1,
                "id": 59
              },
              {
                "name": "编辑投放",
                "pid": 39,
                "component": "deploy/deployEdit",
                "meta": {
                  "title": "编辑投放",
                  "noCache": false,
                  "icon": ""
                },
                "path": "editHistory",
                "hidden": true,
                "type": 1,
                "id": 60
              },
              {
                "name": "DMB编辑投放",
                "pid": 39,
                "component": "deployComponent/editDmbTemplate",
                "meta": {
                  "title": "DMB编辑投放",
                  "noCache": false,
                  "icon": ""
                },
                "path": "editDMB",
                "hidden": true,
                "type": 1,
                "id": 61
              },
              {
                "name": "快捷发布编辑",
                "pid": 39,
                "component": "strategy/quickDpEdit",
                "meta": {
                  "title": "快捷发布编辑",
                  "noCache": false,
                  "icon": ""
                },
                "path": "quickDpEdit",
                "hidden": true,
                "type": 1,
                "id": 62
              },
              {
                "name": "快捷发布查看",
                "pid": 39,
                "component": "strategy/quickDpSee",
                "meta": {
                  "title": "快捷发布查看",
                  "noCache": false,
                  "icon": ""
                },
                "path": "quickDpSee",
                "hidden": true,
                "type": 1,
                "id": 63
              },
              {
                "name": "新建策略",
                "pid": 39,
                "component": "strategy/newStrategy",
                "meta": {
                  "title": "新建策略",
                  "noCache": false,
                  "icon": ""
                },
                "path": "openNewStrategy",
                "hidden": true,
                "type": 1,
                "id": 64
              },
              {
                "name": "使用策略",
                "pid": 39,
                "component": "strategy/useStrategy",
                "meta": {
                  "title": "使用策略",
                  "noCache": false,
                  "icon": ""
                },
                "path": "useStrategy",
                "hidden": true,
                "type": 1,
                "id": 65
              }
            ],
            "meta": {
              "title": "发布管理",
              "noCache": false,
              "icon": "deploy_icon"
            },
            "alwaysShow": false,
            "path": "/deploy",
            "hidden": false,
            "type": 0,
            "id": 39
          },
          {
            redirect: "noredirect",
            name: "商品管理",
            pid: null,
            component: "Layout",
            id: 9,
            meta: {
              title: "商品管理",
              noCache: false,
              icon: "shop_icon"
            },
            alwaysShow: false,
            path: "/goodsManage",
            hidden: false,
            children:[
                {
                    name: "商品管理",
                    pid: 9,
                    component: "goodsManage/ProductCenter",
                    meta: {
                      title: "商品管理",
                      noCache: false,
                      icon: ""
                    },
                    path: "product_center",
                    hidden: false,
                    id: 95
                  },
                  {
                    name: "商品概况",
                    pid: 9,
                    component: "goodsManage/GoodsSellOut",
                    meta: {
                      title: "商品概况",
                      noCache: false,
                      icon: ""
                    },
                    path: "goods_sell_out",
                    hidden: true,
                    id: 97
                  },
                  {
                    name: "商品价格",
                    pid: 9,
                    component: "goodsManage/GoodsPrice",
                    meta: {
                      title: "商品价格",
                      noCache: false,
                      icon: ""
                    },
                    path: "goods_price",
                    hidden: true,
                    id: 97
                  },
            ]
          },
          {
            "redirect": "noredirect",
            "name": "系统管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "组织架构",
                "pid": 67,
                "component": "systemCenter/structure",
                "meta": {
                  "title": "组织架构",
                  "noCache": false,
                  "icon": ""
                },
                "path": "structure",
                "hidden": false,
                "type": 1,
                "id": 68
              },
              {
                "name": "用户管理",
                "pid": 67,
                "component": "system/user/index",
                "meta": {
                  "title": "用户管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "user",
                "hidden": false,
                "type": 1,
                "id": 72
              },
              {
                "name": "数据范围",
                "pid": 67,
                "component": "systemCenter/dataField",
                "meta": {
                  "title": "数据范围",
                  "noCache": false,
                  "icon": ""
                },
                "path": "dataField",
                "hidden": false,
                "type": 1,
                "id": 76
              },
              {
                "name": "新增数据",
                "pid": 67,
                "component": "systemCenter/newDataFiled",
                "meta": {
                  "title": "新增数据",
                  "noCache": false,
                  "icon": ""
                },
                "path": "newdatafiled",
                "hidden": true,
                "type": 1,
                "id": 78
              },
              {
                "name": "角色管理",
                "pid": 67,
                "component": "system/role/index",
                "meta": {
                  "title": "角色管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "role",
                "hidden": false,
                "type": 1,
                "id": 79
              },
              {
                "name": "菜单管理",
                "pid": 67,
                "component": "system/menu/index",
                "children": [
                  {
                    "name": "添加菜单",
                    "pid": 84,
                    "component": "",
                    "meta": {
                      "title": "添加菜单",
                      "noCache": false,
                      "icon": ""
                    },
                    "path": "",
                    "hidden": true,
                    "type": 2,
                    "id": 85
                  },
                  {
                    "name": "删除菜单",
                    "pid": 84,
                    "component": "",
                    "meta": {
                      "title": "删除菜单",
                      "noCache": false,
                      "icon": ""
                    },
                    "path": "",
                    "hidden": true,
                    "type": 2,
                    "id": 86
                  },
                  {
                    "name": "编辑菜单",
                    "pid": 84,
                    "component": "",
                    "meta": {
                      "title": "编辑菜单",
                      "noCache": false,
                      "icon": ""
                    },
                    "path": "",
                    "hidden": true,
                    "type": 2,
                    "id": 87
                  }
                ],
                "meta": {
                  "title": "菜单管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "menu",
                "hidden": true,
                "type": 1,
                "id": 84
              },
              {
                "name": "预警机制",
                "pid": 67,
                "component": "systemCenter/alert",
                "meta": {
                  "title": "预警机制",
                  "noCache": false,
                  "icon": ""
                },
                "path": "alertPolicy",
                "hidden": false,
                "type": 1,
                "id": 88
              },
              {
                "name": "升级管理",
                "pid": 67,
                "component": "systemCenter/updateHist",
                "meta": {
                  "title": "升级管理",
                  "noCache": false,
                  "icon": ""
                },
                "path": "updateHist",
                "hidden": false,
                "type": 1,
                "id": 89
              },
              {
                "name": "操作日志",
                "pid": 67,
                "component": "systemCenter/operationLog",
                "meta": {
                  "title": "操作日志",
                  "noCache": false,
                  "icon": ""
                },
                "path": "syslog",
                "hidden": false,
                "type": 1,
                "id": 94
              },
              {
                "name": "标签配置",
                "pid": 67,
                "component": "systemCenter/tagSet",
                "meta": {
                  "title": "标签配置",
                  "noCache": false,
                  "icon": ""
                },
                "path": "tag",
                "hidden": false,
                "type": 1,
                "id": 95
              },
              {
                "name": "升级计划详情",
                "pid": 67,
                "component": "systemCenter/upgradeDetails",
                "meta": {
                  "title": "升级计划详情",
                  "noCache": false,
                  "icon": ""
                },
                "path": "upgradeDetails",
                "hidden": true,
                "type": 1,
                "id": 92
              },
              {
                "name": "升级计划区域",
                "pid": 67,
                "component": "systemCenter/upgradeCity",
                "meta": {
                  "title": "升级计划区域",
                  "noCache": false,
                  "icon": ""
                },
                "path": "upgradeCity",
                "hidden": true,
                "type": 1,
                "id": 93
              }
            ],
            "meta": {
              "title": "系统管理",
              "noCache": false,
              "icon": "system_icon"
            },
            "alwaysShow": false,
            "path": "/systemCenter",
            "hidden": false,
            "type": 0,
            "id": 67
          },
          {
            "redirect": "noredirect",
            "name": "直播管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "设备列表",
                "pid": 96,
                "component": "LiveManage/LiveDeviceManage",
                "meta": {
                  "title": "设备列表",
                  "noCache": false,
                  "icon": ""
                },
                "path": "liveManage/LiveDeviceManage",
                "hidden": false,
                "type": 1,
                "id": 97
              },
              {
                "name": "直播设置",
                "pid": 96,
                "component": "LiveManage/LiveManage",
                "meta": {
                  "title": "直播设置",
                  "noCache": false,
                  "icon": ""
                },
                "path": "LiveManage/liveSetting",
                "hidden": false,
                "type": 1,
                "id": 98
              }
            ],
            "meta": {
              "title": "直播管理",
              "noCache": false,
              "icon": "live_icon"
            },
            "alwaysShow": false,
            "path": "/LiveManage",
            "hidden": false,
            "type": 0,
            "id": 96
          },
          {
            "redirect": "noredirect",
            "name": "应急管理",
            "pid": null,
            "component": "Layout",
            "children": [
              {
                "name": "预设待机",
                "pid": 99,
                "component": "emergencyManage/emergencyButton",
                "meta": {
                  "title": "预设待机",
                  "noCache": false,
                  "icon": ""
                },
                "path": "emergencyButton",
                "hidden": false,
                "type": 1,
                "id": 100
              },
              {
                "name": "覆盖设备",
                "pid": 99,
                "component": "emergencyManage/emergencyCover",
                "meta": {
                  "title": "覆盖设备",
                  "noCache": false,
                  "icon": ""
                },
                "path": "emergencyCover",
                "hidden": false,
                "type": 1,
                "id": 101
              }
            ],
            "meta": {
              "title": "应急管理",
              "noCache": false,
              "icon": "emergency_icon"
            },
            "alwaysShow": false,
            "path": "/emergencyManage",
            "hidden": false,
            "type": 0,
            "id": 99
          }
        ]
      ],
      "system_time": "1732604737.295",
      "req_id": "4061"
    }
    // console.log(res["data"][0],'xxxx');
    res = res["data"][0];

    // console.log("res", res);
    const sdata = JSON.parse(JSON.stringify(res));
    const rdata = JSON.parse(JSON.stringify(res));
    const sidebarRoutes = filterAsyncRouter(sdata);
    const rewriteRoutes = filterAsyncRouter(rdata, false, true);
    // rewriteRoutes.push({ path: "*", redirect: "/404", hidden: true });
    rewriteRoutes.push({ path: "*", redirect: "/dashboard", hidden: true });

    store.dispatch("GenerateRoutes", rewriteRoutes).then(() => {
      // 存储路由
      router.addRoutes(rewriteRoutes); // 动态添加可访问路由表
      // console.log("to", to)
      next({ ...to, replace: true });
    });
    store.dispatch("SetSidebarRouters", sidebarRoutes);
  });
};

router.afterEach(() => {
  NProgress.done(); // finish progress bar
});
