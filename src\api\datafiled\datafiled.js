import { get, post } from "@/utils/request";

/**
 * @description 获取数据范围列表
 * @param classModel DataRangeTpl
 * @param sort 非必要，排序规则，id,desc
 * @param createdAtS 非必要，查询创建时间开始日期
 * @param createdAtE 非必要，非必要，查询创建时间结束日期
 * @param page 起始页码
 * @param size 每页数据量
 * @param blurry 每页数据量
 */
const get_data_filed = p => post("/dmb/api/json", p, "get_adm_datas");

/**
 * @description 数据范围增删改
 * @param act 必要,操作类型，"add": 新增；"upt":更新，"del":删除"id"必须
 * @param name 必要,用户名标题
 * @param id 非必要,数据范围身份id
 * @param dataAct 必要,操作类型，"new": 新增数据范围；"upt":更新数据范围，"del":删除数据范围"id"必须
 * @param range_shops[],//非必要餐厅id shop_id
 */
const add_upt_del_data = p => post("/dmb/api/json", p, "datastplmgmt");

/**
 * @description 获取数据范围详情
 * @id
 */
const get_data_detail = p => post("/dmb/api/json", p, "get_adm_datas");

export { get_data_filed, add_upt_del_data, get_data_detail };
