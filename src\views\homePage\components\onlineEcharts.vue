<template>
    <!-- <div class="onlineEcharts" > -->
    <div id="onlineEcharts" style="width:100%; height: 100%;"></div>
    <!-- </div> -->
</template>

<script>
import echarts from "echarts";

import { debounce } from "@/utils";

export default {
    name: "onlineEcharts",
    components: {},
    props: {
        echartsList: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            detailData: [],
            chart: {}
        };
    },
    mounted() {
        this.transLateData();
        this.initChart();

        this.__resizeHandler = debounce(() => {
            if (this.chart) {
                this.chart.resize();
            }
        }, 100);
        window.addEventListener("resize", this.__resizeHandler);

    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        window.removeEventListener("resize", this.__resizeHandler);
        this.chart.dispose();
        this.chart = null;
    },
    computed: {},

    watch: {
       
        echartsList: {
            // 新增对prop的监听
            deep: true,
            handler() {
                this.transLateData();
                this.refreshChart();
            },
        },
    },
    created() { },
    methods: {
        switchGenarateOptions(label) {
            switch (label) {
                case "餐牌组投放":
                    return  "餐牌组投放"
                case "普通投放":
                    return "普通投放"
                case "联屏投放":
                    return  "联屏投放"
                default:
                    return label;
            }
        },
        refreshChart() {
            if (this.chart) {
                this.chart.dispose();
            }
            this.initChart();
        },
        getTotalShops() {
            return this.detailData.reduce((sum, d) => sum + d.total.shops, 0);
        },
        transLateData() {
            this.detailData = this.echartsList.map((item) => ({
                // 数据点1
                schedule: item.name,
                markTime: item.mark_tm,
                items: [
                    {
                        type: "餐牌组投放",
                        count: item.note.sub.dmb_plat.btpub_cnt,
                        success: item.note.sub.dmb_plat.succ_shop_cnt,
                        fail: item.note.sub.dmb_plat.fail_shop_cnt,
                    },
                    {
                        type: "普通投放",
                        count: item.note.sub.ds_plat.btpub_cnt,
                        success: item.note.sub.ds_plat.succ_shop_cnt,
                        fail: item.note.sub.ds_plat.fail_shop_cnt,
                    },
                    {
                        type: "联屏投放",
                        count: item.note.sub.vs_plat.btpub_cnt,
                        success: item.note.sub.vs_plat.succ_shop_cnt,
                        fail: item.note.sub.vs_plat.fail_shop_cnt,
                    },
                ],
                total: {
                    shops: item.note.all_shop_cnt,
                    success: item.note.all_succ_shop_cnt,
                    fail: item.note.all_fail_shop_cnt,
                    btpub: item.note.all_btpub_cnt,
                },
            }));
        },
        initChart() {
            this.chart = echarts.init(this.$el);
            const detailData = this.detailData;
            const dataLength = detailData.length;

            const option = {
                dataZoom: [
                    {
                        type: 'inside',
                        startValue: Math.max(dataLength - 7, 0), // 强制从倒数第7个开始
                        endValue: dataLength - 1 // 显示到最后一个

                    },
                    {
                        type: 'slider',
                        top: '100%', //98
                        showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                        height: 0, //5
                        fillerColor: "#44e4a4", // 滚动条颜色
                        startValue: Math.max(dataLength - 7, 0), // 强制从倒数第7个开始
                        endValue: dataLength - 1,// 显示到最后一个
                        borderColor: "#44e4a4",
                        minSpan: 7 / (dataLength || 1) * 100, // 最小缩放范围
                    }
                ],
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                        shadowStyle: {
                            color: "#e1ffdc", // 调整颜色和透明度
                            opacity: 0.3,
                        },
                    },
                    formatter: (params) => {
                        const data = detailData[params[0].dataIndex];
                        let html = `
              <div style="
                  border-radius: 10px;
                  font-weight: 500;
                  color: #000;
                  width:400px;
                  padding: 10px;
                  background: rgba(255, 255, 255, 0.5); /* 半透明背景 */
                  backdrop-filter: blur(20px);
                  overflow: hidden;
              ">
              <div style="margin-bottom:10px; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">${data.schedule}</div>
              <ul background:transparent;margin-top:10px;>`;

                        data.items.forEach((item) => {
                            // 修正图片中的类型名称（原代码中为“管牌组投放”，图片为“餐牌组投放”）
                            //   const type = item.type.replace("管牌组投放", "餐牌组投放");
                            const type = item.type;
                            html += `
                 <li style="margin-bottom: 8px;border-radius: 4px;list-style: none; background: rgba(255, 255, 255, 0.8); display: flex; justify-content: space-between; align-items: center;padding:8px;">
                  <div style="color: #666; font-size: 13px;">
                  ${this.switchGenarateOptions(type)}（${item.count}个）
                  </div>
                  <div style="text-align: right;">
                  <div style="color: #73d13d; font-size: 12px;">成功：${item.success} 家</div>
                  <div style="color: #ff7875; font-size: 12px;">失败：${item.fail} 家</div>
                  </div>
              </li>
          `;
                        });

                        html += `</ul></div>`;
                        return html;
                    },
                    backgroundColor: "none",
                    borderColor: "#d2e0f4",
                    //   borderRadius:4,
                    borderWidth: 0,
                    textStyle: { color: "#333" },
                    extraCssText:
                        "box-shadow: 0 2px 12px rgba(0,0,0,0.1); border-radius: 4px;",
                    padding: [0, 0],
                },
                xAxis: {
                    boundaryGap: true,
                    type: "category",
                    data: detailData.map((item) => item.schedule),
                    axisLine: { lineStyle: { color: "#d2e0f4", width: 1 } },
                    axisTick: {
                        show: true,
                        alignWithLabel: true,
                        lineStyle: { color: "#d2e0f4" },
                    },
                    axisLabel: {
                        color: "#7a869a",
                        fontSize: 12,
                        margin: 8,
                        formatter: (value) => { // 过长文本截断显示
                            return value.length > 15 ? value.substring(0, 15) + '...' : value;
                        }
                        // rotate: 45, // 防止标签重叠
                    },
                    axisPointer: {
                        // 新增配置
                        type: "shadow",
                    },

                },
                yAxis: {

                    type: "value",
                    min: 0,
                    //   max: 30,
                    //   max: Math.max(...this.detailData.map((d) => d.total.shops)),
                    //   max: this.detailData.reduce((d, c) => Math.max(d, c.total.shops), 0) ,
                    //   interval: 5,

                    // name: '门店数',
                    // nameLocation: 'start',
                    // nameTextStyle: {
                    //     color: "#000",
                    //     fontSize: 12,
                    //     padding: [-5, 0, 0, -5], // 下边距5px
                    //     align: 'left',
                    //     // left: '-10%'
                    // },  

                    axisLine: { show: false },
                    axisTick: {
                        show: true,
                        length: 4,
                        lineStyle: { color: "#d2e0f4" },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: { color: "#e6effc", type: "solid" },
                    },
                    axisLabel: {
                        color: "#7a869a",
                        fontSize: 12,
                        margin: 8,
                    },

                },
                grid: {
                    left: "0%",
                    right: '2.1%',
                    bottom: "2%",
                    top: "2%",
                    containLabel: true,
                    backgroundColor: "#ffffff",
                },
                series: [
                    {
                        type: "bar",
                        // data: [13, 18, 22, 10, 25, 28, 30].map((value, index) => ({
                        //   value,
                        //   dataIndex: index,
                        // })),
                        data: this.detailData.map((d) => ({
                            value: d.total.shops, // 使用总发布数作为柱状图高度
                            dataIndex: this.detailData.indexOf(d),
                        })),
                        large: true,
                        itemStyle: {
                            color: "#00c681",
                            borderRadius: [4, 4, 0, 0], // 圆角
                        },
                        barWidth: "20%", // 柱宽
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 6,
                                shadowColor: "rgba(86,96,252,0.3)",
                            },
                        },
                        large: true, // 必须开启大数据模式
                        progressive: 200, // 分片渲染
                        progressiveThreshold: 1000 // 数据量>=1000时启用
                    },
                ],
            };
            // 强制清除旧配置
            this.chart.clear();
            this.chart.setOption(option, true); // 重要！添加notMerge参数
            //   this.chart.setOption(option);
        },
    },
};
</script>

<style scoped lang="scss">
.onlineEcharts {
    //   width: 100%;
    //   height: 100%;
    //   background-color: pink;
}
</style>