import request from '@/utils/request'
import { get, post, uploadFile } from '@/utils/request'

export function getMenusTree(pid) {
  var params = {
    'func': 'sys_menu',
    'pid': pid,
    'version': '1.0'
  }
  return post('dmb/api/json', params, 'get_adm_menus')
}

export function getMenus(pid) {
  var params = {
    'func': 'sys_menu',
    'pid': pid,
    'version': '1.0'
  }
  return post('dmb/api/json', params, 'get_adm_menus')
}

export function getMenuSuperior(ids) {
  const data = Array.isArray(ids) || ids.length === 0 ? ids : Array.of(ids)
  return request({
    url: 'api/menus/superior',
    method: 'post',
    data
  })
}

export function getChild(id) {
  var params = {
    'func': 'children_menu',
    'pid': id,
    'version': '1.0'
  }
  return post('dmb/api/json', params, 'get_adm_menus')
}

export function buildMenus() {
  var params = {
    'func': 'left_menu',
    'pid': 0,
    'version': '1.0'
  }
  return post('dmb/api/json', params, 'get_adm_menus')
}

export function add(data) {
  return request({
    url: 'api/menus',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/menus',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/menus',
    method: 'put',
    data
  })
}

export default { add, edit, del, getMenusTree, getMenuSuperior, getMenus, getChild }
