<template>
  <div class="shop" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <!-- 筛选条件 -->
    <div class="conditio_search flex-1">
      <Search :modelName="modelName" :histSearchList="histSearchList" @handleSearch="handleSearch">
        <template #slot>
          <el-select v-model="searchStatus.jdetrades" clearable placeholder="商圈类型" multiple
          collapse-tags style="margin-right: 10px;">
            <el-option v-for="item in jdeinfo_list" :label="item[1]" :value="item[0]"
              :key="'jde'+item[0]"></el-option>
          </el-select>
          <el-select v-model="searchStatus.shop_status" clearable placeholder="门店营业状态">
            <el-option v-for="item in serachShopStatus" :label="item.label" :value="item.value"
              :key="item.value"></el-option>
          </el-select>
          <el-select v-model="searchStatus.shop_tags" clearable placeholder="门店标签" style="margin-left: 10px;" multiple
            collapse-tags>
            <el-option v-for="item in shopTagsList" :label="item" :value="item" :key="item.value"></el-option>
          </el-select>
          <el-select v-model="searchStatus.filtertags_type" placeholder="门店标签条件状态" style="margin-left: 10px;">
            <el-option v-for="item in condition" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </template>
      </Search>
      <div class="export_btn">
        <el-button
          type="primary"
          style="
            background-color: var(--text-color);
            border: 1px solid var(--text-color);
          "
          @click="exportExcel"
          v-if="checkPer(['dm.scr.exportdslist'])"
          >导出门店数据</el-button
        >
      </div>
      <div class="add_btn">
        <el-button size="small" @click="addShop">添加门店</el-button>
      </div>
    </div>
    <!-- 列表 -->
    <div class="table_wrap flex-1">
      <el-table :data="tableData" :height="autoHeight.height" row-key="shop_id"
        @selection-change="handleSelectionChange" style="width: 100%"
        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="storecode" label="门店编号" width></el-table-column>
        <el-table-column prop="storename" label="门店名称" width></el-table-column>
        <el-table-column prop="marketname" label="营运市场" width></el-table-column>
        <!-- <el-table-column prop="itmarketname" label="IT市场" width></el-table-column> -->
        <!-- <el-table-column prop="usage_type_cn" label="屏幕类型" align="center">
        </el-table-column>-->
        <el-table-column prop="storetypename" label="类型" width></el-table-column>
        <el-table-column prop="daypartinfo" label="经营时段" width></el-table-column>
        <el-table-column prop="createdtime" label="注册时间" width></el-table-column>
        <el-table-column prop="status_display" label="门店营业状态" width></el-table-column>
        <el-table-column prop="diplaynum" label="设备数量" width></el-table-column>
        <el-table-column prop="storeplaylayout_display" label="门店播放动线" width></el-table-column>
        <el-table-column prop="diplaynum" label="门店标签" width show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="flex" style="flex-wrap: wrap;width: 100%;justify-content: center; align-items:center">
              <div class="flex" style="flex-direction:column;align-items: center;"
                v-if="scope.row.shop_tags.length > 2">
                <div style="display:flex;align-items:center">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ scope.row.shop_tags[0] }}
                </div>
                <!-- <div style="display:flex;align-items:center">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ scope.row.shop_tags[1] }}
                </div> -->
                <el-popover placement="top-start" title="门店标签" popper-class="popperOptions" width="200" trigger="hover">
                  <div v-for="item in (new Set(scope.row.shop_tags))" :key="item"
                    style="display:flex;align-items:center">
                    <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                    <span>
                      {{
                        item
                      }}
                    </span>
                  </div>
                  <span class="cursor" slot="reference">...</span>
                </el-popover>
              </div>
              <div class="flex" style="flex-direction:column;align-items: center"
                v-else-if="scope.row.shop_tags.length > 0 && scope.row.shop_tags.length <= 2">

                <div style="display:flex;align-items:center" v-for="item in (new Set(scope.row.shop_tags))" :key="item">
                  <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                  {{ item }}
                </div>
              </div>
            </div>
          </template>

        </el-table-column>
        <el-table-column prop="data_from" :show-overflow-tooltip="true" label="门店来源" width></el-table-column>
        <el-table-column
          prop="operatemode_display"
          label="运营方式"
          width
        >
    </el-table-column>
        <el-table-column fixed="right" label="操作" width="130">
          <template slot-scope="scope">
            <el-button @click.native.prevent="handleShow(scope.row)" type="text" style="color:var(--text-color-light)"
              size="small" v-if="checkPer(['dgm.shop_group.edit'])">查看</el-button>
            <el-button @click.native.prevent="handleEdit(scope.row)" type="text" style="color:var(--text-color-light)"
              size="small">编辑</el-button>
            <el-button @click.native.prevent="handleDel(scope.row)" type="text" style="color:#F56C6C" size="small"
              class="del_text">删除</el-button>
            <el-button type="text" style="color:var(--text-color-light)" size="small" v-if="branchcode == 9999"
              @click="branchClick(scope.row)">分配品牌</el-button>
            <!-- <el-button @click.native.prevent="handleDelete(scope.row)" type="text" style="color:red" size="small">删除</el-button> -->
            <!-- <el-button v-show="!scope.row.delShow" type="text" style="color:rgba(166, 166, 166, 1)" size="small">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 底部以及页码 -->
    <div class="shop_footer flex flex-1">
      <div class="left_button_wrap flex-1">
        <!-- <el-button type="primary" size="small" @click="setDaypart">批量DayPart设置</el-button> -->
        <el-button size="small" style="background-color: var(--btn-background-color);color:#fff"
          @click="setBatchTag">批量标签设置</el-button>
          <!-- <el-button
          size="small"
          style="background-color: var(--btn-background-color); color: #fff"
          @click="setOperationMode"
          >修改运营方式</el-button
        > -->
      </div>
      <div class="right_page_wrap">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
          layout="total,sizes,prev,pager, next, jumper" :total="totalNum"></el-pagination>
      </div>
    </div>
    <Dialog :tagsDialog="tagsDialog" @handleCloseDialog="handleClose" :ShopTagList="ShopTagList"
      @saveTags="batchRequestTags" :delTagsList="delShowList"></Dialog>
    <BrandDialog :branchStateDialog="branchStateDialog" @closeBranch="closeBranch" @saveBranch="saveBranch"
      ref="BrandDialog"></BrandDialog>


    <el-drawer :title="drawer_title" :wrapperClosable='false' :visible.sync="shop_drawer_show"
      :before-close="cancel_submit" custom-class="drawer_min_w drawer_title_b">
      <el-form :model="form" label-position="top" :rules="rules" ref="ruleForm" class="drawer_form"
        style="box-sizing:border-box;padding:0 20px">
        <div style="display:flex;justify-content:space-between;width: 100%;">
          <el-form-item label="组织架构" label-width="80px" prop="p_gid">
            <el-select v-model="form.p_gid" @change="changeLevel($event, 'L1', 'L2')">
              <el-option v-for="item in level_L1_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省" label-width="80px" prop="province_id">
            <el-select v-model="form.province_id" @change="changeLevel($event, 'L2', 'L3')">
              <el-option v-for="item in level_L2_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="市区" label-width="80px" prop="city_id">
            <el-select v-model="form.city_id" @change="changeLevel($event, 'L3')">
              <el-option v-for="item in level_L3_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <!-- <el-form-item label="组织架构" label-width="80px" >
                <el-select v-model="form.p_gid" @change="changeLevel($event,'L1','L2')">
                    <el-option v-for="item in level_L1_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
                </el-select>
                <el-select v-model="form.province_id" @change="changeLevel($event,'L2','L3')">
                    <el-option v-for="item in level_L2_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
                </el-select>
                <el-select v-model="form.city_id" @change="changeLevel($event,'L3')">
                    <el-option v-for="item in level_L3_list" :label="item.text" :value="item.id" :key="item.id"></el-option>
                </el-select>
            </el-form-item> -->
        <el-form-item label="门店名称" label-width="80px" prop="shop_name">
          <el-input v-model="form.shop_name" placeholder="请输入门店名称" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="门店编号" label-width="80px" prop="storecode">
          <el-input v-model="form.storecode" placeholder="请输入门店编号" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="营运市场" label-width="80px" prop="opsmarket">
          <el-select v-model="form.opsmarket" style="width:100%" filterable>
            <el-option v-for="(item, index) in opsmarket_list" :key="'opsmarket_' + index" :label="item[1]"
              :value="item[1]"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="IT市场" label-width="80px" prop="itmarket">
          <el-select v-model="form.itmarket" style="width:100%" filterable>
            <el-option v-for="(item, index) in itmarket_list" :key="'itmarket_' + index" :label="item[1]"
              :value="item[1]"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="门店类型" label-width="80px" prop="storetype">
          <el-select v-model="form.storetype" style="width:100%" filterable>
            <el-option v-for="(item, index) in storetype_list" :key="'storetype_' + index" :label="item[1]"
              :value="item[1]"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="营业状态" label-width="80px" prop="shop_status">
          <el-select v-model="form.shop_status" style="width:100%" filterable>
            <el-option v-for="(item, index) in serachShopStatus" :key="'shop_status_' + index" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="营运方式"
          label-width="80px"
          prop="operatemode"
        >
          <el-select
            v-model="form.operatemode"
            clearable
            placeholder="请选择运营方式"
            style="width: 100%"
          >
            <el-option
              v-for="item in operationModes"
              :label="item[1]"
              :value="item[0]"
              :key="item[0]"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="my_drawer__footer">
        <el-button size="medium" @click="cancel_submit">取 消</el-button>
        <el-button class="save" size="medium" @click="confirm_submit">保 存</el-button>
      </div>
    </el-drawer>
    <el-dialog
      title="修改运营方式"
      :visible.sync="operationModeDialogVisible"
      width="20%"
      center
      custom-class="operation-dialog"
    >
      <el-form
        ref="operationModeForm"
        :model="operationModeForm"
        :rules="operationModeRules"
        label-width="100px"
      >
        <el-form-item
          label="运营方式："
          prop="selectedOperationMode"
        >
          <el-select
            clearable
            v-model="operationModeForm.selectedOperationMode"
            placeholder='请输入选择的营运方式'
          >
            <el-option
              v-for="item in operationModes"
              :key="item[1]"
              :label="item[1]"
              :value="item[1]"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="operationModeDialogVisible = false">{{
         取消
        }}</el-button>
        <el-button
          type="primary"
          @click="confirmOperationMode('operationModeForm')"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { get_adm_datas, hand_newstore_to_brand } from "@/api/shopManage/shop";
import { datas_filter_cond } from "@/api/commonInterface";
import { get_shop_data, create_or_delete_shop_tags } from "@/api/system/label";
import { simple_create_or_edit_shop, simple_get_shop_struct, del_admin_group } from "@/api/device/device"
import Dialog from "./component/Dialog.vue";
import { Actiontags, removeDuplicateObj } from "@/utils/setArray";
import BrandDialog from "./component/brandDialog.vue";
export default {
  components: {
    Dialog,
    BrandDialog
  },
  data() {
    return {
      shop_drawer_show: false,
      drawer_title: '',
      loading: false,
      histSearchList: [], //跳转后保存的筛选条件
      modelName: "GroupShop", //下拉数据接口传参
      queryList: {},
      checkedList: [],
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      searchStatus: {
        shop_status: "",
        shop_tags: [],
        jdetrades:'',
        filtertags_type: 'should'
      },
      currentPage: 1, //页码
      totalNum: 0, //总数据数量
      pageSize: 10,
      tableData: [], //列表数据
      // 批量设置标签
      tagsDialog: false,
      totleNum: 100,
      ShopTagList: [],
      selectShopIds: [],
      delTagsList: [],
      delShowList: [],
      branchcode: "",
      branchStateDialog: false,
      branchShopId: "",
      branchStoreCode: "",
      serachShopStatus: [
        {
          label: "营业中",
          value: 9,
        },

        {
          label: "暂停营业",
          value: 8,
        },
        {
          label: "未开店",
          value: 7,
        },
        // {
        //   label: "筹建中",
        //   value: 5,
        // },
        {
          label: "已关店",
          value: 4,
        },
        // {
        //   label: "修整",
        //   value: 3,
        // },
        // {
        //   label: "翻新",
        //   value: 2,
        // },
      ],
      form: {
        operatemode: "",
        shop_name: '',
        storecode: '',
        opsmarket: '',
        storetype: '',
        itmarket: '',
        shop_status: '',
        category: '',
        p_gid: '',
        province: '',
        province_id: '',
        city: '',
        city_id: '',
        shop_id: ''
      },
      opsmarket_list: [],
      storetype_list: [],
      itmarket_list: [],
      jdeinfo_list:[],
      rules: {
        p_gid: [
          { required: true, message: '组织架构不能为空', trigger: 'change' }
        ],
        province_id: [
          { required: true, message: '组织架构不能为空', trigger: 'change' }
        ],
        city_id: [
          { required: true, message: '组织架构不能为空', trigger: 'change' }
        ],
        shop_name: [
          { required: true, message: '请输入门店名称', trigger: 'blur' }
        ],
        storecode: [
          { required: true, message: '请输入门店编号', trigger: 'blur' }
        ],
        opsmarket: [
          { required: true, message: '请选择营运市场', trigger: 'change' }
        ],
        storetype: [
          { required: true, message: '请选择门店类型', trigger: 'change' }
        ],
        // itmarket: [
        //   { required: true, message: '请选择IT市场', trigger: 'change' }
        // ],
        shop_status: [
          { required: true, message: '请选择营业状态', trigger: 'change' }
        ],
        operatemode: [
          {
            required: true,
            message: '请选择运营方式',
            trigger: "change",
          },
        ],
      },
      headquarters: {},
      level_L1_list: [],
      level_L2_list: [],
      level_L3_list: [],
      shopTagsList: [],
      condition: [
        {
          label: "且(包含全部已选标签,标签选择不能超过十个)",
          value: 'must'
        },
        {
          label: "或(包含任何一个已选标签)",
          value: 'should'
        },
        {
          label: "非(不包含任何已选标签)",
          value: 'must_not'
        }
      ],

      // serachShopStatus: [
      //   {
      //     label: "营业中",
      //     value: 9
      //   },
      //   {
      //     label: "已闭店",
      //     value: 4
      //   },
      //   {
      //     label: "停业IE",
      //     value: 20
      //   },
      //   {
      //     label: "不停业IE",
      //     value: 30
      //   },
      //   {
      //     label: "暂停营业",
      //     value: 31
      //   },
      //   {
      //     label: "未开店",
      //     value: 32
      //   },
      //   {
      //     label: "季节性停业",
      //     value: 33
      //   },
      //   {
      //     label: "突发性停业",
      //     value: 34
      //   },
      //   {
      //     label: "待开业",
      //     value: 7
      //   }
      // ]
      operationModeDialogVisible:false,
      operationModeForm: {
        selectedOperationMode: "",
      },
      operationModeRules: {
        selectedOperationMode: [
          { required: true, message: "请选择运营方式", trigger: "change" },
        ],
      },
      operationModes: [
        // { label: "在线运营", value: "在线运营" },
        // { label: "离线运营", value: "离线运营" },
        // 根据实际需求添加更多选项
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    window.addEventListener("resize", this.getHeight);
    const shopStorage = JSON.parse(sessionStorage.getItem("shopStorage"));
    if (shopStorage) {
      this.currentPage = shopStorage.pageNum;
      this.queryList = shopStorage;
      this.histSearchList = shopStorage;
      this.searchStatus.shop_tags = shopStorage.shop_tags;
      this.searchStatus.filtertags_type = shopStorage.filtertags_type;
      this.searchStatus.shop_status = shopStorage.shop_status;
      this.searchStatus.jdetrades = shopStorage.jdetrades;
      sessionStorage.removeItem("shopStorage");
    }
    this.getShopTag();
    this.getHeight();
    this.getData();
    this.getDrawerSelectData();
    this.getOrganizationalStructure('#')
    this.branchcode = localStorage.getItem("branchcode");
  },
  mounted() {

  },
  methods: {
    
     // 导出
     exportExcel() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let date_str = `${year}/${month}/${day}`;
      // https://ssddvc.yumchina.com/excel/exportimpl/check_data/2023/7/19/allscreenwithsrore.xls
      let localhost = window.location.origin;
      let url = `${localhost}/excel/exportimpl/check_data/${date_str}/${localStorage.getItem('user_id')}_allstores.xls`;
      let a = document.createElement("a");
      a.href = url;
      a.style.display = "none";
      a.click();
      a.remove();
    },
        // 修改运营方式
        setOperationMode() {
      if (this.selectShopIds.length != 0) {
        this.operationModeDialogVisible = true;
      } else {
        this.$message.closeAll();
        this.$message.error('请选择门店'
        );
      }
    },
    confirmOperationMode(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   console.log("Selected Operation Mode:", this.selectedOperationMode);
          this.operationModeDialogVisible = false;
        }
      });
      // 处理确认操作的逻辑
    },


    // 初始化数据
    getData() {
      // this.getSelectDataList()
      this.getTableData();
    },
    // 获取门店列表数据
    getTableData() {
      this.loading = true;
      let condition = {
        classModel: "GroupShop",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize //每页数据量,
        // blurry: this.queryList.search, //店铺名
        // opsmarket: this.queryList.marketname, //运营市场
        // storetype: this.queryList.storetypename, //门店类型
        // itmarket: this.queryList.itmarketname, //IT市场
      };
      console.log(this.queryList, "queryList");
      const params = { ...condition, ...this.queryList };
      // return
      get_adm_datas(params).then(res => {
        if (res.rst == "ok") {
          this.tableData = res.data[0].content;
          this.totalNum = res.data[0].totalElements;
          this.loading = false;
        } else {
          console.log("失败");
        }
      });
    },
    // 搜索
    handleSearch(val) {
      console.log(val);
      this.queryList = val;
      this.queryList.blurry = this.queryList.blurry
        ? this.queryList.blurry.toUpperCase()
        : "";
      this.queryList.shop_status = this.searchStatus.shop_status;
      this.queryList.shop_tags = this.searchStatus.shop_tags;
      this.queryList.filtertags_type = this.searchStatus.filtertags_type;
      this.queryList.jdetrades = this.searchStatus.jdetrades;
      this.currentPage = 1;
      this.getTableData();
    },
    // 列表复选框选中触发事件
    handleSelectionChange(val) {
      this.selectShopIds = [];
      val.forEach(val => {
        this.selectShopIds.push(val.shop_id);
      });
      this.multipleSelection = val;
      this.delTagsList = [];
      this.delShowList = [];
      // val.forEach(item => {
      //     console.log(item.shop_tags);
      //     item.screen_tags = []
      //     if (item.shop_tags.length != 0) {
      //         item.shop_tags.forEach(item1 => {
      //             item.screen_tags.push(item1)
      //         })
      //         this.delTagsList.push(...[item.screen_tags])
      //     }
      // })
      val.forEach(item => {
        console.log(item.shop_tags);
        item.screen_tags = [];
        if (item.shop_tags.length != 0) {
          item.shop_tags.forEach(item1 => {
            item.screen_tags.push(item1);
            this.delTagsList.push(item1);
          });
        }
      });
    },
    // 点击列表查看
    handleShow(row) {
      // 需要将点击的那条数据存到本地
      console.log(row);
      sessionStorage.setItem("shop_detials", JSON.stringify(row));
      //把筛选条件，页码存储
      this.queryList.shop_status = this.searchStatus.shop_status;
      const shopStorage = JSON.parse(JSON.stringify(this.queryList));
      shopStorage.pageNum = this.currentPage;
      sessionStorage.setItem("shopStorage", JSON.stringify(shopStorage));
      // this.$router.push('/shopManage/shopDetials');
      this.$router.push({
        path: "/shopManage/shopDetials",
        query: { shopid: row.shop_id }
      });
    },
    // 点击列表删除
    handleDelete(row) {
      const params = {
        group_shop_id: [row.shop_id], // (List) 店铺节点id列表
        bacth_process: 1 // (Int) 默认值
      };
      console.log(params);
    },
    // 批量DayPart设置
    setDaypart() {
      // this.$message.success('123')
      if (this.checkedList.length == 0) {
        this.$message.error("请先选择门店");
        return;
      }
      // this.isDrawerShow = true;
      console.log(this.checkedList);
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 230 + "px";
      this.autoHeight.heightNum = windowHeight - 230;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTableData();
    },
    getShopTag() {
      this.ShopTagList = [];
      const params = {
        page: 0,
        size: this.totleNum
      };
      get_shop_data(params).then(res => {
        this.totleNum = res.data[0].totalElements;
        this.shopTagsList = res['data'][0]['tags_list']
        res.data[0].tags_list.forEach(item => {
          this.ShopTagList.push({
            name: item,
            active: false
          });
        });
      });
    },
    setBatchTag() {
      this.getShopTag();
      if (this.selectShopIds.length != 0) {
        this.tagsDialog = true;
        // this.delTagsList.forEach((item, index) => {
        //     if (item.length == 0) {
        //         this.delTagsList.splice(index, 1)
        //     }
        // })
        // this.delTagsList = Actiontags(this.delTagsList)
        // console.log("delTagsList", this.delTagsList);
        console.log("this.delTagsList", this.delTagsList);
        this.delTagsList.forEach(item => {
          this.delShowList.push({
            active: false,
            name: item
          });
        });
        console.log(this.delShowList);
        this.delShowList = removeDuplicateObj(this.delShowList);
      } else {
        this.$message.closeAll();
        this.$message.warning("请选择店铺");
      }
    },
    handleClose() {
      this.tagsDialog = false;
    },
    batchRequestTags(tags, activeName) {
      if (tags.length != 0) {
        let params = {
          shop_ids: this.selectShopIds,
          tags: tags,
          action: "add",
          shop_group_id: localStorage.getItem("group_id")
        };
        console.log(tags);
        if (activeName == "relation") {
          params.action = "add";
          create_or_delete_shop_tags(params).then(res => {
            if (res.rst == "ok") {
              this.$message.success("批量增加标签成功");
              this.tagsDialog = false;
              setTimeout(() => {
                this.getTableData();
              }, 800);
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        } else {
          params.action = "del";
          create_or_delete_shop_tags(params).then(res => {
            if (res.rst == "ok") {
              this.$message.success("批量删除标签成功");
              this.tagsDialog = false;
              setTimeout(() => {
                this.getTableData();
              }, 800);
            } else {
              this.$message.warning(res.error_msg);
            }
          });
        }
      } else {
        this.tagsDialog = false;
      }
    },
    branchClick(row) {
      this.branchShopId = row.shop_id;
      this.branchStoreCode = row.storecode;
      this.branchStateDialog = true;
    },
    closeBranch() {
      this.branchStateDialog = false;
      this.$refs.BrandDialog.branchForm = {};
    },
    // 确定
    saveBranch(branchForm) {
      const parmas = {
        shop_id: this.branchShopId,
        storecode: this.branchStoreCode,
        ...branchForm
      };
      hand_newstore_to_brand(parmas).then(res => {
        if (res.rst == "ok") {
          this.$message.success("分配成功");
          this.closeBranch();
          this.getData();
        } else {
          this.$message.warning(res.error_msg);
        }
      });
    },
    addShop() {
      this.shop_drawer_show = true;
      this.drawer_title = '添加门店';
      const level_group_id = this.headquarters.id.split('g')[1];
      this.getOrganizationalStructure(level_group_id, 'L1');
    },
    getDrawerSelectData() {
      const params = {
        classModel: 'GroupShop', //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      }
      datas_filter_cond(params).then(res => {
        if (res.rst == 'ok') {
          res.data[0].forEach(item => {
            if (item.filterkey == 'opsmarket') {
              this.opsmarket_list = [...item.options];
            } else if (item.filterkey == 'storetype') {
              this.storetype_list = [...item.options];
            } else if (item.filterkey == 'itmarket') {
              this.itmarket_list = [...item.options];
            }else if (item.filterkey == 'jdeinfo') {
              this.jdeinfo_list = [...item.options];
            }else if (item.filterkey == "operatemode") {
              this.operationModes = [...item.options];
            }
          })
        } else {
          this.$message.error(res.error_msg)
        }

      })
    },
    changeLevel(value, level_now, level_next) {
      // if(level_now == 'L1'){
      //     const info = this.level_L1_list.find(item=>item.id == value);
      //     this.form.category = info.text;
      // }else if(level_now == 'L2'){
      //     const info = this.level_L2_list.find(item=>item.id == value);
      //     this.form.province = info.text;
      // }else if(level_now == 'L3'){
      //     const info = this.level_L3_list.find(item=>item.id == value);
      //     this.form.city = info.text;
      // }
      const level_group_id = value.split('g')[1];
      if (level_next) {
        this.getOrganizationalStructure(level_group_id, level_next)
      }

    },
    getOrganizationalStructure(group_id, level) {
      const params = {
        group_id,
        sel_unit: 'shop',
        purpose: 'loading_tree'
      }
      simple_get_shop_struct(params).then(res => {
        // console.log(res.data[0].structure,'treeeeeeeeeeeeee');
        if (res.rst == 'ok') {
          if (group_id == '#') {
            this.headquarters = res.data[0].structure[0];
            const needAdd = sessionStorage.getItem("needAddShop");
            if (needAdd && needAdd == '1') {
              this.addShop();
              sessionStorage.removeItem("needAddShop");
            }
          } else {
            if (level == 'L1') {
              this.level_L1_list = [...res.data[0].structure]
            } else if (level == 'L2') {
              this.level_L2_list = [...res.data[0].structure]
            } else if (level == 'L3') {
              this.level_L3_list = [...res.data[0].structure]
            }
          }
        } else {
          this.$message.error(res.error_msg)
        }
      })
    },
    cancel_submit() {
        this.form = {
        operatemode: "",
        shop_name: "",
        storecode: "",
        opsmarket: "",
        storetype: "",
        itmarket: "",
        shop_status: "",
        category: "",
        p_gid: "",
        province: "",
        province_id: "",
        city: "",
        city_id: "",
        shop_id: "",
      };
      this.shop_drawer_show = false;
      this.$refs.ruleForm.resetFields();
      this.form.shop_id = '';
      this.level_L1_list = [];
      this.level_L2_list = [];
      this.level_L3_list = [];
    },
    formValidate() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            resolve('success')
          } else {
            reject('error')
          }
        });
      })
    },
    confirm_submit() {
      this.formValidate().then(res => {
        let params = {
          g_group_id: localStorage.getItem('group_id'),
          shop_name: this.form.shop_name,
          storecode: this.form.storecode,
          shop_status: this.form.shop_status,
          shop_ext: {},
          category: '',
          p_gid: '',
          province: '',
          province_id: '',
          city: '',
          city_id: ''
        }

        let shop_ext = {};
        const opsmarket_info = this.opsmarket_list.find(item => item.indexOf(this.form.opsmarket) != -1);
        shop_ext.opsmarketcode = opsmarket_info[0];
        shop_ext.opsmarketname = opsmarket_info[1];
        // const itmarket_info = this.itmarket_list.find(item => item.indexOf(this.form.itmarket) != -1);
        // shop_ext.itmarketcode = itmarket_info[0];
        // shop_ext.itmarketname = itmarket_info[1];
        const storetype_info = this.storetype_list.find(item => item.indexOf(this.form.storetype) != -1);
        shop_ext.storetypecode = storetype_info[0];
        shop_ext.storetypename = storetype_info[1];
        shop_ext.operatemode = this.form.operatemode;
        params.shop_ext = shop_ext;

        const L1_info = this.level_L1_list.find(item => item.id == this.form.p_gid);
        const L2_info = this.level_L2_list.find(item => item.id == this.form.province_id);
        const L3_info = this.level_L3_list.find(item => item.id == this.form.city_id);

        params.category = L1_info.text;
        params.p_gid = L1_info.id.split('g')[1];
        params.province = L2_info.text;
        params.province_id = L2_info.id.split('g')[1];
        params.city = L3_info.text;
        params.city_id = L3_info.id.split('g')[1];

        if (this.drawer_title == '编辑门店') {
          params.shop_id = this.form.shop_id
        }

        simple_create_or_edit_shop(params).then(res => {
          if (res.rst == 'ok') {
            let text = '';
            this.cancel_submit();
            if (this.drawer_title == '添加门店') {
              this.currentPage = 1;
              text = '添加';
            } else {
              text = '编辑';
            }
            this.$message.success(text + '成功');
            this.getTableData();
          } else {
            this.$message.error(res.error_msg)
          }
        })
      }).catch(err => {
        console.log(err, 2);
      })
    },
    handleEdit(row) {
      console.log(row);
      this.shop_drawer_show = true;
      this.drawer_title = '编辑门店';
      this.BackfillingInformation(row);
    },
    BackfillingInformation(row) {
      this.getOrganizationalStructure(row.parent_ids[0], 'L1');
      this.getOrganizationalStructure(row.parent_ids[1], 'L2');
      this.getOrganizationalStructure(row.parent_ids[2], 'L3');
      this.form.shop_name = row.storename;
      this.form.storecode = row.storecode;
      this.form.shop_status = row.status;
      this.form.category = row.parent_label[1];
      this.form.province = row.parent_label[2];
      this.form.city = row.parent_label[3];
      this.form.p_gid = 'g' + row.parent_ids[1];
      this.form.province_id = 'g' + row.parent_ids[2];
      this.form.city_id = 'g' + row.parent_ids[3];
      this.form.opsmarket = row.marketname;
      this.form.operatemode = row.operatemode;
      // this.form.itmarket = row.itmarketname;
      this.form.storetype = row.storetypename;
      this.form.shop_id = row.shop_id;
    },
    handleDel(row) {
      this.$confirm('此操作将永久门店, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        const params = {
          actl: 99,
          d_g: row.group_id
        }
        del_admin_group(params).then(res => {
          if (res.rst == 'ok') {
            if (this.tableData.length == 1) {
              if (this.currentPage > 1) {
                this.currentPage -= 1;
              } else {
                this.currentPage = 1;
              }
            }
            // this.currentPage = 1;
            this.loading = false;
            this.getTableData();
          } else {
            this.loading = false;
            this.$message.error(res.error_msg);
          }
        })
      }).catch(() => {

      });


    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  }
};
</script>

<style scoped lang="scss">
.shop {
  width: 100%;
}

/* 筛选条件 */
.conditio_search {
   // margin-top: 10px;
   display: flex;
  align-items: center;
  //   height: 87px;
  height: 100px;
  padding: 10px 0 10px 24px;
  justify-content: flex-start;
  flex-wrap: wrap;
  //   background-color: pink;
}
.export_btn {
  margin: 0 10px 0 0;
}

.add_btn {
  padding-right: 20px;

  button {
    background-color: var(--btn-background-color);
    color: #ffffff;
  }

}

.search_btn {
  width: 88px;
  height: 32px;
  /* left: 1089px; */
  /* top: 86px; */
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  border-radius: 6px;
  font-size: 14px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  cursor: pointer;
}

/* 列表区 */
.table_wrap {
  padding-left: 11px;
}

/* 底部以及页码 */
.shop_footer {
  height: 89px;
  align-items: center;
  padding-left: 24px;
  padding-right: 22px;
}

.left_button_wrap {}

.right_page_wrap {
  height: 30px;
  /* background-color: #eff0f2 */
}

.saveBtnParent {
  height: 40px;
  text-align: right;
  background: #fff;
}

.table_wrap .el-checkbox__inner {
  height: 19px !important;
}
</style>
<style lang="scss">
/* 把element table的复选框改为红色 */
.shop .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: var(--text-color) !important;
  border-color: var(--base-color) !important;
}

.shop .el-checkbox__inner {
  /* border-color:red !important; */
  width: 18px;
  height: 18px !important;
  border-radius: 50%;
  padding-left: 16px;
}

.shop .el-checkbox__inner::after {
  left: 6px !important;
  top: 3px !important;
}

.shop .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  left: 0px !important;
  top: 7px !important;
}

.drawer_form {
  height: calc(100% - 80px);
  overflow-y: auto;
}

.my_drawer__footer {
  height: 80px;
  line-height: 80px;
  text-align: right;
  padding: 0 20px;
  box-sizing: border-box;

  .save {
    background-color: var(--btn-background-color);
    color: #fff;
  }
}
.operation-dialog {
  .el-dialog__header {
    padding: 20px;

    .el-dialog__title {
      font-size: 16px;
      color: #333;
    }
  }
  .el-form-item__error {
    color: red;
  }
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before,
  .el-form-item.is-required:not(.is-no-asterisk)
    .el-form-item__label-wrap
    > .el-form-item__label:before {
    color: red;
  }
  .required-item .el-form-item__label:before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
  .dialog-footer {
    // padding: 20px;
    display: flex;
    justify-content: flex-end;
    // text-align: right;
    // border-top: 1px solid #eee;
    margin-top: 10px;
  }

  .el-select {
    width: 100%;
  }

  .el-dialog__body {
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    padding: 20px;
    // background-color: pink;
    .el-form-item__label {
      //   color: #f56c6c;
      line-height: none;
    }
  }
}
</style>