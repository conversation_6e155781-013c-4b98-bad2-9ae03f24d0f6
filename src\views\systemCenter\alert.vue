<template>
    <div class="alert" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
        <!-- title -->
        <div class="alert_title">
            <span class="dot"></span> 预警机制
        </div>
        <!-- 预警提示 -->
        <div class="warning_prompt ">
            <p>
                <i class="el-icon-warning" style="color:rgba(255, 141, 26, 1);font-size:14px;margin-right:13px"></i>
                <span style="color:rgba(255, 141, 26, 1);font-size:14px">温馨提示： 预警规则直接影响预警结果的计算，规则修改后延迟一个小时生效</span>
            </p>
        </div>
        <!-- 表格 -->
        <div class="table_wrap ">
            <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <!-- <el-table-column type="selection" width="50"></el-table-column> -->
                <el-table-column prop="label" label="规则名称" width=""></el-table-column>
                <el-table-column label="启用" width="">
                    <template slot-scope="scope">
                        <span v-if="scope.row.alert_open == '0'">未启用</span>
                        <span v-else-if="scope.row.alert_open == '1'" style="color: rgba(20, 152, 74, 1)">已启用</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="value_limit_lab" label="报警阀值" width="" :formatter="alarmThresholdFmt"></el-table-column> -->
                <el-table-column prop="value" label="报警阀值" width=""></el-table-column>
                <el-table-column prop="alert_level" label="通知方式" width=""></el-table-column>
                <el-table-column prop="alert_time_range_display" label="通知时间段" width=""></el-table-column>
                <el-table-column prop="alert_freq_display" label="通知频次" width=""></el-table-column>
                <el-table-column fixed="right" label="操作" width="130">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="handleEdit(scope.row, scope.$index)" type="text"
                            style="color:#409eff" size="small">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 底部和页码 -->
        <div class="alert_footer ">
            <div class="" style="height:154px">
                <!-- <p style="margin-bottom:9px">当前第{{(currentPage-1)*pageSize}}-{{(currentPage-1)*pageSize+pageSize}}条/共计{{totalNum}}条</p> -->
                <p style="color:var(--background-color)">注：所有预警的数据，不包含设备标记为“备用”和店铺标记为“已闭店”、“待营业”</p>
            </div>
            <div class="right_page_wrap">
                <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5'
                    :page-sizes="[10, 20, 50, 100]" layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
                </el-pagination>
            </div>
        </div>
        <!-- 编辑弹框 -->
        <el-dialog title="预警规则编辑" :visible.sync="dialogVisible" width="697px" :before-close="handleClose">
            <div class="dialog_content">
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">规则名称：</span>
                    <el-input v-model="dialogQueryList.ruleName" disabled size="small" style="width:184px"></el-input>
                </div>
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">是否启用：</span>
                    <el-radio v-model="dialogQueryList.isEnable" label="1">开启</el-radio>
                    <el-radio v-model="dialogQueryList.isEnable" label="0">关闭</el-radio>
                </div>
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">报警阀值：</span>
                    <el-select v-model="dialogQueryList.alarmThreshold" placeholder="请选择报警阀值" size="small"
                        style="width:186px;margin-right:12px">
                        <el-option v-for="item in alarmThresholdList" :key="item[1]" :label="item[1]"
                            :value="item[0]"></el-option>
                    </el-select>
                </div>
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">通知方式：</span>
                    <el-select v-model="dialogQueryList.noticeMode" placeholder="请选择报警阀值" size="small"
                        style="width:186px;margin-right:12px">
                        <el-option v-for="item in noticeModeList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </div>
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">通知时段：</span>
                    <el-select v-model="dialogQueryList.noticeInterval" placeholder="请选择报警阀值" size="small"
                        style="width:186px;margin-right:12px">
                        <el-option v-for="item in noticeIntervalList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </div>
                <div class="dialog_row">
                    <span style="display:inline-block;width:80px">通知频次：</span>
                    <el-select v-model="dialogQueryList.noticeFrequency" placeholder="请选择报警阀值" size="small"
                        style="width:186px;margin-right:12px">
                        <el-option v-for="item in noticeFrequencyList" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="medium" @click="handleClose">取 消</el-button>
                <el-button size="medium" type="primary" @click="handleEditConfirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { adm_alert_rule, edit_alert_rule } from "@/api/sysMange/sysMange"
export default {
    components: {

    },
    data() {
        return {
            loading: false,
            currentPage: 1,
            totalNum: 0, //总数据数量
            pageSize: 10,
            tableData: [],  //列表数据
            dialogVisible: false,  //编辑框dialog是否显示
            dialogQueryList: {
                ruleName: '', //规则名称
                isEnable: '', //是否启用
                alarmThreshold: '', //报警阈值
                noticeMode: '', //通知方式
                noticeInterval: '', //通知时段
                noticeFrequency: '', //通知频次 
            },
            alarmThresholdList: [  //报警阈值下拉数据
                { label: '200兆', value: '200' },
                { label: '500兆', value: '500' }
            ],
            noticeModeList: [  //通知方式下拉数据
                { label: '邮件', value: 1 },
            ],
            noticeIntervalList: [  //通知时段下拉数据
                { label: '6:00-12:00', value: '0' },
                { label: '13:00-16:00', value: '1' }
            ],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
            noticeFrequencyList: [
                { label: '一次/时间段内', value: 1 },
                { label: '一次/俩小时', value: 3 },
                { label: '一次/四小时', value: 4 },
                { label: '一次/12小时', value: 5 },
            ],
            useId: "",
            groupId: "",
            attr: "",
        };
    },
    computed: {

    },
    watch: {

    },
    mounted() {

    },
    methods: {
        // 格式化列表数据
        alarmThresholdFmt(row) {
            // const arr = this.alarmThresholdList.filter(item=>{return row.alarmThreshold == item.value})
            // return arr[0].label
        },
        // 点击列表编辑
        handleEdit(row, idx) {
            console.log(row, 'row');
            // this.dialogQueryList = JSON.parse(JSON.stringify(row));
            this.dialogVisible = true;
            // 双向绑定
            console.log(row.alert_open);
            this.alarmThresholdList = row.val_opts;
            this.dialogQueryList.ruleName = row.label;
            this.dialogQueryList.isEnable = String(row.alert_open);
            this.dialogQueryList.alarmThreshold = row.value;
            this.dialogQueryList.noticeMode = row.alert_open;
            this.dialogQueryList.noticeInterval = row.alert_time_range_display;
            this.dialogQueryList.noticeFrequency = row.alert_freq;
            this.attr = this.tableData[idx].attr;
        },
        //编辑弹框确认按钮
        handleEditConfirm() {
            // 预警机制编辑接口
            const params = {
                ruid: String(this.useId), //（string）用户id
                pgid: String(this.groupId), //（string）账号节点id
                attr: this.attr, //（string）预警编号
                value: this.dialogQueryList.alarmThreshold, //（string）报警阈值
                alert_open: this.dialogQueryList.isEnable, //（int）是否启用，启用：1；不启用：0
                alert_level: 1, //（int）预警通知方式
                alert_time_range: 1, //（int）预警通知时间段
                alert_freq: 1, //（int）通知频次
                iDisplayStart: 0, //（int）
                iDisplayLength: 10 //（int）
            }
            console.log(params);
            // return
            edit_alert_rule(params).then(res => {
                console.log(res, "预警机制，编辑按钮");
                this.$message.success("修改成功");
                this.adm_alert_rule();
            })
            this.resetDialog();
            // 重新调用预警机制列表
            this.dialogVisible = false;
        },
        // 关闭弹窗
        handleClose() {
            this.$message.success('已取消')
            this.dialogVisible = false;
            // this.resetDialog()
        },
        // 清空弹框内容
        resetDialog() {
            this.dialogQueryList.ruleName = '';
            this.dialogQueryList.isEnable = '0';
            this.dialogQueryList.alarmThreshold = '';
            this.dialogQueryList.noticeMode = '';
            this.dialogQueryList.noticeInterval = '';
            this.dialogQueryList.noticeFrequency = '';
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 394 + 'px';
            this.autoHeight.heightNum = windowHeight - 394;
        },
        //页码改变
        handleCurrentChange(val) {
            this.currentPage = val;
            console.log(`现在是第${val}页`);
            this.adm_alert_rule();
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.adm_alert_rule();
        },
        // 预警机制列表
        adm_alert_rule() {
            const params = {
                ruid: String(this.useId), //（string）用户id
                pgid: String(this.groupId), //（string）账号节点id
                iDisplayStart: (this.currentPage - 1) * this.pageSize, //（int）
                iDisplayLength: this.pageSize, //（int）
                sEcho: "1"
            }
            console.log(params);
            adm_alert_rule(params).then(res => {
                console.log(res, "sysMange");
                // 是否启用
                // this.isEnable
                console.log(res.data[0].sEcho, "sEcho");
                this.tableData = res.data[0].data_list;
                // console.log(res.data[0].data_list,"12321312")
                // 总数
                // this.totalNum = Number(res.data[0].sEcho);
                this.totalNum = res.data[0].iTotalRecords;
            })
        },
    },
    created() {
        this.useId = localStorage.getItem('user_id');
        this.groupId = localStorage.getItem('group_id');
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        // 预警机制列表
        this.adm_alert_rule();


    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style scoped>
.alert {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 0 20px;
}

.alert_title {
    /* width: 56px; */
    align-items: center;
    height: 40px;
    color: var(--del-color);
    font-size: 14px;
    line-height: 40px;
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid rgba(145, 195, 252, 1);
}

.alert_title .dot {
    display: inline-block;
    width: 9px;
    height: 9px;
    background-color: var(--del-color);
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
    margin-right: 5px;
}

/* 提示 */
.warning_prompt {
    display: flex;
    width: 100%;
    height: 86px;
    align-items: center;
}

.warning_prompt p {
    width: 971px;
    height: 49px;
    line-height: 49px;
    padding-left: 21px;
    background: rgba(255, 169, 40, 0.1);
}

/* 底部 */
.alert_footer {
    box-sizing: border-box;
    width: 100%;
    height: 205px;
    font-size: 14px;
    padding-top: 17px;
}

.right_page_wrap {
    text-align: right;
    justify-content: flex-end;
    /* flex: 1; */
    width: 100%;
}

.dialog_content {
    box-sizing: border-box;
    height: 350px;
    width: 100%;
    overflow-y: scroll;
    padding: 11px 0;
    border-bottom: 1px solid rgba(153, 153, 153, 0.29)
}

.dialog_content::-webkit-scrollbar {
    width: 0 !important
}

.dialog_content {
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;
}

.dialog_row {
    margin-bottom: 17px;
}
</style>
<style>
.alert .el-dialog {
    border-radius: 16px;
}

.alert .el-dialog .el-dialog__header {
    padding-top: 15px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid rgba(153, 153, 153, 0.29)
}

.alert .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 16px !important;
}

.alert .el-dialog .el-dialog__body {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-right: 0 !important;
}
</style>
