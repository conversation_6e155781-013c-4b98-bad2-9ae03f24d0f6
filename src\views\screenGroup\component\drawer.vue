<template>
  <div class="drawer">
    <el-drawer :visible="drawer" :before-close="handleClose" :with-header="false" width="40%" :wrapperClosable="false">
      <div style="height: 90%; overflow: scroll; overflow-y: auto" class="scrool">
        <div class="drawer_search">
          <el-form :inline="true" ref="form" :model="SearchFrom" label-width="80px">
            <el-form-item class="search_input">
              <el-input v-model="SearchFrom.shop_name" placeholder="门店ID/门店名称">
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="size" class="search_button" @click="searchShop">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="searchTable" v-if="searchState != false" v-loading="ShopLoading">
          <el-table :data="searchTable" style="width: 100%" :show-header="false">
            <el-table-column prop="shop_name" label="店铺" width="180">
            </el-table-column>
            <el-table-column align="right">
              <template slot-scope="scope">
                <el-button style="background-color: var(--btn-background-color);color: #fff;" @click="addShop(scope.row.shop_id)">
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="shopData" v-if="shopState != false" v-loading="ShopLoading">
          <div class="shop_header">
            <div class="header_data">
              <div>餐厅名称:</div>
              <span>
                {{ shopData.storecode ? shopData.storecode : "" }}
                {{ shopData.storename ? shopData.storename : "" }}
              </span>
            </div>
            <div class="header_data">
              <div>所属市场:</div>
              <span>
                {{ shopData.marketname ? shopData.marketname : "" }}
              </span>
            </div>
            <!-- screen_group_list 可能是多个 -->
            <div class="header_data">
              <div>联屏组:</div>
              <!-- {{shopData.screen_group_list.}} -->
              <span>
                {{
                  shopData.screen_group_list.length == 0
                    ? "未绑定联屏组"
                    : shopData.screen_group_list[0].group_name
                }}
              </span>
            </div>
            <!-- {{shopData}} -->
            <div class="header_data">
              <div>门店设备：</div>
              <span style="color: rgba(166, 166, 166, 1)">
                {{
                  shopData.screen_list.length != 0
                    ? `包含${shopData.screen_list.length}个`
                    : `没有绑定的设备`
                }}
              </span>
            </div>
            <div class="header_table" v-if="shopData.screen_list.length != 0">
              <el-table :data="shopData.screen_list" stripe style="width: 100%"
                :header-cell-style="{ background: '#f0f0f0' }" border>
                <!-- <el-table-column prop="screen_index" label="屏幕序号">
                  <template slot-scope="scope">
                     {{scope.row.screen_index}}
                  </template>
                </el-table-column> -->

                <el-table-column prop="displaynum" label="屏幕编号">
                </el-table-column>
                <el-table-column prop="screen_name" label="屏幕名称">
                </el-table-column>
                <el-table-column prop="screen_id" label="屏幕ID">
                </el-table-column>
                <el-table-column prop="tags" label="屏幕横竖">
                  <template slot-scope="scope">
                    {{ scope.row.v_or_h == 0 ? '横' : '竖' }}
                  </template>

                </el-table-column>
                <!-- <el-table-column prop="pmodel" label="设备类型"> </el-table-column> -->
              </el-table>
            </div>
            <div class="screen_setting" v-if="shopData.screen_list.length != 0">
              <div>
                <span class="setting_title"> 联屏设定: </span>
                <el-select v-model="ScreenSettingValue" placeholder="请选择屏幕设定" style="margin-left: 10px"
                  @change="setScreen">
                  <el-option v-for="(item, index) in ScreenSettingList" :key="index" :label="item.title"
                    :value="item.value">
                  </el-option>
                </el-select>
                <el-button class="groupBtn" @click="bindingScroll" v-if="screenGroupState == false" v-show="false">屏幕组合
                </el-button>

                <el-button class="groupBtn" @click="unbindingScroll" v-else>解绑屏幕</el-button>
                <el-button class="groupBtn" @click="clears">清空</el-button>
                <!-- v-else-if="screenGroupState == true" -->
              </div>
              <div class="setting_arrange" v-show="ScreenSettingValueList.length != 0">
                <div style="display:flex;width:85%;margin-top:10px;margin-bottom: 10px;">
                  <el-select v-for="(item, index) in ScreenSettingValueList" :key="index"
                    v-model="ScreenSettingValueList[index].selectValue" @change="settDisable(item, index)">
                    <el-option v-for="(item1, index1) in shopData.screen_list" :key="index1" :label="item1.screen_id"
                      :value="item1.screen_id" :disabled="item1.disabled" />
                  </el-select>
                </div>
                <!-- <div style="display:flex;width:85%;margin-top:10px;margin-bottom: 20px;">
                  <el-select v-for="(item, index) in ScreenSettingValueList" :key="item"
                    v-model="ScreenSettingValueList[index].selectValue" @change="settDisable(item, index)">
                    <el-option v-for="(item1, index1) in shopData.screen_group_list[0].screens" :key="index1"
                      :label="item1.screen_id" :value="item1.screen_id" :disabled="item1.disabled" />
                  </el-select>
                </div> -->

              </div>
            </div>
            <div class="screen_label" v-if="shopData.screen_list.length != 0">
              <span class="label_title"> 联屏组标签: </span>
              <el-checkbox-group v-model="requestLabel" style="margin-top: 10px">
                <el-checkbox v-for="item in SelectLabel" :label="item.label" :key="item.value" style="margin-top:10px">
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <el-button class="cancel" @click="cancel" type="primary">取消</el-button>
        <el-button class="save" @click="saveTags" v-show="IsShowButton">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { bind_shop } from "@/api/screen_group/screen_group";
import { get_tags_list } from "@/api/system/label"
export default {
  data() {
    return {
      // 是否查询 ?
      searchState: false,
      // 搜索form
      SearchFrom: {
        shop_name: "",
        g_group_id: localStorage.getItem("group_id"),
      },
      // 店铺是否显示
      shopState: false,
      // 选择的类型
      ScreenSettingValue: null,
      // 联屏组标签
      SelectLabel: [
        {
          label: "餐牌联屏",
          value: "餐牌联屏",
        },
        {
          label: "自提柜联屏",
          value: "自提柜联屏",
        },
        {
          label: "甜品联屏",
          value: "甜品联屏",
        },
      ],
      // 选择的标签
      requestLabel: [],
      // 店铺id
      shopId: "",
      ScreenSettingValueList: [],
      screenGroupState: false,
      // 设备key
      sg_key: "",
      saveState: false,
      IsShowButton: false,
      disabledArray: []
    };
  },
  props: {
    /**
     * 新增联屏抽屉状态
     */
    drawer: {
      type: "Boolean",
    },
    /**
     * 搜索完成后数据
     */
    searchTable: {
      type: "Array",
    },
    /**
     * 查询店铺数据
     */
    shopData: {
      type: "Object",
    },
    /**
     * 联屏设定数据
     */
    ScreenSettingList: {
      type: "Array",
    },
    /**
     * 查询loading
     */
    ShopLoading: {
      type: Boolean,
    },
  },
  watch: {
    // 查询
    searchTable: {
      handler(newValue, oldValue) { },
      deep: true,
      immediate: true,
    },
    // 店铺数据
    shopData: {
      handler(newValue, oldValue) {
        this.shopData = newValue;
        this.ScreenList = newValue.screen_list;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 取消弹框
    handleClose() {
      this.$emit("closeDrawer", false);
      this.SearchFrom.shop_name = "";
      this.searchState = false;
      this.shopState = false;
    },
    // 搜索店铺
    searchShop() {
      this.$emit("searchShop", this.SearchFrom);
      //   this.shopData = {};
      if (this.SearchFrom.shop_name == "") {
        this.searchState = false;
        this.shopState = false;
      } else {
        this.searchState = true;
        this.shopState = false;
        this.IsShowButton = true;
      }
    },
    // 查询店铺详情
    addShop(shopId) {
      this.$emit("addShop", shopId);
      this.searchState = false;
      this.shopState = true;
      this.ScreenSettingValue = "";
      this.ScreenSettingValueList = [];
      setTimeout(() => {
        if (this.shopData.screen_group_list.length != 0) {
          // this.ScreenSettingValue =
          //   this.shopData.screen_group_list[0].specification;
          // this.ScreenSettingValueList =
          // this.shopData.screen_group_list[0].screens;
          this.sg_key = this.shopData.screen_group_list[0].sg_key;
        }
        // this.ScreenSettingValueList =
        //   this.shopData.screen_list;
        // console.log('ScreenSettingValueList', this.ScreenSettingValueList);
        // this.sg_key = this.shopData.screen_group_list.length != 0 ? this.shopData.screen_group_list[0].sg_key : '';
        // if (this.shopData.screen_group_list.length != 0) {
        //   this.screenGroupState = true;
        // } else {
        //   this.screenGroupState = false;
        //   this.ScreenSettingValue = "";
        // }
        this.requestLabel = this.shopData.shop_tags;
        // console.log(this.shopData.screen_group_list[0].screens, "this.shopData.screen_group_list[0].screens");
        // this.ScreenSettingValueList.forEach((item, index) => {
        //   item.selectValue = item.screen_id;
        //   if (item.selectValue == item.screen_id) {
        //     this.$set(
        //       this.shopData.screen_list[index],
        //       "disabled",
        //       true
        //     );
        //   }
        // });
        console.log(this.ScreenSettingValueList, "ScreenSettingValueList");

      }, 1000);
    },
    // 禁用
    settDisable(item, index) {
      console.log(item)

      this.shopData.screen_list.forEach((item2, index1) => {
        if (item.selectValue == item2.screen_id) {
          this.$set(this.shopData.screen_list[index1], "disabled", true);
        }
        // let index = this.disabledArray.indexOf(item.screen_id)
        // }
      });
    },
    setScreen() {
      this.ScreenSettingValueList = [];
      // this.shopData.screen_group_list[0].screens

      this.shopData.screen_list.forEach((item) => {
        item.disabled = false;
      });
      for (
        let i = 0;
        i < this.ScreenSettingValue[this.ScreenSettingValue.length - 1];
        i++
      ) {
        this.ScreenSettingValueList.push({});
      }
      this.saveState = true
    },
    // 解绑屏幕
    unbindingScroll() {
      this.$emit("unbindingScroll", this.sg_key, this.shopData.shop_id);
      this.ScreenSettingValueList = [];
      this.screenGroupState = false;
      this.saveState = true
    },
    // 保存tags
    saveTags() {
      let screens = [];
      let dir = this.ScreenSettingValue[0]
      this.ScreenSettingValue = this.ScreenSettingValue.split('_')[1]
      console.log(dir, 'dir');
      if (this.ScreenSettingValue == null) {
        this.$message.warning("请选择联屏设定");
      } else {
        this.ScreenSettingValueList.forEach((item, index) => {
          screens.push({
            screen_id: item.selectValue,
            screen_index: index + 1,
            y: 0,
            x: index,
          });
        });
        screens.forEach((item) => {
          if (item.screen_id == null) {
            screens = [];
          }
        });
        if (screens.length <= 0) {
          this.$message.warning("请选择屏幕");
        } else {
          if (this.screenGroupState == false) {
            const bindParam = {
              shop_id: this.shopData.shop_id,
              sg_dire: dir == 'h' ? 0 : 1,
              sg_spec: this.ScreenSettingValue,
              screens: screens,
            };
            bind_shop(bindParam).then((res) => {
              if (res.rst == "ok") {
                const params = {
                  g_group_id: localStorage.getItem("group_id"),
                  shop_id: this.shopData.shop_id,
                  shop_tags: this.requestLabel,
                };
                this.$emit("saveTags", params);
                this.$message.closeAll()
                this.$message.success("绑定成功");

                // this.searchState = false;
                // this.shopState = false;
                // setTimeout(() => {
                //   this.SearchFrom.shop_name = "";
                // }, 500);
              } else {
                // this.$message.warning(res.rst);
                this.$message.warning("绑定失败,请检查绑定的屏幕方向是否一致")
              }
            });
          } else {
            const params = {
              g_group_id: localStorage.getItem("group_id"),
              shop_id: this.shopData.shop_id,
              shop_tags: this.requestLabel,
            };
            this.$emit("saveTags", params);
          }

        }
      }
    },
    // 取消
    cancel() {
      this.$emit("cancel");
      this.SearchFrom.shop_name = "";
      this.searchState = false;
      this.shopState = false;
    },
    // 清空
    clears() {
      // console.log(this.ScreenSettingValueList);
      // this.shopData.screen_group_list[0].screens.forEach((item) => {
      //   item.disabled = false;
      // });
      this.shopData.screen_list.forEach((item) => {
        item.disabled = false;
      });
      this.ScreenSettingValue = null;
      this.saveState = true
      // this.screenGroupState = true;
    },
    // 获取联屏标签
    getConnectTag() {
      const params = {
        tag_type: "sngroup", //（string）标签类型（固定）
        page: 0, //（int）页码
        size: 50, //（int）每页显示的数量
        dmbSource: Boolean(true), //（bool）是否为dmb的联屏组（固定为Ture）
      };
      get_tags_list(params).then(res => {
        res.data[0].tags_list.forEach(item => {
          this.SelectLabel.push({
            value: item,
            label: item
          })
        })
      })
    }
  },
  created() {
    this.getConnectTag()
  },
};
</script>
<style lang="scss" scoped>
.drawer {
  position: relative;

  .drawer_search {
    margin: 50px 16px 2px 20px;

    .search_input {
      .el-input {
        width: 300px;
        height: 35px;
      }
    }

    .search_button {
      width: 88px;
      height: 32px;
      background-color: var(--btn-background-color);
      font-size: 14px;
      color: #fff;
    }
  }

  .searchTable {
    padding: 0 20px;
  }

  .shopData {
    padding: 0 0 0 23px;
    margin-top: -12px;

    .shop_header {
      .header_data {
        margin-top: 20px;
        font-size: 14px;
        font-weight: bold;
        text-align: left;
        display: flex;

        div {
          width: 70px;
        }

        span {
          padding-left: 10px;
        }
      }
    }

    .header_table {
      margin-top: 18px;
    }

    .screen_setting {
      margin-top: 20px;

      .setting_title {
        font-size: 14px;
        color: rgba(80, 80, 80, 1);
        font-weight: bold;
      }

      .groupBtn {
        width: 88px;
        height: 38px;
        background-color: var(--text-color);
        border-radius: 6px;
        font-size: 14px;
        color: #fff;
        font-weight: bold;
        margin-left: 10px;
      }

      .setting_arrange {
        display: flex;
        justify-content: flex-start;

        div {
          width: 90px;
          height: 48px;
          margin-top: 24px;

          ::v-deep .el-input__inner {
            height: 50px !important;
          }
        }
      }
    }

    .screen_label {
      margin-top: 30px;

      .label_title {
        font-size: 14px;
        color: rgba(80, 80, 80, 1);
        font-weight: bold;
      }
    }
  }

  .bottom {
    // position: absolute;
    // bottom: 40px;
    // right: 20px;
    margin-top: 20px;
    width: 100%;
    height: 60px;
    background-color: #fff;
    text-align: right;

    .cancel {
      width: 88px;
      height: 35px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: bold;
      color: #fff;
      background-color: var(--btn-background-color);
    }

    .save {
      width: 88px;
      border-radius: 6px;
      font-size: 14px;
      background-color: var(--btn-background-color);
      color: #fff;
      font-weight: bold;
    }
  }
}

.scrool {

  /* 定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 2px; //定义滚动条宽度
    height: 2px; //定义滚动条高度
    border-radius: 10px; //定义滚动条圆角
    background-color: #ccc; //定义滚动条颜色
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5); //定义轨道阴影
    border-radius: 10px; //定义轨道圆角
    background-color: rgba(4, 56, 114, 0.5); //定义轨道颜色
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px; //定义滑块圆角
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5); //定义滑块阴影
    background-color: rgba(2, 33, 54, 0.5); //定义滑块颜色
  }
}

::v-deep .el-drawer {
  width: 35% !important;
}
</style>