<template>
  <div class='box'>
    <div class='top'>
      <div>
        <span class='el-icon-back' @click.stop='backPrePage'></span>
        设备列表
      </div>
    </div>
    <ul>
      <li v-for='(item,index) in 8' :key='index'>
        <div>
          <img src='../../assets/img/home_img/devlist.jpg'>
          <div class='play' @click.stop='clickShowImg'>
            <span class='el-icon-view'></span>
            预览
          </div>
        </div>
        <div>
          <span>截取时间：</span>
          <span>2022-03-18 18:26:11</span>
        </div>
      </li>
      <div class='bigBg' v-show='viewBgImg'>
        <!-- <img src='../../assets/img/home_img/audio.png'> -->
        <p @click.stop='viewBgImg=false'>×</p>
      </div>

    </ul>
<!--    大图-->

  </div>
</template>

<script>
export default {
  name: 'ShotRecord',
  data(){
    return{
    //  大图预览显示
      viewBgImg:false,
    }
  },
  methods:{
    //点击预览
    clickShowImg(){
      this.viewBgImg = true;
    }
  }
}
</script>

<style scoped>
.box{
  width: 100%;
  background-color: #fff;
}
.top{
  height: 60px;
  width: 100%;
  padding: 0 18px 0 13px;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
  border-bottom: 1px solid #ecebeb;
}
.el-icon-back{
  color: var(--btn-background-color);
  font-size: 25px;
  margin-right: 10px;
  vertical-align: middle;
}
ul{
  display: flex;
  padding: 17px 24px;
  flex-wrap: wrap;
  position: relative;
}
ul>li{
  width: 235px;
  height: 200px;
  margin-right: 15px;
  position: relative;
}
ul>li>div>img{
  height: 152px;
  width: 100%;
}
ul>li>div{
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  margin-top: 10px;
  position: relative;
}
ul>li>div>img:hover ~.play{
  display: block;
}
ul>li>div>span:nth-last-child(1){
  margin-left: 20px;
}
.play{
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.4642857142857143);
  border-radius: 30px;
  font-size: 14px;
  color: #fff;
  border: rgba(255, 255, 255, 1) solid 1px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  margin: auto;
  right: 0;
  bottom: 0;
  display: none;
}
.play>span{
  font-size: 24px;
  display: block;
  margin-top: 10px;
  vertical-align: middle;
  color: rgba(255, 255, 255, 1);
}
.sideStep>div>img:hover ~.play{
  display: block;
}
.bigBg{
  width: 900px;
  height: 506px;
  position: absolute;
  left: 50%;
  top: 50px;
  object-fit: contain;
  margin-left: -450px;
}
.bigBg>img{
  width: 100%;
  height: 100%;
}
.bigBg>p{
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: #ffff;
  text-align: center;
  position: absolute;
  top: -15px;
  right: -20px;
  font-size: 30px;
  background-color: #111111;
}
</style>
