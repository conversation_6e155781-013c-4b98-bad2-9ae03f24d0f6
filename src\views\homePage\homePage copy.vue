<template>
  <div class="homePage">
    <div class="homebody">
      <div class="homebodyLeft borderRadius" :style="{ width: $i18n.locale === 'en' || 'vi' ? '81%' : '79%' }">
        <div class="homebodyLeftTop">
          <div class="homebodyLeftTop-left2" style="font-size: 0.15rem">
            欢迎回来，{{ userName }}
          </div>
          <div class="homebodyLeftTop-right2"
            :style="{ flex: $i18n.locale === 'en' ? '0 1 42%' : $i18n.locale === 'vi' ? '0 1 50%' : '0 1 35%' }">
            <span>
              数据更新时间：
              {{ abUpdateTime }}</span>
            <!-- <span class="click-effect2" @click="updateData">{{ $transition_lang("dashboardHome.Update_data") }} </span> -->
            <span class="click-effect2" @click="handleUpdate">{{
              this.has_running_job ? '数据更新中...' : '更新数据'
            }} </span>
          </div>
        </div>
        <div class="homebodyLeftList">
          <div class="homebodyLeftList-top">
            <div class="homebodyLeftList-top-left">
              门店数据
            </div>
            <div class="homebodyLeftList-top-right">
              设备数据
            </div>
          </div>
          <ul class="stats-list">
            <li @click="openPage('AllStores')">
              <el-popover transition="el-fade-in-linear" placement="right-end" visible-arrow="false" trigger="hover"
                popper-class="popover-cla">
                <div style="font-size: 0.13rem; font-weight: 600">
                  门店分析
                </div>
                <div class="popover-content-cal">
                  <div class="popover-content-cal-title">
                    营业状态
                  </div>
                  <div class="popover-content-cal-Progress"
                    v-if="progressWidths.online || progressWidths.offline || progressWidths.pendingOperation || progressWidths.pause || progressWidths.closed">
                    <div class="popover-content-cal-Progress-online" v-if="progressWidths.online"
                      :style="{ width: progressWidths.online, borderRadius: borderRadii.online }"></div>
                    <div class="popover-content-cal-Progress-offline" v-if="progressWidths.offline"
                      :style="{ width: progressWidths.offline, borderRadius: borderRadii.offline }"> </div>
                    <div class="popover-content-cal-Progress-pendingOperation" v-if="progressWidths.pendingOperation"
                      :style="{ width: progressWidths.pendingOperation, borderRadius: borderRadii.pendingOperation }">
                    </div>
                    <div class="popover-content-cal-Progress-stop_business" v-if="progressWidths.pause"
                      :style="{ width: progressWidths.pause, borderRadius: borderRadii.pause }"></div>
                    <div class="popover-content-cal-Progress-closed" v-if="progressWidths.closed"
                      :style="{ width: progressWidths.closed, borderRadius: borderRadii.closed }"></div>
                  </div>
                  <div class="popover-content-cal-content">
                    <div class="status-item">
                      <div class="color-block online"></div>
                      <span>营业中-在线
                        <el-tooltip placement="top" :content="$transition_lang(
                          'dashboardHome.statuses.online.desc'
                        )
                          " popper-class="tooltip-cla">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>
                        ：</span>

                      <div class="count">
                        {{ shop_And_ds.shop_open_online_cnt }} {{
                          $transition_lang("dashboardHome.Stores") }}
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="color-block offline"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.statuses.normalOffline.label"
                        )
                      }}
                        <el-tooltip placement="top" :content="$transition_lang(
                          'dashboardHome.statuses.normalOffline.desc'
                        )
                          " popper-class="tooltip-cla">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>
                        ：</span>

                      <div class="count">
                        {{ shop_And_ds.shop_open_offline_normal_cnt }} {{ $transition_lang("dashboardHome.Stores") }}
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="color-block pending"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.statuses.pending2.label"
                        )
                      }}
                        <el-tooltip placement="top" :content="$transition_lang(
                          'dashboardHome.statuses.pending2.desc'
                        )
                          " popper-class="tooltip-cla">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>
                        ：</span>

                      <div class="count">
                        {{ shop_And_ds.shop_wait_cnt }} {{ $transition_lang("dashboardHome.Stores") }}
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="color-block stop_business"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.statuses.stop_business.label"
                        )
                      }}
                        <el-tooltip placement="top" :content="$transition_lang(
                          'dashboardHome.statuses.stop_business.desc'
                        )
                          " popper-class="tooltip-cla">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>
                        ：</span>

                      <div class="count">
                        {{ shop_And_ds.shop_pause_cnt }} {{ $transition_lang("dashboardHome.Stores") }}
                      </div>
                    </div>
                    <div class="status-item">
                      <div class="color-block closed"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.statuses.closed.label"
                        )
                      }}
                        <el-tooltip placement="top" :content="$transition_lang(
                          'dashboardHome.statuses.closed.desc'
                        )
                          " popper-class="tooltip-cla">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>
                        ：</span>

                      <div class="count">
                        {{ shop_And_ds.shop_close_cnt }} {{ $transition_lang("dashboardHome.Stores") }}
                      </div>
                    </div>

                  </div>
                </div>
                <div class="stats-list-box" slot="reference">
                  <div class="circle-icon">
                    <i class="el-icon-s-shop"></i>
                  </div>
                  <div class="stats-content">
                    <div class="stats-content-num">
                      {{ shop_And_ds.shop_all_cnt }}
                      <span>{{
                        $transition_lang("dashboardHome.Stores")
                      }}</span>
                    </div>
                    <div class="stats-content-title">
                      {{
                        $transition_lang("dashboardHome.metrics.totalStores")
                      }}
                    </div>
                  </div>
                </div>
              </el-popover>
            </li>
            <li>
              <div class="stats-list-box">
                <div class="circle-icon">
                  <i class="el-icon-s-shop" style="color: red"></i>
                </div>
                <div class="stats-content">
                  <div class="stats-content-num">
                    {{ shop_And_ds.shop_open_offline_abnormal_cnt }}
                    <span>{{ $transition_lang("dashboardHome.Stores") }}</span>
                  </div>
                  <div class="stats-content-title">
                    {{
                      $transition_lang("dashboardHome.metrics.abnormalOffline2")
                    }}
                    <el-tooltip popper-class="tooltip-cla" placement="top" :content="$transition_lang('dashboardHome.metrics.networkAlert')
                      ">
                      <i class="el-icon-warning-outline tip-icon"></i>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </li>
            <li @click="openPage('totalDevices')">
              <el-popover transition="el-fade-in-linear" placement="right-start" visible-arrow="false" trigger="hover"
                popper-class="popover-cla2">
                <div style="font-size: 0.13rem; font-weight: 600">
                  {{
                    $transition_lang("dashboardHome.metrics.deviceAnalytics")
                  }}
                </div>
                <div class="popover-content-cal">
                  <div class="popover-content-cal-title">
                    {{ $transition_lang("dashboardHome.metrics.deviceStatus") }}
                  </div>
                  <div class="popover-content-cal-Progress"
                    v-if="progressWidths2.online2 || progressWidths2.offline2 || progressWidths2.abnormal">
                    <div class="popover-content-cal-Progress-online2" v-if="progressWidths2.online2"
                      :style="{ width: progressWidths2.online2, borderRadius: borderRadii2.online2 }"></div>
                    <div class="popover-content-cal-Progress-offline2" v-if="progressWidths2.offline2"
                      :style="{ width: progressWidths2.offline2, borderRadius: borderRadii2.offline2 }">
                    </div>
                    <div class="popover-content-cal-Progress-abnormal" v-if="progressWidths2.abnormal"
                      :style="{ width: progressWidths2.abnormal, borderRadius: borderRadii2.abnormal }">
                    </div>
                  </div>
                  <div class="popover-content-cal-content2">
                    <div class="status-item2">
                      <div class="color-block online"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.metrics.onlineDevices"
                        )
                      }}
                        <el-tooltip :content="$transition_lang('dashboardHome.metrics.onlineTip')
                          " popper-class="tooltip-cla" placement="top">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>：</span>

                      <div class="count">
                        {{ shop_And_ds.ds_online_cnt }} {{ $transition_lang("dashboardHome.devices") }}
                      </div>
                    </div>
                    <div class="status-item2">
                      <div class="color-block pending2"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.metrics.normalOfflineDevices"
                        )
                      }}
                        <el-tooltip :content="$transition_lang(
                          'dashboardHome.metrics.normalOfflineTip'
                        )
                          " popper-class="tooltip-cla" placement="top">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>：</span>

                      <div class="count">
                        {{ shop_And_ds.ds_offline_normal_cnt }} {{ $transition_lang("dashboardHome.devices") }}
                      </div>
                    </div>
                    <div class="status-item2">
                      <div class="color-block abnormal"></div>
                      <span>{{
                        $transition_lang(
                          "dashboardHome.metrics.abnormalOffline"
                        )
                      }}
                        <el-tooltip :content="$transition_lang(
                          'dashboardHome.metrics.abnormalOfflineTip'
                        )
                          " popper-class="tooltip-cla" placement="top">
                          <i class="el-icon-warning-outline tip-icon"></i> </el-tooltip>：</span>

                      <div class="count">
                        {{ shop_And_ds.ds_offline_abnormal_cnt }} {{
                          $transition_lang("dashboardHome.devices") }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="stats-list-box" slot="reference">
                  <div class="circle-icon">
                    <i class="el-icon-monitor"></i>
                  </div>
                  <div class="stats-content">
                    <div class="stats-content-num">
                      {{ shop_And_ds.ds_all_cnt }}
                      <span>{{
                        $transition_lang("dashboardHome.devices")
                      }}</span>
                    </div>
                    <div class="stats-content-title">
                      {{
                        $transition_lang("dashboardHome.metrics.totalDevices")
                      }}
                    </div>
                  </div>
                </div>
              </el-popover>

              <!-- <el-divider direction="vertical"></el-divider> -->
            </li>
            <li @click="openPage('networkExceptions')">
              <div class="stats-list-box">
                <div class="circle-icon">
                  <i class="el-icon-monitor" style="color: red"></i>
                </div>
                <div class="stats-content">
                  <div class="stats-content-num2">
                    {{ shop_And_ds.ds_offline_abnormal_cnt }}
                    <span>{{ $transition_lang("dashboardHome.devices") }}</span>
                  </div>
                  <div class="stats-content-title">
                    {{
                      $transition_lang("dashboardHome.metrics.abnormalOffline2")
                    }}
                    <el-tooltip popper-class="tooltip-cla" :content="$transition_lang('dashboardHome.metrics.abnormalOfflineTip')
                      " placement="top">
                      <i class="el-icon-warning-outline tip-icon"></i>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="homebodyLeftEchartsOnline">
          <div class="EchartsTitle">
            <div class="homebodyLeftTop-left">
              {{ $transition_lang("dashboardHome.operations.campaign.title") }}
            </div>
            <div class="homebodyLeftTop-right">
              <span>{{
                $transition_lang(
                  "dashboardHome.operations.campaign.lastUpdated"
                )
              }}{{ updateTime }}</span>
              <span class="click-effect" @click="openMarkScheduleDialog">{{
                $transition_lang(
                  "dashboardHome.operations.campaign.markCampaign"
                )
              }}</span>
              <span class="click-effect" @click="openDownloadDataDialog">{{
                $transition_lang(
                  "dashboardHome.operations.campaign.downloadData"
                )
              }}</span>
            </div>
          </div>
          <div class="EchartsOnline">
            <onlineEcharts :echartsList="echartsList"></onlineEcharts>
          </div>
        </div>
        <!-- <div class="homebodyLeftEchartsOffline">
            <div class="EchartsTitle">离线运行档期数据</div>
            <div class="EchartsOffline"><offlineEcharts></offlineEcharts></div>
          </div> -->
      </div>
      <div class="homebodyright" :style="{ width: $i18n.locale === 'en' || 'vi' ? '18%' : '20%' }">
        <div class="homebodyrightTop">
          <div class="homebodyrightTop-title">
            {{ $transition_lang("quickAccess") }}
          </div>
          <div class="homebodyrightTop-list">
            <ul>
              <li v-for="(item, index) in homeTop" :key="index" @click.stop="toGo(index)">
                <img :src="topimgs[index]" />
                <p>{{ $transition_lang(item) }}</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="homebodyrightBottom">
          <div class="homebodyrightBottom-title">
            {{ $t("h.earlyWarning") }}
          </div>
          <div class="homebodyrightBottom-list">
            <ul>
              <li v-for="(item, index) in mainList" :key="index" :class="liIndex == index ? 'warning-active' : ''"
                @click.stop="toScreenList(item, index)">
                <div class="warning-top">
                  <img :src="homeImg[index]" />
                  <p>
                    {{ $t(item.changeTab) }}{{ item.threshold }}
                    {{ $t(item.timeType) }}
                  </p>
                </div>
                <div class="warning-bottom">
                  <div>
                    <img src="../../assets/img/home_img/little_work.svg" />
                    <span>{{ item.cnt }}</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <el-dialog :title="$transition_lang('dashboardHome.operations.campaign.markCampaign')
      " :visible.sync="markScheduleDialogVisible" width="25%" center custom-class="operation-dialog"
      :before-close="handleCloseMarkScheduleDialog">
      <el-form ref="markScheduleForm" :model="markScheduleForm" :rules="markScheduleRules" label-width="100px">
        <el-form-item :label="$transition_lang(
          'dashboardHome.operations.campaign.form.campaignName'
        )
          " prop="markScheduleName" :label-width="$i18n.locale === 'en' || 'vi' ? '150px' : '100px'">
          <el-input v-model="markScheduleForm.markScheduleName" :placeholder="$transition_lang(
            'dashboardHome.operations.campaign.form.inputName')" type="text" maxlength="15" show-word-limit></el-input>
        </el-form-item>
        <el-form-item :label="$transition_lang(
          'dashboardHome.operations.campaign.form.markMethod'
        )
          " prop="" :label-width="$i18n.locale === 'en' || 'vi' ? '150px' : '100px'">
          <el-radio-group v-model="markScheduleForm.markScheduleMode" @change="changeResource">
            <el-radio :label="1">{{
              $transition_lang(
                "dashboardHome.operations.campaign.form.immediate"
              )
            }}</el-radio>
            <el-radio :label="2">{{
              $transition_lang(
                "dashboardHome.operations.campaign.form.scheduled"
              )
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$transition_lang('dashboardHome.operations.campaign.form.markTime')
          " prop="markScheduleTime" v-if="markScheduleForm.markScheduleMode === 2"
          :label-width="$i18n.locale === 'en' || 'vi' ? '150px' : '100px'">
          <el-date-picker v-model="markScheduleForm.markScheduleTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
            style="width: 100%" :placeholder="$transition_lang(
              'dashboardHome.operations.campaign.form.selectDateTime'
            )
              ">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseMarkScheduleDialog">{{
          $transition_lang("cancel")
        }}</el-button>
        <el-button type="primary" @click="confirMarkSchedule('markScheduleForm')">{{ $transition_lang("confirm")
        }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$transition_lang('dashboardHome.operations.campaign.downloadData')
      " :visible.sync="operationalDataDialogVisible" width="25%" center custom-class="operation-dialog">
      <el-form ref="operationalDataForm" :model="operationalDataForm" :rules="operationalDataRules" label-width="100px">
        <el-form-item :label="$transition_lang(
          'dashboardHome.operations.campaign.form.campaignName'
        )
          " prop="scheduleName" :label-width="$i18n.locale === 'en' || 'vi' ? '150px' : '100px'">
          <el-select clearable v-model="operationalDataForm.scheduleName" :placeholder="$transition_lang(
            'dashboardHome.operations.campaign.form.validation.selectCampaign'
          )
            ">
            <el-option v-for="item in optionEchartsList" :key="item.admin_uid" :label="item.name"
              :value="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="operationalDataDialogVisible = false">{{
          $transition_lang("cancel")
        }}</el-button>
        <el-button type="primary" @click="downloadData('operationalDataForm')">{{ $transition_lang("confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import onlineEcharts from "./components/onlineEcharts.vue";
import { get_adm_home_info, get_latest_ds_shop_stat_datas, upt_ds_shop_stat_datas } from "@/api/index/index";
import {
  get_sche_stat_job_list,
  create_sche_stat_job,
} from "@/api/homepage/homepage.js";
import { translate as $t } from "@/i18n";
import { h } from "@/i18n/lan/zh.js";
// console.log($t, "$t");

import {
  player,
  up,
  plug,
  tan,
  timer,
  no,
  pubShare,
  sources,
  addStore,
  tian,
} from "../../utils/publicInfo";
export default {
  components: {
    onlineEcharts,
  },
  watch: {
    "$i18n.locale"() {
      this.setFormRules();
      // this.isShowEcharts=false
      // this.isShowEcharts=true
      this.dataUpdataText = this.$transition_lang("dashboardHome.Update_data")

    },
    "$store.state.user.canRefresh"(newVal, oldVal) {
      if (newVal) {
        //   this.get_adm_home_info();
        this.get_latest_ds_shop_stat_datas()
        this.getInfoLev();
        this.$store.state.user.canRefresh = false;
      }
    },

  },
  data() {
    return {
      userName: "",
      storeCount: 91110000,
      deviceCount: 2000000,
      abnormalStoreCount: 2712311,
      abnormalDeviceCount: 480000000,
      homeTop: [
        "quickPublish",
        "uploadResource",
        "addStore",
        "contentProduction",
      ],
      homeImg: [player, up, plug, tan, timer, no],
      topimgs: [pubShare, sources, addStore, tian],
      // homeTitle:["player离线≥72h","U盘异常接入≥1m","HDMI线异常≥1h","MAC地址冲突","CPU≥80% 5min预警","无内容≥48h"],
      mainList: [],
      liIndex: 0,
      //门店数据
      bottomList: [],
      //设备数据
      bottomList2: [],

      //   门店和设备数据
      bottomList3: [],

      //   标记档期
      markScheduleDialogVisible: false,

      markScheduleForm: {
        markScheduleName: "",
        markScheduleMode: "",
        markScheduleTime: "",
      },

      markScheduleRules: {
        // markScheduleName: [
        //   { required: true, message: "请输入档期名称", trigger: "blur" },
        // ],
        // markScheduleTime: [
        //   { required: true, message: "请选择档期时间", trigger: "blur" },
        // ],
      },
      //   运营数据
      operationalDataDialogVisible: false,

      operationalDataForm: {
        scheduleName: "",
      },
      operationalDataRules: {
        scheduleName: [
          //   { required: true, message: "请选择档期", trigger: "change" },
        ],
      },
      echartsList: [],
      optionEchartsList: [],
      updateTime: "",
      isShowEcharts: true,
      isUpdating: false, // 是否正在拉取数据
      clickTimer: null,      // 点击计时器
      dataUpdataText: this.$transition_lang("dashboardHome.Update_data"),
      abUpdateTime: '', // 异常离线更新时间，
      has_running_job: 0, //  开关  是否数据正在更新

      shop_And_ds: {
        shop_all_cnt: 0,  // 门店总数
        shop_wait_cnt: 0,  // 待营业
        shop_close_cnt: 0,  // 已闭店
        shop_pause_cnt: 0, // 暂停营业
        shop_open_cnt: 0, // 营业中 - 门店总数
        shop_open_online_cnt: 0,  // 营业中 - 在线总数
        shop_open_offline_cnt: 0, // 营业中 - 离线总数
        shop_open_offline_normal_cnt: 0, // 营业中 - 正常离线数
        shop_open_offline_abnormal_cnt: 0,  // 营业中 - 异常离线数

        ds_all_cnt: 0, // 设备总数
        ds_online_cnt: 0,// 在线设备数
        ds_offline_cnt: 0,  // 离线设备数
        ds_offline_normal_cnt: 0, // 正常离线设备数
        ds_offline_abnormal_cnt: 0,// 异常离线设备数


      }



    };
  },
  created() {
    this.userName = this.$store.state.user.user.username;
    this.$store.dispatch("GetInfo").then((res) => {
      // console.log(res);
      if (res.data[0].curent_branch) {
        //   console.log("%cyou", "color:red");
      } else {
      }
    });
    //   this.get_adm_home_info();
    this.get_latest_ds_shop_stat_datas()

  },
  computed: {
    // 计算每个状态的宽度
    progressWidths() {
      return {
        online: this.calProgress('online'),
        offline: this.calProgress('offline'),
        pendingOperation: this.calProgress('pendingOperation'),
        closed: this.calProgress('closed'),
        pause: this.calProgress('pause')


      };
    },
    // 计算每个状态的圆角
    borderRadii() {
      return {
        online: this.changeBorderRadius('online'),
        offline: this.changeBorderRadius('offline'),
        pendingOperation: this.changeBorderRadius('pendingOperation'),
        closed: this.changeBorderRadius('closed'),
        pause: this.changeBorderRadius('pause'),
      }
    },

    // 计算每个状态的宽度
    progressWidths2() {
      return {
        online2: this.calProgress2('online2'),
        offline2: this.calProgress2('offline2'),
        abnormal: this.calProgress2('abnormal')
      };
    },
    // 计算每个状态的圆角
    borderRadii2() {
      return {
        online2: this.changeBorderRadius2('online2'),
        offline2: this.changeBorderRadius2('offline2'),
        abnormal: this.changeBorderRadius2('abnormal')
      };
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setFormRules();
      this.getInfoLev();
      this.get_sche_stat_job_list_fn();
    });

    // this.$nextTick(() => {
    //   const walker = document.createTreeWalker(this.$el, NodeFilter.SHOW_TEXT);
    //   const texts = new Set();
    //   while (walker.nextNode()) {
    //     const text = walker.currentNode.textContent.trim();
    //     if (
    //       text &&
    //       !walker.currentNode.parentElement.closest("script, style")
    //     ) {
    //       texts.add(text);
    //     }
    //   }
    //   console.log("当前组件文本节点：", Array.from(texts));
    // });
  },

  methods: {
    async get_latest_ds_shop_stat_datas() {
      // 门店数据和设备数据
      try {
        const res = await get_latest_ds_shop_stat_datas({})
        if (res.rst === "ok") {
          this.abUpdateTime = res.data[0].update_tm
          this.has_running_job = res.data[0].has_running_job

          // 处理数据更新
          const node = res.data[0].note
          Object.keys(this.shop_And_ds).forEach(k2 => {
            if (node.hasOwnProperty(k2)) {
              this.shop_And_ds[k2] = node[k2]
            }
          })
        }
      } catch (error) {
        console.error('获取最新数据失败:', error)
      }
    },

    handleUpdate() {
      // 防抖处理：500ms内重复点击无效
      if (this.clickTimer) return

      this.clickTimer = setTimeout(() => {
        clearTimeout(this.clickTimer)
        this.clickTimer = null
      }, 500)

      // 正在更新时的提示
      if (this.has_running_job > 0) {
        this.$message.warning(this.$transition_lang("dashboardHome.Fetching_data"))
        return
      }
      this.updateData()
    },
    async updateData() {
      try {
        // this.isUpdating = true
        // this.dataUpdataText = this.$transition_lang("dashboardHome.Updating_data")
        const res = await upt_ds_shop_stat_datas({})
        if (res.rst === 'ok') {
          // 成功后的数据获取
          const data = res.data[0] // 次数
          await this.get_latest_ds_shop_stat_datas()
        } else {
          this.$message.warning(this.$transition_lang("dashboardHome.Fetching_data"))
          return
        }
      } catch (error) {
        this.$message.error(this.$transition_lang("dashboardHome.updateFailed"))

      } finally {
        // this.isUpdating = false
        // this.dataUpdataText = this.$transition_lang("dashboardHome.Update_data")
      }
    },


    openPage(item) {
      switch (item) {
        case 'AllStores':
          this.$router.push({
            path: "/shopManage/storelist",
          });
          break;
        //   case 'OnlineOperaton':
        //     this.$router.push({
        //       path: "/shopManage/storelist",
        //       query: {
        //         value: 9,// 营业中
        //       }
        //     });
        //     break;
        case 'totalDevices':
          this.$router.push({
            path: "deviceManage/screenGroup",
          })
          break;
        case 'networkExceptions':
          this.$router.push({
            path: "deviceManage/abnormalDetail",
            //   query: {
            //    ds_offline_abnormal_cnt:this.shop_And_ds.ds_offline_abnormal_cnt
            //   }
          })
          break;
      }
    },
    get_sche_stat_job_list_fn() {
      const params = {
        // mark_stime: "2025-03-01 09:00:00",                    // optional (String)检索开始时间
        // mark_etime: "2025-03-01 10:00:00",                    //  optional (String)检索结束时间
      };
      get_sche_stat_job_list(params).then((res) => {
        if (res.rst == "ok") {
          const data = res.data[0].content;
          this.echartsList = data.reverse();
          this.optionEchartsList = [...data].reverse();
          this.updateTime = this.echartsList[this.echartsList.length - 1].mark_tm;


        } else {
          this.echartsList = []
          this.optionEchartsList = []
          this.$message.warning(res.error_msg);
        }
      });
    },
    setFormRules() {
      this.markScheduleRules = {
        markScheduleName: [
          {
            required: true,
            message: this.$transition_lang(
              "dashboardHome.operations.campaign.form.inputName"
            ),
            trigger: "blur",
          },
        ],
        markScheduleTime: [
          {
            required: true,
            message: this.$transition_lang(
              "dashboardHome.operations.campaign.form.validation.selectTime"
            ),
            trigger: "blur",
          },
        ],
      };

      this.operationalDataRules = {
        scheduleName: [
          {
            required: true,
            message: this.$transition_lang(
              "dashboardHome.operations.campaign.form.validation.selectCampaign"
            ),
            trigger: "change",
          },
        ],
      };
    },
    calProgress2(item) {

      const total = this.shop_And_ds.ds_online_cnt + this.shop_And_ds.ds_offline_normal_cnt + this.shop_And_ds.ds_offline_abnormal_cnt
      const ds_online_cnt = this.shop_And_ds.ds_online_cnt
      const ds_offline_op_cnt = this.shop_And_ds.ds_offline_normal_cnt
      const abnormal = this.shop_And_ds.ds_offline_abnormal_cnt
      if (item === "online2") {

        if (!ds_online_cnt) {
          return 0;
        }
        const percentage = ((ds_online_cnt / total) * 100).toFixed(2);
        return `calc(${percentage}% - 2px)`;
      }
      if (item === "offline2") {
        if (!ds_offline_op_cnt) {
          return 0
        }
        const percentage = ((ds_offline_op_cnt / total) * 100).toFixed(2);
        return `calc(${percentage}% - 4px)`;
      }

      if (item === "abnormal") {
        if (!(abnormal)) {
          return 0
        }
        const percentage = (((abnormal) / total) * 100).toFixed(2);
        return `calc(${percentage}% - 2px)`;
      }
    },
    changeBorderRadius(currentItem) {
      // 定义状态顺序（根据视觉层叠顺序）
      const statusOrder = ['online', 'offline', 'pendingOperation', 'pause', 'closed'];

      // 检查每个状态是否存在（通过宽度判断）
      const existingStatus = statusOrder.filter(status => {
        const width = this.calProgress(status);
        return width !== 0; // 确保宽度有效且大于0
      });

      // 当前状态不存在时直接返回
      if (!existingStatus.includes(currentItem)) return '0';

      // 根据条件判断圆角
      switch (currentItem) {
        case 'online':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if (existingStatus[0] === 'online' && existingStatus.length > 1) { // 第一个存在且有后续
            return '0.1rem 0 0 0.1rem';
          }
          break;

        case 'offline':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if ( // 中间存在
            existingStatus.indexOf('offline') > 0 &&
            existingStatus.indexOf('offline') < existingStatus.length - 1
          ) {
            return '0';
          } else if ( // 第一个存在
            existingStatus[0] === 'offline' &&
            existingStatus.length > 1
          ) {
            return '0.1rem 0 0 0.1rem';
          } else if ( // 最后一个存在
            existingStatus[existingStatus.length - 1] === 'offline' &&
            existingStatus.length > 1
          ) {
            return '0 0.1rem 0.1rem 0';
          }
          break;

        case 'pendingOperation':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if ( // 中间存在
            existingStatus.indexOf('pendingOperation') > 0 &&
            existingStatus.indexOf('pendingOperation') < existingStatus.length - 1
          ) {
            return '0';
          } else if ( // 第一个存在
            existingStatus[0] === 'pendingOperation' &&
            existingStatus.length > 1
          ) {
            return '0.1rem 0 0 0.1rem';
          } else if ( // 最后一个存在
            existingStatus[existingStatus.length - 1] === 'pendingOperation' &&
            existingStatus.length > 1
          ) {
            return '0 0.1rem 0.1rem 0';
          }
          break;

        case 'pause':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if ( // 中间存在
            existingStatus.indexOf('pause') > 0 &&
            existingStatus.indexOf('pause') < existingStatus.length - 1
          ) {
            return '0';
          } else if ( // 第一个存在
            existingStatus[0] === 'pause' &&
            existingStatus.length > 1
          ) {
            return '0.1rem 0 0 0.1rem';
          } else if ( // 最后一个存在
            existingStatus[existingStatus.length - 1] === 'pause' &&
            existingStatus.length > 1
          ) {
            return '0 0.1rem 0.1rem 0';
          }
          break;


        case 'closed':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if (existingStatus[existingStatus.length - 1] === 'closed' && existingStatus.length > 1) { // 最后一个存在
            return '0 0.1rem 0.1rem 0';
          }
          break;
      }

      // 默认无圆角
      return '0';
    },
    changeBorderRadius2(currentItem) {
      // 定义状态顺序
      const statusOrder = ['online2', 'offline2', 'abnormal'];

      // 检查每个状态是否存在（通过宽度判断）
      const existingStatus = statusOrder.filter(status => {
        const width = this.calProgress2(status);
        // console.log(width,'width');
        return width !== 0; // 确保宽度有效且大于0
      });

      // 当前状态不存在时直接返回
      if (!existingStatus.includes(currentItem)) return '0';

      //  console.log(existingStatus,'existingStatus');

      // 根据条件判断圆角
      switch (currentItem) {
        case 'online2':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if (existingStatus[0] === 'online2' && existingStatus.length > 1) { // 第一个存在且有后续
            return '0.1rem 0 0 0.1rem';
          }
          break;

        case 'offline2':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if ( // 中间存在
            existingStatus.indexOf('offline2') > 0 &&
            existingStatus.indexOf('offline2') < existingStatus.length - 1
          ) {
            return '0';
          } else if ( // 第一个存在
            existingStatus[0] === 'offline2' &&
            existingStatus.length > 1
          ) {
            return '0.1rem 0 0 0.1rem';
          } else if ( // 最后一个存在
            existingStatus[existingStatus.length - 1] === 'offline2' &&
            existingStatus.length > 1
          ) {
            return '0 0.1rem  0.1rem 0';
          }
          break;

        case 'abnormal':
          if (existingStatus.length === 1) { // 唯一存在
            return '0.1rem';
          } else if (existingStatus[existingStatus.length - 1] === 'abnormal' && existingStatus.length > 1) { // 最后一个存在
            return '0 0.1rem 0.1rem 0';
          }
          break;
      }
      // 默认无圆角
      return '0';
    },
    calProgress(item) {
      const total = this.shop_And_ds.shop_open_online_cnt + this.shop_And_ds.shop_open_offline_normal_cnt + this.shop_And_ds.shop_wait_cnt + this.shop_And_ds.shop_close_cnt + this.shop_And_ds.shop_pause_cnt
      const online = this.shop_And_ds.shop_open_online_cnt
      const offline = this.shop_And_ds.shop_open_offline_normal_cnt
      const pendingOperation = this.shop_And_ds.shop_wait_cnt
      const closed = this.shop_And_ds.shop_close_cnt
      const pause = this.shop_And_ds.shop_pause_cnt

      // const total =30;
      // const online=10
      // const offline=0
      // const pendingOperation=0
      // const closed=20


      if (item === "online") {
        if (!(online)) {
          return 0;
        }
        const percentage = (((online) / total) * 100).toFixed(2);
        //   console.log(percentage,'percentage1');
        return `calc(${percentage}% - 2px)`;
      }
      if (item === "offline") {
        if (!offline) {
          return 0;
        }
        const percentage = (((offline) / total) * 100).toFixed(2);
        //   console.log(percentage,'percentage2');
        return `calc(${percentage}% - 4px)`;
        // return `${percentage}%`;
      }
      if (item === "pendingOperation") {
        if (!pendingOperation) {
          return 0
        }

        const percentage = (((pendingOperation) / total) * 100).toFixed(2);
        //   console.log(percentage,'percentage3');
        return `calc(${percentage}% - 4px)`;
      }
      if (item === 'pause') {
        if (!pause) {
          return 0
        }

        const percentage = (((pause) / total) * 100).toFixed(2);
        //   console.log(percentage,'percentage3');
        return `calc(${percentage}% - 4px)`;

      }
      if (item === "closed") {
        if (!closed) {
          return 0
        }
        const percentage = (((closed) / total) * 100).toFixed(2);
        //   console.log(percentage,'percentage4');
        return `calc(${percentage}% - 2px)`;
      }
    },
    changeResource(value) {
      //   console.log(value);
    },
    openMarkScheduleDialog() {
      this.markScheduleDialogVisible = true;
      this.markScheduleForm = {}; // 清空表单
    },
    handleCloseMarkScheduleDialog() {
      this.markScheduleForm = {};
      this.$refs["markScheduleForm"] &&
        this.$refs["markScheduleForm"].clearValidate();
      this.markScheduleDialogVisible = false;
    },
    confirMarkSchedule(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            name: this.markScheduleForm.markScheduleName,
            mark_tm: "",
          };
          if (this.markScheduleForm.markScheduleMode === 1) {
            params.mark_tm = moment().format("YYYY-MM-DD HH:mm:ss");
          } else {
            params.mark_tm = this.markScheduleForm.markScheduleTime;
          }
          create_sche_stat_job(params).then((res) => {
            if (res.rst == "ok") {
              // console.log(res,'创建档期成功');
              this.$message({
                type: "success",
                message: this.$transition_lang('dashboardHome.campaign.create'),
              });
              this.get_sche_stat_job_list_fn();

            } else {
              this.$message({
                type: "error",
                message: res.error_msg,
              });
            }

            this.markScheduleDialogVisible = false;
          })
        }
      });
    },

    openDownloadDataDialog() {
      this.operationalDataForm.scheduleName = "";
      this.operationalDataDialogVisible = true;
    },
    downloadData(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.echartsList.find(
            (item) => item.name === this.operationalDataForm.scheduleName
          );

          // console.log(data,'data1111');

          // 验证数据有效性
          if (!data) {
            this.$message({
              message: "请选择有效数据",
              type: "warning",
            });
            return;
          }


          // 提取文件名
          const url = data.note.download_url;
          const fileName = url.substring(url.lastIndexOf('/') + 1);

          //   console.log(data, "下载的数据");
          const a = document.createElement("a");
          document.body.appendChild(a); // 必须插入DOM才能生效
          a.style.display = "none";
          a.href = url;
          a.download = fileName;
          setTimeout(() => {
            a.click();
            URL.revokeObjectURL(a.href); // 释放资源
            document.body.removeChild(a); // 清理DOM
            this.operationalDataDialogVisible = false;
          }, 100);
        }
      });
    },

    getBgColor(index) {
      const colors = ["#e4edff", "#eee5fe", "#ffe8ea", "#c7f0ed"];
      return colors[index % 4];
    },
    extractTimeUnit(tab) {
      const regex = /(\d+)(小时|分钟|秒|M)/; // 匹配数字后面跟小时、分钟或秒
      const match = tab.match(regex); // 如果匹配到，返回匹配结果
      if (match) {
        const number = match[1]; // 提取数字部分
        const unit = match[2]; // 提取单位部分
        return { number, unit };
      }
      return null; // 如果没有匹配到，则返回 null
    },
    // 新增翻译方法
    translateBottomList(list = []) {
      let newObj = [];
      if (!list || !list.length) return newObj;
      else {
        list.forEach((item) => {
          for (const key in h) {
            if (item.label && h[key] === item.label) {
              item.changeLable = "h." + key;
              break;
            }
            // 检查 item.tab 是否存在，且如果存在，检查 item.tab 中是否包含 h[key]
            if (item.tab && item.tab.includes(h[key])) {
              item.changeTab = "h." + key;
              switch (item.changeTab) {
                case "h.offline":
                  item.changeTab = "h.deviceOffline";
                  break;
                case "h.earlyWarning":
                  item.changeTab = "h.storageWarning";
                  break;
              }

              // 提取 item.tab 中的时间单位（小时、分钟、秒）
              const timeData = this.extractTimeUnit(item.tab);
              if (timeData) {
                const { number, unit } = timeData;
                switch (unit) {
                  case "小时":
                    item.timeType = `h.Hours`; // 设置小时单位
                    break;
                  case "分钟":
                    item.timeType = `h.Minutes`; // 设置分钟单位
                    break;
                  case "秒":
                    item.timeType = `h.Seconds`; // 设置秒单位
                    break;
                  case "M":
                    item.timeType = `h.M`; // 默认情况
                }
              }
              break;
            }
          }
          newObj.push({
            ...item,
          });
        });
      }
      return newObj;
    },
    toGo(idx) {
      // let jump_url = ["/deploy/quickpub", "/contentCenter/srclist", "/shopManage/openNewShop", "/contentCenter/csproducer"]
      let jump_url = [
        "/deploy/quickDeploy",
        "/contentCenter/srcfiles",
        "/shopManage/storelist",
        "/contentCenter",
      ];
      if (idx == 2) {
        sessionStorage.setItem("needAddShop", "1");
      }
      this.$router.push({
        path: jump_url[idx],
      });
    },
    clickLis(idx) {
      this.liIndex = idx;
    },
    // 门店数据和设备数据
    get_adm_home_info() {
      const params = {
        pgid: localStorage.getItem("group_id"), // (String)账号节点id
        filter_data: "base", // (String)获取店铺和设备数据固定用"base"
        force: 1, // (Int)固定传 1
        rst_range: 'mgmt'
      };
      get_adm_home_info(params).then((res) => {
        if (res.rst == "ok") {
          console.log(res, 'res');
          this.bottomList3 = res.data[0].home_base
          for (const key in this.bottomList3) {
            if (this.bottomList3[key] === '' || this.bottomList3[key] === undefined || this.bottomList3[key] === 0) {
              this.bottomList3[key] = 0

            }
          }
          //   console.log(this.bottomList3, "bottomList3");
        }
      });
    },
    getInfoLev() {
      const params = {
        pgid: localStorage.getItem("group_id"), // (String) 账号节点id
        filter_data: "alert", // (String) 获取内容统计数据标识
        force: 1, // (String) 固定参数
      };
      get_adm_home_info(params).then((res) => {
        if (res.rst == "ok") {
          // console.log('res',res.data[0].home_alert);
          this.mainList = this.translateBottomList(res.data[0].home_alert);
          // console.log(this.mainList, "mainList");
        }
      });
    },
    toShopList(item, index) {
      this.liIndex = index;
      // console.log(item);
      // console.log("?");
    },
    toScreenList(item, index) {
      this.liIndex = index;
      let queryString = "";
      let quertType = null;
      console.log(item, "item");
      // switch (index) {
      //   case 0:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 1:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 2:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;
      //   case 3:
      //     queryString = item.cnt;
      //     quertType = index;
      //     break;

      //   default:
      //     break;
      // }

      this.$router.push({
        path: "/deviceManage/screenGroup",
        query: {
          // queryString: item.threshold,
          // quertType: quertType,
          inonline: "false",
          fcond: JSON.stringify(item.fcond),
        },
      });
    },
  },
};
</script>

<style lang="scss">
.popover-cla {
  height: 1.5rem;
  /* background-color: ; */
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.5);
  top: 1.9rem !important;
  /* overflow-y:auto ; */
  /* left: 10px !important; */
  /* width: 100%; */
  width: 30.1%;
}

.popover-cla2 {
  height: 1.4rem;
  /* background-color: ; */
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.5);
  top: 1.9rem !important;
  /* overflow-y:auto ; */

  width: 30.1%;
}

.tooltip-cla {
  background-color: #3b425f !important;
}

.el-tooltip__popper[x-placement^="top"].is-dark .popper__arrow {
  border-top-color: #3b425f !important;
}

.el-tooltip__popper[x-placement^="top"].is-dark .popper__arrow::after {
  border-top-color: #3b425f !important;
}

.el-popper[x-placement^="right"] .popper__arrow {
  border-right-color: transparent !important;
}

.popper__arrow::after {
  display: none !important;
}

.popover-content-cal {
  width: 100%;
  height: 88%;
  /* background-color:pink; */
  border-radius: 0.05rem;
}

.popover-content-cal-title {
  margin: 0.05rem 0;
  font-size: 0.11rem;
}

.popover-content-cal-Progress {
  width: 100%;
  display: flex;
  /* justify-content: space-between; */
  height: 0.1rem;
  // background-color: #eaedf6; 
  border-radius: 0.1rem;
  overflow: hidden;
}

.popover-content-cal-Progress-online {
  // width: 50%;
  height: 100%;
  background-color: #28be69;
  // border-top-left-radius: 0.1rem;
  // border-bottom-left-radius: 0.1rem;
  margin-right: 2px !important;
}

.popover-content-cal-Progress-online2 {
  // width: 80%;
  height: 100%;
  background-color: #28be69;
  // border-top-left-radius: 0.1rem;
  // border-bottom-left-radius: 0.1rem;
  margin-right: 2px !important;
}

.popover-content-cal-Progress-offline2 {
  // width: 15%;
  height: 100%;
  background-color: #759fff;
  margin: 0 2px !important;
}

.popover-content-cal-Progress-abnormal {
  // width: 5%;
  height: 100%;
  background-color: #d81e06;
  // border-top-right-radius: 0.1rem;
  // border-bottom-right-radius: 0.1rem;
  margin-left: 2px !important;

}

.popover-content-cal-Progress-offline {
  // width: 5%;
  height: 100%;
  background-color: #9ef2c2;
  margin: 0 2px !important;
}

.popover-content-cal-Progress-pendingOperation {
  // width: 30%;
  height: 100%;
  background-color: #759fff;
  margin: 0 2px !important;
}

.popover-content-cal-Progress-stop_business {
  height: 100%;
  background-color: #ecf627;
  margin: 0 2px !important;
}

.popover-content-cal-Progress-closed {
  // width: 15%;
  height: 100%;
  background-color: #eaedf6;
  // border-top-right-radius: 0.1rem;
  // border-bottom-right-radius: 0.1rem;
  margin-left: 2px !important;
}

.popover-content-cal-content {
  padding: 0.05rem 0 0 0;
  display: flex;
  flex-wrap: wrap;
}

.status-item {
  flex: 0 1 50%;
  display: flex;
  align-items: center;
  margin: 0.1rem 0 0 0;
  font-size: 0.09rem;
}

.popover-content-cal-content2 {
  padding: 0.05rem 0 0 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.status-item2 {
  flex: 0 1 50%;
  display: flex;
  align-items: center;
  margin: 0.1rem 0 0 0;
  font-size: 0.09rem;

  span {
    white-space: nowrap;
  }

  div {
    white-space: nowrap;
  }

  /* background-color: pink; */
}

.color-block {
  width: 0.08rem;
  height: 0.08rem;
  border-radius: 0.02rem;
  margin-right: 0.05rem;
}

.count {
  /* margin-left: ; */
  color: #666;
}

.online {
  background: #28be69;
}

.offline {
  background: #9ef2c2;
}

.pending {
  background: #759fff;
}

.pending2 {
  background: #759fff;
}

.closed {
  background: #eaedf6;
}

.stop_business {
  background: #ecf627;
}

.abnormal {
  background: #d81e06;
}

.tip-icon:hover {
  color: red;
  cursor: pointer;
}
</style>

<style scoped lang="scss">
.homePage {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #e5e5e5;
  padding: 0.14rem;

  ::v-deep .el-radio-group {
    // background-color: pink;
    height: 32px;
    font-size: 0.12rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

  }

  ::v-deep .el-radio {
    margin-right: 5px;
  }

  ::v-deep .el-popover__title {
    font-size: 20px !important;
  }

  .popper__arrow {
    display: none;
  }

  ul {
    list-style: none;
  }

  ::v-deep .el-popover__reference-wrapper {
    // display:flex ;
    display: flex;
    width: 1.9621rem;
    height: 100%;
  }

  ::v-deep .el-popover .el-popper {
    //   background-color: pink;
  }

  ::v-deep .el-divider--vertical {
    display: inline-block;
    width: 0.005rem;
    height: 0.7rem;
    margin: 0.008rem;
    vertical-align: middle;
    position: relative;
  }

  ::v-deep .el-dialog__header {
    padding-bottom: 0;
  }

  ::v-deep .el-dialog__body {
    padding-top: 0.1rem;
    // height: 3.5rem;
  }

  ::v-deep .el-tabs__content {
    height: 100%;
    width: 100%;
    // background-color: pink;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .el-form-item__error {
    color: red !important;
  }

  ::v-deep .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before,
  .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before {
    color: red !important;
  }

  .homebody {
    width: 100%;
    height: 100%;
    // background-color: pink;
    display: flex;
    justify-content: space-between;

    .borderRadius {
      border-radius: 0.05rem;
    }

    .homebodyLeft {
      padding: 0.14rem;
      // width: 79%;
      background-color: #fff;

      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // overflow: hidden;

      .homebodyLeftTop {
        flex: 0 1 5%;
        // background-color: red;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .homebodyLeftTop-left2 {
          flex: 0 1 65%;
          font-size: 0.14rem;
        }

        .homebodyLeftTop-left {
          flex: 0 1 65%;
          font-size: 0.14rem;
        }

        .homebodyLeftTop-right2 {
          flex: 0 1 35%;
          display: flex;
          justify-content: space-evenly;
          font-size: 0.1rem;
          flex-wrap: nowrap;
          // background-color: pink;

          span {
            color: grey;
            margin: 0 0.05rem;
          }

          .click-effect2 {
            cursor: pointer;
            transition: transform 0.2s;
            color: #3e74f1;

            &:active {
              transform: scale(0.95);
            }
          }

        }

        .homebodyLeftTop-right {
          flex: 0 1 35%;
          display: flex;
          justify-content: space-evenly;
          //   background-color: pink;

          span {
            font-size: 0.1rem;

            &:nth-child(1) {
              flex: 0 1 55%;
              color: grey;
              text-align: end;
            }

            &:nth-child(2) {
              margin: 0 0.05rem;
              text-align: center;
              color: #3e74f1;
            }

            &:nth-child(3) {
              color: #3e74f1;
            }

            &.click-effect {
              cursor: pointer;
              transition: transform 0.2s;

              &:active {
                transform: scale(0.95);
              }
            }
          }
        }
      }

      .homebodyLeftList {
        flex: 0 1 15%;
        transition: width 0.3s ease;

        // background-color: blue;
        .homebodyLeftList-top {
          height: 0.3rem;
          // background-color: pink;
          display: flex;
          align-content: center;

          .homebodyLeftList-top-left {
            flex: 1;
            font-size: 0.13rem;
            line-height: 0.3rem;
          }

          .homebodyLeftList-top-right {
            flex: 1;
            font-size: 0.13rem;
            line-height: 0.3rem;
          }
        }

        .stats-list {
          border-top: 0.02rem solid #f6f7fb;
          border-bottom: 0.02rem solid #f6f7fb;
          display: flex;
          justify-content: space-between;
          height: 100%;

          li {
            &:nth-child(1) {
              justify-content: flex-start;

              .stats-list-box:hover {
                cursor: pointer;
                background-color: #f6f8fd;
              }
            }

            &:nth-child(2) {
              justify-content: flex-start;

              .stats-list-box:hover {
                //   cursor: pointer;
                background-color: #f6f8fd;
              }
            }

            &:nth-child(3) {
              justify-content: flex-start;

              .stats-list-box:hover {
                cursor: pointer;
                background-color: #f6f8fd;
              }
            }

            &:nth-child(4) {
              justify-content: flex-start;

              .stats-list-box:hover {
                cursor: pointer;
                background-color: #f6f8fd;
              }
            }

            display: flex;
            justify-content: center;
            align-content: center;
            width: 25%;
            padding: 0.2rem;

            // background-color: pink;
            &:nth-child(2) {
              position: relative;

              //   background-color: red;
              &::after {
                right: 15%;
                top: 50%;
                transform: translateY(-50%);
                position: absolute;
                content: " ";
                height: 0.7rem;
                width: 0.01rem;
                background-color: #f6f7fb;
              }
            }

            .stats-list-box {
              width: 90% !important;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: space-around;
              border-radius: 0.1rem;
              //   background-color: pink;

              .circle-icon {
                width: 0.5rem;
                height: 0.5rem;
                border-radius: 50%;
                background-color: #e6e6e6;
                // background-color: pink;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 0.1rem;

                i {
                  font-size: 0.2rem;
                  color: #666;
                }
              }

              .stats-content {
                flex: 1;
                margin-left: 0.2px;
                height: 0.5rem;
                // background-color: pink;
                // margin-right: 1rem;
                display: flex;
                flex-direction: column;
                justify-content: space-around;

                // align-content: space-between;
                .stats-content-num {
                  font-size: 0.16rem;
                  // margin: 0.1rem 0 0;
                  color: #333;
                  font-weight: 400;

                  span {
                    font-size: 0.1rem;
                    color: #666;
                  }
                }

                .stats-content-num2 {
                  font-size: 0.18rem;
                  // margin: 0.1rem 0 0;
                  color: #3e74f1;
                  font-weight: 400;

                  span {
                    font-size: 0.1rem;
                    color: #666;
                  }
                }


                .stats-content-title {
                  font-size: 0.1rem;
                  color: #666;
                  display: flex;
                  // justify-content: ;
                  align-content: center;

                  i {
                    margin-left: 0.05rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-content: center;
                    line-height: 0 !important;
                  }
                }
              }
            }
          }
        }
      }

      .homebodyLeftEchartsOnline {
        margin-top: 0.3rem;
        flex: 0 1 70%;
        // background-color: yellow;
        display: flex;
        flex-direction: column;

        .EchartsTitle {
          // background-color: skyblue;
          font-size: 0.12rem;
          //   line-height: 10%;
          flex: 0 1 15%;
          display: flex;
          align-items: center; // 垂直居中
          justify-content: flex-start; // 水平左对齐

          .homebodyLeftTop-left {
            flex: 0 1 55%;
            font-size: 0.14rem;
            // background-color: pink;
          }

          .homebodyLeftTop-right {
            flex: 0 1 45%;
            display: flex;
            justify-content: space-evenly;
            // background-color: pink;

            span {
              font-size: 0.1rem;

              &:nth-child(1) {
                flex: 0 1 55%;
                color: grey;
                text-align: end;
              }

              &:nth-child(2) {
                margin: 0 0.05rem;
                text-align: center;
                color: #3e74f1;
              }

              &:nth-child(3) {
                color: #3e74f1;
              }

              &.click-effect {
                cursor: pointer;
                transition: transform 0.2s;

                &:active {
                  transform: scale(0.95);
                }
              }
            }
          }
        }

        .EchartsOnline {
          flex: 0 1 85%;
          // height: 100%;
          // background-color: red;
          overflow: hidden;
        }
      }

      .homebodyLeftEchartsOffline {
        flex: 0 1 35%;
        // background-color: red;
        display: flex;
        flex-direction: column;

        .EchartsTitle {
          //   background-color: skyblue;
          font-size: 0.12rem;
          flex: 0 1 15%;
          display: flex;
          align-items: center; // 垂直居中
          justify-content: flex-start; // 水平左对齐
        }

        .EchartsOffline {
          flex: 0 1 85%;
        }
      }
    }

    .homebodyright {
      // width: 20%;
      //   background-color: green;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .homebodyrightTop {
        border-radius: 0.05rem;
        padding: 0.14rem;
        flex: 0 1 30%;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .homebodyrightTop-title {
          font-size: 0.14rem;
          height: 0.3247rem;
          //   background-color: red;
          line-height: 0.3247rem;
        }

        .homebodyrightTop-list {
          flex: 1;

          //   background-color: pink;
          ul {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            height: 100%;
            padding: 0.1rem 0;

            li {
              width: 48%;
              height: 45%;
              display: flex;
              align-items: center;
              flex-direction: column;
              justify-content: center;
              //   border-radius: 8px;
              //   margin-bottom: 8px;
              cursor: pointer;
              //   padding: 0 12px;
              transition: all 0.3s;

              &:hover {
                opacity: 0.9;
                transform: translateY(-2px);
              }

              img {
                width: 0.24rem;
                height: 0.24rem;
              }

              p {
                font-size: 0.09rem;
                color: #333;
                font-weight: 500;
              }
            }
          }
        }
      }

      .homebodyrightBottom {
        margin-top: 0.14rem;
        flex: 0 1 70%;
        border-radius: 0.05rem;
        padding: 0.14rem;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .homebodyrightBottom-title {
          font-size: 0.14rem;
          height: 0.3247rem;
          //   background-color: red;
          line-height: 0.3247rem;
        }

        .homebodyrightBottom-list {
          flex: 1;
          padding: 10px 0;

          //   background-color: pink;
          ul {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            height: 100%;
          }

          li {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            height: 22%;
            border: 1px solid #e5e5e5;
            border-radius: 0.04rem;
            margin-bottom: 0.08rem;
            padding: 0.08rem;
            cursor: pointer;
            transition: all 0.3s;

            &:nth-child(1) {
              margin-top: 0.15rem;
              background-color: #fe7538;
            }

            &:nth-child(2) {
              background-color: #2196f3;
            }

            &:nth-child(3) {
              background-color: #00bcd4;
            }

            &:nth-child(4) {
              background-color: #00c681;
            }

            // &.warning-active {
            //   background: rgba(211, 57, 57, 0.06);
            //   border-color: var(--text-color);
            // }

            .warning-top {
              display: flex;
              align-items: center;
              margin-bottom: 0.08rem;

              img {
                width: 0.16rem;
                height: 0.16rem;
                margin-right: 0.08rem;
              }

              p {
                color: #fff;
                font-size: 0.1rem;

                line-height: 1.5;
              }
            }

            .warning-bottom {
              display: flex;
              justify-content: flex-end;

              img {
                width: 0.2rem;
                height: 0.2rem;
                vertical-align: text-bottom;
              }

              span {
                font-size: 0.2rem;
                color: #fff;
                margin-left: 0.04rem;
                font-weight: bold;
              }
            }

            &:hover {
              box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
              transform: translateY(-0.02rem);
            }
          }
        }
      }
    }

    // 增加弹窗样式
  }

  .hover-dialog {
    // 确保弹窗贴近触发元素
    margin-top: -20px !important;

    // 消除与父元素的间隙
    .storeStatusEcharts-cla,
    .deviceStatusEcharts-cla {
      width: 2.5rem;
      height: 2.5rem;
      //   background-color: skyblue;
    }
  }

  .operation-dialog {
    .el-dialog__header {
      padding: 20px;

      .el-dialog__title {
        font-size: 16px;
        color: #333;
      }
    }

    .el-form-item__error {
      color: red !important;
    }

    .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before {
      color: red !important;
    }

    .dialog-footer {
      // padding: 20px;
      display: flex;
      justify-content: flex-end;
      // text-align: right;
      // border-top: 1px solid #eee;
      margin-top: 10px;
    }

    .el-select {
      width: 100%;
    }

    .el-dialog__body {
      border-bottom: 1px solid #eee;
      border-top: 1px solid #eee;
      padding: 20px;

      // background-color: pink;
      .el-form-item__label {
        //   color: #f56c6c;
        line-height: none;
      }
    }
  }

  .tip-icon {
    &:hover {
      color: red;
      cursor: pointer;
    }
  }
}
</style>