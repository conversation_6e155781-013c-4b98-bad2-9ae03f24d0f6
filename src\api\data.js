import { get, post, uploadFile } from '@/utils/request'

export function buildMenus(func) {
  var params = {
    'func': func,
    'pid': 0,
    'version': '1.0'
  }
  return post('dmb/api/json', params, 'get_adm_menus')
}

export function initData(url, params) {
  let classModel = 'GroupTreeRole'
  if (url == 'api/users') {
    classModel = 'GroupTreeUsers'
  } else if (url == 'api/menus') {
    return buildMenus('left_menu')
  } else if (url == 'api/menusAll') {
    return buildMenus('sys_menu')
  }
  var _params = {
    'classModel': classModel,
    'sort': '', // 非必要，排序规则，id,desc
    'createdAtS': '', // 非必要，查询创建时间开始日期
    'createdAtE': '', // 非必要，查询创建时间结束日期
    'page': params['page'], // 起始页码,
    'size': params['size'], // 每页数据量
    'fromtype': params['fromtype'] // 组织架构
    // "blurry": 0 // 模糊
}
if (params['createTime']) {
  _params['createdAtS'] = params['createTime'][0]
  _params['createdAtE'] = params['createTime'][1]
}
if (params['blurry']) {
  _params['blurry'] = params['blurry']
}
if (params['enabled'] != undefined) {
  _params['enabled'] = params['enabled'] == 'true' ? 1 : 0
}
return post('dmb/api/json', _params, 'get_adm_datas')
  // return request({
  //   url: url + '?' + qs.stringify(params, { indices: false }),
  //   method: 'get'
  // })
}

export function download(url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}
