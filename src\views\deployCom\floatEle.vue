<template>
  <div class='box'>
<!--    浮层元素-->
    <div class='floatEle'>
      <div class='content'>
        <div class='newAdd' @click.stop='floatNewAdd'>
          <img src='../../assets/img/home_img/jia.svg' style='width: 20px;height: 20px;margin-top: 5px'>
          <p style='color: #fff;margin-top: -4px'>新增</p>
        </div>
      </div>
      <div class='player'>
        播放速度
        <el-select v-model="playerSpeed" placeholder="请选择">
          <el-option
            v-for="item in playerOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class='player' v-show='showPlayerRange'>
        播放范围
        <el-select v-model="playerRange" placeholder="请选择">
          <el-option
            v-for="item in playerOptionRange"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
    </div>
<!--    新增全局浮层-->
    <div class='alertBox floatAlert' v-show='showAlertFloat'>
      <div class='top'>
        <h3>新增内容</h3>
        <p @click.stop='showAlertFloat=false'>×</p>
      </div>
      <div class='threeParts'>
        <!--        tab栏切换-->
        <el-tabs v-model="activeName" >
          <el-tab-pane label="全局浮层" name="first">
            <div class='content'>
              <div class='chunk'>
                <!-- <img src='../../assets/img/home_img/breakfast.png' /> -->
                <div></div>
                <p>1920*1080</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class='selectAll'>
          <el-select v-model="floatSelectValue" placeholder="请选择" class='select1'>
            <el-option
              v-for="item in floatSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class='view' style="cursor: pointer">查看</div>
      </div>
      <!-- <el-upload
        class="upload-demo"
        action="https://jsonplaceholder.typicode.com/posts/"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        multiple
        :limit="3"
        :on-exceed="handleExceed"
        :file-list="fileList">
      </el-upload> -->
      <el-button size="small" type="primary" @click="$router.push('/srcfiles')">点击上传1</el-button>
      <div class='btns'>
        <div class='close' @click.stop='showAlertFloat=false'>取消</div>
        <div @click.stop='floatOk'>确定</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatEle',
  data(){
    return{
      showAlertFloat:false,
      //播放范围
      showPlayerRange:false,
      //全局浮层默认第一个
      activeName: 'first',
      //浮层选择框
      floatSelect: [{
        value: '选项1',
        label: '全国市场'
      }, {
        value: '选项2',
        label: '上海市场'
      }, {
        value: '选项3',
        label: '北京市场'
      }, {
        value: '选项3',
        label: '成都市场'
      }],
      floatSelectValue:"全国市场",
      //  播放速度
      playerOption: [{
        value: '选项1',
        label: '正常'
      }, {
        value: '选项2',
        label: '慢速'
      }, {
        value: '选项3',
        label: '快速'
      }],
      playerSpeed: '正常',
      //播放范围
      playerOptionRange: [{
        value: '选项1',
        label: '1号屏显示'
      }, {
        value: '选项2',
        label: '2号屏显示'
      }, {
        value: '选项3',
        label: '3号屏显示'
      }],
      playerRange:"全部屏幕",
      //上传文件
      fileList: []
    }
  },
  methods:{
    //  浮层元素新增
    floatNewAdd(){
      console.log(324)
      this.showAlertFloat = true;
    },
  //  确定
    floatOk(){
      this.showAlertFloat = false;
      this.showPlayerRange = true;
    },
  // 上传文件
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${ file.name }？`);
    }
  }
}
</script>

<style scoped>
.box{
  width: 100%;
  height: 100%;
  background-color: #fff;
}
/*点击后弹出*/
.alertBox{
  width: 10rem;
  height: 6.75rem;
  position: absolute;
  margin: auto;
  left: 0px;
  right: 0;
  top: -1rem;
  bottom: 0;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0.16rem;
  font-size: 0.14rem;
  line-height: 150%;
  text-align: center;
  padding: 0 0.15rem 0.21rem 0.3rem;
  border: 1px solid #ddd;
  z-index: 50;
}
.alertBox>.top{
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 0.5rem;
  border-bottom: 0.01rem solid #e5e5e5;
}
.alertBox>.top>h3{
  color: rgba(80, 80, 80, 1);
  font-size: 0.16rem;
  text-align: left;
  font-weight: bold;
}
.alertBox>.top>p{
  width: 0.2rem;
  height: 0.2rem;
  font-size: 0.3rem;
  color: #999999;
}
/*浮层选择*/
.floatAlert .selectAll{
  width: 290px;
}
.threeParts{
  display: flex;
  justify-content: space-between;
  position: relative;
}
.threeParts>.el-tabs{
  width: 100%;
}
::v-deep .el-tabs__header{
  margin: 0;
}
.selectAll{
  width: 380px;
  position: absolute;
  right:0;
  display: flex;
  align-items: center;
  height: 40px;
}
/*!*下拉框内容*!*/
.select1{
  margin:-10px 8px;
  width: 160px;
  height: 32px;
  border: 1px solid #ebebeb;
  color: rgba(80, 80, 80, 1);
}
::v-deep .el-input__inner{
  height: 32px;
}
.view{
  width: 0.88rem;
  height: 0.32rem;
  color: rgba(80, 80, 80, 1);
  background-color: var(--background-color);
  border-radius: 0.06rem;
  font-size: 0.14rem;
  text-align: center;
  right: 0.24rem;
  top: 3px;
  color: #fff;
  line-height: 0.32rem;
  position: absolute;
}
.content{
  width: 9.78rem;
  height: 4.77rem;
  color: rgba(80, 80, 80, 1);
  font-size: 0.14rem;
  border: rgba(229, 229, 229, 1) solid 1px;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  padding: 0.23rem 0 0 0.19rem;
}
.content>.chunk{
  width: 1.85rem;
  height: 1.85rem;
  position: relative;
  margin-right: 0.1rem;
  box-shadow: 0rem 0.03rem 0.03rem 0rem rgba(0, 0, 0, 0.12857142857142861);
}
.content>.chunk>img{
  width: 100%;
  height: 100%;
}
.content>.chunk>div{
  width: 0.23rem;
  height: 0.23rem;
  background-color: #787363;
  position: absolute;
  border-radius: 50%;
  top: 0.13rem;
  left: 0.1rem;
}
.content>.chunk>p{
  height: 0.26rem;
  width: 100%;
  line-height: 0.26rem;
  color: rgba(255, 255, 255, 1);
  background-color: rgba(0, 0, 0, 0.35);
  text-align: center;
  position: absolute;
  opacity: 0.7;
  bottom: 0;
  background-color: #a29d86;
  font-size: 0.11rem;
}
.upload-demo{
  position: absolute;
  left: 30px;
  margin-top: 25px;
  /*background-color: var(--background-color);*/
}
.el-button{
  background-color: var(--background-color);
}
/*取消*/
.btns{
  display: flex;
  justify-content: flex-end;
  margin-top: 0.25rem;
  cursor: pointer;
}
.btns>div{
  width: 0.81rem;
  height: 0.36rem;
  border-radius: 0.04rem;
  font-size: 0.14rem;
  border: rgba(166, 166, 166, 1) solid 1px;
  text-align: center;
  line-height: 0.36rem;
  color: #fff;
  font-weight: bold;
  background-color: rgba(108, 178, 255, 1);
}
.btns>.close{
  margin-right: 0.2rem;
  color: rgba(128, 128, 128, 1);
  background-color: #fff;
}
/*浮层元素*/
.floatEle{
  width: 8.21rem;
  height: 4.53rem;
  margin: 0 auto;
}
.floatEle>.content{
  width: 8.21rem;
  margin: 5px 0;
  height: 350px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(153, 153, 153, 1);
  /*font-size: 0.14rem;*/
  text-align: center;
}
.floatEle>div>.newAdd{
  border-radius: 50%;
  width: 50px;
  height:50px;
  border: 1px solid #fff;
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.floatEle>.player{
  display: flex;
  align-items: center;
  /*position: absolute;*/
  height: 40px;
  background-color: #fff;
  color: rgba(80, 80, 80, 1);
  font-weight: bold;
}
.floatEle>.player>.el-select{
  margin-left: 20px;
}
.floatEle>.player:nth-child(2){
  bottom: -146px;
}
.floatEle>.player:nth-child(3){
  bottom: -200px;
}
</style>
