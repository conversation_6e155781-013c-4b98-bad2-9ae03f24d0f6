<template>
    <div class="operation_log">
        <!-- title -->
        <div class="operation_log_title">
            操作日志1
        </div>
        <!-- 搜索区 -->
        <div class="operation_log_search flex">
            <div class="left_search flex">
                <el-input placeholder="账户ID" size="small" prefix-icon="el-icon-search" v-model="queryList.accountName"
                    style="width:186px;margin-right:12px"></el-input>
                <el-select filterable v-model="queryList.logType" clearable placeholder="全部日志类型" size="small"
                    style="width:186px;margin-right:12px">
                    <el-option v-for="item in logTypeList" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>
                <date-range-picker valueFormat="yyyyMMdd" v-model="queryList.data" class="date-item" />
                <div class="search_button" @click="handleSearch">搜索</div>
            </div>
            <!-- <div class="right_export">
                <span @click="exportToExcel('all')">Excel导出全部</span>
                <span @click="exportToExcel">Excel导出当页</span>
            </div> -->
        </div>
        <!-- 内容表格区 -->
        <div class="table_wrap">
            <el-table :data="tableData" v-loading='loading' element-loading-background="rgba(0, 0, 0, 0.8)"
                element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading" :height="autoHeight.height"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }">
                <!-- <el-table-column type="selection" width="50"></el-table-column> -->
                <el-table-column prop="num" label="序号" width="110"></el-table-column>
                <el-table-column prop="account" label="账户" width="" show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="date" label="操作时间" width="" show-overflow-tooltip="true"></el-table-column>
                <el-table-column prop="opType" label="操作类型" width="150"></el-table-column>
                <el-table-column prop="details" label="详情" width="" show-overflow-tooltip="true"></el-table-column>
            </el-table>
        </div>
        <!-- 底部页码区 -->
        <div class="operation_log_footer">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page.sync="currentPage" :page-size="pageSize" :pager-count='5' :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { log } from '@/api/system/log'
import DateRangePicker from '@/components/DateRangePicker'
export default {
    components: {
        DateRangePicker
    },
    data() {
        return {
            loading: true,
            currentPage: 1,  //页码
            totalNum: 0, //总数据数量
            pageSize: 10,
            tableData: [],  //列表数据
            queryList: {
                accountName: '', //账户名称
                logType: '', //全部日志类型
                data: '',//日期
            },
            logTypeList: [{ 'label': '场景化登录', 'value': 'admin|scene-login' },
            { 'label': '根帐号注册', 'value': 'admin|root|reg' },
            { 'label': '登录', 'value': 'admin|login' },
            { 'label': '添加分区', 'value': 'admin-mgmt|add_group' },
            { 'label': '编辑分区', 'value': 'admin-mgmt|edit_group' },
            { 'label': '删除分区', 'value': 'admin-mgmt|delete_group' },
            { 'label': '添加店铺', 'value': 'admin-mgmt|add_shop' },
            {
                'label': '添加独立店铺',
                'value': 'admin-mgmt|login_auto_addshop'
            },
            { 'label': '删除店铺', 'value': 'admin-mgmt|delete_shop' },
            {
                'label': '编辑店铺信息',
                'value': 'admin-mgmt|edit_shop'
            },
            {
                'label': '重置登录密码',
                'value': 'admin-mgmt|reset_pw'
            },
            {
                'label': '修改登录密码',
                'value': 'admin-mgmt|edit_pw'
            },
            {
                'label': '预警设置',
                'value': 'admin-mgmt|ds_setting|admalert'
            },
            {
                'label': '终端管理帐号设置',
                'value': 'admin-mgmt|ds_setting|accinfo'
            },
            {
                'label': '终端待机内容设置',
                'value': 'admin-mgmt|ds_setting|waitplay'
            },
            {
                'label': '修改管理员手机号',
                'value': 'admin-mgmt|change_phone|role'
            },
            {
                'label': '修改登录手机号',
                'value': 'admin-mgmt|change_phone|current'
            },
            {
                'label': '修改子店手机号',
                'value': 'admin-mgmt|change_phone|shop'
            },
            { 'label': '文件处理', 'value': 'file-mgmt|csv_upt' },
            {
                'label': '批量处理通过',
                'value': 'file-mgmt|entity-inspect|approval'
            },
            {
                'label': '批量处理驳回',
                'value': 'file-mgmt|entity-inspect|deny'
            },
            {
                'label': '卡片批量创建',
                'value': 'draft|batch_create'
            },
            { 'label': '单张内容创建', 'value': 'draft|create' },
            { 'label': '单张内容编辑', 'value': 'draft|edit' },
            { 'label': '单张内容删除', 'value': 'draft|delete' },
            { 'label': '单张内容发布', 'value': 'draft|publish' },
            {
                'label': '设置卡片顺序',
                'value': 'draft|set_order'
            },
            { 'label': '卡片拷贝', 'value': 'draft|copy' },
            {
                'label': '卡片监控配置',
                'value': 'draft|monitor_edit'
            },
            { 'label': '卡片更新投放', 'value': 'draft|remodify' },
            {
                'label': '卡片主副屏设置',
                'value': 'draft|set_dual_type'
            },
            { 'label': '特效内容创建', 'value': 'widget|create' },
            { 'label': '特效内容编辑', 'value': 'widget|edit' },
            { 'label': '特效内容删除', 'value': 'widget|delete' },
            { 'label': '特效内容发布', 'value': 'widget|publish' },
            {
                'label': '删除已发布特效',
                'value': 'widget-pub|delete'
            },
            { 'label': '创建专辑', 'value': 'classify|create' },
            { 'label': '编辑专辑', 'value': 'classify|edit' },
            { 'label': '暂停专辑', 'value': 'classify|pause' },
            {
                'label': '获取发布区域信息',
                'value': 'classify|get_pub_info'
            },
            {
                'label': '选择投放区域',
                'value': 'classify|bind_pub_area'
            },
            { 'label': '播放专辑', 'value': 'classify|play' },
            { 'label': '删除专辑', 'value': 'classify|delete' },
            { 'label': '专辑复制', 'value': 'classify|copy' },
            { 'label': '专辑发布', 'value': 'classify|publish' },
            {
                'label': '场景内容删除',
                'value': 'classify|scene|delete'
            },
            {
                'label': '设备场景内容清除',
                'value': 'classify|scene|clear'
            },
            {
                'label': '场景内容发布',
                'value': 'classify|scene|publish'
            },
            {
                'label': '删除已发布专辑',
                'value': 'classify-pub|delete'
            },
            {
                'label': '添加专辑内容',
                'value': 'classify-draft|create'
            },
            {
                'label': '编辑专辑内容',
                'value': 'classify-draft|edit'
            },
            {
                'label': '删除专辑内容',
                'value': 'classify-draft|delete'
            },
            { 'label': '复制专辑', 'value': 'classify|fork' },
            { 'label': '更新专辑', 'value': 'classify|confirm' },
            {
                'label': '设置专辑顺序',
                'value': 'classify|set_order'
            },
            {
                'label': '投放中二次编辑',
                'value': 'classify|remodify'
            },
            {
                'label': '更新监控内容',
                'value': 'classify|monitor_update'
            },
            { 'label': '创建游戏', 'value': 'game|create' },
            { 'label': '编辑游戏', 'value': 'game|edit' },
            { 'label': '删除游戏', 'value': 'game|delete' },
            {
                'label': '联动设备创建',
                'value': 'vs-group|create'
            },
            {
                'label': '联动设备删除',
                'value': 'vs-group|delete'
            },
            { 'label': '联动设备编辑', 'value': 'vs-group|edit' },
            {
                'label': '联动设备关联',
                'value': 'vs-group|setup_screen'
            },
            {
                'label': '联动设备解除',
                'value': 'vs-group|free_screen'
            },
            {
                'label': '设置联动边框',
                'value': 'vs-group|set_frame'
            },
            { 'label': '联动任务创建', 'value': 'vs-task|create' },
            { 'label': '联动任务编辑', 'value': 'vs-task|edit' },
            { 'label': '联动任务删除', 'value': 'vs-task|delete' },
            { 'label': '联动任务提交', 'value': 'vs-task|submit' },
            { 'label': '联动内容创建', 'value': 'vs-cs|create' },
            { 'label': '联动内容编辑', 'value': 'vs-cs|edit' },
            { 'label': '联动内容删除', 'value': 'vs-cs|delete' },
            { 'label': '联动内容提交', 'value': 'vs-cs|submit' },
            { 'label': 'pin卡续费', 'value': 'screen-mgmt|pin_paid' },
            { 'label': '设备绑定', 'value': 'screen-mgmt|bind' },
            {
                'label': '激活码绑定设备',
                'value': 'screen-mgmt|bind|verifycode'
            },
            {
                'label': '服务卡绑定设备',
                'value': 'screen-mgmt|bind|pincard'
            },
            {
                'label': '联动设备激活',
                'value': 'screen-mgmt|bind-vs|verifycode'
            },
            {
                'label': '验证码校验绑定设备',
                'value': 'screen-mgmt|bind-ds|verifycode'
            },
            { 'label': '设备解邦', 'value': 'screen-mgmt|unbind' },
            { 'label': '设备替换', 'value': 'screen-mgmt|switch' },
            {
                'label': '设备激活换机',
                'value': 'screen-mgmt|active-switch'
            },
            {
                'label': '总部批准拆机',
                'value': 'screen-mgmt|pro-unbind'
            },
            { 'label': '设备删除', 'value': 'screen-mgmt|delete' },
            { 'label': '设备编辑', 'value': 'screen-mgmt|edit' },
            { 'label': '设备注册', 'value': 'screen-mgmt|reg' },
            { 'label': '设备找回', 'value': 'screen-mgmt|fb' },
            {
                'label': '设备批量操作',
                'value': 'screen-mgmt|batch_process'
            },
            {
                'label': '设备标签管理',
                'value': 'screen-mgmt|tags'
            },
            { 'label': '任务创建', 'value': 'schedule|create' },
            { 'label': '任务删除', 'value': 'schedule|delete' },
            { 'label': '任务编辑', 'value': 'schedule|edit' },
            { 'label': '内容创建', 'value': 'section|create' },
            { 'label': '内容删除', 'value': 'section|delete' },
            { 'label': '内容编辑', 'value': 'section|edit' },
            {
                'label': '源视频文件删除',
                'value': 'src_content|delete'
            },
            { 'label': '商品创建', 'value': 'product|create' },
            { 'label': '商品编辑', 'value': 'product|edit' },
            { 'label': '商品删除', 'value': 'product|delete' },
            { 'label': '申请审核', 'value': 'entity-inspect|apply' },
            { 'label': '开始审核', 'value': 'entity-inspect|begin' },
            { 'label': '完成审核', 'value': 'entity-inspect|finish' },
            { 'label': '更换影城', 'value': 'cinema-wanda|change' },
            {
                'label': '大招牌联屏专辑复制',
                'value': 'wddzp_vs_classify|copy'
            },
            {
                'label': '创建大招牌单屏专辑',
                'value': 'wddzp_draft|create'
            },
            {
                'label': '创建大招牌餐牌专辑',
                'value': 'wddzp_vs_draft|create'
            },
            {
                'label': '删除大招牌餐牌专辑',
                'value': 'wddzp_vs_draft|delete'
            },
            {
                'label': '批量操作大招牌餐牌内容',
                'value': 'wddzp_vs_draft|batch_edit'
            },
            {
                'label': '编辑大招牌餐牌专辑名称',
                'value': 'wddzp_vs_draft|edit_vs_name'
            },
            {
                'label': '辑编大招牌单屏专辑名称',
                'value': 'wddzp_draft|edit_name'
            },
            {
                'label': '创建大招牌单个餐牌',
                'value': 'wddzp_vs_draft|create_page'
            },
            {
                'label': '删除大招牌单个餐牌',
                'value': 'wddzp_vs_draft|delete_page'
            },
            {
                'label': '设置大招牌餐牌播放时间',
                'value': 'wddzp_vs_draft|edit_page_duration'
            },
            {
                'label': '设置大招牌餐牌播放顺序',
                'value': 'wddzp_vs_draft|edit_page_order'
            },
            {
                'label': '添加大招牌餐牌内容',
                'value': 'wddzp_vs_draft|add_content'
            },
            {
                'label': '设置大招牌播放模式',
                'value': 'wddzp_vs_draft|set_mode'
            },
            {
                'label': '播放大招牌餐牌专辑',
                'value': 'wddzp_vs_draft|active'
            },
            {
                'label': '暂停大招牌餐牌专辑',
                'value': 'wddzp_vs_draft|pause'
            },
            {
                'label': '创建大招牌叫号专辑',
                'value': 'wddzp_vc_draft|create'
            },
            {
                'label': '删除大招牌叫号专辑',
                'value': 'wddzp_vc_draft|delete'
            },
            {
                'label': '播放大招牌叫号专辑',
                'value': 'wddzp_vc_draft|active'
            },
            {
                'label': '暂停大招牌叫号专辑',
                'value': 'wddzp_vc_draft|pause'
            },
            {
                'label': '修改大招牌餐牌布局',
                'value': 'wddzp_vs_draft|change_page_index'
            },
            {
                'label': '修改联动(大招牌)专辑发布区域',
                'value': 'vs_draft|bind_pub_area'
            }],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },

        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.getDataLsit();
    },
    mounted() {

    },
    methods: {
        // 搜索
        handleSearch() {
            console.log(this.queryList);
            this.currentPage = 1;
            this.getDataLsit(1)
        },
        // 获取列表数据
        getDataLsit(flag) {
            this.loading = true;
            const list = [{
                num: '1',
                account: '<EMAIL>',
                date: '2022.3.18 09:55:36',
                opType: '登录',
                details: '登录云后台（117.32.251.202.100.121.72.181）'
            },
            {
                num: '2',
                account: '<EMAIL>',
                date: '2022.3.18 09:55:36',
                opType: '登录',
                details: '登录云后台（117.32.251.202.100.121.72.181）'
            },]
            const pgid = localStorage.getItem("group_id")
            const params = {
                "pgid": pgid, //（string）账号节点id
                // "act": "admin|scene-login", //（string）操作名称，筛选查询时使用
                // "sstm": 0, //（string）查询开始时间
                // "setm": 0, //（string）查询结束时间
                //"filter_shop_name":"", //（string）店铺名称，筛选查询时使用
                // "filter_user_id": "********", //（string）用户id，筛选查询时使用
                "dataSource": "dmb",
                "iDisplayStart": (this.currentPage - 1) * 10, //（int）显示的起始位置
                "iDisplayLength": this.pageSize, //（int）显示的最大数量
                "sEcho": this.currentPage - 1 //（int）页码
            }
            if (flag) {

                if (this.queryList["data"]) {
                    // const reg = new RegExp("-", "g")
                    // const sstm = this.queryList["data"][0].split(" ")[0].replace(reg, "")
                    // const setm = this.queryList["data"][1].split(" ")[0].replace(reg, "") 
                    params["sstm"] = this.queryList["data"][0]
                    params["setm"] = this.queryList["data"][1]
                }
                if (this.queryList["logType"]) params["act"] = this.queryList["logType"]
                if (this.queryList["accountName"]) params["filter_user_id"] = this.queryList["accountName"]

            }
            const show_list = []
            log(params).then(res => {
                console.log("log", res)
                const data = res["data"][0]
                this.totalNum = data.iTotalRecords
                const track_list = data.track_list
                track_list.forEach(element => {
                    const track = {
                        num: element[0],
                        account: element[2],
                        date: element[1],
                        opType: element[4],
                        details: element[5]
                    }
                    show_list.push(track)
                });
                this.tableData = show_list
            this.loading = false;
                
            })
            // this.tableData = list;
        },
        // excel导出
        exportToExcel(val) {
            if (val == 'all') {
                this.$message.success('导出全部数据');
            } else {
                this.$message.success(`导出第${this.currentPage}页数据`);
            }
        },
        //页码改变
        handleCurrentChange(val) {
            // this.$message.success(`现在是第${val}页`);
            this.currentPage = val;
            this.getDataLsit(1)
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getDataLsit(1)
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 209 + 'px';
            this.autoHeight.heightNum = windowHeight - 209;
        },
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style scoped>.operation_log {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 0 20px;
}

.operation_log_title {
    width: 100%;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    color: rgba(42, 130, 228, 1);
    font-size: 14px;
    line-height: 40px;
    text-align: left;
    font-weight: bold;
}

/* 搜索区 */
.operation_log_search {
    width: 100%;
    height: 40px;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 9px;
}

.left_search {
    flex: 1;
    height: 100%;
    align-items: center;
}

.search_button {
    width: 64px;
    height: 31px;
    top: 99px;
    color: #fff;
    background-color: var(--text-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 31px;
    text-align: center;
    cursor: pointer;
    margin-top: -1px;
    margin-left: 15px;
}

.search_button:hover {
    background-color: rgba(211, 57, 57, .8);
}

.right_export {
    width: 116px;
    height: 100%;
    display: flex;
    align-items: center;
    border: 1px solid var(--background-color);
    border-radius: 5px;
}

.right_export span {
    display: inline-block;
    box-sizing: border-box;
    width: 106px;
    height: 100%;
    line-height: 40px;
    color: var(--background-color);
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.right_export span:hover {
    color: rgba(212, 48, 48, .7);
}

/* .right_export span:nth-of-type(1){
        border-right: 1px solid var(--background-color);
    } */
/* 表格区 */
.table_wrap {
    border-bottom: 1px solid rgba(236, 235, 235, 1);
}

/* 底部页码区 */
.operation_log_footer {
    box-sizing: border-box;
    width: 100%;
    height: 66px;
    font-size: 14px;
    padding-top: 17px;
    text-align: right;
}</style>
