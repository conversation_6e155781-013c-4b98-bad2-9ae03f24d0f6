<template>
  <div class="comBox">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="speTab">
      <el-tab-pane label="横屏" name="across">
        <div class="twoDiv" v-for="item in templateHList" :key="item, id" @click="SelectTemplate(0, item)">
          <div class="bigDiv" style="position: relative;">
            <span style="color:#000;font-size:12px;font-weight: bold;z-index:9">{{ item.name }}</span>
            <img :src="item.thumb_url" style="position:absolute;left:0;top:0;width:100%;height:100%" alt="">
            <!-- <span class="el-icon-picture" style="line-height: 120px;"></span> -->
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="竖屏" name="vertical">
        <div class="shuScreen" @click="SelectTemplate(2, activeName)">
          <div class="vedioDiv">
            <span class="el-icon-video-camera-solid"></span>
          </div>
          <div class="imgDiv">
            <span class="el-icon-picture"></span>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "contentModel",
  data() {
    return {
      activeName: "across",
    };
  },
  props: {
    templateHList: {
      type: Array
    }
  },
  methods: {

    SelectTemplate(type, template) {
      this.$emit("SelectTemplate", type, template);
    },
  },
};
</script>

<style lang="scss" scoped>
.comBox {
  color: #fff;
  padding: 20px 15px 0 15px;
}

.comBox .speTab {
  display: flex;
  color: #fff;
  flex-direction: column;
}

.comBox ::v-deep .el-tabs__nav-wrap::after {
  width: 0;
  height: 0;
}

.comBox .el-tabs {
  width: 100% !important;
}

.twoDiv {
  width: 278px;
  height: 146px;
  color: rgba(255, 255, 255, 0.67);
  background-color: rgba(166, 166, 166, 1);
  border: rgba(255, 255, 255, 1) solid 2px;
  display: flex;
  text-align: center;
  align-content: center;
  font-size: 30px;
  margin-bottom: 30px;
  cursor: pointer;
}

.twoDiv>.bigDiv {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.twoDiv>.smallDiv {
  width: 77px;
  text-align: center;
}

.fourDiv {
  width: 278px;
  height: 146px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  color: rgba(255, 255, 255, 0.67);
  background-color: rgba(166, 166, 166, 1);
  border: rgba(255, 255, 255, 1) solid 1px;
}

.fourDiv>div {
  width: 50%;
  text-align: center;
  height: 50%;
  line-height: 76px;
  font-size: 30px;
  border: rgba(255, 255, 255, 1) solid 1px;
}

.fourDiv>div:last-child {
  border-right: none;
}

/* 竖屏 */
.shuScreen {
  width: 254px;
  color: rgba(128, 128, 128, 1);
  background-color: rgba(166, 166, 166, 1);
  font-size: 21px;
  border: rgba(255, 255, 255, 1) solid 2px;
  text-align: center;
  cursor: pointer;
}

.shuScreen>div {
  line-height: 270px;
  font-size: 30px;
  color: rgba(255, 255, 255, 1);
}

.shuScreen>.vedioDiv {
  height: 120px;
  line-height: 150px;
  border-bottom: rgba(255, 255, 255, 1) solid 2px;
}

::v-deep .is-active {
  color: var(--text-color) !important;
  border-bottom: 3px solid var(--text-color) !important;
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
::v-deep .speTab #tab-across {
  padding: 0 10px !important;
}

/* 给最后一个设置padding */
::v-deep .speTab #tab-vertical {
  padding: 0 10px !important;
}

::v-deep .el-tabs__item {
  color: white !important;
}

::v-deep .el-tabs__content {
  overflow: scroll !important;
}
</style>
<style>
/* tabs选中的样式 */
.speTab .is-active {
  color: var(--text-color) !important;
  background-color: var(--active-color) !important;
  /* border-bottom: 2px solid var(--text-color) !important; */
}

/* 给第一个设置padding,id为 #tab-设置的name名*/
.speTab #tab-first {
  padding: 0 20px !important;
}

/* 给最后一个设置padding */
.speTab #tab-second {
  padding: 0 20px !important;
}

/* 选中tabs下边横线样式 */
/* tabs鼠标移入样式 */
.speTab .el-tabs__item:hover {
  color: var(--text-color) !important;
}

.speTab #tab-first {
  color: #fff;
}

.speTab #tab-second {
  color: #fff;
}
</style>
