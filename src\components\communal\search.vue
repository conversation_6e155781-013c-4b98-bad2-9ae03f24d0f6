<template>
    <div class="search flex">
        <div v-for="item in mylist" :key="item.placeholder">
            <el-input v-if="item.type == 'input'" :placeholder="item.placeholder" size="small" prefix-icon="el-icon-search"
                @keyup.enter.native='handleSearch' v-model="queryList[item.filterkey]"
                style="width:186px;margin-right:12px">
            </el-input>
            <el-select v-if="item.type == 'select'" v-model="queryList[item.filterkey]" clearable filterable
                :placeholder="item.placeholder" size="small" style="width:186px;margin-right:12px" @change="changeSelect">
                <el-option v-for="items in item.options" :key="items[1]" :label="items[1]" :value="items[0]">
                </el-option>
            </el-select>
        </div>
        <slot name="slot"></slot>
        <div class="search_button" @click="handleSearch">搜索</div>
    </div>
</template>

<script>
import { datas_filter_cond } from '@/api/commonInterface'

export default {
    props: {
        modelName: {
            type: String,
            default: ''
        },
        histSearchList: {
            type: Array,
            default: []
        },
    },
    components: {

    },
    data() {
        return {
            queryList: {
                blurry:'',
                opsmarket:'',
                storetype:'',
                itmarket:''
            },
            mylist: []
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        this.getSelectDataList()
        let arr = Object.keys(this.histSearchList);
        //如果没有存筛选记录，不用走循环赋值
        if (arr.length > 0) {
            for (let i = 0; i < arr.length; i++) {
                this.$set(this.queryList, arr[i], this.histSearchList[arr[i]])
            }
        }
        console.log(arr, 'arr');

    },
    mounted() {

    },
    methods: {
        handleSearch() {
            this.$emit('handleSearch', this.queryList)
            this.getSelectDataList()
            this.$forceUpdate()
        },
        // 获取下拉数据
        getSelectDataList() {
            const params = {
                classModel: this.modelName, //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
            }
            datas_filter_cond(params).then(res => {
                console.log(res,'[res1]');
                
                this.mylist = res.data[0].filter(item=>item.filterkey != 'itmarket' && item.filterkey != 'jdeinfo' )
                // this.mylist = res.data[0];




                console.log(this.mylist, 'mylist');
            })
        },
        changeSelect() {
            console.log('?');
        }
    },
};
</script>

<style scoped lang="scss">
.search {
    align-items: center;
    margin-right: 10px;
}

/* 搜索按钮 */
.search_button {
    width: 64px;
    height: 31px;
    top: 99px;
    color: #fff;
    background-color: var(--btn-background-color);
    border-radius: 4px;
    font-size: 13px;
    line-height: 31px;
    text-align: center;
    cursor: pointer;
    margin-top: -1px;
    margin-left: 10px;
}

.search_button:hover {
    // background-color: rgba(211, 57, 57, .8);
    filter: brightness(1.1);
}
</style>
