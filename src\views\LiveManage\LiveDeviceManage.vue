<template>
  <div
    class="box"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading"
  >
    <div class="top">
      <!--          运营市场-->
      <el-select v-model="queryList.opsmarket" placeholder="营运市场" clearable filterable>
        <el-option
          v-for="item in options1.options"
          :key="item[1]"
          :label="item[1]"
          :value="item[0]"
        ></el-option>
      </el-select>
      <el-select v-model="queryList.itmarket" placeholder="IT市场" clearable filterable>
        <el-option
          v-for="item in options2.options"
          :key="item[1]"
          :label="item[1]"
          :value="item[0]"
        ></el-option>
      </el-select>
      <el-input
        placeholder="门店ID/设备ID"
        prefix-icon="el-icon-search"
        v-model="queryList.blurry"
        @keyup.native.enter="get_live_devicesData"
      ></el-input>
      <!-- </div> -->
      <!--          设备类型-->
      <!-- <el-select v-model="queryList.pmodel" placeholder="设备类型" clearable>
                <el-option v-for="item in options3.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>-->
      <!-- <el-select v-model="queryList.dsusagetype" placeholder="屏幕类型" clearable>
                <el-option v-for="item in options4.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
      </el-select>-->
      <!--          设备状态-->
      <el-select v-model="queryList.isonline" placeholder="设备状态/全部">
        <el-option
          v-for="item in options5.options"
          :key="item[1]"
          :label="item[1]"
          :value="item[0]"
        ></el-option>
      </el-select>
      <el-select v-model="queryList.liveisactive" placeholder="播放状态">
        <el-option
          v-for="item in PlayerStateOption"
          :key="item['value']"
          :label="item['label']"
          :value="item['value']"
        ></el-option>
      </el-select>

      <el-select v-model="queryList.audioplayer" placeholder="音频状态" clearable>
        <el-option
          v-for="item in audioPlayerStateOption"
          :key="item['label']"
          :label="item['label']"
          :value="item['value']"
        ></el-option>
      </el-select>
      <el-select v-model="queryList.livestatus" placeholder="直播状态" clearable>
        <el-option
          v-for="item in LiveStateOption"
          :key="item['label']"
          :label="item['label']"
          :value="item['value']"
        ></el-option>
      </el-select>
      <el-select v-model="queryList.usage_type" placeholder="直播类型">
        <el-option label="实时直播" value="1"></el-option>
        <el-option label="录播" value="2"></el-option>
      </el-select>
      <!--      搜索-->
      <el-button
        type="primary"
        style="background-color: var(--text-color); height: 32px"
        @click="handleSearch"
      >搜索</el-button>
    </div>
    <!--    表格-->
    <el-table
      :height="autoHeight.height"
      ref="multipleTable"
      :data="tableData"
      :row-key="shop_id"
      tooltip-effect="dark"
      style="width: 100%"
      :header-cell-style="getRowClass"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" header-align="center" width="55"></el-table-column> -->
      <!-- <el-table-column label="设备方向" prop="devDir" align="center" width="80">
      </el-table-column>-->
      <el-table-column prop="devId" label="屏幕编号" align="center">
        <template slot-scope="scope">
          <div align="center">
            <!-- <span v-for="(item, index) in scope.row.all_displayinfo"
                            :class="item.display.v_or_h == 'horizontal' ? 'setDev' : 'shu'" class :key="index">{{
            item.displaynum }}</span>-->
            <span
              :class="scope.row.all_displayinfo[0].display.v_or_h == 'horizontal' ? 'setDev' : 'shu'"
            >
              {{
              scope.row.displaynum
              }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="screen_id"
        label="设备ID"
        align="center"
        width="80"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="storecode" label="门店编号" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="storename" label="门店名称" align="center"></el-table-column>
      <el-table-column prop="itmarketname" label="IT市场" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="isactive_display" label="播放状态" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="livestatus_diplay" label="直播状态" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column
        prop="audioplayer_status_display"
        label="音频状态"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="isonline" align="center" label="屏幕状态" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>{{ scope.row.isonline == 0 ? "离线" : "在线" }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="pmodel"
        label="设备类型"
        width="100px"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <!-- <el-table-column prop="tags" align="center" label="设备标签" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="flex" style="flex-wrap: wrap;align-items:center">
                        <div class="flex" style="flex-direction:column;width:105px" v-if="scope.row.tags.length > 2">
                            <div style="display:flex;align-items:center">
                                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                                {{ scope.row.tags[0].name }}
                            </div>
                            <div style="display:flex;align-items:center">
                                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                                {{ scope.row.tags[1].name }}
                            </div>
                            <el-popover placement="top-start" title="设备标签" popper-class="popperOptions" width="200"
                                trigger="hover">
                                <div v-for="item in scope.row.tags" :key="item" style="display:flex;align-items:center">
                                    <img src="../../assets/img/home_img/little_label.svg"
                                        style="width: 24px; height: 24px" />
                                    <span>
                                        {{
                                            item.name
                                        }}
                                    </span>
                                </div>
                                <span class="cursor" slot="reference">...</span>
                            </el-popover>
                        </div>
                        <div class="flex" style="flex-direction:column;width:105px"
                            v-else-if="scope.row.tags.length > 0 && scope.row.tags.length <= 2">
                            <div style="display:flex;align-items:center" v-for="item in  scope.row.tags" :key="item">
                                <img src="../../assets/img/home_img/little_label.svg" style="width: 24px; height: 24px" />
                                {{ item.name }}
                            </div>
                        </div>
                    </div>
                </template>
      </el-table-column>-->

      <el-table-column prop="data" align="center" width="120px" label="操作" show-overflow-tooltip>
        <template slot-scope="scope">
          <div
            class="event"
            style="display: flex;justify-content: space-around;align-items: center; flex-wrap: wrap;"
          >
            <el-button
              @click.native.prevent="detail(scope.row)"
              type="text"
              size="small"
              v-if="checkPer(['dm.scr.detailview'])"
            >详情</el-button>
            <el-button
              type="text"
              size="small"
              v-if="scope.row.isactive_display == '播放'"
              @click="play_and_stop(scope.row, '暂停')"
            >暂停</el-button>
            <el-button
              type="text"
              size="small"
              v-if="scope.row.isactive_display == '暂停'"
              @click="play_and_stop(scope.row, '播放')"
            >播放</el-button>
            <el-button
              type="text"
              size="small"
              v-if="scope.row.audioplayer_status_display == '错误'"
              @click="openAudioDetail(scope.row.audioplayer_info)"
            >音频详情</el-button>
            <el-button @click.native.prevent="deleteLive(scope.row)" type="text" size="small">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title
      :visible.sync="AudioPlayerStateVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-descriptions title="音频异常信息" direction="horizontal" :column="1" border>
        <el-descriptions-item label="音频型号">{{ audioErrorData.box_model }}</el-descriptions-item>
        <el-descriptions-item label="音量">{{ audioErrorData.box_volume }}</el-descriptions-item>
        <el-descriptions-item label="音频版本">{{ audioErrorData.box_rom_version }}</el-descriptions-item>
        <el-descriptions-item label="音频应用版本">{{ audioErrorData.box_app_version }}</el-descriptions-item>
        <el-descriptions-item label="是否在推流">
          {{ audioErrorData.box_streaming == 0 ? '否' : '是'
          }}
        </el-descriptions-item>
        <el-descriptions-item label="推流时长">{{ audioErrorData.box_stream_time }}秒</el-descriptions-item>
        <el-descriptions-item label="盒子推流时钟校对次数">{{ audioErrorData.box_stream_fix }} 次</el-descriptions-item>
        <el-descriptions-item label="开机时长">{{ audioErrorData.box_uptime }}秒</el-descriptions-item>
        <el-descriptions-item label="错误信息 ">{{ audioErrorData.error_info }}</el-descriptions-item>
      </el-descriptions>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="AudioPlayerStateVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <newDevices_Dialog
      @newhandleClose="newhandleClose"
      @searchShopScreen="searchShopScreen"
      @request_Add_devices="request_Add_devices"
      :dialogVisible="dialogVisible"
      ref="newDevices"
      :serachShopSreenList="serachShopSreenList"
    ></newDevices_Dialog>

    <!--    按钮+分页-->
    <div class="bottom">
      <div style="padding-left:20px">
        <el-button type="primary" @click="newDevices">新增设备</el-button>
      </div>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="this.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :pager-count="5"
          background
          layout="total,sizes,prev, pager, next, jumper"
          :total="totalNum"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
  
<script>
import { get_live_devices, change_live_status } from "@/api/live/live";
import { datas_filter_cond } from "@/api/commonInterface";
import { get_adm_datas } from "@/api/device/device";
import { removeDuplicateObj } from "@/utils/setArray";
import newDevices_Dialog from "./component/Dialog.vue";

export default {
  name: "LiveDevice",
  components: {
    newDevices_Dialog
  },
  data() {
    return {
      loading: false,
      pageSize: 10, //每页显示多少条数据
      currentPage: 1, //页码
      totalNum: 0, //数据总数
      //横屏
      hengShow: false,
      shuShow: false,
      inputValue: "",
      queryList: {
        blurry: "", //门店
        isonline: "0", // -1 :all 1: online 0 : offline
        opsmarket: "", //营运市场
        pmodel: "", //设备类型
        itmarket: "",
        dsusagetype: "",
        liveisactive: -1,
        audioplayer: "",
        livestatus: "",
        usage_type: "1"
      },
      //表格
      tableData: [],
      multipleSelection: [],
      //截屏loading
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      options1: [],
      options2: [],
      options3: [],
      options4: [],
      options5: [],
      PlayerStateOption: [
        {
          label: "全部",
          value: -1
        },
        {
          label: "播放",
          value: 1
        },
        {
          label: "暂停",
          value: 0
        }
      ],
      audioPlayerStateOption: [
        {
          label: "正常",
          value: "ok"
        },
        {
          label: "错误",
          value: "error"
        }
      ],
      LiveStateOption: [
        {
          label: "直播中",
          value: "livestart"
        },
        {
          label: "直播结束",
          value: "liveend"
        },
        {
          label: "直播错误",
          value: "liveerr"
        }
      ],
      AudioPlayerStateVisible: false,
      audioErrorData: {},
      dialogVisible: false,
      serachShopSreenList: []
    };
  },

  methods: {
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background: var(--text-color-light);color:#fff";
      }
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = [];
      this.timeSelection = [];
      this.delTagsList = [];
      this.delShowList = [];
      val.forEach(item => {
        item.screen_tags = [];
        this.multipleSelection.push(item.screen_id);
        if (item.pmodel != "X86") {
          this.timeSelection.push(item.screen_id);
        }
        if (item.tags.length != 0) {
          item.tags.forEach(item1 => {
            item.screen_tags.push(item1.name);
            this.delTagsList.push(item1.name);
          });
        }
      });
      // this.delTagsList.forEach((item, index) => {
      //   if (item.length == 0) {
      //     this.delTagsList.splice(index, 1)
      //   }
      // })
      // this.delTagsList = Actiontags(this.delTagsList)
      // this.delTagsList = removeDuplicateObj(this.delTagsList)

      this.delTagsList.forEach(item => {
        this.delShowList.push({
          active: false,
          name: item
        });
      });
      this.delShowList = removeDuplicateObj(this.delShowList);
    },

    //  详情
    detail(data) {
      let tempStr = {};
      data.all_displayinfo.forEach((item, index) => {
        tempStr[item.displaynum] = item.display.v_or_h;
      });
      this.$router.push({
        path: "/deviceManage/screenDetail",
        query: {
          sid: data.screen_id,
          storecode: data.storecode,
          shop_id: data.shop_id,
          pmodel: data.pmodel,
          all_displayinfo: JSON.stringify(tempStr)
        }
      });
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage
      };
      console.log(sessionQuery);
      sessionStorage.setItem(
        "ScreenSearchCondition",
        JSON.stringify(sessionQuery)
      );
      // this.$store.commit("setScreenTags",data.tags)
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 200 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    // 获取数据
    get_live_devicesData() {
      this.loading = true;
      this.queryList.blurry = this.queryList.blurry.toUpperCase();
      const params = {
        classModel: "DsLiveScreens",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize, //每页数据量,
        isonline: this.queryList.isonline,
        //  storecode:'XMN191'
        blurry: this.queryList.blurry,
        opsmarket: this.queryList.opsmarket,
        pmodel: this.queryList.pmodel,
        dsusagetype: this.queryList.dsusagetype,
        itmarket: this.queryList.itmarket,
        loffline: this.queryList.loffline,
        alert_db_act: this.queryList.alert_db_act,
        alert_db_val: this.queryList.alert_db_val,
        liveisactive: this.queryList.liveisactive,
        livestatus: this.queryList.livestatus,
        audioplayer: this.queryList.audioplayer,
        usage_type: this.queryList.usage_type
        //  shop_id:89566
      };
      console.log(params, "params");
      get_live_devices(params)
        .then(res => {
          if (res.rst == "ok") {
            this.tableData = res.data[0].content;
            this.totalNum = res.data[0].totalElements;
            this.loading = false;
            sessionStorage.removeItem("ScreenSearchCondition");
          } else {
            this.$message.error(res.error_msg);
            this.loading = false;
          }
        })
        .catch(rej => {});
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "ScreenMgmt" //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;GroupShop:设备列表帅选条件
      };
      datas_filter_cond(params).then(res => {
        this.options1 = res.data[0][0]; //营运市场下拉数据
        this.options2 = res.data[0][1]; //IT市场下拉数据
        this.options3 = res.data[0][3]; //设备类型下拉数据
        this.options4 = res.data[0][4]; // 屏幕类型下拉数据
        this.options5 = res.data[0][5]; // 设备状态下拉数据

        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
      });
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.get_live_devicesData();
    },
    // pageSize改变
    handleSizeChange(e) {
      this.pageSize = e;
      this.get_live_devicesData();
    },
    // 页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.get_live_devicesData();
    },
    openAudioDetail(row) {
      this.audioErrorData = row;
      console.log(row, "row");
      console.log(this.audioErrorData, "audioErrorData");
      this.AudioPlayerStateVisible = true;
    },
    play_and_stop(devices, type) {
      if (type == "播放") {
        change_live_status({
          screen_ids: [devices.screen_id],
          job: "start",
          usage_type: this.queryList.usage_type
        }).then(res => {
          console.log(res, "res");
          if (res["data"][0]["status"] == 1) {
            this.$message.success(`${type}成功`);
            this.get_live_devicesData();
          }
        });
      } else if (type == "暂停") {
        change_live_status({
          screen_ids: [devices.screen_id],
          job: "stop",
          usage_type: this.queryList.usage_type
        }).then(res => {
          console.log(res, "res");
          if (res["data"][0]["status"] == 1) {
            this.$message.success(`${type}成功`);
            this.get_live_devicesData();
          }
        });
      }
    },
    deleteLive(row) {
      console.log(row);
      console.log(this.queryList.usage_type,'queryList.usage_type');
      this.$confirm("此操作将删除此设备, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          console.log("?");
          change_live_status({
            screen_ids: [row.screen_id],
            job: "remove",
            usage_type:this.queryList.usage_type
          }).then(res => {
            console.log(res, "res");
            if (res["data"][0]["status"] == 1) {
              this.$message({
                type: "success",
                message: "删除成功!"
              });
            } else {
              this.$message({
                type: "error",
                message: res["error_msg"]
              });
            }
            this.get_live_devicesData();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    newDevices() {
      this.$refs.newDevices.newDevicesForm.shop_id = "";
      this.$refs.newDevices.newDevicesForm.usage_type = "1";
      this.$refs.newDevices.active_scree_num = "";
      this.dialogVisible = true;
    },
    newhandleClose() {
      this.serachShopSreenList = [];
      this.dialogVisible = false;
    },
    searchShopScreen(shop_id) {
      get_adm_datas({
        classModel: "ScreenMgmt",
        blurry: shop_id
      }).then(res => {
        let shop_screen = res["data"][0]["content"].map(item => {
          return item.all_displayinfo;
        });

        let screen_list = [];
        shop_screen.forEach(item_screen => {
          item_screen.forEach(item => {
            if (item.display.v_or_h == "horizontal") {
              screen_list.push(item);
            }
          });
        });
        this.serachShopSreenList = screen_list;
      });
    },
    request_Add_devices(params) {
      console.log("request");
      console.log(params, "params");
      change_live_status({
        screen_ids: params["screen_id"],
        usage_type: params["usage_type"],
        job: "add"
      }).then(res => {
        console.log(res, "ressss");
        if (res["data"][0]["status"] == 1) {
          this.$message.success("新增成功");
          this.newhandleClose();
          this.get_live_devicesData();
        } else {
          this.$message.error(res["error_msg"]);
        }
      });
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.getSelectDataList();
    if (this.$route.query) {
      this.queryList.blurry = this.$route.query.screen_id
        ? this.$route.query.screen_id
        : "";
      this.getSelectDataList();
      if (this.$route.query.quertType == 0) {
        this.queryList.alert_db_act = "screenalert";
        this.queryList.loffline = this.$route.query.queryString;
        this.queryList.isonline = "2";
      } else if (this.$route.query.quertType == 1) {
        this.queryList.alert_db_act = "hdmialert";
        // this.queryList.loffline = this.$route.query.queryString;
      } else if (this.$route.query.quertType == 2) {
        this.queryList.alert_db_act = "dsspacealert";
        // this.queryList.alert_db_val = this.$route.query.queryString;
        this.queryList.alert_db_val = "200";
        this.queryList.loffline = this.$route.query.queryString;
      } else if (this.$route.query.quertType == 3) {
        // this.queryList.alert_db_act = "dsspacealert";
        this.queryList.alert_db_act = "upanalert";
        this.queryList.alert_db_val = this.$route.query.queryString;
      }
      console.log(this.queryList, " this.queryList.");
    }

    if (sessionStorage.getItem("ScreenSearchCondition")) {
      let ScreenSearchQuery = JSON.parse(
        sessionStorage.getItem("ScreenSearchCondition")
      );
      this.pageSize = ScreenSearchQuery.pagesize;
      this.currentPage = ScreenSearchQuery.curpage;
      this.queryList = ScreenSearchQuery;
      this.handleSearch();
    } else {
      this.get_live_devicesData();
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
  }
};
</script>
  
<style lang="scss" scoped>
.box {
  background-color: #fff;
  width: 100%;
}

.box > .top {
  display: flex;
  height: 72px;
  align-items: center;
}

.box > .top > div {
  margin: 0.2rem 0.1rem;
}

.box > .top > button {
  height: 0.4rem;
  width: 0.88rem;
}

::v-deep .el-input {
  width: 1.4rem;
}

::v-deep .el-table {
  flex: 1;
}

table {
  color: rgba(91, 91, 91, 1);
  font-size: 14px;
  font-weight: bold;
}

::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-checkbox__inner::before {
  top: 7px !important;
}

::v-deep .el-checkbox__inner::after {
  top: 3px;
  left: 6px;
}

::v-deep .el-checkbox__inner {
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .cell {
  display: flex;
  justify-content: center;
}

::v-deep .cell > img {
  width: 23px;
  height: 23px;
}

.setDev {
  display: inline-block;
  width: 30px;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}

.shu {
  display: inline-block;
  height: 26px;
  width: 22px;
  line-height: 26px;
  vertical-align: middle;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 14px;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}

::v-deep .el-tooltip {
  display: flex;
  flex-wrap: wrap;
}

/*.cell>p{*/
/*  width: 50px;*/
/*  color: rgba(108, 178, 255, 1);*/
/*}*/
::v-deep .el-table__row > td:last-child > .cell.el-tooltip {
  display: flex;
}

::v-deep .el-table .cell.el-tooltip > button {
  width: 50px;
}

.reset {
  color: var(--background-color) !important;
}

.bottom {
  display: flex;
  align-items: center;
  position: relative;
  height: 75px;
  width: 100%;
}

.bottom > button {
  margin-left: 20px;
  width: 106px;
  height: 32px;
}

::v-deep .el-button--primary {
  // line-height: 5px;
}

.block {
  /* width: 4.85rem; */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 0.32rem;
  position: absolute;
  right: 10px;
}

::v-deep .el-pagination__jump > .el-input {
  width: 45px;
}

/*重启颜色*/
.el-button--text:nth-child(2) {
  color: var(--background-color);
}

// .resDevice .el-dialog__header{
//   padding: 20px 41px !important;
// }
.icon {
  font-size: 18px;

  span {
    color: rgba(255, 170, 0, 1);
    font-weight: bold;
    position: absolute;
    font-size: 23px;
    top: 22px;
    left: 10px;
  }
}

.el-button + .el-button {
  margin-left: 5px !important;
}
</style>
<style scoped>
::v-deep .resDevice .el-dialog__title {
  margin-left: 27px !important;
}

.resDevice {
  padding: 0 19px;
}

.resDevice .el-dialog__body {
  margin-top: -26px;
  font-size: 25px;
  color: #ffab04;
  margin-left: -27px;
  border-radius: 4px;
}

.event {
  justify-content: space-between;
}
</style>
  
  
  