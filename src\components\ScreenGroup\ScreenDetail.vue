<template>
  <div class='box'>
    <!--    头部-->
    <div class='useTop'>
      <!--      左边箭头-->
      <div class='pubArrow'>
        <span class='el-icon-back' @click.stop='goBack'></span>
        快捷投放
      </div>
      <!--      右边保存发布按钮-->
      <div class='saveBtns'>
        <div class='save' @click.stop='onlySave'>仅保存</div>
        <div class='savePub' @click.stop='savePub'>保存并发布</div>
      </div>
    </div>
    <!--    表格-->
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        header-align='center'
        width="55">
      </el-table-column>
      <el-table-column
        label="设备方向"
        prop="devDir"
        align='center'
        width="80">
      </el-table-column>
      <el-table-column
        prop="devId"
        label="设备编号"
        align='center'>
        <div v-for='(item,index) in 2' :key='index' class='setDev shu'>
          <span>{{item}}</span>
        </div>
      </el-table-column>
      <el-table-column
        prop="screenId"
        label="屏幕ID"
        align='center'
        width="80"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="storeId"
        label="门店编号"
        align='center'
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="it"
        label="IT市场"
        align='center'
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="screenState"
        align='center'
        label="屏幕状态"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="devType"
        label="设备类型"
        width='100px'
        align='center'
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="devLabel"
        align='center'
        label="设备标签"
        show-overflow-tooltip>
        <img src='../../assets/img/home_img/little_label.svg'>
        菜单屏
      </el-table-column>
      <el-table-column
        prop="data"
        align='center'
        label="注册时间"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="data"
        align='center'
        width='150px'
        label="操作"
        show-overflow-tooltip>
        <p @click='shotScreen'>截屏</p>
        <p class='reset'>重启</p>
<!--        @click.native.prevent="deleteRow(scope.$index, tableData)-->
        <p @click='clickRecord'>记录</p>
        <p>详情</p>
      </el-table-column>
    </el-table>
    <!--    按钮+分页-->
    <div class='bottom'>
      <el-button type="primary">批量标签</el-button>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="5"
          layout="prev, pager, next, jumper"
          :total="25">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreenDetail',
  data(){
    return {

    }
  },
  methods:{
    //记录
    clickRecord(){
      this.$router.push({
        path:"/ShotRecord"
      })
    },
  //  重启
    shotScreen(){
      // console.log("重启")
    }
  }
}
</script>

<style scoped>
.box>.useTop{
  display: flex;
  padding: 0.08rem 0 0.08rem 0.12rem;
  justify-content: space-between;
}
.el-icon-back{
  color: var(--btn-background-color);
  font-size: 0.25rem;
  margin-right: 0.1rem;
  vertical-align: middle;
}
.useShortCuts>.useTop>.pubArrow{
  width: 1.5rem;
  height: 0.4rem;
  line-height: 0.4rem;
  font-size: 0.14rem;
}
table{
  color: rgba(91, 91, 91, 1);
  font-size: 0.14rem;
  font-weight: bold;
}
::v-deep .has-gutter{
  height: 0.42rem;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 0.14rem;
  text-align: center;
}
::v-deep .el-checkbox__inner::after{
  width: 0.02rem;
  height: 0.14rem;
  margin: 0.02rem 0.06rem;
}
::v-deep .el-checkbox__inner{
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
}
::v-deep .cell{
  display: flex;
  justify-content: center;
}
::v-deep .cell>img{
  width: 0.23rem;
  height: 0.23rem;
}
.setDev{
  display: inline-block;
  width: 0.25rem;
  height: 0.23rem;
  line-height: 0.25rem;
  vertical-align: middle;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 0.14rem;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}
.shu{
  display: inline-block;
  height: 0.25rem;
  width: 0.23rem;
  line-height: 0.25rem;
  vertical-align: middle;
  color: rgba(39, 177, 126,1);
  background-color: rgba(108, 178, 255, 0.3642857142857143);
  font-size: 0.14rem;
  border: rgba(108, 178, 255, 1) solid 1px;
  text-align: center;
}
::v-deep .el-tooltip{
  display: flex;
  flex-wrap: wrap;
}
.cell>p{
  width: 0.5rem;
  color: rgba(108, 178, 255, 1);
}
.reset{
  color: var(--background-color) !important;
}
.bottom{
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 0.2rem;
  width: 100%;
}
.bottom>button{
  margin-left: 0.2rem;
  width: 1.06rem;
  height: 0.32rem;
}
::v-deep .el-button--primary{
  line-height: 0.05rem;
}
.block{
  width: 4.85rem;
  height: 0.32rem;
  position: absolute;
  right: 0.1rem;
}
.el-pagination{
  height: 0.32rem;
}

::v-deep .number{
  margin: 0 0.05rem;
  border: 1px solid #cfcfcf;
}
::v-deep .el-pagination>.btn-next{
  width: 0.4rem;
  height: 0.28rem;
  border: 0.01rem solid #ddd;
}
::v-deep .el-pagination__jump>.el-input{
  width: 0.35rem;
}
</style>
