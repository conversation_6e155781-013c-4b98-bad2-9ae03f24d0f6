<template>
  <div>
    <div class="setType">
      <div class="right">
        <div class="left" style="position: relative;">
          <div class="select_type" style="position: relative;margin-left: 10px;">
            <p style="font-weight: bold;">发布类型:</p>
            <div v-for="(item, item_index) in leftMenu.slice(0, 2)" :class="active == item.index ? 'active' : ''"
              :key="item_index" @click="changeActive(item.index)">
              <!-- <i v-show="item.icon" :class="item.icon"></i> -->
              <span v-show="item.arrange" class="ispan" :style="item_index == 1 ? 'width:50px' : ''">
                <span v-for="item1 in item.arrange" :key="item1" :class="item.isStyle == false ? 'text' : ''"></span>
              </span>
              <p>{{ item.name }}</p>
            </div>
            <div :class="active == leftMenu[2].index ? 'active' : ''" @click="changeActive(leftMenu[2].index)"
              v-if="checkPer(['dm.vscf.pub'])">
              <i v-show="leftMenu[2].icon" :class="leftMenu[2].icon"></i>
              <span v-show="leftMenu[2].arrange" class="ispan">
                <span v-for="item1 in leftMenu[2].arrange" :key="item1"
                  :class="leftMenu[2].isStyle == false ? 'text' : ''"></span>
              </span>
              <p>{{ leftMenu[2].name }}</p>
            </div>
          </div>
        </div>
        <div class="lie">
          <AssignShop v-if="active == 0" ref="AssignShop"></AssignShop>
          <Dmblaunch v-else-if="active == 1" ref="Dmblaunch"></Dmblaunch>
          <GeneralLaunch v-else-if="active == 2" ref="GeneralLaunch"></GeneralLaunch>
          <ConnectLaunch v-else-if="active == 3" ref="ConnectLaunch"></ConnectLaunch>
        </div>
      </div>
    </div>
    <div class="bottom">
      <el-button @click="next" v-show="nextActice == 0 || nextActice == 1">下一步</el-button>
    </div>
  </div>
</template>

<script>
import AssignShop from "./AssignShop.vue";
import Dmblaunch from "./Dmblaunch.vue";
import GeneralLaunch from "./GeneralLaunch.vue";
import ConnectLaunch from "./ConnectLaunch.vue";
export default {
  props: {
    nextActice: {
      type: Number
    }
  },
  data() {
    return {
      leftMenu: [
        {
          name: "餐牌组投放",
          arrange: 3,
          index: 1
        },
        {
          name: "普通投放",
          icon: "el-icon-upload",
          arrange: 1,
          isStyle: false,
          index: 2
        },
        {
          name: "联屏投放",
          arrange: 1,
          isStyle: false,
          index: 3
        }
      ],
      active: 1
    };
  },
  components: {
    AssignShop,
    Dmblaunch,
    GeneralLaunch,
    ConnectLaunch
  },

  methods: {
    changeActive(index) {
      this.active = index;
      console.log(this.active, " this.active");
      this.$emit("changeActive", this.active);
    },
    next() {
      let params = null;
      if (this.active == 0) {
        params = this.$refs.AssignShop;
      } else if (this.active == 1) {
        params = this.$refs.Dmblaunch.newDmbForm;
      } else if (this.active == 2) {
        params = this.$refs.GeneralLaunch.newGeneral;
        console.log(
          "this.$refs.GeneralLaunch.newGeneral",
          this.$refs.GeneralLaunch.newGeneral
        );
      } else if (this.active == 3) {
        params = this.$refs.ConnectLaunch.newConnect;
        if (params.scene_type_state == '插播场景') {
          params.scene_type = 1
        } else if (params.scene_type_state == '默认场景') {
          params.scene_type = 0
        }
        console.log(params, 'params');

      }
      this.$emit("next", params);
    }
  }
};
</script>
<style lang="scss" scoped>
.setType {
  display: flex;
  height: 100%;
  margin-top: 26px;
  height: calc(100vh - 370px);
  margin: 0 auto;
  width: 7rem;
  overflow: scroll;


  .left {
    justify-content: space-around;
    align-items: center;
    border-right: 2px solid rgba(229, 229, 229, 1);

    .select_type {
      display: flex;
      padding-left: 25px;
      align-items: center;

      div {
        width: 1.1rem;
        height: 110px;
        // height: 109px;
        border-radius: 3px;
        font-size: 14px;
        color: rgba(80, 80, 80, 1);
        border: var(--dev-border) solid 1px;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: var(--dev-border);
        justify-content: center;
        cursor: pointer;
        margin-left: 10px;

        i {
          font-size: 28px;
        }

        .ispan {
          width: 75px;
          height: 20px;
          border: var(--dev-border) solid 2px;
          display: flex;

          span {
            flex: 1;
          }

          span:nth-child(2n) {
            border-right: var(--dev-border) solid 2px;
          }

          span:nth-child(1) {
            border-right: var(--dev-border) solid 2px;
          }
        }
      }
    }

    p {
      margin-top: 5px;
    }
  }

  .right {
    flex: 6.8;
    margin-top: 30px;
  }
}

.text {
  border: 0 !important;
}

.active {
  background-color: #56ae81;

  p {
    color: #fff !important;
  }

  .ispan {
    span {
      background-color: #fff;
    }
  }
}

.bottom {
  width: 100%;
  text-align: center;
  height: 50px;
  margin-top: 20px;

  button {
    width: 181px;
    height: 48px;
    color: white;
    font-weight: bold;
    background-color: var(--btn-background-color);
    border-radius: 10px;
  }
}

.lie {
  margin: 0 auto;
}
</style>
<style>
.lie .el-form {
  margin-left: -50px !important;
}
</style>