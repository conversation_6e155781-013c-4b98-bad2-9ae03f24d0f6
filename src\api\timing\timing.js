import { get, post } from '@/utils/request'

/**
 * @description 定时开关机
 * @param screen_key  屏幕id
 * @param deploy_info {
 *   on_off_policy {
 *    every_day:[]
 *  }
 * }
 */
const set_screen_timing = p => post('geo/api_digital_signage/json', p, 'screen_edit')

/**
 * @description 批量开关机
 * @param /geo/api_ds/json
 * @param screen_ids
 * @param batch_job:"edit_screen"
 * @param batch_info {
 *  deploy_info:{
 *      on_off_policy:{
 *          every_day:[]
 *      }
 *   }
 * }
 */
const set_batch_screen_timing = p => post('geo/api_ds/json', p, 'screen_batch_processing')

export {
    set_screen_timing,
    set_batch_screen_timing
}
