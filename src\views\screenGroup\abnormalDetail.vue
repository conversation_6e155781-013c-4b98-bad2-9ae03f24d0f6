<template>
  <div class="box" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="拼命加载中,请稍等"
    element-loading-spinner="el-icon-loading">
    <div class="top">
      <!-- 门店id/设备id -->
      <el-input placeholder="门店ID/设备ID" prefix-icon="el-icon-search"
        v-model="queryList.blurry" @keyup.native.enter="get_adm_datas" style="width: auto !important"></el-input>
      <!-- </div> -->
      <!--          设备类型-->
      <el-select v-model="queryList.pmodel" placeholder="设备类型" clearable>
        <el-option v-for="item in options3.options" :key="item[1]" :label="switchGenarateOptions(item[1])"
          :value="item[0]"></el-option>
      </el-select>
      <!-- 屏幕类型 -->
      <el-select v-model="queryList.dsusagetype" placeholder="屏幕类型" clearable>
        <el-option v-for="item in options4.options" :key="item[1]" :label="switchGenarateOptions(item[1])"
          :value="item[0]"></el-option>

      </el-select>
      <!--      搜索-->
      <el-button type="primary" style="background-color: var(--text-color); margin-left: 5px" @click="handleSearch"> 搜索 </el-button>
      <el-button type="primary" style="
          background-color: var(--text-color);
          border: 1px solid var(--text-color);
        " @click="exportExcel" v-if="checkPer(['dm.scr.exportdslist'])">导出异常设备数据</el-button>
      <div class="homebodyLeftTop">
        <span>
          数据更新时间：{{ abUpdateTime }}
        </span>
        <!-- <span class="click-effect2" @click="handleUpdate" > {{ dataUpdataText }} </span> -->
        <span class="click-effect2" @click="handleUpdate"> {{
          this.has_running_job ? '数据更新中...' :'更新数据'
          }} </span>

      </div>
    </div>
    <div class="filter_box">
      <el-dropdown placement="bottom-end" trigger="click" :hide-on-click="false">
        <el-tooltip class="el-dropdown-link" effect="dark" content="列表显示隐藏"
          placement="left">
          <el-button icon="el-icon-menu" circle size="mini"></el-button>
        </el-tooltip>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(option, index) in table_filtering_options" :key="'option_' + index">
            <el-checkbox :value="option.checked" @change="checkFilteringOption($event, option)">{{
              option.label }}</el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!--    表格-->
    <div style="flex: 1; flex-grow: 1; min-height: 1px">
      <el-table v-if="isShowTabel" ref="multipleTable" height="100%" :data="tableData" :row-key="shop_id"
        tooltip-effect="dark" :header-cell-style="getRowClass">
        <el-table-column type="index" width="55">
        </el-table-column>
        <el-table-column prop="devId" label="屏幕编号" align="center" min-width="100"
          v-if="filteredColumns('displaynum')">
          <template slot-scope="scope">
            <div align="center">
              <span v-for="(item, index) in scope.row.all_displayinfo"
                :class="item.display.v_or_h == 'horizontal' ? 'setDev' : 'shu'" class :key="index">{{ item.displaynum
                }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="screen_id" label="设备ID" align="center" min-width="80"
          show-overflow-tooltip v-if="filteredColumns('screen_id')"></el-table-column>
        <el-table-column prop="storecode" label="门店编号" align="center" min-width="100"
          show-overflow-tooltip v-if="filteredColumns('storecode')"></el-table-column>
        <el-table-column prop="storename" label="门店名称" align="center" min-width="100"
          v-if="filteredColumns('storename')"></el-table-column>
        <el-table-column prop="usage_type_cn" label="屏幕类型" align="center"
          min-width="100" v-if="filteredColumns('usage_type_cn')">
          <template slot-scope="scope">
            {{ switchGenarateOptions(scope.row.usage_type_cn) }}
          </template>
        </el-table-column>

        <el-table-column prop="last_online_tm" label="设备异常离线时间"
          min-width="100" align="center" show-overflow-tooltip
          v-if="filteredColumns('last_online_tm')"></el-table-column>
        <el-table-column prop="pmodel" label="设备类型" min-width="100" align="center"
          show-overflow-tooltip v-if="filteredColumns('pmodel')"></el-table-column>
        <el-table-column prop="net_type_diplay" label="网络状态" align="center"
          min-width="180" show-overflow-tooltip v-if="filteredColumns('net_type_diplay')">
          <template slot-scope="scope">
            <div>
              {{ scope.row.lan_ip ? scope.row.lan_ip : "-" }} /
              {{ scope.row.net_type_diplay ? scope.row.net_type_diplay : "-" }}
            </div>
          </template>
        </el-table-column>


      </el-table>
      <el-empty v-else style="height: 100%"></el-empty>
    </div>
    <!--    按钮+分页-->
    <div class="bottom">
      <div class="block">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage" :page-size="this.pageSize" :page-sizes="[10, 20, 50, 100]" :pager-count="5"
          background layout="total,sizes,prev, pager, next, jumper" :total="totalNum"></el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import {
  get_adm_datas,
} from "@/api/device/device";
import { datas_filter_cond } from "@/api/commonInterface";
import { get_latest_ds_shop_stat_item_datas } from '@/api/abnormalDetail/abnormalDetail.js'
import { upt_ds_shop_stat_datas } from "@/api/index/index";
export default {
  name: "ScreenDevice",
  data() {
    return {
      operationModes: [
        { label: "online", value: "online" },
        { label: "offline", value: "offline" },
        // 根据实际需求添加更多选项
      ],
      loading: false,
      pageSize: 10, //每页显示多少条数据
      currentPage: 1, //页码
      totalNum: 0, //数据总数
      queryList: {
        blurry: "", //门店
        isonline: "0", // -1 :all 1: online 0 : offline
        opsmarket: "", //营运市场
        pmodel: "", //设备类型
        jdetrades: "", //商圈类型
        itmarket: "",
        dsusagetype: "",
        screen_tags: [],
        filterscrtags_type: "should",
        stm: "",
        etm: "",
        timeslot: "",
        operatemode: "",
      },
      //运营市场
      options1: [],
      //IT市场
      options2: [],
      //设备类型
      options3: [],
      //屏幕类型
      options4: [],
      //设备状态
      options5: [],
      // 商圈类型
      options7: [],
      //表格
      tableData: [],

      autoHeight: {
        //列表区高度
        height: "",
        heightNum: "",
      },
      table_filtering_options: [
        { label: '屏幕编号', value: "displaynum", checked: true },
        { label: "设备ID", value: "screen_id", checked: true },
        { label: "门店编号", value: "storecode", checked: true },
        { label: "门店名称", value: "storename", checked: true },
        { label: "屏幕类型", value: "usage_type_cn", checked: true },
        { label: '设备异常离线时间', value: 'last_online_tm', checked: true },
        { label: "设备类型", value: "pmodel", checked: true },
        {
          label: "网络状态",
          value: "net_type_diplay",
          checked: true,
        },
      ],
      show_tab_list_checked: [],
      screen_id: "",

      screenOnlineList: [
        ["0", "设备状态"],
        ["1", "在线"],
        ["2", "离线(5分钟)"],
        // ["3", "离线5分钟"],
        ["3", "离线(24小时)"],
        ["4", "离线(72小时)"],
      ],
      condition: [
        {
          label: "且(包含全部已选标签,标签选择不能超过十个)",
          value: "must",
        },
        {
          label: "或(包含任何一个已选标签)",
          value: "should",
        },
        {
          label: "非(不包含任何已选标签)",
          value: "must_not",
        },
      ],
      screenOnline: "0",
      serachShopStatus: [
        {
          label: "营业中",
          value: 9,
        },

        {
          label: "暂停营业",
          value: 8,
        },
        {
          label: "未开店",
          value: 7,
        },
        // {
        //   label: "筹建中",
        //   value: 5,
        // },
        {
          label: "已关店",
          value: 4,
        },
        // {
        //   label: "修整",
        //   value: 3,
        // },
        // {
        //   label: "翻新",
        //   value: 2,
        // },
      ],
      searchTagsList: [],
      isUpdating: false, // 是否正在拉取数据
      clickTimer: null,      // 点击计时器
      dataUpdataText: '更新数据',
      abUpdateTime: '', // 异常离线更新时间，
      has_running_job: 0, //  开关  是否数据正在更新
      abUpdateTime: '',
      export_url: '',
      isShowTabel: true
    };
  },
  watch: {

  },
  computed: {
    filteredColumns() {
      return (label) => {
        if (this.show_tab_list_checked.indexOf(label) == -1) {
          return false;
        } else {
          return true;
        }
      };
    },
  },
  created() {
    this.getHeight();
    let shop_table_filtering = localStorage.getItem("screen_table_filtering2");
    if (shop_table_filtering) {
      this.show_tab_list_checked = JSON.parse(shop_table_filtering);
      this.table_filtering_options.forEach((item) => {
        if (this.show_tab_list_checked.indexOf(item.value) == -1) {
          item.checked = false;
        } else {
          item.checked = true;
        }
      });
    } else {
      this.getTableCheckedList();
    }

    this.getSelectDataList();


    if (JSON.stringify(this.$route.query) != "{}") {
      console.log(this.$route.query.ds_offline_abnormal_cnt, 'ds_offline_abnormal_cnt');

      this.get_latest_ds_shop_stat_item_datas()


    } else {
      if (sessionStorage.getItem("ScreenSearchCondition2")) {
        let ScreenSearchQuery = JSON.parse(
          sessionStorage.getItem("ScreenSearchCondition2")
        );
        this.pageSize = ScreenSearchQuery.pagesize;
        this.currentPage = ScreenSearchQuery.curpage;
        this.queryList = ScreenSearchQuery;
        this.get_latest_ds_shop_stat_item_datas()
      } else {

        this.get_latest_ds_shop_stat_item_datas()
      }
    }


  },
  methods: {
    handleUpdate() {
      // 防抖处理：500ms内重复点击无效
      if (this.clickTimer) return

      this.clickTimer = setTimeout(() => {
        clearTimeout(this.clickTimer)
        this.clickTimer = null
      }, 500)

      // 正在更新时的提示
      if (this.has_running_job > 0) {
        this.$message.warning('数据正在获取中...')
        return
      }
      this.updateData()
    },
    async updateData() {
      try {
        // this.isUpdating = true
        // this.dataUpdataText = this.$transition_lang("dashboardHome.Updating_data")
        const res = await upt_ds_shop_stat_datas({})
        if (res.rst === 'ok') {
          // 成功后的数据获取
          const data = res.data[0] // 次数
          this.currentPage = 1;
          await this.get_latest_ds_shop_stat_item_datas()
        } else {
          this.$message.warning('数据正在获取中...')
          return
        }
      } catch (error) {
        this.$message.error('数据更新失败')

      } finally {
        // this.isUpdating = false
        // this.dataUpdataText = this.$transition_lang("dashboardHome.Update_data")
      }
    },


    switchGenarateOptions(label) {
      return label;
    },
    cbcbcbcb(e) {
      this.queryList.shop_status = e;
    },
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background: var(--text-color-light);color:#fff";
      }
    },
    changeScreenOnline(e) {
      switch (this.screenOnline) {
        case "0":
          this.queryList.isonline = 0;
          this.queryList.loffline = "";
          break;

        case "1":
          this.queryList.isonline = 1;
          this.queryList.loffline = "";
          break;

        case "2":
          this.queryList.isonline = 2;
          this.queryList.loffline = 0;
          break;

        case "3":
          this.queryList.isonline = 2;
          this.queryList.loffline = 24;
          break;
        case "4":
          this.queryList.isonline = 2;
          this.queryList.loffline = 72;
          break;
      }
    },
    // 导出
    exportExcel() {
      if (!this.export_url) {
        return
      }
      // 提取文件名
      const url = this.export_url;
      const fileName = url.substring(url.lastIndexOf('/') + 1);

      //   console.log(data, "下载的数据");
      const a = document.createElement("a");
      document.body.appendChild(a); // 必须插入DOM才能生效
      a.style.display = "none";
      a.href = url;
      a.download = fileName;
      setTimeout(() => {
        a.click();
        URL.revokeObjectURL(a.href); // 释放资源
        document.body.removeChild(a); // 清理DOM
        this.operationalDataDialogVisible = false;
      }, 100);
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 200 + "px";
      this.autoHeight.heightNum = windowHeight - 160;
    },
    // 获取数据
    async get_latest_ds_shop_stat_item_datas() {
      this.loading = true;
      this.queryList.blurry = this.queryList.blurry.toUpperCase();
      const params = {
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize, //每页数据量,
        pmodel: this.queryList.pmodel,
        blurry: this.queryList.blurry,
        usage_type: this.queryList.dsusagetype
      };
      try {
        const res = await get_latest_ds_shop_stat_item_datas(params)
        if (res.rst == "ok") {
          this.abUpdateTime = res.data[0].meta.update_tm
          this.has_running_job = res.data[0].meta.has_running_job
          this.tableData = res.data[0].content;
          this.totalNum = res.data[0].totalElements;
          this.export_url = res.data[0].meta.note.download_url
          this.loading = false;
          // console.log(this.tableData,'tableData');
          if (this.tableData.length > 0) {
            this.isShowTabel = true
          } else {
            this.isShowTabel = false
          }
          this.$forceUpdate()
          // console.log(res.data[0],'get_latest_ds_shop_stat_item_datas');

        } else {
          this.$message.error(res.error_msg);
          this.loading = false;
        }

      } catch (err) {
        console.log(err);
        this.loading = false;

      }



    },
    // 获取数据
    get_adm_datas() {
      this.loading = true;
      this.queryList.blurry = this.queryList.blurry.toUpperCase();
      const params = {
        classModel: "ScreenMgmt",
        sort: "", //非必要，排序规则，storecode,createdAtS
        page: this.currentPage - 1, //起始页码,
        size: this.pageSize, //每页数据量,
        isonline: this.queryList.isonline,
        blurry: this.queryList.blurry,
        opsmarket: this.queryList.opsmarket,
        pmodel: this.queryList.pmodel,
        jdetrades: this.queryList.jdetrades,
        dsusagetype: this.queryList.dsusagetype,
        itmarket: this.queryList.itmarket,
        loffline: this.queryList.loffline,
        alert_db_act: this.queryList.alert_db_act,
        alert_db_val: this.queryList.alert_db_val,
        shop_status: this.queryList.shop_status,
        chk_exp: this.queryList.chk_exp,
        screen_tags: this.queryList.screen_tags,
        filterscrtags_type: this.queryList.filterscrtags_type,
        operatemode: this.queryList.operatemode,
        stm:
          this.queryList.timeslot && this.queryList.timeslot.length > 0
            ? this.queryList.timeslot[0] + "00"
            : "",
        etm:
          this.queryList.timeslot && this.queryList.timeslot.length > 0
            ? this.queryList.timeslot[1] + "00"
            : "",
      };


      get_adm_datas(params)
        .then((res) => {
          if (res.rst == "ok") {
            this.tableData = res.data[0].content;
            this.totalNum = res.data[0].totalElements;
            this.loading = false;
          } else {
            this.$message.error(res.error_msg);
            this.loading = false;
          }
        })
        .catch((rej) => { });
    },
    // 获取下拉数据
    getSelectDataList() {
      const params = {
        classModel: "ScreenMgmt", //GroupShop：店铺列表帅选条件>> GroupTreeRole：角色列表帅选条件;GroupTreeUsers:用户列表帅选条件;GroupTreeJob:职位列表帅选条件;ScreenMgmt:设备列表帅选条件
      };
      datas_filter_cond(params).then((res) => {
        this.options1 = res.data[0][0]; //营运市场下拉数据
        this.options2 = res.data[0][1]; //IT市场下拉数据
        this.options3 = res.data[0][3]; //设备类型下拉数据
        this.options4 = res.data[0][4]; // 屏幕类型下拉数据
        this.options5 = res.data[0][5]; // 设备状态下拉数据
        this.options7 = res.data[0][7]; // 设备状态下拉数据

        //this.allEquipmentList = res.data[0][4]; //全部设备下拉数据
      });
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      //   this.get_adm_datas();
      this.get_latest_ds_shop_stat_item_datas()
    },
    // pageSize改变
    handleSizeChange(e) {
      this.pageSize = e;
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage,
      };
      sessionStorage.setItem(
        "ScreenSearchCondition2",
        JSON.stringify(sessionQuery)
      );
      //   this.get_adm_datas();
      this.get_latest_ds_shop_stat_item_datas()
    },
    // 页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      let sessionQuery = {
        ...this.queryList,
        pagesize: this.pageSize,
        curpage: this.currentPage,
      };
      console.log(sessionQuery, "sessionQuery");
      sessionStorage.setItem(
        "ScreenSearchCondition2",
        JSON.stringify(sessionQuery)
      );
      //   this.get_adm_datas();
      this.get_latest_ds_shop_stat_item_datas()
    },
    checkFilteringOption(status, value) {
      value.checked = status;
      this.getTableCheckedList();
    },

    getTableCheckedList() {
      let arr = [];
      this.table_filtering_options.filter((item) => {
        if (item.checked) {
          arr.push(item.value);
        }
      });
      this.show_tab_list_checked = [...arr];
      localStorage.setItem("screen_table_filtering2", JSON.stringify(arr));
      this.$nextTick(() => {
        // 对 Table 进行重新布局
        // this.$refs.multipleTable?.doLayout();
        this.$refs.multipleTable.doLayout();
      });
    },
  },

  destroyed() {
    // window.removeEventListener("resize", this.getHeight);
    sessionStorage.removeItem("ScreenSearchCondition2");
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .inputFund input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  .inputFund input[type="number"] {
    -moz-appearance: textfield;
  }
}

.box {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  width: 100%;
  height: calc(100vh - 50px);
  min-height: calc(100vh - 50px);
}

.box>.top {
  display: flex;
  padding: 20px 20px 0 0;
  //   padding-right: 20px;
  align-items: center;
  flex-wrap: wrap;
  min-height: 70px;
}

.box>.top>div {
  margin: 5px;
}

table {
  color: rgba(91, 91, 91, 1);
  font-size: 14px;
  font-weight: bold;
}

::v-deep .has-gutter {
  height: 42px;
  color: rgba(80, 80, 80, 1);
  background-color: var(--text-color-light);
  font-size: 14px;
  text-align: center;
}

::v-deep .el-checkbox__inner::before {
  top: 7px !important;
}

::v-deep .el-checkbox__inner::after {
  top: 3px;
  left: 6px;
}

::v-deep .el-checkbox__inner {
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

::v-deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

::v-deep .cell {
  display: flex;
  justify-content: center;
}

::v-deep .cell>img {
  width: 23px;
  height: 23px;
}

.setDev {
  display: inline-block;
  width: 30px;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
  color: #fff;
  background-color: var(--screen-color);
  font-size: 14px;
  border: var(--screen-color) solid 1px;
  text-align: center;
}

.shu {
  display: inline-block;
  height: 26px;
  width: 22px;
  line-height: 26px;
  vertical-align: middle;
  color: #fff;
  background-color: var(--screen-color);
  font-size: 14px;
  border: var(--screen-color) solid 1px;
  text-align: center;
}

::v-deep .el-tooltip {
  display: flex;
  flex-wrap: wrap;
}

/*.cell>p{*/
/*  width: 50px;*/
/*  color: rgba(108, 178, 255, 1);*/
/*}*/
::v-deep .el-table__row>td:last-child>.cell.el-tooltip {
  display: flex;
}

::v-deep .el-table .cell.el-tooltip>button {
  width: 50px;
}

.reset {
  color: var(--background-color) !important;
}

.bottom {
  display: flex;
  align-items: center;
  position: relative;
  height: 75px;
  width: 100%;
}

.bottom>button {
  margin-left: 20px;
  height: 32px;
}

.block {
  /* width: 4.85rem; */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 0.32rem;
  position: absolute;
  right: 10px;
}

::v-deep .el-pagination__jump>.el-input {
  width: 45px;
}

/*重启颜色*/
.el-button--text:nth-child(2) {
  color: var(--background-color);
}

.icon {
  font-size: 18px;

  span {
    color: rgba(255, 170, 0, 1);
    font-weight: bold;
    position: absolute;
    font-size: 23px;
    top: 22px;
    left: 10px;
  }
}

.homebodyLeftTop {

  span {
    color: grey;
    margin-right: 10px;
  }

  .click-effect2 {
    cursor: pointer;
    transition: transform 0.2s;
    color: #3e74f1;

    &:active {
      transform: scale(0.95);
    }
  }

}
</style>
<style scoped>
::v-deep .resDevice .el-dialog__title {
  margin-left: 27px !important;
}

.resDevice {
  padding: 0 19px;
}

.resDevice .el-dialog__body {
  margin-top: -26px;
  font-size: 25px;
  color: #ffab04;
  margin-left: -27px;
  border-radius: 4px;
}

.event {
  justify-content: space-between;
}

.event button {
  width: 50%;
  margin-left: 0 !important;
}

::v-deep .add_screen_drawer {
  box-sizing: border-box;
  padding: 0 10px;
}

::v-deep .add_screen_drawer .el-drawer__header {
  margin-bottom: 0px !important;
  padding: 0px !important;
}

::v-deep .add_screen_drawer .el-tabs {
  margin-bottom: 0px !important;
}

.drawer_close {
  position: absolute;
  top: 0;
  right: 12px;
  font-size: 18px;
  z-index: 10;
}

.drawer_form {
  height: calc(100% - 140px);
  overflow-y: auto;
  /* border: 1px solid blue; */
  box-sizing: border-box;
  border-bottom: 1px solid #dfe4ed;
}

::v-deep .drawer_form .el-radio-group .el-radio {
  border: 1px solid #ececec;
  border-radius: 5px;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__input {
  display: none !important;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__label {
  padding-left: 0 !important;
}

::v-deep .drawer_form .el-radio-group .el-radio .el-radio__label img {
  width: 65px;
  height: 65px;
}

::v-deep .drawer_form .el-radio-group .is-checked {
  border-color: var(--background-color);
  box-sizing: border-box;
}

.my_drawer__footer {
  height: 80px;
  line-height: 80px;
  text-align: right;
  padding: 0 20px;
  box-sizing: border-box;
}

.filter_box {
  height: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20px;
}
</style>