<template>
  <div class="update_hist" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)"
    element-loading-text="拼命加载中,请稍等" element-loading-spinner="el-icon-loading">
    <!-- title -->
    <div class="update_title flex">
      <div>升级记录</div>

      <div class="flex" style="align-items:center">
        <div class="uptext_btn" v-show="isVideoUploading" @click="showProgess = true">上传进度</div>
        <div class="upload_wrap flex">
          <div class="upload_button" @click="upgradePackage" v-if="checkPer(['spr.tags.updateBatch'])">+ 上传升级包</div>
        </div>
      </div>
    </div>

    <!-- 表格区 -->
    <div class="table_wrap">
      <el-table :data="tableData" :height="autoHeight.height" @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px', 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }">
        <!-- <el-table-column type="selection" width="50"></el-table-column> -->
        <el-table-column prop="plan_name_dis" label="任务名称" width></el-table-column>
        <el-table-column prop="ct_time" label="创建时间" width></el-table-column>
        <el-table-column prop="screen_count" label="升级数量" width></el-table-column>
        <el-table-column prop="pkg" label="升级apk" width></el-table-column>
        <el-table-column prop="up_time" label="升级时间" width></el-table-column>
        <el-table-column prop="install_success" label="升级成功" width></el-table-column>
        <el-table-column prop="installing" label="正在升级" width></el-table-column>
        <el-table-column prop="download_success" label="下载完成" width></el-table-column>
        <el-table-column prop="downloading" label="正在下载" width></el-table-column>
        <el-table-column prop="waiting" label="等待响应" width></el-table-column>
        <el-table-column prop="error" label="升级出错" width></el-table-column>

        <el-table-column prop="date" label="升级详情" width>
          <template slot-scope="scope">
            <el-button @click="handleSee(scope.row)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部和页码 -->
    <div class="alert_footer">
      <div class="right_page_wrap flex">
        <div style="min-width:240px">
          <el-button size="small" type="primary" @click="batchUpgrade" v-if="checkPer(['spr.tags.updateBatch'])">批量升级</el-button>
          <!-- <el-button size="small" type="primary" @click="batchExportUpgrade">批量导表升级</el-button> -->
        </div>
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="currentPage" :page-size="pageSize" :pager-count="5" :page-sizes="[10, 20, 50, 100]"
          layout="total,sizes,prev,pager, next, jumper" :total="totalNum"></el-pagination>
      </div>
    </div>

    <!-- 批量升级弹框 -->
    <el-dialog title="批量升级" :visible.sync="dialogVisible" width="697px" :before-close="handleClose"
      :close-on-click-modal="false">
      <div class="dialog_content">
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">升级区域：</span>
          <el-select v-model="dialogQueryList.upgradeArea" multiple collapse-tags filterable placeholder="请选择升级区域"
            size="small" style="width:186px;margin-right:12px">
            <el-option v-for="item in upgradeAreaList" :key="item[0]" :label="item[1]" :value="item[1]"></el-option>
          </el-select>
        </div>
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">升级设备：</span>
          <el-select v-model="dialogQueryList.upgradeEquipment" placeholder="请选择升级设备" size="small"
            @change="getVersionList" style="width:186px;margin-right:12px">
            <el-option v-for="item in upgradeEquipmentList" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
          </el-select>
        </div>
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">应用类型：</span>
          <el-select v-model="dialogQueryList.applicationType" placeholder="请选择应用类型" size="small" @change="getVersionList"
            style="width:186px;margin-right:12px">
            <el-option v-for="item in applicationTypeList" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
          </el-select>
        </div>
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">版本列表：</span>
          <el-select v-model="dialogQueryList.versionList" placeholder="请选择版本列表" size="small"
            style="width:186px;margin-right:12px">
            <el-option v-for="item in versionSelectList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </div>
        <div class="dialog_row" style="margin: -10px 0 13px 0;font-size:12px;color:#e11313;">
          <span style="display:inline-block;width:80px"></span>
          <span>
            <i>*</i> 请先选择升级设备和应用类型后获取版本信息
          </span>
        </div>
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">升级时段：</span>
          <el-date-picker v-model="dialogQueryList.upgradePeriod" type="date" :picker-options="pickerOptions"
            placeholder="请选择升级时段" size="small" style="width:186px;margin-right:12px"></el-date-picker>
        </div>
        <div class="dialog_row">
          <span style="display:inline-block;width:80px">升级时间：</span>
          <el-time-picker v-model="dialogQueryList.startTime" format="HH:mm" placeholder="请选择升级时间" size="small"
            :picker-options="{
              selectableRange: `00:00:00 - ${dialogQueryList.endTime ? new Date(new Date(dialogQueryList.endTime).getTime() - 3600000) : '23:59:59'}`
            }" style="width:186px;"></el-time-picker>
          <span style="border-bottom:1px solid rgba(153, 153, 153, 0);padding-bottom:2px;margin:0 10px">——</span>
          <el-time-picker v-model="dialogQueryList.endTime" format="HH:mm" placeholder="请选择升级时间" size="small"
            :picker-options="{
              selectableRange: `${dialogQueryList.startTime ? new Date(new Date(dialogQueryList.startTime).getTime() + 3600000) : '00:00:00'} - 23:59:59`
            }" style="width:186px;"></el-time-picker>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="handleClose">取 消</el-button>
        <el-button size="medium" type="primary" @click="handleEditConfirm" v-show="!isUploading">确 定</el-button>
        <el-button size="medium" type="primary" style="width:74px" v-show="isUploading">
          <i class="el-icon-loading"></i>
        </el-button>
      </span>
    </el-dialog>

    <!-- 上传dialog -->
    <el-dialog title="上传升级包" :visible.sync="uploadVisible" width="697px" :before-close="uploadDialogClose">
      <div class="dialog_content" style="height:460px">
        <div class="dialog_row upload_list">
          <p class="content_name">升级类型：</p>
          <p class="content_option">
            <el-radio v-for="item in upgradeTypeList" v-model="upgradeType" :label="item.label" :key="item.label">{{
              item.val }}</el-radio>
          </p>
        </div>
        <div class="dialog_row upload_list">
          <p class="content_name">板卡类型：</p>
          <p class="content_option" v-if="upgradeType == 1">
            <el-select v-model="boardType" placeholder="请选择板卡类型"  style="width:420px">
                <el-option v-for="item in boardTypeListRom" :key="item[0]" :label="item[1]" :value="item[0]"></el-option>
            </el-select>
          </p>
          <p class="content_option" v-if="upgradeType == 2">
            <el-radio :disabled="boardDisable" v-for="item in boardTypeListApk" v-model="boardTypeApk" :label="item.label"
              :key="item.label">{{ item.val }}</el-radio>
          </p>
        </div>
        <!-- rom上传链接 -->
        <div class="dialog_row upload_list" v-show="upgradeType == 1">
          <p class="content_name">下载链接：</p>
          <el-input placeholder="请输入升级包下载链接" v-model="dialogQueryList.downloadUrl" style="width:420px"></el-input>
        </div>
        <div class="dialog_row upload_list" v-show="upgradeType == 1">
          <p class="content_name">文件Md5：</p>
          <el-input placeholder="请输入文件Md5值" v-model="dialogQueryList.romMd5" style="width:420px"></el-input>
        </div>
        <!-- apk上传包 -->
        <div class="dialog_row upload_list" style="margin-bottom:0" >
          <p class="content_name">上传包：</p>
          <div class="upload_dragger" id="upload_dragger" @click="openUpload">
            <div class="icon_wrap">
              <i class="el-icon-upload"></i>
            </div>
            <div>
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
          </div>
        </div>
        <div class="upload_file_info">{{ upFileName }}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="uploadDialogClose">取 消</el-button>
        <el-button size="medium" type="primary" @click="uploadConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <input type="file" class="upload_ipt" :accept="upgradeType == 1 ? '.zip,.img' : '.apk'" ref="upload_ipt"
      @change="packageChange" />

    <!-- 上传进度条 -->
    <div style="z-index:9999" class="progress_bar" v-show="showProgess">
      <p class="progress_title">
        <span v-show="isuploadError" style="color:#e11313">上传失败</span>
        <span v-show="istoMd5 && !isuploadError">正在处理升级包，请稍等...</span>
        <span v-show="!istoMd5 && !isuploadError">{{ isVideoUploading ? '上传中 ( 请不要刷新或关闭页面 )' : '上传完成' }}</span>
        <i class="el-icon-close" @click="closeProgress"></i>
      </p>
      <el-progress :percentage="UploadProgress" :stroke-width="16" :color="customColorMethod"
        style="display:flex;align-items: center"></el-progress>
      <!-- <p style="margin-top:15px">上传速率：{{ uploadRate }}</p> -->
    </div>
  </div>
</template>

<script>
// import { up_plan_list } from '@/api/updateApp/update'
import { decrypt } from "@/utils/rsaEncryptUpload";
import {
  datas_filter_cond,
  pull_pkg_info_by_model,
  add_new_dsapk,
  up_plan_list_v2,
  batch_upgrade,
  create_up_plan,
  screen_batch_processing
} from "@/api/systemCenter/updateHist";
import {
  refresh_ali_sts
} from "@/api/files/files";
import readFileMd5 from "../../../static/js/readFileMd5";
import ObsClient from "esdk-obs-browserjs/src/obs";
import OSS from 'ali-oss';
import { resolve } from "path";
import { log } from "util";
export default {
  components: {},
  data() {
    return {
      loading: false,
      currentPage: 1, //页码
      totalNum: 0, //总数据数量
      pageSize: 10,
      tableData: [], //列表数据
      dialogVisible: false, //批量升级弹框
      uploadVisible: false, //上传dialog
      dialogQueryList: {
        upgradeArea: "", //升级区域
        upgradeEquipment: "", //升级设备
        applicationType: "", //应用类型
        versionList: "", //版本列表
        upgradePeriod: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()), //升级时段
        startTime: new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate(), 0, 0), //开始时间
        // endTime: "", //结束时间
        endTime: new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate(), 23, 59), //结束时间
        pkg: "",
        model: "",
        downloadUrl: "", //rom上传，升级包的下载链接
        romMd5: "" //rom上传文件的md5值
      },
      upgradeType: 1,
      upgradeTypeList: [
        { val: "rom", label: 1 },
        { val: "apk", label: 2 }
      ],
      boardType: '',
      boardTypeApk: 1,
      boardDisable: false,
      boardTypeListRom: [
        { val: "X86", label: 1 },
        { val: "lenovo", label: 2 },
        { val: "yanhua", label: 3 },
      ],
      boardTypeListApk: [
        { val: "X86", label: 1 },
        // { val: "lenovo", label: 2 },
        { val: "commonapks", label: 3 }
      ],
      upgradeAreaList: [], //升级区域下拉数据
      upgradeEquipmentList: [], //升级设备下拉数据
      applicationTypeList: [], // 应用类型下拉数据
      versionSelectList: [], //版本列表下拉数据
      autoHeight: {
        //列表区高度
        height: "",
        heightNum: ""
      },
      draggerDom: "",
      dropActive: false,
      upFileName: "",
      upFiles: "",
      isVideoUploading: false,
      istoMd5: false, //是否正在处理Md5
      isuploadError: false, //是否上传失败
      showProgess: false, //是否显示上传进度
      UploadProgress: "00.00", //上传进度
      uploadRate: "0.00 kb/s",
      uploadLink: "https://obs.cn-east-2.myhuaweicloud.com", //上传华为云的server链接
      // 日期选择器不能选择当天之前的日期
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() < new Date().getTime() - 86400000; //  - 86400000是否包括当天
        }
      },
      isUploading: false,
      BucketName:'',
      AK:
        "Dry7QZE+oRvxnOwQkGjWD1+yRRxu9e593eBIflyk9Fk2aQFTmHJ6aEdE8VNyaYUR1wSFaor26bIubShO1WEhCxJNwbUxOgEN/qjY/uvphshD8c+q9XJaMmH45BnqBLPi1rcQu3GyMINFSNK36H1kiHf1wWhTvDwpbJUsZDVatMw=",
      SK:
        "U+AX8iABdSH09dRtfqhZz2IJ82ygx2mcT7rIYmkQDuuJ6SxNwdyGOKdF3tvIgQAdAh2M4Pgdg+Xt0VltGyKYMTVOBVKHzCtjDnW5i4UIvnxKoM9b9tFn0CV0PZ0IJFZ62qMRk7t1xFRzeYe5jVAyW2l4sPDwf6IfoI12nMUt0dc="
    };
  },
  computed: {},
  watch: {
    uploadVisible(val) {
      if (val) {
        this.$nextTick(() => {
          let dropArea = document.getElementById("upload_dragger");
          dropArea.addEventListener("drop", this.dropEvent, false);
          dropArea.addEventListener("dragleave", e => {
            e.stopPropagation();
            e.preventDefault();
            this.dropActive = false;
          });
          dropArea.addEventListener("dragenter", e => {
            e.stopPropagation();
            e.preventDefault();
            this.dropActive = true;
          });
          dropArea.addEventListener("dragover", e => {
            e.stopPropagation();
            e.preventDefault();
            this.dropActive = true;
          });
        });
      }
    }
  },
  created() {
    window.addEventListener("resize", this.getHeight);
    document.addEventListener("drop", this.stops);
    document.addEventListener("dragover", this.stops);
    this.getAliOssInfo();
    this.getHeight();
    this.getSelectList();
    this.getDataLsit();
  },
  mounted() { },
  methods: {
    // 获取列表数据
    getDataLsit() {
      this.loading = true;
      const user_id = localStorage.getItem("user_id");
      const params = {
        ruid: user_id, //（string）登录ID
        iDisplayStart: (this.currentPage - 1) * this.pageSize, //（int）显示的起始位置
        iDisplayLength: this.pageSize, //（int）显示的最大数量
        list_type: "plan"
      };

      up_plan_list_v2(params).then(res => {
        if (res.rst == "ok") {
          this.tableData = res.data[0].up_last_his;
          this.totalNum = res.data[0].iTotalRecords;
          this.loading = false;
        } else {
          this.$message.error(res.error_msg);
          this.loading = false;
        }
      });
    },
    // 查看升级详情
    handleSee(val) {
      this.$router.push({
        path: "/systemCenter/upgradeCity",
        query: {
          name_time: val.name_time
        }
      });
    },
    // 获取升级区域、升级设备、应用类型下拉
    getSelectList() {
      const params = {
        classModel: "BatchUpgrade"
      };
      datas_filter_cond(params).then(res => {
        if (res.rst == "ok") {
          this.upgradeAreaList = res.data[0][0]["options"];
          this.upgradeEquipmentList = res.data[0][1]["options"];
          this.applicationTypeList = res.data[0][2]["options"];
          this.boardTypeListRom = res.data[0][3]["options"];
        } else {
          this.$message.error(res.error_msg);
        }
      });
    },
    // 用 升级设备，应用类型查版本列表
    getVersionList() {
      this.dialogQueryList.versionList = "";
      if (
        this.dialogQueryList.applicationType != "" &&
        this.dialogQueryList.upgradeEquipment != ""
      ) {
        const user_id = localStorage.getItem("user_id");
        const params = {
          ruid: user_id, //（string）登录ID
          pkg: this.dialogQueryList.applicationType, //（string）应用类型
          pmodel: this.dialogQueryList.upgradeEquipment //（string）设备类型
        };
        pull_pkg_info_by_model(params).then(res => {
          this.versionSelectList = res.data[0]["version"];
          this.dialogQueryList.pkg = res.data[0].pkg;
          this.dialogQueryList.model = res.data[0].model;
        });
      }
    },
    // 格式化升级状态
    getUpgradeStatus(row) {
      if (row.error) {
        return "升级失败";
      }
      if (row.downloading) {
        return "下载中";
      }
      if (row.download_success) {
        return "下载完成";
      }
      if (row.installing) {
        return "升级中";
      }
      if (row.install_success) {
        return "升级成功";
      }
      return "等待升级";
    },
    getUpgradeColor(row) {
      if (row.error) {
        return "color:var(--text-color)";
      }
      if (row.install_success) {
        return "color:rgba(31, 152, 80, 1)";
      }
      return "color:rgba(42, 130, 228, 1)";
    },
    // 点击上传按钮触发input的点击事件
    upgradePackage() {
      this.uploadVisible = true;
    },
    openUpload() {
      this.$refs.upload_ipt.dispatchEvent(new MouseEvent("click"));
    },
    //上传dialog关闭、取消
    uploadDialogClose() {
      this.uploadVisible = false;
      this.upFileName = "";
      this.upFiles = "";
      this.$refs.upload_ipt.value = "";
      this.dialogQueryList.downloadUrl = "";
      this.dialogQueryList.romMd5 = "";
      this.upgradeType = 1;
      this.boardType = '';
      this.boardTypeApk = 1;
    },
    //上传dialog确定
    uploadConfirm() {
      if (this.upgradeType == 1) {
        if(this.boardType == ''){
            this.$message.warning("请先选择板卡类型");
            return;
        }
        if(this.upFileName != ""){
            this.updateRomPackage();
        }else{
            if (this.dialogQueryList.downloadUrl.trim() == "") {
                this.$message.warning("请先输入升级包链接或上传升级包文件");
                return;
            }
            if (this.dialogQueryList.romMd5.trim() == "") {
                this.$message.warning("请先输入升级包Md5值");
                return;
            }
            // let checkStatus = this.checkDragSuffix(
            //   this.dialogQueryList.downloadUrl
            // );
            // if (checkStatus == "format_error") {
            //   this.$message.warning("升级包类型错误");
            //   return;
            // } else if (checkStatus == "card_error") {
            //   this.$message.warning("板卡类型与升级包信息不符");
            //   return;
            // }
            let ds_model = this.boardType;
            // switch (this.boardType) {
            //   case 1:
            //     ds_model = "SHOWTOP_x86";
            //     break;
            //   case 2:
            //     ds_model = "Lenovo_3399";
            //     break;
            //   case 3:
            //     ds_model = "st-yh3399-01";
            //     break;
            // }

            // this.getPackageSize().then(res => {
                let params = {
                file_url: this.dialogQueryList.downloadUrl.trim(),
                ds_model,
                //   file_size: res.filesize,
                file_md5: this.dialogQueryList.romMd5.trim(),
                //   file_name: res.filename
                };
                console.log(params, "准备上传的参数");
                add_new_dsapk(params).then(res => {
                if (res.rst == "ok") {
                    this.$message.success("升级包上传成功");
                    this.uploadDialogClose();
                } else {
                    if (res.error_code == 8001) {
                    //DS Apk or Ota version not support.//升级包版本不合法，
                    this.$message.error("升级包版本不合法，");
                    } else if (res.error_code == 8002) {
                    //DS Apk or Ota pkgName not support.//升级包启动报名不合法
                    this.$message.error("升级包启动报名不合法");
                    } else if (res.error_code == 8003) {
                    //DS Apk or Ota dsmodel not support.//升级包对应的板卡型号不合法
                    this.$message.error("升级包对应的板卡型号不合法");
                    }
                }
                });
            //   })
            //   .catch(rej => {
            //     console.log(rej);
            //   });
            return;
        }
        
      } else if (this.upgradeType == 2) {
        if (this.upFileName != "") {
          let checkStatus = this.checkDragSuffix();
          if (checkStatus == "format_error") {
            this.$message.warning("文件类型错误，请重新上传");
            return;
          } else if (checkStatus == "card_error") {
            this.$message.warning("板卡类型与升级包信息不符");
            return;
          }
          this.istoMd5 = true;
          this.isVideoUploading = true;
          this.isuploadError = false;
          let bucketName = "",
            ds_model = "",
            fileDownloadUrl = "";

          // 上传apk
          let key = ''

          switch (this.boardTypeApk) {
            case 1:
              // X86
              // bucketName = "sbml/dsuppkg/apk_update/x86";
              key = "dsuppkg/apk_update/x86/"+this.upFiles.name;
              ds_model = "SHOWTOP_x86";
              fileDownloadUrl = `/dsuppkg/apk_update/x86/${this.upFiles.name}`;
              break;
            case 2:
              //联想
              // bucketName = "sbml/dsuppkg/apk_update/arm/lenovo";
              key = "dsuppkg/apk_update/arm/lenovo/"+this.upFiles.name;
              ds_model = "Lenovo_3399";
              fileDownloadUrl = `/dsuppkg/apk_update/arm/lenovo/${this.upFiles.name}`;
              break;
            case 3:
              // commonapk
              // bucketName = "sbml/dsuppkg/apk_update/commonapks";
              key = "dsuppkg/apk_update/commonapks/"+this.upFiles.name;
              ds_model = "commonapks";
              fileDownloadUrl = `/dsuppkg/apk_update/commonapks/${this.upFiles.name}`;
              break;
          }

          console.log(fileDownloadUrl);
          this.loading = true
          readFileMd5(this.upFiles).then(res => {
            this.istoMd5 = false;
            const params = {
              file_url: fileDownloadUrl,
              ds_model: ds_model,
              file_size: this.upFiles.size,
              file_md5: res.md5,
              file_name: this.upFiles.name
            };
            this.putUploadObjectali(params, key,'apk');
          });
        } else {
          this.$message.warning("请先选择要上传的文件");
          return;
        }
      }

      this.uploadVisible = false;
      this.showProgess = true;
    },
    // 上传升级包-rom
    updateRomPackage(){
        return new Promise((resolve, reject) => {
            this.showProgess = true;
            this.isVideoUploading = true;
            this.uploadVisible = false;
            readFileMd5(this.upFiles).then(res => {
                const {md5} = res;
                
                const key = "dsuppkg/apk_update/"+this.boardType+"/"+this.upFiles.name;
                const ds_model = this.boardType;
                const fileDownloadUrl = `/dsuppkg/apk_update/${this.boardType}/${this.upFiles.name}`;

                this.dialogQueryList.romMd5 = md5;
                

                const params = {
                    file_url: fileDownloadUrl,
                    ds_model: ds_model,
                    file_size: this.upFiles.size,
                    file_md5: md5,
                    file_name: this.upFiles.name
                }
                this.putUploadObjectali(params, key,'rom');
            })
        })
    },
    // rom包上传根据链接解析出升级包文件大小
    getPackageSize() {
      return new Promise((resolve, reject) => {
        let fileUrl = this.dialogQueryList.downloadUrl.trim();
        let index = fileUrl.indexOf(".com/") + 5;
        let splitArr = fileUrl.slice(index).split("/");
        // let splitArr = (fileUrl.split('https://ssdcdn.yumchina.com/')[1]).split('/');
        // let bucketName = "sbml";
        let bucketName = this.BucketName;
        let key = splitArr[splitArr.length - 1];
        let ket_header = ''
        for (let i = 0; i < splitArr.length - 1; i++) {
          ket_header += `${splitArr[i]}`;
        }
        let ack = decrypt(this.AK),
          sek = decrypt(this.SK);
        let obsClient = new ObsClient({
          access_key_id: ack,
          secret_access_key: sek,
          server: this.uploadLink
        });
        console.log(obsClient, 'obsClient');
        obsClient.getObjectMetadata(
          {
            Bucket: bucketName,
            Key: ket_header + '/' + key
          },
          function (err, result) {
            console.log(result, 'result');

            if (err) {
              reject(err);
            } else {
              resolve({
                filesize: result.InterfaceResult.ContentLength,
                filename: key
              });
              // console.log(result.InterfaceResult.ContentLength);
            }
          }
        );
      });
    },
    getAliOssInfo(){
        const params = {
              'expire_tm': '1800',
              'bt': 'wcmp' 
        }
        refresh_ali_sts(params).then(res=>{
            if(res.rst == 'ok'){
                const serverInfo = res.data[0]['ServerInfo'];
                const {BucketName} = serverInfo;
                this.BucketName = BucketName;
            }
        })
    },
    putUploadObjectali(p,key,type){
          const params = {
              'expire_tm': '1800',
              'bt': 'wcmp' 
          }
          let that = this
          refresh_ali_sts(params).then(res=>{
              console.log("res",res)
              if(res.rst == 'ok'){

                  const serverInfo = res.data[0]['ServerInfo'];
                  const {BucketName,ServerHost} = serverInfo;
                    
                  const url = `${BucketName}.${ServerHost}`;
                  
                  p.file_url = 'http://'+url+ p.file_url;
                //   console.log(p);
                  const bucket = BucketName;
                  const region = ServerHost.replace('.aliyuncs.com','');
                  const Credentials = res.data[0]['Credentials'];
                  const { AccessKeyId,AccessKeySecret,SecurityToken } = Credentials;

                  const ossInfo = {
                      region,
                      accessKeyId:AccessKeyId,
                      accessKeySecret:AccessKeySecret,
                      stsToken:SecurityToken,
                      bucket
                  }
                //   console.log(ossInfo,'ossInfo');
                  let client = new OSS(ossInfo);
                  client.multipartUpload(key,that.upFiles,{
                              progress:function(p,cpt){
                                  let percentNum = parseFloat(Math.floor(p * 10000) / 100);
                                  if(percentNum >= 99){
                                      percentNum = 99;
                                  }
                                  that.UploadProgress = percentNum == 100 ? "99.99" : percentNum.toFixed(2);
                                  // console.log("store.state.videoUploadList", store.state.videoUploadList)
                              },
                              partSize:1 * 1024 * 1024,   // 分片上传时每一个分片大小,这里设置为1M
                          }).then(res=>{
                                if(type == 'apk'){
                                    that.addDsapk(p);
                                }else{
                                    that.dialogQueryList.downloadUrl = p.file_url;
                                    that.addDsapk(p);
                                }
                          }).catch(err=>{
                              if(err.name && err.name == 'cancel'){
                                      console.error(err, "传失败信息");
                                      that.$message.error("取消上传");
                                      that.isuploadError = true;
                              }else{
                                      console.error(err, "上传失败信息");
                                      that.$message.error("上传失败");
                                      that.isuploadError = true;
                              }
                              that.loading = false
                          })
              }else{
                  this.$message.error('上传出错',res.error_msg)
              }
          }).catch(err=>{
              this.$message.error('上传出错',err.error_msg)
          })
    },
    // 开始上传华为云
    putUploadObject(p, key) {
        let that = this;
        let ack = decrypt(this.AK),
            sek = decrypt(this.SK);
        let obsClient = new ObsClient({
            access_key_id: ack,
            secret_access_key: sek,
            server: this.uploadLink
        });
        var callback = function (transferredAmount, totalAmount, totalSeconds) {
            // 获取上传平均速率（KB/S）
            let uploadSpeed = (
            (transferredAmount * 1.0) /
            totalSeconds /
            1024
            ).toFixed(2);
            that.uploadRate =
            uploadSpeed / 1024 > 1
                ? (uploadSpeed / 1024).toFixed(2) + " MB/s"
                : uploadSpeed + " kb/s";
            // console.log(s,'获取上传平均速率');
            // 获取上传进度百分比
            let progress = (transferredAmount * 100.0) / totalAmount;
            that.UploadProgress = progress == 100 ? "99.99" : progress.toFixed(2);
            // console.log(this.videoUploadProgress,'获取上传进度百分比');
        };
        obsClient.putObject(
            {
                Bucket: "sbml",
                Key: key,
                SourceFile: this.upFiles,
                ProgressCallback: callback
            },
            function (err, result) {
                if (err) {
                    console.error(err, "华为云上传失败信息");
                    that.$message.error("华为云上传失败");
                    that.isuploadError = true;
                } else {
                    that.addDsapk(p);
                }
            }
        );
    },
    // 上传到华为云后添加升级包
    addDsapk(p) {
      add_new_dsapk(p).then(res => {
        this.loading = false
        if (res.rst == "ok") {
          this.UploadProgress = "100.00";
          this.isVideoUploading = false;
          this.$message.success("升级包上传成功");
          this.uploadDialogClose();
          this.closeProgress();
        } else {
          if (res.error_code == 8001) {
            //DS Apk or Ota version not support.//升级包版本不合法，
            this.$message.error("升级包版本不合法，");
          } else if (res.error_code == 8002) {
            //DS Apk or Ota pkgName not support.//升级包启动报名不合法
            this.$message.error("升级包启动报名不合法");
          } else if (res.error_code == 8003) {
            //DS Apk or Ota dsmodel not support.//升级包对应的板卡型号不合法
            this.$message.error("升级包对应的板卡型号不合法");
          }
          this.upFileName = "";
          this.upFiles = "";
          this.$refs.upload_ipt.value = "";
          this.isuploadError = true;
        }
      });
    },
    // 确定后上传的文件
    packageChange(e) {
      const files = e.target.files[0];
      this.upFileName = files.name;
      this.upFiles = files;
    },
    // 批量升级
    batchUpgrade() {
      this.dialogVisible = true;
    },
    // 批量导表升级
    batchExportUpgrade() {
      this.$message.success("批量导表升级");
    },
    //编辑弹框确认按钮
    handleEditConfirm() {
      let status = this.valueCheck();
      if (status != "") {
        this.$message.warning(status);
        return;
      }
      this.isUploading = true;
      let day = dayjs(this.dialogQueryList.upgradePeriod).format("YYYY-MM-DD");
      let stime = dayjs(this.dialogQueryList.startTime).format("HH:mm");
      let etime = dayjs(this.dialogQueryList.endTime).format("HH:mm");
      
      // let pmodel = ''
      // if(this.dialogQueryList.upgradeEquipment == '3399_yh'){
      //   pmodel = 'YANHUA'
      // }else if(this.dialogQueryList.upgradeEquipment == 'X86'){
      //   pmodel = 'X86'
      // }else if(this.dialogQueryList.upgradeEquipment == '3399_Lenovo'){
      //   pmodel = 'LENOVO'
      // }


      const params = {
        ruid: localStorage.getItem("user_id"),
        group_name: this.dialogQueryList.upgradeArea,
        up_type: "0",
        plan_time: `${day}|${stime}~${etime}`,
        pkg: this.dialogQueryList.pkg,
        pmodel: this.dialogQueryList.upgradeEquipment,
        // pmodel: pmodel,
        model: this.dialogQueryList.model,
        version: this.dialogQueryList.versionList,
        upgrade_type: "batch"
      };
      batch_upgrade(params).then(res => {
        if (res.rst == "ok") {
          setTimeout(() => {
            this.$message.success("创建升级计划成功");
            this.dialogVisible = false;
            this.isUploading = false;
            this.resetDialog();
            this.getDataLsit();
          }, 500);
        } else {
          this.$message.error(res.error_msg);
          this.dialogVisible = false;
          this.isUploading = false;
        }
      });
    },
    // 创建升级计划
    // ceeateUpPlan(params){
    //     create_up_plan(params).then(res=>{
    //         if(res.rst == 'ok'){
    //             this.deviceUpgrade(params)
    //         }else{
    //             if(res.error_msg.results !== 1002){
    //                 this.$message.error(res.error_msg);
    //             }
    //             this.isUploading = false;
    //         }
    //     })
    // },
    // 设备升级
    // deviceUpgrade(val){
    //     let arr = [];
    //     val.screencheck.split('-').forEach(item=>{
    //         if(item != ''){
    //             arr.push(item)
    //         }
    //     })
    //     let params = {
    //         screen_ids:arr,
    //         batch_job:'do_upgrade',
    //         batch_info:'do_upgrade'
    //     }
    //     screen_batch_processing(params).then(res=>{
    //         if(res.rst == 'ok'){
    //             this.$message.success('创建升级计划成功')
    //             this.dialogVisible = false;
    //             this.isUploading = false;
    //             this.getDataLsit();
    //             this.resetDialog();
    //         }else{
    //             this.$message.error(res.error_msg)
    //             this.dialogVisible = false;
    //             this.isUploading = false;
    //         }
    //     })

    // },
    // 校验
    valueCheck() {
      console.log(this.dialogQueryList.startTime);
      console.log(this.dialogQueryList.endTime);

      let startTime = new Date(this.dialogQueryList.startTime).toLocaleString()
      let endTime = new Date(this.dialogQueryList.endTime).toLocaleString()
      console.log(startTime, 'startTime');
      console.log(endTime, 'endTime');

      let value = this.dialogQueryList;
      if (value.upgradeArea == "") {
        return "请先选择升级区域";
      }
      if (value.upgradeEquipment == "") {
        return "请先选择升级设备";
      }
      if (value.applicationType == "") {
        return "请先选择应用类型";
      }
      if (value.versionList == "") {
        return "请先选择升级版本";
      }
      if (value.upgradePeriod == "") {
        return "请先选择升级时段";
      }
      if (value.startTime == "") {
        return "请先选择开始时间";
      }
      if (value.endTime == "") {
        return "请先选择结束时间";
      }
      return "";
    },
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.resetDialog();
    },
    // 清空弹框内容
    resetDialog() {
      this.dialogQueryList.upgradeArea = "";
      this.dialogQueryList.upgradeEquipment = "";
      this.dialogQueryList.applicationType = "";
      this.dialogQueryList.versionList = "";
      this.dialogQueryList.upgradePeriod = new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate());
      this.dialogQueryList.startTime = new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate(), 0, 0);
      this.dialogQueryList.endTime = new Date(new Date().getFullYear(), new Date().getMonth() , new Date().getDate(), 23, 59), //结束时间;
      this.dialogQueryList.pkg = "";
      this.dialogQueryList.model = "";
      this.versionSelectList = [];
    },
    // 列表区高度自适应
    getHeight() {
      let windowHeight = parseInt(window.innerHeight);
      this.autoHeight.height = windowHeight - 168 + "px";
      this.autoHeight.heightNum = windowHeight - 218;
    },
    //页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataLsit();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataLsit();
    },
    dropEvent(e) {
      this.dropActive = false;
      e.stopPropagation();
      e.preventDefault();
      this.uploadFile(e.dataTransfer.files);
    },
    uploadFile(file) {
      // 渲染上传文件
      let files = file[0];
      if (file && file.length) {
        //自行发挥，存本地或上传接口请求
        this.upFileName = files.name;
        this.upFiles = files;
      }
    },
    // 上传检查格式
    checkDragSuffix(val) {
      if (this.upgradeType == 1) {
        let suffix = val.split(".");
        if (
          suffix[suffix.length - 1] != "img" &&
          suffix[suffix.length - 1] != "zip"
        ) {
          return "format_error";
        } else {
          console.log(this.boardType,'boardType');
          if (this.boardType == 1) {
            return val.indexOf("x86") == -1 ? "card_error" : "success";
          } else if (this.boardType == 2) {
            return val.indexOf("Lenovo_3399") == -1 ? "card_error" : "success";
          } else if (this.boardType == 3) {
            console.log(val,'val');
            return val.indexOf("yh") == -1 ? "card_error" : "success";
          }
        }
      } else if (this.upgradeType == 2) {
        let suffix = this.upFileName.split(".");
        if (suffix[suffix.length - 1] != "apk") {
          return "format_error";
        } else {
          if (this.boardTypeApk == 1) {
            return this.upFileName.indexOf("x86") == -1
              ? "card_error"
              : "success";
          } else if (this.boardTypeApk == 2) {
            return this.upFileName.indexOf("armeabi") == -1
              ? "card_error"
              : "success";
          } else if (this.boardTypeApk == 3) {
            return "success";
          }
        }
      }

      return "success";
    },
    //阻止浏览器默认事件，防止将文件没有拖到上传区域时会自动下载
    stops(e) {
      e.stopPropagation();
      e.preventDefault();
    },
    // 关闭进度条
    closeProgress() {
      if (this.isuploadError) {
        this.isVideoUploading = false;
        this.isuploadError = false;
      }
      if (!this.isVideoUploading) {
        this.UploadProgress = "00.00";
        this.uploadRate = "0.00 kb/s";
      }
      this.showProgess = false;
      this.boardType = '';
      this.dialogQueryList.romMd5 = '';
      this.dialogQueryList.downloadUrl = '';
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.getHeight);
    document.removeEventListener("drop", this.stops);
    document.removeEventListener("dragover", this.stops);
  }
};
</script>

<style lang='scss' scoped>
.update_hist {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}

.update_title {
  width: 100%;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  color: rgba(42, 130, 228, 1);
  font-size: 14px;
  line-height: 40px;
  text-align: left;
  font-weight: bold;
  margin-bottom: 17px;
  border-bottom: 1px solid rgba(145, 195, 252, 1);
}

.upload_wrap {
  position: relative;
  height: 32px;
  width: 118px;
}

.upload_ipt {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 0px;
  height: 0px;
  overflow: hidden;
  opacity: 0;
}

.uptext_btn {
  cursor: pointer;
  font-weight: normal;
  margin-right: 15px;
}

.upload_button {
  // position: absolute;
  // left: 0;
  // top: 0;
  // margin-left: 10px;
  width: 118px;
  height: 32px;
  line-height: 32px;
  background: var(--text-color);
  border-radius: 6px;
  font-size: 13px;
  text-align: center;
  color: #fff;
  cursor: pointer;
}

.upload_button:hover {
  background: rgba(211, 57, 57, 0.85);
}

/* 表格区 */
.table_wrap {
  border-bottom: 1px solid rgba(145, 195, 252, 1);
}

/* 底部 */
.alert_footer {
  box-sizing: border-box;
  width: 100%;
  height: 55px;
  font-size: 14px;
  padding-top: 17px;
}

.right_page_wrap {
  align-items: center;
  justify-content: space-between;
}

/* 弹框 */
.dialog_content {
  box-sizing: border-box;
  height: 350px;
  width: 100%;
  overflow-y: scroll;
  padding: 11px 0;
  padding: 16px 40px;
  border: 1px solid rgba(153, 153, 153, 0.29);
}

.dialog_content::-webkit-scrollbar {
  width: 0 !important;
}

.dialog_content {
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
}

.dialog_row {
  margin-bottom: 17px;
}

.upload_list {
  display: flex;
}

.upload_list .content_name {
  width: 100px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  color: rgba(80, 80, 80, 1);
  font-weight: bold;
}

.upload_list .content_option {
  height: 30px;
  line-height: 30px;
}

.upload_dragger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
  height: 200px;
  width: 350px;
  border: 1px dashed rgb(217, 217, 217);
  cursor: pointer;
  border-radius: 6px;
}

.upload_dragger:hover {
  border-color: #409eff;
}

.upload_dragger em {
  color: #409eff;
  font-style: normal;
}

.icon_wrap {
  margin-top: -20px;
}

.icon_wrap i {
  font-size: 67px;
  color: #c0c4cc;
}

.upload_file_info {
  padding-left: 100px;
  padding-top: 10px;
}

.progress_bar {
  --wrap-width: 450px;
  --wrap-height: 125px;
  /* border: 1px solid red; */
  border: 1px solid #ebeef5;
  position: absolute;
  width: var(--wrap-width);
  height: var(--wrap-height);
  top: calc(50% - (var(--wrap-height) / 2) - 50px);
  left: calc(50% - (var(--wrap-width) / 2));
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  padding: 15px;
  z-index: 2;
}

.progress_title {
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
  padding-bottom: 0px;

  i {
    cursor: pointer;
  }
}

.show_progress {
  // width: 95px;
  color: #409eff;
  margin-right: 8px;
  cursor: pointer;
}

::v-deep .el-progress__text {
  font-size: 16px !important;
}

.err_tips {
  color: #f56c6c;
  font-size: 14px;
}
</style>
<style >
.update_hist .el-dialog {
  border-radius: 16px;
}

.update_hist .el-dialog .el-dialog__header {
  padding-top: 15px !important;
  padding-bottom: 10px !important;
  /* border-bottom:1px solid rgba(153, 153, 153, 0.29) */
}

.update_hist .el-dialog .el-dialog__header .el-dialog__title {
  font-size: 16px !important;
}

.update_hist .el-dialog .el-dialog__body {
  padding-top: 10px !important;
  padding-bottom: 0 !important;
  /* padding-right:0px !important; */
}
</style>
