<template>
    <div class="push_screen">
        <!-- 搜索区 -->
        <div class="flex push_screen_header">
            <div class="go_back">
                <i class="el-icon-back" @click="goBack"></i>
            </div>
            <div class="search">
                <!-- <el-input placeholder="门店ID/设备ID" size="small" prefix-icon="el-icon-search"
                    @keyup.enter.native='handleSearch' v-model="queryList.search" style="width:166px;margin-right:12px">
                </el-input> -->
                <!-- <el-select v-model="queryList.shebeitype" clearable filterable placeholder="设备类型/全部" size="small" style="width:166px;margin-right:12px">
                    <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
                </el-select>
                <el-select v-model="queryList.fangxiang" clearable filterable placeholder="屏幕方向" size="small" style="width:166px;margin-right:12px">
                    <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]" :value="item[0]"></el-option>
                </el-select> -->
                <!-- <el-select v-model="queryList.screen_id" clearable filterable placeholder="屏幕类型" size="small"
                    style="width:166px;margin-right:12px">
                    <el-option v-for="item in operatingMarketList.options" :key="item[1]" :label="item[1]"
                        :value="item[0]"></el-option>
                </el-select> -->
                <el-input placeholder="门店ID/设备ID" size="small" prefix-icon="el-icon-search"
                    @keyup.enter.native='handleSearch' v-model="queryList.screen_id"
                    style="width:166px;margin-right:12px" clearable>
                </el-input>

                <el-button size="small" class="btn" type="danger" style="" @click="handleSearch">搜索</el-button>
            </div>
        </div>
        <!-- 列表区 -->
        <div class="table_wrap" :style="{ height: autoHeight.height }">
            <el-table :data="tableData" v-loading='loading' :height="autoHeight.height"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#24b17d', color: '#fff', 'font-size': '13px' }">

                <el-table-column prop="screen_id" label="屏幕ID" width="" align="center"></el-table-column>
                <el-table-column prop="display_num" label="屏幕序号" width="" align="center"></el-table-column>
                <el-table-column prop="use_type" label="屏幕类型" width="" align="center"></el-table-column>
                <el-table-column prop="pmodel" label="设备类型" width="" align="center"></el-table-column>
                <el-table-column prop="v_or_h" label="屏幕方向" width="" align="center"></el-table-column>
                <el-table-column prop="zip_cnt" label="素材数量" width="" align="center"></el-table-column>
                <el-table-column prop="program_cnt" label="内容数量" width="" align="center"></el-table-column>
                <el-table-column prop="error_cnt" label="失败数量" width="" align="center"></el-table-column>
                <el-table-column prop="isonline" label="设备状态" width="" align="center">
                    <template slot-scope="scope">
                        <el-popover trigger="click" width="250" placement="top" :ref="`popover${scope.$index}`">
                            <!-- scope.row.xxxx 获取该行数据 -->
                            <div class="flex">
                                <div style="margin-right:10px">
                                    <i v-if="scope.row.isonline == 0" class="el-icon-circle-close"
                                        style="color:var(--text-color);font-size:22px"></i>
                                    <i v-else-if="scope.row.isonline == 1" class="el-icon-circle-check"
                                        style="color:rgba(23, 159, 78, 1);font-size:22px"></i>
                                    <i v-else-if="scope.row.isonline == 2" class="el-icon-warning-outline"
                                        style="color:#e6a23c;font-size:22px"></i>
                                </div>
                                <div>
                                    <p style="font-weight:bold;font-size:15px;margin-bottom:5px;">
                                        设备{{ getOnlineStatus(scope.row.isonline) }}</p>
                                    <p style="font-size:12px">请去设备查看{{ getOnlineStatus(scope.row.isonline) }}详情</p>
                                </div>
                            </div>

                            <!-- 触发popover的节点 -->
                            <div slot="reference" class="name-wrapper" style="cursor:pointer;">
                                <div :class="getOnlineClass(scope.row.isonline)">{{ getOnlineStatus(scope.row.isonline)
                                }}
                                </div>
                                <!-- {{ scope.row.isonline?'在线':'离线' }} -->
                            </div>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right" align="center">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="toDeviceDetails(scope.row)" type="text" size="small">设备详情
                        </el-button>
                        <el-button @click.native.prevent="toContentView(scope.row)" type="text" size="small">内容查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 底部以及页码 -->
        <div class="push_screen_footer">
            <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="currentPage" :page-size="pageSize" :pager-count='5' :page-sizes="[10, 20, 50, 100]"
                layout="total,sizes,prev,pager, next, jumper" :total="totalNum">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { get_screen_content_pub_status_list } from "@/api/historical/historical"

export default {
    components: {

    },
    data() {
        return {
            loading: false,
            currentPage: 0, //页码
            totalNum: 20, //总数据数量
            pageSize: 10,
            queryList: {
                search: '',
                shebeitype: '',
                fangxiang: '',
                pingmuleixing: ''
            },
            operatingMarketList: {
                options: [
                    [
                        "8",
                        "快闪/急速点餐"
                    ],
                    [
                        "4",
                        "汽车穿梭"
                    ],
                    [
                        "2",
                        "外带窗口"
                    ],
                    [
                        "7",
                        "多层/多个柜台"
                    ],
                    [
                        "0",
                        "常规POS"
                    ],
                    [
                        "9",
                        "备用店（命名待定）"
                    ],
                    [
                        "3",
                        "咖啡角"
                    ],
                    [
                        "6",
                        "K-bar"
                    ],
                    [
                        "1",
                        "标准店"
                    ],
                    [
                        "5",
                        "甜品站(店内)"
                    ]
                ]
            },
            tableData: [  //列表数据
            ],
            autoHeight: {    //列表区高度
                height: '',
                heightNum: '',
            },
        };
    },
    computed: {

    },
    watch: {

    },
    created() {
        window.addEventListener('resize', this.getHeight);
        this.getHeight();
        this.get_screen_content_pub_status_list()
    },
    mounted() {

    },
    methods: {
        handleSearch() {
            console.log(this.queryList);
            this.loading = true;
            this.get_screen_content_pub_status_list()
        },
        get_screen_content_pub_status_list() {
            const params = {
                "batch_id": this.$route.query.batch_id,
                "shop_id": this.$route.query.shop_id,
                "page_size": this.pageSize,
                "page_num": this.currentPage,
                "screen_id": this.queryList.screen_id
            }
            get_screen_content_pub_status_list(params).then((res) => {
                console.log(res);
                if (res.rst == "ok") {
                    this.tableData = res.data[0].content;
                    this.totalNum = res.data[0].totalElements;
                    this.loading = false;
                } else {
                    this.$message.warning(res.error_msg);
                }
            });
        },
        // 设备详情
        toDeviceDetails(data) {
            // this.$message.success('设备详情')
            this.$router.push({
                path: "/deviceManage/screenDetail",
                query: { sid: data.screen_id, storecode: data.store_code, shop_id: this.$route.query.shop_id },
            });
        },
        // 内容查看
        toContentView(data) {
            this.$router.push({
                path: "/deploy/contentview",
                query: { sid: data.screen_id, batch_id: this.$route.query.batch_id, shop_id: this.$route.query.shop_id },
            });
            // this.$router.push({path:'/deploy/contentview'})
        },
        // 关闭Popover
        closePopover(index) {
            this.$refs[`popover${index}`].doClose()
        },
        //页码改变
        handleCurrentChange(val) {
            console.log(`现在是第${val}页`);
            this.currentPage = val;
        },
        handleSizeChange(val) {
            console.log(`每页${val}条`);
            this.pageSize = val;
        },
        goBack() {
            this.$router.go(-1);
        },
        getOnlineClass(val) {
            switch (val) {
                case 0:
                    return 'off_line';
                case 1:
                    return 'on_line'
                case 2:
                    return 'error'
            }
        },
        getOnlineStatus(val) {
            switch (val) {
                case 0:
                    return '离线';
                case 1:
                    return '在线'
                case 2:
                    return '异常'
            }
        },
        // 列表区高度自适应
        getHeight() {
            let windowHeight = parseInt(window.innerHeight);
            this.autoHeight.height = windowHeight - 173 + 'px';
            this.autoHeight.heightNum = windowHeight - 168;
        },
    },
    destroyed() {
        window.removeEventListener('resize', this.getHeight);
    },
};
</script>

<style lang='scss' scoped>
* {
    box-sizing: border-box;
}

.push_screen {
    width: 100%;
    padding: 0 11px;
}

/* 底部页码区 */
.push_screen_footer {
    box-sizing: border-box;
    width: 100%;
    height: 66px;
    font-size: 14px;
    padding-top: 17px;
    text-align: right;
    padding-right: 20px;
}

.push_screen_header {
    align-items: center;
    justify-content: space-between;
    padding: 6px 0;

    .go_back {
        display: flex;
        height: 45px;
        align-items: center;

        i {
            color: rgba(108, 178, 255, 1);
            font-weight: bold;
            font-size: 24px;
            cursor: pointer;
        }
    }
}

.btn {
    height: 32px;
    width: 88px;
    border-radius: 6px;
    background: var(--text-color);
    border: 1px solid var(--text-color);
}

.off_line {
    color: var(--text-color);
}

.on_line {
    color: rgba(23, 159, 78, 1);
}

.error {
    color: rgba(56, 56, 56, 1);
}
</style>
<style>
.el-popover {
    overflow-y: visible !important;
}
</style>
